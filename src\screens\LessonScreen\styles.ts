import { StyleSheet, Dimensions } from 'react-native';
import { colors } from '../../theme/colors';
import { fonts } from '../../theme/fonts';

const { width } = Dimensions.get('window');

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    backgroundColor: colors.card,
    paddingHorizontal: 16,
    paddingTop: 8,
    paddingBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
    elevation: 2,
    zIndex: 2,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  lessonTitle: {
    fontSize: fonts.size.large,
    fontWeight: 'bold',
    color: colors.primary,
    fontFamily: fonts.bold,
    marginBottom: 2,
  },
  dayNavContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dayText: {
    fontSize: fonts.size.medium,
    color: colors.textSecondary,
    fontFamily: fonts.regular,
  },
  arrowButton: {
    padding: 8,
  },
  summaryButton: {
    marginLeft: 12,
    padding: 8,
  },
  contentContainer: {
    flex: 1,
    backgroundColor: colors.background,
    paddingHorizontal: 16,
    paddingTop: 8,
  },
  fabContainer: {
    position: 'absolute',
    right: 24,
    // bottom: definido dinamicamente pelo SafeArea
    zIndex: 10,
  },
  fabButton: {
    backgroundColor: colors.primary,
    borderRadius: 28,
    width: 56,
    height: 56,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
});

export default styles; 