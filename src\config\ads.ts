/**
 * Configuração de Anúncios AdMob
 * 
 * Para publicação na Google Play Store, sempre usar IDs de produção.
 * Para desenvolvimento/teste, pode alternar entre teste e produção.
 */

import { TestIds } from 'react-native-google-mobile-ads';

// IDs de Produção (reais)
export const PRODUCTION_ADS = {
  APP_ID: 'ca-app-pub-8578368178484138~9333274602',
  BANNER_ID: 'ca-app-pub-8578368178484138/4623873403',
  INTERSTITIAL_ID: 'ca-app-pub-8578368178484138/6541258600',
};

// IDs de Teste (para desenvolvimento)
export const TEST_ADS = {
  APP_ID: 'ca-app-pub-3940256099942544~3347511713', // Test App ID
  BANNER_ID: TestIds.BANNER,
  INTERSTITIAL_ID: TestIds.INTERSTITIAL,
};

// Configuração atual - SEMPRE PRODUÇÃO para publicação
export const AD_CONFIG = {
  // Para publicação na Google Play Store
  USE_PRODUCTION: true,
  
  // Configurações de comportamento
  MIN_INTERVAL_BETWEEN_ADS: 30000, // 30 segundos
  NAVIGATION_AD_FREQUENCY: 3, // Mostrar anúncio a cada 3 navegações
  TAB_PRESS_AD_FREQUENCY: 4, // Mostrar anúncio a cada 4 mudanças de tab
  
  // Configurações de timeout
  AD_LOAD_TIMEOUT: 10000, // 10 segundos
  NAVIGATION_DELAY_AFTER_AD: 500, // 500ms
};

// IDs ativos (baseado na configuração)
export const ACTIVE_ADS = AD_CONFIG.USE_PRODUCTION ? PRODUCTION_ADS : TEST_ADS;

// Funções utilitárias
export const getAdUnitId = (type: 'banner' | 'interstitial'): string => {
  switch (type) {
    case 'banner':
      return ACTIVE_ADS.BANNER_ID;
    case 'interstitial':
      return ACTIVE_ADS.INTERSTITIAL_ID;
    default:
      throw new Error(`Tipo de anúncio inválido: ${type}`);
  }
};

export const isProductionMode = (): boolean => {
  return AD_CONFIG.USE_PRODUCTION;
};

export const getAdConfig = () => {
  return {
    ...AD_CONFIG,
    activeAds: ACTIVE_ADS,
    mode: AD_CONFIG.USE_PRODUCTION ? 'PRODUCTION' : 'TEST',
  };
};

// Log da configuração atual
console.log('🎯 Configuração de Anúncios:', {
  mode: AD_CONFIG.USE_PRODUCTION ? 'PRODUCTION' : 'TEST',
  bannerId: ACTIVE_ADS.BANNER_ID,
  interstitialId: ACTIVE_ADS.INTERSTITIAL_ID,
  appId: ACTIVE_ADS.APP_ID,
});
