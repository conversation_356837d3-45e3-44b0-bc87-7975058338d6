# Script PowerShell para gerar build de produção para Google Play Store
# Este script garante que os anúncios estejam configurados para produção

Write-Host "🚀 Iniciando build de produção para Google Play Store..." -ForegroundColor Green

# Verificar se estamos no diretório correto
if (-not (Test-Path "package.json")) {
    Write-Host "❌ Erro: Execute este script na raiz do projeto React Native" -ForegroundColor Red
    exit 1
}

# Verificar se o Android SDK está configurado
if (-not $env:ANDROID_HOME) {
    Write-Host "❌ Erro: ANDROID_HOME não está configurado" -ForegroundColor Red
    Write-Host "Configure o Android SDK antes de continuar" -ForegroundColor Yellow
    exit 1
}

Write-Host "📋 Verificando configurações de anúncios..." -ForegroundColor Cyan

# Verificar se os anúncios estão configurados para produção
$adsConfig = Get-Content "src/config/ads.ts" -Raw
if ($adsConfig -match "USE_PRODUCTION: true") {
    Write-Host "✅ Anúncios configurados para PRODUÇÃO" -ForegroundColor Green
} else {
    Write-Host "❌ Erro: Anúncios não estão configurados para produção" -ForegroundColor Red
    Write-Host "Verifique src/config/ads.ts e defina USE_PRODUCTION: true" -ForegroundColor Yellow
    exit 1
}

Write-Host "🧹 Limpando cache e dependências..." -ForegroundColor Cyan

# Limpar cache do npm
npm cache clean --force

# Limpar build anterior do Android
Set-Location android
if (Test-Path "gradlew.bat") {
    .\gradlew.bat clean
} else {
    .\gradlew clean
}
Set-Location ..

Write-Host "📦 Instalando dependências..." -ForegroundColor Cyan
npm install

Write-Host "🔧 Gerando bundle de produção..." -ForegroundColor Cyan

# Criar diretório de assets se não existir
$assetsDir = "android/app/src/main/assets"
if (-not (Test-Path $assetsDir)) {
    New-Item -ItemType Directory -Path $assetsDir -Force
}

# Gerar bundle JavaScript para produção
npx react-native bundle `
    --platform android `
    --dev false `
    --entry-file index.js `
    --bundle-output android/app/src/main/assets/index.android.bundle `
    --assets-dest android/app/src/main/res

Write-Host "🏗️ Compilando APK de produção..." -ForegroundColor Cyan

# Compilar APK de release
Set-Location android
if (Test-Path "gradlew.bat") {
    .\gradlew.bat assembleRelease
} else {
    .\gradlew assembleRelease
}
Set-Location ..

Write-Host "📱 Gerando AAB para Google Play Store..." -ForegroundColor Cyan

# Gerar Android App Bundle (AAB) para Google Play
Set-Location android
if (Test-Path "gradlew.bat") {
    .\gradlew.bat bundleRelease
} else {
    .\gradlew bundleRelease
}
Set-Location ..

Write-Host "✅ Build de produção concluído!" -ForegroundColor Green
Write-Host ""
Write-Host "📁 Arquivos gerados:" -ForegroundColor Cyan
Write-Host "   APK: android/app/build/outputs/apk/release/app-release.apk" -ForegroundColor White
Write-Host "   AAB: android/app/build/outputs/bundle/release/app-release.aab" -ForegroundColor White
Write-Host ""
Write-Host "📋 Próximos passos:" -ForegroundColor Cyan
Write-Host "1. Teste o APK em dispositivos reais" -ForegroundColor White
Write-Host "2. Verifique se os anúncios estão funcionando" -ForegroundColor White
Write-Host "3. Faça upload do AAB para o Google Play Console" -ForegroundColor White
Write-Host ""
Write-Host "⚠️  IMPORTANTE:" -ForegroundColor Yellow
Write-Host "   - Os anúncios estão configurados para PRODUÇÃO" -ForegroundColor White
Write-Host "   - Teste em dispositivos reais antes de publicar" -ForegroundColor White
Write-Host "   - Verifique se todos os anúncios carregam corretamente" -ForegroundColor White
