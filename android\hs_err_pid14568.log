#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 1052736 bytes. Error detail: Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:168), pid=14568, tid=5956
#
# JRE version: OpenJDK Runtime Environment (21.0.5) (build 21.0.5+-13047016-b750.29)
# Java VM: OpenJDK 64-Bit Server VM (21.0.5+-13047016-b750.29, mixed mode, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -XX:MaxMetaspaceSize=512m -XX:+HeapDumpOnOutOfMemoryError -Xmx1536m -Dfile.encoding=UTF-8 -Duser.country=BR -Duser.language=pt -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.14.1-bin\baw1sv0jfoi8rxs14qo3h49cs\gradle-8.14.1\lib\agents\gradle-instrumentation-agent-8.14.1.jar org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.14.1

Host: Intel(R) Xeon(R) CPU E3-1220 V2 @ 3.10GHz, 4 cores, 7G,  Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
Time: Wed Aug  6 14:23:47 2025 Hora oficial do Brasil elapsed time: 97.075206 seconds (0d 0h 1m 37s)

---------------  T H R E A D  ---------------

Current thread (0x000001756556be60):  JavaThread "C2 CompilerThread1" daemon [_thread_in_native, id=5956, stack(0x0000005f8c500000,0x0000005f8c600000) (1024K)]


Current CompileTask:
C2:97075 28389       4       com.android.tools.r8.internal.A9::a (1719 bytes)

Stack: [0x0000005f8c500000,0x0000005f8c600000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6cfb29]
V  [jvm.dll+0x85df93]
V  [jvm.dll+0x8604ee]
V  [jvm.dll+0x860bd3]
V  [jvm.dll+0x27e6b6]
V  [jvm.dll+0xbff6d]
V  [jvm.dll+0xc04a3]
V  [jvm.dll+0x2f2750]
V  [jvm.dll+0x5fa0c9]
V  [jvm.dll+0x250d62]
V  [jvm.dll+0x24956d]
V  [jvm.dll+0x24712e]
V  [jvm.dll+0x1c5ee4]
V  [jvm.dll+0x25697c]
V  [jvm.dll+0x254ec6]
V  [jvm.dll+0x3f0ce6]
V  [jvm.dll+0x806368]
V  [jvm.dll+0x6ce3fd]
C  [ucrtbase.dll+0x21bb2]
C  [KERNEL32.DLL+0x17374]
C  [ntdll.dll+0x4cc91]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00000175691b1c90, length=102, elements={
0x000001753eb53eb0, 0x000001755527a490, 0x000001755527b1a0, 0x000001755527ce80,
0x000001755527d4e0, 0x000001755527df30, 0x0000017555265210, 0x0000017555267c70,
0x0000017555268310, 0x0000017554f365c0, 0x000001755559d180, 0x000001755b22fc90,
0x000001755aa04be0, 0x000001755adeb610, 0x000001755b21fd30, 0x000001755b354810,
0x000001755b29f7e0, 0x000001755b29eac0, 0x000001755b29dda0, 0x000001755b29f150,
0x000001755b8a7ff0, 0x000001755b8a5f20, 0x000001755b8a65b0, 0x000001755cc8ee80,
0x000001755cc90230, 0x000001755cc90f50, 0x000001755cc915e0, 0x000001755cc92300,
0x000001755cc8e7f0, 0x000001755cc8d440, 0x000001755cc8ba00, 0x000001755cc8c720,
0x000001755cc8e160, 0x00000175556960c0, 0x0000017555699540, 0x0000017555697b00,
0x000001755569c9c0, 0x000001755569a8f0, 0x0000017555696750, 0x000001755569d6e0,
0x0000017555698820, 0x000001755569af80, 0x000001755569b610, 0x000001755569d050,
0x0000017555696de0, 0x000001755b29d080, 0x000001755b8a72d0, 0x000001755eb785a0,
0x000001755eb764d0, 0x000001755eb7ad00, 0x000001755eb7c740, 0x000001755eb79950,
0x000001755eb7d460, 0x000001755eb7b390, 0x000001755eb7ba20, 0x000001755eb7a670,
0x000001755eb76b60, 0x000001755eb7daf0, 0x000001755eb792c0, 0x000001755eb79fe0,
0x000001755eb7cdd0, 0x000001755eb771f0, 0x000001755eb7c0b0, 0x000001755b8a6c40,
0x000001755b8a5890, 0x000001755b8a8680, 0x000001755b2a0500, 0x000001755b29e430,
0x000001755c8eaec0, 0x000001755c8ebbe0, 0x000001755c8ed620, 0x000001755c8ec900,
0x000001755c8ecf90, 0x000001755c8edcb0, 0x000001755c8ee340, 0x000001755c8eb550,
0x000001755c8ec270, 0x000001755cad4520, 0x000001755cad1dc0, 0x000001755caccf00,
0x000001755cad1730, 0x000001755cacefd0, 0x000001755cad2450, 0x000001755cacd590,
0x000001755cace2b0, 0x000001755cacfcf0, 0x000001755cad2ae0, 0x000001755f42a530,
0x000001755cad0a10, 0x000001755cabd360, 0x000001755cac28b0, 0x000001755f45af90,
0x0000017566cc72f0, 0x000001755f42e040, 0x000001755f42cc90, 0x000001755f42f3f0,
0x000001755f4314c0, 0x000001755cac35d0, 0x000001755cac0e70, 0x000001755cabfac0,
0x000001755cac0150, 0x000001756556be60
}

Java Threads: ( => current thread )
  0x000001753eb53eb0 JavaThread "main"                              [_thread_blocked, id=9248, stack(0x0000005f8c600000,0x0000005f8c700000) (1024K)]
  0x000001755527a490 JavaThread "Reference Handler"          daemon [_thread_blocked, id=9472, stack(0x0000005f8ce00000,0x0000005f8cf00000) (1024K)]
  0x000001755527b1a0 JavaThread "Finalizer"                  daemon [_thread_blocked, id=15380, stack(0x0000005f8cf00000,0x0000005f8d000000) (1024K)]
  0x000001755527ce80 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=9048, stack(0x0000005f8d000000,0x0000005f8d100000) (1024K)]
  0x000001755527d4e0 JavaThread "Attach Listener"            daemon [_thread_blocked, id=11144, stack(0x0000005f8d100000,0x0000005f8d200000) (1024K)]
  0x000001755527df30 JavaThread "Service Thread"             daemon [_thread_blocked, id=14596, stack(0x0000005f8d200000,0x0000005f8d300000) (1024K)]
  0x0000017555265210 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=16724, stack(0x0000005f8d300000,0x0000005f8d400000) (1024K)]
  0x0000017555267c70 JavaThread "C2 CompilerThread0"         daemon [_thread_in_native, id=3644, stack(0x0000005f8d400000,0x0000005f8d500000) (1024K)]
  0x0000017555268310 JavaThread "C1 CompilerThread0"         daemon [_thread_in_native, id=4808, stack(0x0000005f8d500000,0x0000005f8d600000) (1024K)]
  0x0000017554f365c0 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=6584, stack(0x0000005f8d600000,0x0000005f8d700000) (1024K)]
  0x000001755559d180 JavaThread "Notification Thread"        daemon [_thread_blocked, id=1968, stack(0x0000005f8d700000,0x0000005f8d800000) (1024K)]
  0x000001755b22fc90 JavaThread "Daemon health stats"               [_thread_blocked, id=14816, stack(0x0000005f8dc00000,0x0000005f8dd00000) (1024K)]
  0x000001755aa04be0 JavaThread "Incoming local TCP Connector on port 57161"        [_thread_in_native, id=12312, stack(0x0000005f8dd00000,0x0000005f8de00000) (1024K)]
  0x000001755adeb610 JavaThread "Daemon periodic checks"            [_thread_blocked, id=4488, stack(0x0000005f8de00000,0x0000005f8df00000) (1024K)]
  0x000001755b21fd30 JavaThread "Daemon"                            [_thread_blocked, id=13848, stack(0x0000005f8df00000,0x0000005f8e000000) (1024K)]
  0x000001755b354810 JavaThread "Daemon worker"                     [_thread_blocked, id=14188, stack(0x0000005f8e200000,0x0000005f8e300000) (1024K)]
  0x000001755b29f7e0 JavaThread "Cache worker for journal cache (C:\Users\<USER>\.gradle\caches\journal-1)"        [_thread_blocked, id=16580, stack(0x0000005f8e600000,0x0000005f8e700000) (1024K)]
  0x000001755b29eac0 JavaThread "File lock request listener"        [_thread_in_native, id=15344, stack(0x0000005f8e700000,0x0000005f8e800000) (1024K)]
  0x000001755b29dda0 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\.gradle\caches\8.14.1\fileHashes)"        [_thread_blocked, id=12424, stack(0x0000005f8e800000,0x0000005f8e900000) (1024K)]
  0x000001755b29f150 JavaThread "File watcher server"        daemon [_thread_in_native, id=16064, stack(0x0000005f8eb00000,0x0000005f8ec00000) (1024K)]
  0x000001755b8a7ff0 JavaThread "File watcher consumer"      daemon [_thread_blocked, id=1828, stack(0x0000005f8ec00000,0x0000005f8ed00000) (1024K)]
  0x000001755b8a5f20 JavaThread "jar transforms"                    [_thread_blocked, id=15444, stack(0x0000005f8ed00000,0x0000005f8ee00000) (1024K)]
  0x000001755b8a65b0 JavaThread "Cache worker for file content cache (C:\Users\<USER>\.gradle\caches\8.14.1\fileContent)"        [_thread_blocked, id=7392, stack(0x0000005f8ef00000,0x0000005f8f000000) (1024K)]
  0x000001755cc8ee80 JavaThread "Memory manager"                    [_thread_blocked, id=10656, stack(0x0000005f8da00000,0x0000005f8db00000) (1024K)]
  0x000001755cc90230 JavaThread "Handler for socket connection from /127.0.0.1:57161 to /127.0.0.1:57173"        [_thread_in_native, id=9548, stack(0x0000005f8e000000,0x0000005f8e100000) (1024K)]
  0x000001755cc90f50 JavaThread "Cancel handler"                    [_thread_blocked, id=8076, stack(0x0000005f8e100000,0x0000005f8e200000) (1024K)]
  0x000001755cc915e0 JavaThread "Asynchronous log dispatcher for DefaultDaemonConnection: socket connection from /127.0.0.1:57161 to /127.0.0.1:57173"        [_thread_blocked, id=14772, stack(0x0000005f8e300000,0x0000005f8e400000) (1024K)]
  0x000001755cc92300 JavaThread "Stdin handler"                     [_thread_blocked, id=13284, stack(0x0000005f8e500000,0x0000005f8e600000) (1024K)]
  0x000001755cc8e7f0 JavaThread "Daemon client event forwarder"        [_thread_blocked, id=12548, stack(0x0000005f8e900000,0x0000005f8ea00000) (1024K)]
  0x000001755cc8d440 JavaThread "Cache worker for file hash cache (F:\PROJETO\React\EscolaSabatina\android\.gradle\8.14.1\fileHashes)"        [_thread_blocked, id=3180, stack(0x0000005f8ea00000,0x0000005f8eb00000) (1024K)]
  0x000001755cc8ba00 JavaThread "Cache worker for Build Output Cleanup Cache (F:\PROJETO\React\EscolaSabatina\android\.gradle\buildOutputCleanup)"        [_thread_blocked, id=5960, stack(0x0000005f8ee00000,0x0000005f8ef00000) (1024K)]
  0x000001755cc8c720 JavaThread "Cache worker for checksums cache (F:\PROJETO\React\EscolaSabatina\android\.gradle\8.14.1\checksums)"        [_thread_blocked, id=15960, stack(0x0000005f8f100000,0x0000005f8f200000) (1024K)]
  0x000001755cc8e160 JavaThread "Cache worker for cache directory md-supplier (C:\Users\<USER>\.gradle\caches\8.14.1\md-supplier)"        [_thread_blocked, id=17080, stack(0x0000005f8f200000,0x0000005f8f300000) (1024K)]
  0x00000175556960c0 JavaThread "Cache worker for cache directory md-rule (C:\Users\<USER>\.gradle\caches\8.14.1\md-rule)"        [_thread_blocked, id=6168, stack(0x0000005f8f300000,0x0000005f8f400000) (1024K)]
  0x0000017555699540 JavaThread "Cache worker for Build Output Cleanup Cache (F:\PROJETO\React\EscolaSabatina\node_modules\@react-native\gradle-plugin\.gradle\buildOutputCleanup)"        [_thread_blocked, id=16640, stack(0x0000005f8f400000,0x0000005f8f500000) (1024K)]
  0x0000017555697b00 JavaThread "Unconstrained build operations"        [_thread_blocked, id=6336, stack(0x0000005f8f500000,0x0000005f8f600000) (1024K)]
  0x000001755569c9c0 JavaThread "Unconstrained build operations Thread 2"        [_thread_blocked, id=9996, stack(0x0000005f8f600000,0x0000005f8f700000) (1024K)]
  0x000001755569a8f0 JavaThread "Unconstrained build operations Thread 3"        [_thread_blocked, id=14168, stack(0x0000005f8f700000,0x0000005f8f800000) (1024K)]
  0x0000017555696750 JavaThread "Unconstrained build operations Thread 4"        [_thread_blocked, id=17268, stack(0x0000005f8f800000,0x0000005f8f900000) (1024K)]
  0x000001755569d6e0 JavaThread "Unconstrained build operations Thread 5"        [_thread_blocked, id=13736, stack(0x0000005f8f900000,0x0000005f8fa00000) (1024K)]
  0x0000017555698820 JavaThread "Unconstrained build operations Thread 6"        [_thread_blocked, id=12788, stack(0x0000005f8fa00000,0x0000005f8fb00000) (1024K)]
  0x000001755569af80 JavaThread "Unconstrained build operations Thread 7"        [_thread_blocked, id=8072, stack(0x0000005f8fb00000,0x0000005f8fc00000) (1024K)]
  0x000001755569b610 JavaThread "Unconstrained build operations Thread 8"        [_thread_blocked, id=5380, stack(0x0000005f8fc00000,0x0000005f8fd00000) (1024K)]
  0x000001755569d050 JavaThread "Unconstrained build operations Thread 9"        [_thread_blocked, id=7264, stack(0x0000005f8fd00000,0x0000005f8fe00000) (1024K)]
  0x0000017555696de0 JavaThread "Unconstrained build operations Thread 10"        [_thread_blocked, id=3428, stack(0x0000005f8fe00000,0x0000005f8ff00000) (1024K)]
  0x000001755b29d080 JavaThread "build event listener"              [_thread_blocked, id=12328, stack(0x0000005f8ff00000,0x0000005f90000000) (1024K)]
  0x000001755b8a72d0 JavaThread "ForkJoinPool.commonPool-worker-1" daemon [_thread_blocked, id=11956, stack(0x0000005f90000000,0x0000005f90100000) (1024K)]
  0x000001755eb785a0 JavaThread "ForkJoinPool.commonPool-worker-2" daemon [_thread_blocked, id=6420, stack(0x0000005f90100000,0x0000005f90200000) (1024K)]
  0x000001755eb764d0 JavaThread "ForkJoinPool.commonPool-worker-3" daemon [_thread_blocked, id=7624, stack(0x0000005f90200000,0x0000005f90300000) (1024K)]
  0x000001755eb7ad00 JavaThread "included builds"                   [_thread_in_Java, id=16552, stack(0x0000005f8f000000,0x0000005f8f100000) (1024K)]
  0x000001755eb7c740 JavaThread "Execution worker"                  [_thread_blocked, id=14948, stack(0x0000005f90600000,0x0000005f90700000) (1024K)]
  0x000001755eb79950 JavaThread "Execution worker Thread 2"         [_thread_blocked, id=15432, stack(0x0000005f90700000,0x0000005f90800000) (1024K)]
  0x000001755eb7d460 JavaThread "Execution worker Thread 3"         [_thread_in_vm, id=2748, stack(0x0000005f90800000,0x0000005f90900000) (1024K)]
  0x000001755eb7b390 JavaThread "Cache worker for execution history cache (F:\PROJETO\React\EscolaSabatina\node_modules\@react-native\gradle-plugin\.gradle\8.14.1\executionHistory)"        [_thread_blocked, id=16760, stack(0x0000005f90900000,0x0000005f90a00000) (1024K)]
  0x000001755eb7ba20 JavaThread "Unconstrained build operations Thread 11"        [_thread_blocked, id=16236, stack(0x0000005f90a00000,0x0000005f90b00000) (1024K)]
  0x000001755eb7a670 JavaThread "Unconstrained build operations Thread 12"        [_thread_blocked, id=16984, stack(0x0000005f90b00000,0x0000005f90c00000) (1024K)]
  0x000001755eb76b60 JavaThread "Unconstrained build operations Thread 13"        [_thread_blocked, id=11728, stack(0x0000005f90c00000,0x0000005f90d00000) (1024K)]
  0x000001755eb7daf0 JavaThread "Unconstrained build operations Thread 14"        [_thread_blocked, id=9800, stack(0x0000005f90d00000,0x0000005f90e00000) (1024K)]
  0x000001755eb792c0 JavaThread "Unconstrained build operations Thread 15"        [_thread_blocked, id=7344, stack(0x0000005f90e00000,0x0000005f90f00000) (1024K)]
  0x000001755eb79fe0 JavaThread "Unconstrained build operations Thread 16"        [_thread_blocked, id=10128, stack(0x0000005f90f00000,0x0000005f91000000) (1024K)]
  0x000001755eb7cdd0 JavaThread "Unconstrained build operations Thread 17"        [_thread_blocked, id=14864, stack(0x0000005f91000000,0x0000005f91100000) (1024K)]
  0x000001755eb771f0 JavaThread "Unconstrained build operations Thread 18"        [_thread_blocked, id=6500, stack(0x0000005f91100000,0x0000005f91200000) (1024K)]
  0x000001755eb7c0b0 JavaThread "Unconstrained build operations Thread 19"        [_thread_blocked, id=10936, stack(0x0000005f91200000,0x0000005f91300000) (1024K)]
  0x000001755b8a6c40 JavaThread "Unconstrained build operations Thread 20"        [_thread_blocked, id=10356, stack(0x0000005f91300000,0x0000005f91400000) (1024K)]
  0x000001755b8a5890 JavaThread "Unconstrained build operations Thread 21"        [_thread_blocked, id=11988, stack(0x0000005f91400000,0x0000005f91500000) (1024K)]
  0x000001755b8a8680 JavaThread "Unconstrained build operations Thread 22"        [_thread_blocked, id=13644, stack(0x0000005f91500000,0x0000005f91600000) (1024K)]
  0x000001755b2a0500 JavaThread "Unconstrained build operations Thread 23"        [_thread_blocked, id=15812, stack(0x0000005f91600000,0x0000005f91700000) (1024K)]
  0x000001755b29e430 JavaThread "Unconstrained build operations Thread 24"        [_thread_blocked, id=8836, stack(0x0000005f91700000,0x0000005f91800000) (1024K)]
  0x000001755c8eaec0 JavaThread "Unconstrained build operations Thread 25"        [_thread_blocked, id=14552, stack(0x0000005f91800000,0x0000005f91900000) (1024K)]
  0x000001755c8ebbe0 JavaThread "Unconstrained build operations Thread 26"        [_thread_blocked, id=11744, stack(0x0000005f91900000,0x0000005f91a00000) (1024K)]
  0x000001755c8ed620 JavaThread "Unconstrained build operations Thread 27"        [_thread_blocked, id=9636, stack(0x0000005f91a00000,0x0000005f91b00000) (1024K)]
  0x000001755c8ec900 JavaThread "Unconstrained build operations Thread 28"        [_thread_blocked, id=15144, stack(0x0000005f91b00000,0x0000005f91c00000) (1024K)]
  0x000001755c8ecf90 JavaThread "Unconstrained build operations Thread 29"        [_thread_blocked, id=5732, stack(0x0000005f91c00000,0x0000005f91d00000) (1024K)]
  0x000001755c8edcb0 JavaThread "Unconstrained build operations Thread 30"        [_thread_blocked, id=7032, stack(0x0000005f91d00000,0x0000005f91e00000) (1024K)]
  0x000001755c8ee340 JavaThread "Unconstrained build operations Thread 31"        [_thread_blocked, id=14820, stack(0x0000005f91e00000,0x0000005f91f00000) (1024K)]
  0x000001755c8eb550 JavaThread "Unconstrained build operations Thread 32"        [_thread_blocked, id=7948, stack(0x0000005f91f00000,0x0000005f92000000) (1024K)]
  0x000001755c8ec270 JavaThread "jar transforms Thread 2"           [_thread_blocked, id=3564, stack(0x0000005f92000000,0x0000005f92100000) (1024K)]
  0x000001755cad4520 JavaThread "jar transforms Thread 3"           [_thread_blocked, id=11912, stack(0x0000005f92200000,0x0000005f92300000) (1024K)]
  0x000001755cad1dc0 JavaThread "Unconstrained build operations Thread 33"        [_thread_blocked, id=10504, stack(0x0000005f92100000,0x0000005f92200000) (1024K)]
  0x000001755caccf00 JavaThread "Unconstrained build operations Thread 34"        [_thread_blocked, id=7212, stack(0x0000005f92300000,0x0000005f92400000) (1024K)]
  0x000001755cad1730 JavaThread "Unconstrained build operations Thread 35"        [_thread_blocked, id=13596, stack(0x0000005f92400000,0x0000005f92500000) (1024K)]
  0x000001755cacefd0 JavaThread "Unconstrained build operations Thread 36"        [_thread_blocked, id=7340, stack(0x0000005f92500000,0x0000005f92600000) (1024K)]
  0x000001755cad2450 JavaThread "Unconstrained build operations Thread 37"        [_thread_blocked, id=12844, stack(0x0000005f92600000,0x0000005f92700000) (1024K)]
  0x000001755cacd590 JavaThread "Unconstrained build operations Thread 38"        [_thread_blocked, id=9140, stack(0x0000005f92700000,0x0000005f92800000) (1024K)]
  0x000001755cace2b0 JavaThread "Unconstrained build operations Thread 39"        [_thread_blocked, id=5048, stack(0x0000005f92900000,0x0000005f92a00000) (1024K)]
  0x000001755cacfcf0 JavaThread "Unconstrained build operations Thread 40"        [_thread_blocked, id=11512, stack(0x0000005f92a00000,0x0000005f92b00000) (1024K)]
  0x000001755cad2ae0 JavaThread "jar transforms Thread 4"           [_thread_blocked, id=3968, stack(0x0000005f8c300000,0x0000005f8c400000) (1024K)]
  0x000001755f42a530 JavaThread "Problems report writer"            [_thread_blocked, id=12824, stack(0x0000005f92b00000,0x0000005f92c00000) (1024K)]
  0x000001755cad0a10 JavaThread "build event listener"              [_thread_blocked, id=3520, stack(0x0000005f92c00000,0x0000005f92d00000) (1024K)]
  0x000001755cabd360 JavaThread "Exec process"                      [_thread_blocked, id=10000, stack(0x0000005f92800000,0x0000005f92900000) (1024K)]
  0x000001755cac28b0 JavaThread "Exec process Thread 2"             [_thread_blocked, id=13936, stack(0x0000005f92d00000,0x0000005f92e00000) (1024K)]
  0x000001755f45af90 JavaThread "Exec process Thread 3"             [_thread_blocked, id=6640, stack(0x0000005f92e00000,0x0000005f92f00000) (1024K)]
  0x0000017566cc72f0 JavaThread "Cache worker for execution history cache (F:\PROJETO\React\EscolaSabatina\android\.gradle\8.14.1\executionHistory)"        [_thread_blocked, id=10976, stack(0x0000005f93000000,0x0000005f93100000) (1024K)]
  0x000001755f42e040 JavaThread "WorkerExecutor Queue"              [_thread_in_native, id=13552, stack(0x0000005f8c400000,0x0000005f8c500000) (1024K)]
  0x000001755f42cc90 JavaThread "WorkerExecutor Queue Thread 2"        [_thread_blocked, id=12624, stack(0x0000005f8e400000,0x0000005f8e500000) (1024K)]
  0x000001755f42f3f0 JavaThread "WorkerExecutor Queue Thread 3"        [_thread_in_Java, id=2248, stack(0x0000005f92f00000,0x0000005f93000000) (1024K)]
  0x000001755f4314c0 JavaThread "WorkerExecutor Queue Thread 4"        [_thread_blocked, id=6796, stack(0x0000005f93100000,0x0000005f93200000) (1024K)]
  0x000001755cac35d0 JavaThread "ForkJoinPool-1-worker-1"    daemon [_thread_blocked, id=11380, stack(0x0000005f93200000,0x0000005f93300000) (1024K)]
  0x000001755cac0e70 JavaThread "ForkJoinPool-1-worker-2"    daemon [_thread_blocked, id=13340, stack(0x0000005f93300000,0x0000005f93400000) (1024K)]
  0x000001755cabfac0 JavaThread "ForkJoinPool-1-worker-3"    daemon [_thread_blocked, id=13636, stack(0x0000005f93400000,0x0000005f93500000) (1024K)]
  0x000001755cac0150 JavaThread "ForkJoinPool-1-worker-4"    daemon [_thread_blocked, id=14464, stack(0x0000005f93500000,0x0000005f93600000) (1024K)]
=>0x000001756556be60 JavaThread "C2 CompilerThread1"         daemon [_thread_in_native, id=5956, stack(0x0000005f8c500000,0x0000005f8c600000) (1024K)]
Total: 102

Other Threads:
  0x0000017554f997b0 VMThread "VM Thread"                           [id=13220, stack(0x0000005f8cd00000,0x0000005f8ce00000) (1024K)]
  0x0000017554f34380 WatcherThread "VM Periodic Task Thread"        [id=9576, stack(0x0000005f8cc00000,0x0000005f8cd00000) (1024K)]
  0x000001753eb9ca80 WorkerThread "GC Thread#0"                     [id=12452, stack(0x0000005f8c700000,0x0000005f8c800000) (1024K)]
  0x0000017555711830 WorkerThread "GC Thread#1"                     [id=12520, stack(0x0000005f8d800000,0x0000005f8d900000) (1024K)]
  0x00000175556f7680 WorkerThread "GC Thread#2"                     [id=12888, stack(0x0000005f8d900000,0x0000005f8da00000) (1024K)]
  0x000001755a35e110 WorkerThread "GC Thread#3"                     [id=3924, stack(0x0000005f8db00000,0x0000005f8dc00000) (1024K)]
  0x000001753eba9970 ConcurrentGCThread "G1 Main Marker"            [id=9412, stack(0x0000005f8c800000,0x0000005f8c900000) (1024K)]
  0x000001753ebab500 WorkerThread "G1 Conc#0"                       [id=14384, stack(0x0000005f8c900000,0x0000005f8ca00000) (1024K)]
  0x000001753ec1e9d0 ConcurrentGCThread "G1 Refine#0"               [id=14852, stack(0x0000005f8ca00000,0x0000005f8cb00000) (1024K)]
  0x0000017554df8630 ConcurrentGCThread "G1 Service"                [id=16924, stack(0x0000005f8cb00000,0x0000005f8cc00000) (1024K)]
Total: 10

Threads with active compile tasks:
C2 CompilerThread0  97117 28513       4       com.android.tools.r8.graph.B2::b (61 bytes)
C2 CompilerThread1  97117 28389       4       com.android.tools.r8.internal.A9::a (1719 bytes)
Total: 2

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x00000000a0000000, size: 1536 MB, Compressed Oops mode: 32-bit

CDS archive(s) not mapped
Compressed class space mapped at: 0x0000000100000000-0x000000011a000000, reserved size: 436207616
Narrow klass base: 0x0000000000000000, Narrow klass shift: 3, Narrow klass range: 0x11a000000

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 5 size 8 Array Of Cards #cards 12 size 40 Howl #buckets 4 coarsen threshold 1843 Howl Bitmap #cards 512 size 80 coarsen threshold 460 Card regions per heap region 1 cards per card region 2048
 CPUs: 4 total, 4 available
 Memory: 8161M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Heap Region Size: 1M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 128M
 Heap Max Capacity: 1536M
 Pre-touch: Disabled
 Parallel Workers: 4
 Concurrent Workers: 1
 Concurrent Refinement Workers: 4
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 995328K, used 792576K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 208 young (212992K), 14 survivors (14336K)
 Metaspace       used 165333K, committed 168448K, reserved 622592K
  class space    used 22371K, committed 23872K, reserved 425984K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom
|   0|0x00000000a0000000, 0x00000000a0100000, 0x00000000a0100000|100%|HS|  |TAMS 0x00000000a0000000| PB 0x00000000a0000000| Complete 
|   1|0x00000000a0100000, 0x00000000a0200000, 0x00000000a0200000|100%|HC|  |TAMS 0x00000000a0100000| PB 0x00000000a0100000| Complete 
|   2|0x00000000a0200000, 0x00000000a0300000, 0x00000000a0300000|100%|HC|  |TAMS 0x00000000a0200000| PB 0x00000000a0200000| Complete 
|   3|0x00000000a0300000, 0x00000000a0400000, 0x00000000a0400000|100%| O|  |TAMS 0x00000000a0300000| PB 0x00000000a0300000| Untracked 
|   4|0x00000000a0400000, 0x00000000a0500000, 0x00000000a0500000|100%| O|  |TAMS 0x00000000a0400000| PB 0x00000000a0400000| Untracked 
|   5|0x00000000a0500000, 0x00000000a0600000, 0x00000000a0600000|100%| O|  |TAMS 0x00000000a0500000| PB 0x00000000a0500000| Untracked 
|   6|0x00000000a0600000, 0x00000000a0700000, 0x00000000a0700000|100%| O|  |TAMS 0x00000000a0600000| PB 0x00000000a0600000| Untracked 
|   7|0x00000000a0700000, 0x00000000a0800000, 0x00000000a0800000|100%|HS|  |TAMS 0x00000000a0700000| PB 0x00000000a0700000| Complete 
|   8|0x00000000a0800000, 0x00000000a0900000, 0x00000000a0900000|100%| O|  |TAMS 0x00000000a0800000| PB 0x00000000a0800000| Untracked 
|   9|0x00000000a0900000, 0x00000000a0a00000, 0x00000000a0a00000|100%| O|  |TAMS 0x00000000a0900000| PB 0x00000000a0900000| Untracked 
|  10|0x00000000a0a00000, 0x00000000a0b00000, 0x00000000a0b00000|100%| O|  |TAMS 0x00000000a0a00000| PB 0x00000000a0a00000| Untracked 
|  11|0x00000000a0b00000, 0x00000000a0c00000, 0x00000000a0c00000|100%| O|  |TAMS 0x00000000a0b00000| PB 0x00000000a0b00000| Untracked 
|  12|0x00000000a0c00000, 0x00000000a0d00000, 0x00000000a0d00000|100%| O|  |TAMS 0x00000000a0c00000| PB 0x00000000a0c00000| Untracked 
|  13|0x00000000a0d00000, 0x00000000a0e00000, 0x00000000a0e00000|100%| O|  |TAMS 0x00000000a0d00000| PB 0x00000000a0d00000| Untracked 
|  14|0x00000000a0e00000, 0x00000000a0f00000, 0x00000000a0f00000|100%| O|  |TAMS 0x00000000a0e00000| PB 0x00000000a0e00000| Untracked 
|  15|0x00000000a0f00000, 0x00000000a1000000, 0x00000000a1000000|100%| O|  |TAMS 0x00000000a0f00000| PB 0x00000000a0f00000| Untracked 
|  16|0x00000000a1000000, 0x00000000a1100000, 0x00000000a1100000|100%| O|  |TAMS 0x00000000a1000000| PB 0x00000000a1000000| Untracked 
|  17|0x00000000a1100000, 0x00000000a1200000, 0x00000000a1200000|100%| O|  |TAMS 0x00000000a1100000| PB 0x00000000a1100000| Untracked 
|  18|0x00000000a1200000, 0x00000000a1300000, 0x00000000a1300000|100%| O|  |TAMS 0x00000000a1200000| PB 0x00000000a1200000| Untracked 
|  19|0x00000000a1300000, 0x00000000a1400000, 0x00000000a1400000|100%| O|  |TAMS 0x00000000a1300000| PB 0x00000000a1300000| Untracked 
|  20|0x00000000a1400000, 0x00000000a1500000, 0x00000000a1500000|100%| O|  |TAMS 0x00000000a1400000| PB 0x00000000a1400000| Untracked 
|  21|0x00000000a1500000, 0x00000000a1600000, 0x00000000a1600000|100%| O|  |TAMS 0x00000000a1500000| PB 0x00000000a1500000| Untracked 
|  22|0x00000000a1600000, 0x00000000a1700000, 0x00000000a1700000|100%| O|  |TAMS 0x00000000a1600000| PB 0x00000000a1600000| Untracked 
|  23|0x00000000a1700000, 0x00000000a1800000, 0x00000000a1800000|100%| O|  |TAMS 0x00000000a1700000| PB 0x00000000a1700000| Untracked 
|  24|0x00000000a1800000, 0x00000000a1900000, 0x00000000a1900000|100%| O|  |TAMS 0x00000000a1800000| PB 0x00000000a1800000| Untracked 
|  25|0x00000000a1900000, 0x00000000a1a00000, 0x00000000a1a00000|100%| O|  |TAMS 0x00000000a1900000| PB 0x00000000a1900000| Untracked 
|  26|0x00000000a1a00000, 0x00000000a1b00000, 0x00000000a1b00000|100%| O|  |TAMS 0x00000000a1a00000| PB 0x00000000a1a00000| Untracked 
|  27|0x00000000a1b00000, 0x00000000a1c00000, 0x00000000a1c00000|100%| O|  |TAMS 0x00000000a1b00000| PB 0x00000000a1b00000| Untracked 
|  28|0x00000000a1c00000, 0x00000000a1d00000, 0x00000000a1d00000|100%| O|  |TAMS 0x00000000a1c00000| PB 0x00000000a1c00000| Untracked 
|  29|0x00000000a1d00000, 0x00000000a1e00000, 0x00000000a1e00000|100%| O|  |TAMS 0x00000000a1d00000| PB 0x00000000a1d00000| Untracked 
|  30|0x00000000a1e00000, 0x00000000a1f00000, 0x00000000a1f00000|100%| O|  |TAMS 0x00000000a1e00000| PB 0x00000000a1e00000| Untracked 
|  31|0x00000000a1f00000, 0x00000000a2000000, 0x00000000a2000000|100%| O|  |TAMS 0x00000000a1f00000| PB 0x00000000a1f00000| Untracked 
|  32|0x00000000a2000000, 0x00000000a2100000, 0x00000000a2100000|100%| O|  |TAMS 0x00000000a2000000| PB 0x00000000a2000000| Untracked 
|  33|0x00000000a2100000, 0x00000000a2200000, 0x00000000a2200000|100%| O|  |TAMS 0x00000000a2100000| PB 0x00000000a2100000| Untracked 
|  34|0x00000000a2200000, 0x00000000a2300000, 0x00000000a2300000|100%| O|  |TAMS 0x00000000a2200000| PB 0x00000000a2200000| Untracked 
|  35|0x00000000a2300000, 0x00000000a2400000, 0x00000000a2400000|100%| O|  |TAMS 0x00000000a2300000| PB 0x00000000a2300000| Untracked 
|  36|0x00000000a2400000, 0x00000000a2500000, 0x00000000a2500000|100%| O|  |TAMS 0x00000000a2400000| PB 0x00000000a2400000| Untracked 
|  37|0x00000000a2500000, 0x00000000a2600000, 0x00000000a2600000|100%| O|  |TAMS 0x00000000a2500000| PB 0x00000000a2500000| Untracked 
|  38|0x00000000a2600000, 0x00000000a2700000, 0x00000000a2700000|100%| O|  |TAMS 0x00000000a2600000| PB 0x00000000a2600000| Untracked 
|  39|0x00000000a2700000, 0x00000000a2800000, 0x00000000a2800000|100%|HS|  |TAMS 0x00000000a2700000| PB 0x00000000a2700000| Complete 
|  40|0x00000000a2800000, 0x00000000a2900000, 0x00000000a2900000|100%| O|  |TAMS 0x00000000a2800000| PB 0x00000000a2800000| Untracked 
|  41|0x00000000a2900000, 0x00000000a2a00000, 0x00000000a2a00000|100%| O|  |TAMS 0x00000000a2900000| PB 0x00000000a2900000| Untracked 
|  42|0x00000000a2a00000, 0x00000000a2b00000, 0x00000000a2b00000|100%|HS|  |TAMS 0x00000000a2a00000| PB 0x00000000a2a00000| Complete 
|  43|0x00000000a2b00000, 0x00000000a2c00000, 0x00000000a2c00000|100%|HC|  |TAMS 0x00000000a2b00000| PB 0x00000000a2b00000| Complete 
|  44|0x00000000a2c00000, 0x00000000a2d00000, 0x00000000a2d00000|100%|HC|  |TAMS 0x00000000a2c00000| PB 0x00000000a2c00000| Complete 
|  45|0x00000000a2d00000, 0x00000000a2e00000, 0x00000000a2e00000|100%| O|  |TAMS 0x00000000a2d00000| PB 0x00000000a2d00000| Untracked 
|  46|0x00000000a2e00000, 0x00000000a2f00000, 0x00000000a2f00000|100%| O|  |TAMS 0x00000000a2e00000| PB 0x00000000a2e00000| Untracked 
|  47|0x00000000a2f00000, 0x00000000a3000000, 0x00000000a3000000|100%|HS|  |TAMS 0x00000000a2f00000| PB 0x00000000a2f00000| Complete 
|  48|0x00000000a3000000, 0x00000000a3100000, 0x00000000a3100000|100%|HS|  |TAMS 0x00000000a3000000| PB 0x00000000a3000000| Complete 
|  49|0x00000000a3100000, 0x00000000a3200000, 0x00000000a3200000|100%|HC|  |TAMS 0x00000000a3100000| PB 0x00000000a3100000| Complete 
|  50|0x00000000a3200000, 0x00000000a3300000, 0x00000000a3300000|100%|HC|  |TAMS 0x00000000a3200000| PB 0x00000000a3200000| Complete 
|  51|0x00000000a3300000, 0x00000000a3400000, 0x00000000a3400000|100%| O|  |TAMS 0x00000000a3300000| PB 0x00000000a3300000| Untracked 
|  52|0x00000000a3400000, 0x00000000a3500000, 0x00000000a3500000|100%| O|  |TAMS 0x00000000a3400000| PB 0x00000000a3400000| Untracked 
|  53|0x00000000a3500000, 0x00000000a3600000, 0x00000000a3600000|100%| O|  |TAMS 0x00000000a3500000| PB 0x00000000a3500000| Untracked 
|  54|0x00000000a3600000, 0x00000000a3700000, 0x00000000a3700000|100%| O|  |TAMS 0x00000000a3600000| PB 0x00000000a3600000| Untracked 
|  55|0x00000000a3700000, 0x00000000a3800000, 0x00000000a3800000|100%| O|  |TAMS 0x00000000a3700000| PB 0x00000000a3700000| Untracked 
|  56|0x00000000a3800000, 0x00000000a3900000, 0x00000000a3900000|100%| O|  |TAMS 0x00000000a3800000| PB 0x00000000a3800000| Untracked 
|  57|0x00000000a3900000, 0x00000000a3a00000, 0x00000000a3a00000|100%| O|  |TAMS 0x00000000a3900000| PB 0x00000000a3900000| Untracked 
|  58|0x00000000a3a00000, 0x00000000a3b00000, 0x00000000a3b00000|100%| O|  |TAMS 0x00000000a3a00000| PB 0x00000000a3a00000| Untracked 
|  59|0x00000000a3b00000, 0x00000000a3c00000, 0x00000000a3c00000|100%| O|  |TAMS 0x00000000a3b00000| PB 0x00000000a3b00000| Untracked 
|  60|0x00000000a3c00000, 0x00000000a3d00000, 0x00000000a3d00000|100%| O|  |TAMS 0x00000000a3c00000| PB 0x00000000a3c00000| Untracked 
|  61|0x00000000a3d00000, 0x00000000a3e00000, 0x00000000a3e00000|100%| O|  |TAMS 0x00000000a3d00000| PB 0x00000000a3d00000| Untracked 
|  62|0x00000000a3e00000, 0x00000000a3f00000, 0x00000000a3f00000|100%| O|  |TAMS 0x00000000a3e00000| PB 0x00000000a3e00000| Untracked 
|  63|0x00000000a3f00000, 0x00000000a4000000, 0x00000000a4000000|100%| O|  |TAMS 0x00000000a3f00000| PB 0x00000000a3f00000| Untracked 
|  64|0x00000000a4000000, 0x00000000a4100000, 0x00000000a4100000|100%| O|  |TAMS 0x00000000a4000000| PB 0x00000000a4000000| Untracked 
|  65|0x00000000a4100000, 0x00000000a4200000, 0x00000000a4200000|100%| O|  |TAMS 0x00000000a4100000| PB 0x00000000a4100000| Untracked 
|  66|0x00000000a4200000, 0x00000000a4300000, 0x00000000a4300000|100%| O|  |TAMS 0x00000000a4200000| PB 0x00000000a4200000| Untracked 
|  67|0x00000000a4300000, 0x00000000a4400000, 0x00000000a4400000|100%| O|  |TAMS 0x00000000a4300000| PB 0x00000000a4300000| Untracked 
|  68|0x00000000a4400000, 0x00000000a4500000, 0x00000000a4500000|100%| O|  |TAMS 0x00000000a4400000| PB 0x00000000a4400000| Untracked 
|  69|0x00000000a4500000, 0x00000000a4600000, 0x00000000a4600000|100%| O|  |TAMS 0x00000000a4500000| PB 0x00000000a4500000| Untracked 
|  70|0x00000000a4600000, 0x00000000a4700000, 0x00000000a4700000|100%| O|  |TAMS 0x00000000a4600000| PB 0x00000000a4600000| Untracked 
|  71|0x00000000a4700000, 0x00000000a4800000, 0x00000000a4800000|100%| O|  |TAMS 0x00000000a4700000| PB 0x00000000a4700000| Untracked 
|  72|0x00000000a4800000, 0x00000000a4900000, 0x00000000a4900000|100%| O|  |TAMS 0x00000000a4800000| PB 0x00000000a4800000| Untracked 
|  73|0x00000000a4900000, 0x00000000a4a00000, 0x00000000a4a00000|100%| O|  |TAMS 0x00000000a4900000| PB 0x00000000a4900000| Untracked 
|  74|0x00000000a4a00000, 0x00000000a4b00000, 0x00000000a4b00000|100%|HS|  |TAMS 0x00000000a4a00000| PB 0x00000000a4a00000| Complete 
|  75|0x00000000a4b00000, 0x00000000a4c00000, 0x00000000a4c00000|100%| O|  |TAMS 0x00000000a4b00000| PB 0x00000000a4b00000| Untracked 
|  76|0x00000000a4c00000, 0x00000000a4d00000, 0x00000000a4d00000|100%| O|  |TAMS 0x00000000a4c00000| PB 0x00000000a4c00000| Untracked 
|  77|0x00000000a4d00000, 0x00000000a4e00000, 0x00000000a4e00000|100%| O|  |TAMS 0x00000000a4d00000| PB 0x00000000a4d00000| Untracked 
|  78|0x00000000a4e00000, 0x00000000a4f00000, 0x00000000a4f00000|100%| O|  |TAMS 0x00000000a4e00000| PB 0x00000000a4e00000| Untracked 
|  79|0x00000000a4f00000, 0x00000000a5000000, 0x00000000a5000000|100%|HS|  |TAMS 0x00000000a4f00000| PB 0x00000000a4f00000| Complete 
|  80|0x00000000a5000000, 0x00000000a5100000, 0x00000000a5100000|100%| O|  |TAMS 0x00000000a5000000| PB 0x00000000a5000000| Untracked 
|  81|0x00000000a5100000, 0x00000000a5200000, 0x00000000a5200000|100%| O|  |TAMS 0x00000000a5100000| PB 0x00000000a5100000| Untracked 
|  82|0x00000000a5200000, 0x00000000a5300000, 0x00000000a5300000|100%| O|  |TAMS 0x00000000a5200000| PB 0x00000000a5200000| Untracked 
|  83|0x00000000a5300000, 0x00000000a5400000, 0x00000000a5400000|100%|HS|  |TAMS 0x00000000a5300000| PB 0x00000000a5300000| Complete 
|  84|0x00000000a5400000, 0x00000000a5500000, 0x00000000a5500000|100%|HS|  |TAMS 0x00000000a5400000| PB 0x00000000a5400000| Complete 
|  85|0x00000000a5500000, 0x00000000a5600000, 0x00000000a5600000|100%| O|  |TAMS 0x00000000a5500000| PB 0x00000000a5500000| Untracked 
|  86|0x00000000a5600000, 0x00000000a5700000, 0x00000000a5700000|100%| O|  |TAMS 0x00000000a5600000| PB 0x00000000a5600000| Untracked 
|  87|0x00000000a5700000, 0x00000000a5800000, 0x00000000a5800000|100%|HS|  |TAMS 0x00000000a5700000| PB 0x00000000a5700000| Complete 
|  88|0x00000000a5800000, 0x00000000a5900000, 0x00000000a5900000|100%|HS|  |TAMS 0x00000000a5800000| PB 0x00000000a5800000| Complete 
|  89|0x00000000a5900000, 0x00000000a5a00000, 0x00000000a5a00000|100%|HS|  |TAMS 0x00000000a5900000| PB 0x00000000a5900000| Complete 
|  90|0x00000000a5a00000, 0x00000000a5b00000, 0x00000000a5b00000|100%|HS|  |TAMS 0x00000000a5a00000| PB 0x00000000a5a00000| Complete 
|  91|0x00000000a5b00000, 0x00000000a5c00000, 0x00000000a5c00000|100%|HC|  |TAMS 0x00000000a5b00000| PB 0x00000000a5b00000| Complete 
|  92|0x00000000a5c00000, 0x00000000a5d00000, 0x00000000a5d00000|100%|HC|  |TAMS 0x00000000a5c00000| PB 0x00000000a5c00000| Complete 
|  93|0x00000000a5d00000, 0x00000000a5e00000, 0x00000000a5e00000|100%| O|  |TAMS 0x00000000a5d00000| PB 0x00000000a5d00000| Untracked 
|  94|0x00000000a5e00000, 0x00000000a5f00000, 0x00000000a5f00000|100%| O|  |TAMS 0x00000000a5e00000| PB 0x00000000a5e00000| Untracked 
|  95|0x00000000a5f00000, 0x00000000a6000000, 0x00000000a6000000|100%| O|  |TAMS 0x00000000a5f00000| PB 0x00000000a5f00000| Untracked 
|  96|0x00000000a6000000, 0x00000000a6100000, 0x00000000a6100000|100%| O|  |TAMS 0x00000000a6000000| PB 0x00000000a6000000| Untracked 
|  97|0x00000000a6100000, 0x00000000a6200000, 0x00000000a6200000|100%| O|  |TAMS 0x00000000a6100000| PB 0x00000000a6100000| Untracked 
|  98|0x00000000a6200000, 0x00000000a6300000, 0x00000000a6300000|100%| O|  |TAMS 0x00000000a6200000| PB 0x00000000a6200000| Untracked 
|  99|0x00000000a6300000, 0x00000000a6400000, 0x00000000a6400000|100%|HS|  |TAMS 0x00000000a6300000| PB 0x00000000a6300000| Complete 
| 100|0x00000000a6400000, 0x00000000a6500000, 0x00000000a6500000|100%| O|  |TAMS 0x00000000a6400000| PB 0x00000000a6400000| Untracked 
| 101|0x00000000a6500000, 0x00000000a6600000, 0x00000000a6600000|100%| O|  |TAMS 0x00000000a6500000| PB 0x00000000a6500000| Untracked 
| 102|0x00000000a6600000, 0x00000000a6700000, 0x00000000a6700000|100%| O|  |TAMS 0x00000000a6600000| PB 0x00000000a6600000| Untracked 
| 103|0x00000000a6700000, 0x00000000a6800000, 0x00000000a6800000|100%| O|  |TAMS 0x00000000a6700000| PB 0x00000000a6700000| Untracked 
| 104|0x00000000a6800000, 0x00000000a6900000, 0x00000000a6900000|100%| O|  |TAMS 0x00000000a6800000| PB 0x00000000a6800000| Untracked 
| 105|0x00000000a6900000, 0x00000000a6a00000, 0x00000000a6a00000|100%| O|  |TAMS 0x00000000a6900000| PB 0x00000000a6900000| Untracked 
| 106|0x00000000a6a00000, 0x00000000a6b00000, 0x00000000a6b00000|100%| O|  |TAMS 0x00000000a6a00000| PB 0x00000000a6a00000| Untracked 
| 107|0x00000000a6b00000, 0x00000000a6c00000, 0x00000000a6c00000|100%| O|  |TAMS 0x00000000a6b00000| PB 0x00000000a6b00000| Untracked 
| 108|0x00000000a6c00000, 0x00000000a6d00000, 0x00000000a6d00000|100%| O|  |TAMS 0x00000000a6c00000| PB 0x00000000a6c00000| Untracked 
| 109|0x00000000a6d00000, 0x00000000a6e00000, 0x00000000a6e00000|100%| O|  |TAMS 0x00000000a6d00000| PB 0x00000000a6d00000| Untracked 
| 110|0x00000000a6e00000, 0x00000000a6f00000, 0x00000000a6f00000|100%| O|  |TAMS 0x00000000a6e00000| PB 0x00000000a6e00000| Untracked 
| 111|0x00000000a6f00000, 0x00000000a7000000, 0x00000000a7000000|100%| O|  |TAMS 0x00000000a6f00000| PB 0x00000000a6f00000| Untracked 
| 112|0x00000000a7000000, 0x00000000a7100000, 0x00000000a7100000|100%| O|  |TAMS 0x00000000a7000000| PB 0x00000000a7000000| Untracked 
| 113|0x00000000a7100000, 0x00000000a7200000, 0x00000000a7200000|100%| O|  |TAMS 0x00000000a7100000| PB 0x00000000a7100000| Untracked 
| 114|0x00000000a7200000, 0x00000000a7300000, 0x00000000a7300000|100%| O|  |TAMS 0x00000000a7200000| PB 0x00000000a7200000| Untracked 
| 115|0x00000000a7300000, 0x00000000a7400000, 0x00000000a7400000|100%| O|  |TAMS 0x00000000a7300000| PB 0x00000000a7300000| Untracked 
| 116|0x00000000a7400000, 0x00000000a7500000, 0x00000000a7500000|100%| O|  |TAMS 0x00000000a7400000| PB 0x00000000a7400000| Untracked 
| 117|0x00000000a7500000, 0x00000000a7600000, 0x00000000a7600000|100%| O|  |TAMS 0x00000000a7500000| PB 0x00000000a7500000| Untracked 
| 118|0x00000000a7600000, 0x00000000a7700000, 0x00000000a7700000|100%| O|  |TAMS 0x00000000a7600000| PB 0x00000000a7600000| Untracked 
| 119|0x00000000a7700000, 0x00000000a7800000, 0x00000000a7800000|100%| O|  |TAMS 0x00000000a7700000| PB 0x00000000a7700000| Untracked 
| 120|0x00000000a7800000, 0x00000000a7900000, 0x00000000a7900000|100%| O|  |TAMS 0x00000000a7800000| PB 0x00000000a7800000| Untracked 
| 121|0x00000000a7900000, 0x00000000a7a00000, 0x00000000a7a00000|100%| O|  |TAMS 0x00000000a7900000| PB 0x00000000a7900000| Untracked 
| 122|0x00000000a7a00000, 0x00000000a7b00000, 0x00000000a7b00000|100%| O|  |TAMS 0x00000000a7a00000| PB 0x00000000a7a00000| Untracked 
| 123|0x00000000a7b00000, 0x00000000a7c00000, 0x00000000a7c00000|100%| O|  |TAMS 0x00000000a7b00000| PB 0x00000000a7b00000| Untracked 
| 124|0x00000000a7c00000, 0x00000000a7d00000, 0x00000000a7d00000|100%| O|  |TAMS 0x00000000a7c00000| PB 0x00000000a7c00000| Untracked 
| 125|0x00000000a7d00000, 0x00000000a7e00000, 0x00000000a7e00000|100%| O|  |TAMS 0x00000000a7d00000| PB 0x00000000a7d00000| Untracked 
| 126|0x00000000a7e00000, 0x00000000a7f00000, 0x00000000a7f00000|100%| O|  |TAMS 0x00000000a7e00000| PB 0x00000000a7e00000| Untracked 
| 127|0x00000000a7f00000, 0x00000000a8000000, 0x00000000a8000000|100%| O|  |TAMS 0x00000000a7f00000| PB 0x00000000a7f00000| Untracked 
| 128|0x00000000a8000000, 0x00000000a8100000, 0x00000000a8100000|100%| O|  |TAMS 0x00000000a8000000| PB 0x00000000a8000000| Untracked 
| 129|0x00000000a8100000, 0x00000000a8200000, 0x00000000a8200000|100%| O|  |TAMS 0x00000000a8100000| PB 0x00000000a8100000| Untracked 
| 130|0x00000000a8200000, 0x00000000a8300000, 0x00000000a8300000|100%| O|  |TAMS 0x00000000a8200000| PB 0x00000000a8200000| Untracked 
| 131|0x00000000a8300000, 0x00000000a8400000, 0x00000000a8400000|100%| O|  |TAMS 0x00000000a8300000| PB 0x00000000a8300000| Untracked 
| 132|0x00000000a8400000, 0x00000000a8500000, 0x00000000a8500000|100%| O|  |TAMS 0x00000000a8400000| PB 0x00000000a8400000| Untracked 
| 133|0x00000000a8500000, 0x00000000a8600000, 0x00000000a8600000|100%| O|  |TAMS 0x00000000a8500000| PB 0x00000000a8500000| Untracked 
| 134|0x00000000a8600000, 0x00000000a8700000, 0x00000000a8700000|100%| O|  |TAMS 0x00000000a8600000| PB 0x00000000a8600000| Untracked 
| 135|0x00000000a8700000, 0x00000000a8800000, 0x00000000a8800000|100%| O|  |TAMS 0x00000000a8700000| PB 0x00000000a8700000| Untracked 
| 136|0x00000000a8800000, 0x00000000a8900000, 0x00000000a8900000|100%| O|  |TAMS 0x00000000a8800000| PB 0x00000000a8800000| Untracked 
| 137|0x00000000a8900000, 0x00000000a8a00000, 0x00000000a8a00000|100%| O|  |TAMS 0x00000000a8900000| PB 0x00000000a8900000| Untracked 
| 138|0x00000000a8a00000, 0x00000000a8b00000, 0x00000000a8b00000|100%| O|  |TAMS 0x00000000a8a00000| PB 0x00000000a8a00000| Untracked 
| 139|0x00000000a8b00000, 0x00000000a8c00000, 0x00000000a8c00000|100%| O|  |TAMS 0x00000000a8b00000| PB 0x00000000a8b00000| Untracked 
| 140|0x00000000a8c00000, 0x00000000a8d00000, 0x00000000a8d00000|100%| O|  |TAMS 0x00000000a8c00000| PB 0x00000000a8c00000| Untracked 
| 141|0x00000000a8d00000, 0x00000000a8e00000, 0x00000000a8e00000|100%| O|  |TAMS 0x00000000a8d00000| PB 0x00000000a8d00000| Untracked 
| 142|0x00000000a8e00000, 0x00000000a8f00000, 0x00000000a8f00000|100%| O|  |TAMS 0x00000000a8e00000| PB 0x00000000a8e00000| Untracked 
| 143|0x00000000a8f00000, 0x00000000a9000000, 0x00000000a9000000|100%| O|  |TAMS 0x00000000a8f00000| PB 0x00000000a8f00000| Untracked 
| 144|0x00000000a9000000, 0x00000000a9100000, 0x00000000a9100000|100%| O|  |TAMS 0x00000000a9000000| PB 0x00000000a9000000| Untracked 
| 145|0x00000000a9100000, 0x00000000a9200000, 0x00000000a9200000|100%| O|  |TAMS 0x00000000a9100000| PB 0x00000000a9100000| Untracked 
| 146|0x00000000a9200000, 0x00000000a9300000, 0x00000000a9300000|100%| O|  |TAMS 0x00000000a9200000| PB 0x00000000a9200000| Untracked 
| 147|0x00000000a9300000, 0x00000000a9400000, 0x00000000a9400000|100%| O|  |TAMS 0x00000000a9300000| PB 0x00000000a9300000| Untracked 
| 148|0x00000000a9400000, 0x00000000a9500000, 0x00000000a9500000|100%| O|  |TAMS 0x00000000a9400000| PB 0x00000000a9400000| Untracked 
| 149|0x00000000a9500000, 0x00000000a9600000, 0x00000000a9600000|100%| O|  |TAMS 0x00000000a9500000| PB 0x00000000a9500000| Untracked 
| 150|0x00000000a9600000, 0x00000000a9700000, 0x00000000a9700000|100%| O|  |TAMS 0x00000000a9600000| PB 0x00000000a9600000| Untracked 
| 151|0x00000000a9700000, 0x00000000a9800000, 0x00000000a9800000|100%| O|  |TAMS 0x00000000a9700000| PB 0x00000000a9700000| Untracked 
| 152|0x00000000a9800000, 0x00000000a9900000, 0x00000000a9900000|100%| O|  |TAMS 0x00000000a9800000| PB 0x00000000a9800000| Untracked 
| 153|0x00000000a9900000, 0x00000000a9a00000, 0x00000000a9a00000|100%| O|  |TAMS 0x00000000a9900000| PB 0x00000000a9900000| Untracked 
| 154|0x00000000a9a00000, 0x00000000a9b00000, 0x00000000a9b00000|100%| O|  |TAMS 0x00000000a9a00000| PB 0x00000000a9a00000| Untracked 
| 155|0x00000000a9b00000, 0x00000000a9c00000, 0x00000000a9c00000|100%| O|  |TAMS 0x00000000a9b00000| PB 0x00000000a9b00000| Untracked 
| 156|0x00000000a9c00000, 0x00000000a9d00000, 0x00000000a9d00000|100%| O|  |TAMS 0x00000000a9c00000| PB 0x00000000a9c00000| Untracked 
| 157|0x00000000a9d00000, 0x00000000a9e00000, 0x00000000a9e00000|100%| O|  |TAMS 0x00000000a9d00000| PB 0x00000000a9d00000| Untracked 
| 158|0x00000000a9e00000, 0x00000000a9f00000, 0x00000000a9f00000|100%| O|  |TAMS 0x00000000a9e00000| PB 0x00000000a9e00000| Untracked 
| 159|0x00000000a9f00000, 0x00000000aa000000, 0x00000000aa000000|100%| O|  |TAMS 0x00000000a9f00000| PB 0x00000000a9f00000| Untracked 
| 160|0x00000000aa000000, 0x00000000aa100000, 0x00000000aa100000|100%| O|  |TAMS 0x00000000aa000000| PB 0x00000000aa000000| Untracked 
| 161|0x00000000aa100000, 0x00000000aa200000, 0x00000000aa200000|100%|HS|  |TAMS 0x00000000aa100000| PB 0x00000000aa100000| Complete 
| 162|0x00000000aa200000, 0x00000000aa300000, 0x00000000aa300000|100%|HS|  |TAMS 0x00000000aa200000| PB 0x00000000aa200000| Complete 
| 163|0x00000000aa300000, 0x00000000aa400000, 0x00000000aa400000|100%|HS|  |TAMS 0x00000000aa300000| PB 0x00000000aa300000| Complete 
| 164|0x00000000aa400000, 0x00000000aa500000, 0x00000000aa500000|100%| O|  |TAMS 0x00000000aa400000| PB 0x00000000aa400000| Untracked 
| 165|0x00000000aa500000, 0x00000000aa600000, 0x00000000aa600000|100%| O|  |TAMS 0x00000000aa500000| PB 0x00000000aa500000| Untracked 
| 166|0x00000000aa600000, 0x00000000aa700000, 0x00000000aa700000|100%| O|  |TAMS 0x00000000aa600000| PB 0x00000000aa600000| Untracked 
| 167|0x00000000aa700000, 0x00000000aa800000, 0x00000000aa800000|100%| O|  |TAMS 0x00000000aa700000| PB 0x00000000aa700000| Untracked 
| 168|0x00000000aa800000, 0x00000000aa900000, 0x00000000aa900000|100%|HS|  |TAMS 0x00000000aa800000| PB 0x00000000aa800000| Complete 
| 169|0x00000000aa900000, 0x00000000aaa00000, 0x00000000aaa00000|100%| O|  |TAMS 0x00000000aa900000| PB 0x00000000aa900000| Untracked 
| 170|0x00000000aaa00000, 0x00000000aab00000, 0x00000000aab00000|100%| O|  |TAMS 0x00000000aaa00000| PB 0x00000000aaa00000| Untracked 
| 171|0x00000000aab00000, 0x00000000aac00000, 0x00000000aac00000|100%| O|  |TAMS 0x00000000aab00000| PB 0x00000000aab00000| Untracked 
| 172|0x00000000aac00000, 0x00000000aad00000, 0x00000000aad00000|100%| O|  |TAMS 0x00000000aac00000| PB 0x00000000aac00000| Untracked 
| 173|0x00000000aad00000, 0x00000000aae00000, 0x00000000aae00000|100%| O|  |TAMS 0x00000000aad00000| PB 0x00000000aad00000| Untracked 
| 174|0x00000000aae00000, 0x00000000aaf00000, 0x00000000aaf00000|100%| O|  |TAMS 0x00000000aae00000| PB 0x00000000aae00000| Untracked 
| 175|0x00000000aaf00000, 0x00000000ab000000, 0x00000000ab000000|100%| O|  |TAMS 0x00000000aaf00000| PB 0x00000000aaf00000| Untracked 
| 176|0x00000000ab000000, 0x00000000ab100000, 0x00000000ab100000|100%| O|  |TAMS 0x00000000ab000000| PB 0x00000000ab000000| Untracked 
| 177|0x00000000ab100000, 0x00000000ab200000, 0x00000000ab200000|100%| O|  |TAMS 0x00000000ab100000| PB 0x00000000ab100000| Untracked 
| 178|0x00000000ab200000, 0x00000000ab300000, 0x00000000ab300000|100%| O|  |TAMS 0x00000000ab200000| PB 0x00000000ab200000| Untracked 
| 179|0x00000000ab300000, 0x00000000ab400000, 0x00000000ab400000|100%| O|  |TAMS 0x00000000ab300000| PB 0x00000000ab300000| Untracked 
| 180|0x00000000ab400000, 0x00000000ab500000, 0x00000000ab500000|100%| O|  |TAMS 0x00000000ab400000| PB 0x00000000ab400000| Untracked 
| 181|0x00000000ab500000, 0x00000000ab600000, 0x00000000ab600000|100%| O|  |TAMS 0x00000000ab500000| PB 0x00000000ab500000| Untracked 
| 182|0x00000000ab600000, 0x00000000ab700000, 0x00000000ab700000|100%| O|  |TAMS 0x00000000ab600000| PB 0x00000000ab600000| Untracked 
| 183|0x00000000ab700000, 0x00000000ab800000, 0x00000000ab800000|100%| O|  |TAMS 0x00000000ab700000| PB 0x00000000ab700000| Untracked 
| 184|0x00000000ab800000, 0x00000000ab900000, 0x00000000ab900000|100%| O|  |TAMS 0x00000000ab800000| PB 0x00000000ab800000| Untracked 
| 185|0x00000000ab900000, 0x00000000aba00000, 0x00000000aba00000|100%| O|  |TAMS 0x00000000ab900000| PB 0x00000000ab900000| Untracked 
| 186|0x00000000aba00000, 0x00000000abb00000, 0x00000000abb00000|100%| O|  |TAMS 0x00000000aba00000| PB 0x00000000aba00000| Untracked 
| 187|0x00000000abb00000, 0x00000000abc00000, 0x00000000abc00000|100%| O|  |TAMS 0x00000000abb00000| PB 0x00000000abb00000| Untracked 
| 188|0x00000000abc00000, 0x00000000abd00000, 0x00000000abd00000|100%| O|  |TAMS 0x00000000abc00000| PB 0x00000000abc00000| Untracked 
| 189|0x00000000abd00000, 0x00000000abe00000, 0x00000000abe00000|100%| O|  |TAMS 0x00000000abd00000| PB 0x00000000abd00000| Untracked 
| 190|0x00000000abe00000, 0x00000000abf00000, 0x00000000abf00000|100%|HS|  |TAMS 0x00000000abe00000| PB 0x00000000abe00000| Complete 
| 191|0x00000000abf00000, 0x00000000ac000000, 0x00000000ac000000|100%| O|  |TAMS 0x00000000abf00000| PB 0x00000000abf00000| Untracked 
| 192|0x00000000ac000000, 0x00000000ac100000, 0x00000000ac100000|100%| O|  |TAMS 0x00000000ac000000| PB 0x00000000ac000000| Untracked 
| 193|0x00000000ac100000, 0x00000000ac200000, 0x00000000ac200000|100%|HS|  |TAMS 0x00000000ac100000| PB 0x00000000ac100000| Complete 
| 194|0x00000000ac200000, 0x00000000ac300000, 0x00000000ac300000|100%| O|  |TAMS 0x00000000ac200000| PB 0x00000000ac200000| Untracked 
| 195|0x00000000ac300000, 0x00000000ac400000, 0x00000000ac400000|100%|HS|  |TAMS 0x00000000ac300000| PB 0x00000000ac300000| Complete 
| 196|0x00000000ac400000, 0x00000000ac500000, 0x00000000ac500000|100%|HS|  |TAMS 0x00000000ac400000| PB 0x00000000ac400000| Complete 
| 197|0x00000000ac500000, 0x00000000ac600000, 0x00000000ac600000|100%|HC|  |TAMS 0x00000000ac500000| PB 0x00000000ac500000| Complete 
| 198|0x00000000ac600000, 0x00000000ac700000, 0x00000000ac700000|100%| O|  |TAMS 0x00000000ac600000| PB 0x00000000ac600000| Untracked 
| 199|0x00000000ac700000, 0x00000000ac800000, 0x00000000ac800000|100%| O|  |TAMS 0x00000000ac700000| PB 0x00000000ac700000| Untracked 
| 200|0x00000000ac800000, 0x00000000ac900000, 0x00000000ac900000|100%| O|  |TAMS 0x00000000ac800000| PB 0x00000000ac800000| Untracked 
| 201|0x00000000ac900000, 0x00000000aca00000, 0x00000000aca00000|100%| O|  |TAMS 0x00000000ac900000| PB 0x00000000ac900000| Untracked 
| 202|0x00000000aca00000, 0x00000000acb00000, 0x00000000acb00000|100%| O|  |TAMS 0x00000000aca00000| PB 0x00000000aca00000| Untracked 
| 203|0x00000000acb00000, 0x00000000acc00000, 0x00000000acc00000|100%| O|  |TAMS 0x00000000acb00000| PB 0x00000000acb00000| Untracked 
| 204|0x00000000acc00000, 0x00000000acd00000, 0x00000000acd00000|100%| O|  |TAMS 0x00000000acc00000| PB 0x00000000acc00000| Untracked 
| 205|0x00000000acd00000, 0x00000000ace00000, 0x00000000ace00000|100%| O|  |TAMS 0x00000000acd00000| PB 0x00000000acd00000| Untracked 
| 206|0x00000000ace00000, 0x00000000acf00000, 0x00000000acf00000|100%| O|  |TAMS 0x00000000ace00000| PB 0x00000000ace00000| Untracked 
| 207|0x00000000acf00000, 0x00000000ad000000, 0x00000000ad000000|100%| O|  |TAMS 0x00000000acf00000| PB 0x00000000acf00000| Untracked 
| 208|0x00000000ad000000, 0x00000000ad100000, 0x00000000ad100000|100%| O|  |TAMS 0x00000000ad000000| PB 0x00000000ad000000| Untracked 
| 209|0x00000000ad100000, 0x00000000ad200000, 0x00000000ad200000|100%| O|  |TAMS 0x00000000ad100000| PB 0x00000000ad100000| Untracked 
| 210|0x00000000ad200000, 0x00000000ad300000, 0x00000000ad300000|100%| O|  |TAMS 0x00000000ad200000| PB 0x00000000ad200000| Untracked 
| 211|0x00000000ad300000, 0x00000000ad400000, 0x00000000ad400000|100%| O|  |TAMS 0x00000000ad300000| PB 0x00000000ad300000| Untracked 
| 212|0x00000000ad400000, 0x00000000ad500000, 0x00000000ad500000|100%| O|  |TAMS 0x00000000ad400000| PB 0x00000000ad400000| Untracked 
| 213|0x00000000ad500000, 0x00000000ad600000, 0x00000000ad600000|100%| O|  |TAMS 0x00000000ad500000| PB 0x00000000ad500000| Untracked 
| 214|0x00000000ad600000, 0x00000000ad700000, 0x00000000ad700000|100%| O|  |TAMS 0x00000000ad600000| PB 0x00000000ad600000| Untracked 
| 215|0x00000000ad700000, 0x00000000ad800000, 0x00000000ad800000|100%| O|  |TAMS 0x00000000ad700000| PB 0x00000000ad700000| Untracked 
| 216|0x00000000ad800000, 0x00000000ad900000, 0x00000000ad900000|100%| O|  |TAMS 0x00000000ad800000| PB 0x00000000ad800000| Untracked 
| 217|0x00000000ad900000, 0x00000000ada00000, 0x00000000ada00000|100%| O|  |TAMS 0x00000000ad900000| PB 0x00000000ad900000| Untracked 
| 218|0x00000000ada00000, 0x00000000adb00000, 0x00000000adb00000|100%| O|  |TAMS 0x00000000ada00000| PB 0x00000000ada00000| Untracked 
| 219|0x00000000adb00000, 0x00000000adc00000, 0x00000000adc00000|100%|HS|  |TAMS 0x00000000adb00000| PB 0x00000000adb00000| Complete 
| 220|0x00000000adc00000, 0x00000000add00000, 0x00000000add00000|100%|HS|  |TAMS 0x00000000adc00000| PB 0x00000000adc00000| Complete 
| 221|0x00000000add00000, 0x00000000ade00000, 0x00000000ade00000|100%| O|  |TAMS 0x00000000add00000| PB 0x00000000add00000| Untracked 
| 222|0x00000000ade00000, 0x00000000adf00000, 0x00000000adf00000|100%| O|  |TAMS 0x00000000ade00000| PB 0x00000000ade00000| Untracked 
| 223|0x00000000adf00000, 0x00000000ae000000, 0x00000000ae000000|100%| O|  |TAMS 0x00000000adf00000| PB 0x00000000adf00000| Untracked 
| 224|0x00000000ae000000, 0x00000000ae100000, 0x00000000ae100000|100%|HS|  |TAMS 0x00000000ae000000| PB 0x00000000ae000000| Complete 
| 225|0x00000000ae100000, 0x00000000ae200000, 0x00000000ae200000|100%| O|  |TAMS 0x00000000ae100000| PB 0x00000000ae100000| Untracked 
| 226|0x00000000ae200000, 0x00000000ae300000, 0x00000000ae300000|100%| O|  |TAMS 0x00000000ae200000| PB 0x00000000ae200000| Untracked 
| 227|0x00000000ae300000, 0x00000000ae400000, 0x00000000ae400000|100%| O|  |TAMS 0x00000000ae300000| PB 0x00000000ae300000| Untracked 
| 228|0x00000000ae400000, 0x00000000ae500000, 0x00000000ae500000|100%| O|  |TAMS 0x00000000ae400000| PB 0x00000000ae400000| Untracked 
| 229|0x00000000ae500000, 0x00000000ae600000, 0x00000000ae600000|100%|HS|  |TAMS 0x00000000ae500000| PB 0x00000000ae500000| Complete 
| 230|0x00000000ae600000, 0x00000000ae700000, 0x00000000ae700000|100%| O|  |TAMS 0x00000000ae600000| PB 0x00000000ae600000| Untracked 
| 231|0x00000000ae700000, 0x00000000ae800000, 0x00000000ae800000|100%|HS|  |TAMS 0x00000000ae700000| PB 0x00000000ae700000| Complete 
| 232|0x00000000ae800000, 0x00000000ae900000, 0x00000000ae900000|100%|HC|  |TAMS 0x00000000ae800000| PB 0x00000000ae800000| Complete 
| 233|0x00000000ae900000, 0x00000000aea00000, 0x00000000aea00000|100%| O|  |TAMS 0x00000000ae900000| PB 0x00000000ae900000| Untracked 
| 234|0x00000000aea00000, 0x00000000aeb00000, 0x00000000aeb00000|100%| O|  |TAMS 0x00000000aea00000| PB 0x00000000aea00000| Untracked 
| 235|0x00000000aeb00000, 0x00000000aec00000, 0x00000000aec00000|100%| O|  |TAMS 0x00000000aeb00000| PB 0x00000000aeb00000| Untracked 
| 236|0x00000000aec00000, 0x00000000aed00000, 0x00000000aed00000|100%| O|  |TAMS 0x00000000aec00000| PB 0x00000000aec00000| Untracked 
| 237|0x00000000aed00000, 0x00000000aee00000, 0x00000000aee00000|100%| O|  |TAMS 0x00000000aed00000| PB 0x00000000aed00000| Untracked 
| 238|0x00000000aee00000, 0x00000000aef00000, 0x00000000aef00000|100%| O|  |TAMS 0x00000000aee00000| PB 0x00000000aee00000| Untracked 
| 239|0x00000000aef00000, 0x00000000af000000, 0x00000000af000000|100%| O|  |TAMS 0x00000000aef00000| PB 0x00000000aef00000| Untracked 
| 240|0x00000000af000000, 0x00000000af100000, 0x00000000af100000|100%| O|  |TAMS 0x00000000af000000| PB 0x00000000af000000| Untracked 
| 241|0x00000000af100000, 0x00000000af200000, 0x00000000af200000|100%| O|  |TAMS 0x00000000af100000| PB 0x00000000af100000| Untracked 
| 242|0x00000000af200000, 0x00000000af300000, 0x00000000af300000|100%| O|  |TAMS 0x00000000af200000| PB 0x00000000af200000| Untracked 
| 243|0x00000000af300000, 0x00000000af400000, 0x00000000af400000|100%| O|  |TAMS 0x00000000af300000| PB 0x00000000af300000| Untracked 
| 244|0x00000000af400000, 0x00000000af500000, 0x00000000af500000|100%| O|  |TAMS 0x00000000af400000| PB 0x00000000af400000| Untracked 
| 245|0x00000000af500000, 0x00000000af600000, 0x00000000af600000|100%| O|  |TAMS 0x00000000af500000| PB 0x00000000af500000| Untracked 
| 246|0x00000000af600000, 0x00000000af700000, 0x00000000af700000|100%| O|  |TAMS 0x00000000af600000| PB 0x00000000af600000| Untracked 
| 247|0x00000000af700000, 0x00000000af800000, 0x00000000af800000|100%| O|  |TAMS 0x00000000af700000| PB 0x00000000af700000| Untracked 
| 248|0x00000000af800000, 0x00000000af900000, 0x00000000af900000|100%| O|  |TAMS 0x00000000af800000| PB 0x00000000af800000| Untracked 
| 249|0x00000000af900000, 0x00000000afa00000, 0x00000000afa00000|100%| O|  |TAMS 0x00000000af900000| PB 0x00000000af900000| Untracked 
| 250|0x00000000afa00000, 0x00000000afb00000, 0x00000000afb00000|100%| O|  |TAMS 0x00000000afa00000| PB 0x00000000afa00000| Untracked 
| 251|0x00000000afb00000, 0x00000000afc00000, 0x00000000afc00000|100%| O|  |TAMS 0x00000000afb00000| PB 0x00000000afb00000| Untracked 
| 252|0x00000000afc00000, 0x00000000afd00000, 0x00000000afd00000|100%| O|  |TAMS 0x00000000afc00000| PB 0x00000000afc00000| Untracked 
| 253|0x00000000afd00000, 0x00000000afe00000, 0x00000000afe00000|100%| O|  |TAMS 0x00000000afd00000| PB 0x00000000afd00000| Untracked 
| 254|0x00000000afe00000, 0x00000000aff00000, 0x00000000aff00000|100%|HS|  |TAMS 0x00000000afe00000| PB 0x00000000afe00000| Complete 
| 255|0x00000000aff00000, 0x00000000b0000000, 0x00000000b0000000|100%|HS|  |TAMS 0x00000000aff00000| PB 0x00000000aff00000| Complete 
| 256|0x00000000b0000000, 0x00000000b0100000, 0x00000000b0100000|100%| O|  |TAMS 0x00000000b0000000| PB 0x00000000b0000000| Untracked 
| 257|0x00000000b0100000, 0x00000000b0200000, 0x00000000b0200000|100%| O|  |TAMS 0x00000000b0100000| PB 0x00000000b0100000| Untracked 
| 258|0x00000000b0200000, 0x00000000b0300000, 0x00000000b0300000|100%| O|  |TAMS 0x00000000b0200000| PB 0x00000000b0200000| Untracked 
| 259|0x00000000b0300000, 0x00000000b0400000, 0x00000000b0400000|100%| O|  |TAMS 0x00000000b0300000| PB 0x00000000b0300000| Untracked 
| 260|0x00000000b0400000, 0x00000000b0500000, 0x00000000b0500000|100%| O|  |TAMS 0x00000000b0400000| PB 0x00000000b0400000| Untracked 
| 261|0x00000000b0500000, 0x00000000b0600000, 0x00000000b0600000|100%| O|  |TAMS 0x00000000b0500000| PB 0x00000000b0500000| Untracked 
| 262|0x00000000b0600000, 0x00000000b0700000, 0x00000000b0700000|100%| O|  |TAMS 0x00000000b0600000| PB 0x00000000b0600000| Untracked 
| 263|0x00000000b0700000, 0x00000000b0800000, 0x00000000b0800000|100%| O|  |TAMS 0x00000000b0700000| PB 0x00000000b0700000| Untracked 
| 264|0x00000000b0800000, 0x00000000b0900000, 0x00000000b0900000|100%| O|  |TAMS 0x00000000b0800000| PB 0x00000000b0800000| Untracked 
| 265|0x00000000b0900000, 0x00000000b0a00000, 0x00000000b0a00000|100%|HS|  |TAMS 0x00000000b0900000| PB 0x00000000b0900000| Complete 
| 266|0x00000000b0a00000, 0x00000000b0b00000, 0x00000000b0b00000|100%|HC|  |TAMS 0x00000000b0a00000| PB 0x00000000b0a00000| Complete 
| 267|0x00000000b0b00000, 0x00000000b0c00000, 0x00000000b0c00000|100%|HC|  |TAMS 0x00000000b0b00000| PB 0x00000000b0b00000| Complete 
| 268|0x00000000b0c00000, 0x00000000b0d00000, 0x00000000b0d00000|100%|HS|  |TAMS 0x00000000b0c00000| PB 0x00000000b0c00000| Complete 
| 269|0x00000000b0d00000, 0x00000000b0e00000, 0x00000000b0e00000|100%|HC|  |TAMS 0x00000000b0d00000| PB 0x00000000b0d00000| Complete 
| 270|0x00000000b0e00000, 0x00000000b0f00000, 0x00000000b0f00000|100%| O|  |TAMS 0x00000000b0e00000| PB 0x00000000b0e00000| Untracked 
| 271|0x00000000b0f00000, 0x00000000b1000000, 0x00000000b1000000|100%| O|  |TAMS 0x00000000b0f00000| PB 0x00000000b0f00000| Untracked 
| 272|0x00000000b1000000, 0x00000000b1100000, 0x00000000b1100000|100%| O|  |TAMS 0x00000000b1000000| PB 0x00000000b1000000| Untracked 
| 273|0x00000000b1100000, 0x00000000b1200000, 0x00000000b1200000|100%| O|  |TAMS 0x00000000b1100000| PB 0x00000000b1100000| Untracked 
| 274|0x00000000b1200000, 0x00000000b1300000, 0x00000000b1300000|100%| O|  |TAMS 0x00000000b1200000| PB 0x00000000b1200000| Untracked 
| 275|0x00000000b1300000, 0x00000000b1400000, 0x00000000b1400000|100%| O|  |TAMS 0x00000000b1300000| PB 0x00000000b1300000| Untracked 
| 276|0x00000000b1400000, 0x00000000b1500000, 0x00000000b1500000|100%| O|  |TAMS 0x00000000b1400000| PB 0x00000000b1400000| Untracked 
| 277|0x00000000b1500000, 0x00000000b1600000, 0x00000000b1600000|100%| O|  |TAMS 0x00000000b1500000| PB 0x00000000b1500000| Untracked 
| 278|0x00000000b1600000, 0x00000000b1700000, 0x00000000b1700000|100%| O|  |TAMS 0x00000000b1600000| PB 0x00000000b1600000| Untracked 
| 279|0x00000000b1700000, 0x00000000b1800000, 0x00000000b1800000|100%|HS|  |TAMS 0x00000000b1700000| PB 0x00000000b1700000| Complete 
| 280|0x00000000b1800000, 0x00000000b1900000, 0x00000000b1900000|100%|HC|  |TAMS 0x00000000b1800000| PB 0x00000000b1800000| Complete 
| 281|0x00000000b1900000, 0x00000000b1a00000, 0x00000000b1a00000|100%|HC|  |TAMS 0x00000000b1900000| PB 0x00000000b1900000| Complete 
| 282|0x00000000b1a00000, 0x00000000b1b00000, 0x00000000b1b00000|100%|HC|  |TAMS 0x00000000b1a00000| PB 0x00000000b1a00000| Complete 
| 283|0x00000000b1b00000, 0x00000000b1c00000, 0x00000000b1c00000|100%|HS|  |TAMS 0x00000000b1b00000| PB 0x00000000b1b00000| Complete 
| 284|0x00000000b1c00000, 0x00000000b1d00000, 0x00000000b1d00000|100%|HC|  |TAMS 0x00000000b1c00000| PB 0x00000000b1c00000| Complete 
| 285|0x00000000b1d00000, 0x00000000b1e00000, 0x00000000b1e00000|100%|HS|  |TAMS 0x00000000b1d00000| PB 0x00000000b1d00000| Complete 
| 286|0x00000000b1e00000, 0x00000000b1f00000, 0x00000000b1f00000|100%|HC|  |TAMS 0x00000000b1e00000| PB 0x00000000b1e00000| Complete 
| 287|0x00000000b1f00000, 0x00000000b2000000, 0x00000000b2000000|100%|HS|  |TAMS 0x00000000b1f00000| PB 0x00000000b1f00000| Complete 
| 288|0x00000000b2000000, 0x00000000b2100000, 0x00000000b2100000|100%|HC|  |TAMS 0x00000000b2000000| PB 0x00000000b2000000| Complete 
| 289|0x00000000b2100000, 0x00000000b2200000, 0x00000000b2200000|100%|HS|  |TAMS 0x00000000b2100000| PB 0x00000000b2100000| Complete 
| 290|0x00000000b2200000, 0x00000000b2300000, 0x00000000b2300000|100%|HS|  |TAMS 0x00000000b2200000| PB 0x00000000b2200000| Complete 
| 291|0x00000000b2300000, 0x00000000b2400000, 0x00000000b2400000|100%|HC|  |TAMS 0x00000000b2300000| PB 0x00000000b2300000| Complete 
| 292|0x00000000b2400000, 0x00000000b2500000, 0x00000000b2500000|100%|HC|  |TAMS 0x00000000b2400000| PB 0x00000000b2400000| Complete 
| 293|0x00000000b2500000, 0x00000000b2600000, 0x00000000b2600000|100%|HS|  |TAMS 0x00000000b2500000| PB 0x00000000b2500000| Complete 
| 294|0x00000000b2600000, 0x00000000b2700000, 0x00000000b2700000|100%|HC|  |TAMS 0x00000000b2600000| PB 0x00000000b2600000| Complete 
| 295|0x00000000b2700000, 0x00000000b2800000, 0x00000000b2800000|100%|HC|  |TAMS 0x00000000b2700000| PB 0x00000000b2700000| Complete 
| 296|0x00000000b2800000, 0x00000000b2900000, 0x00000000b2900000|100%| O|  |TAMS 0x00000000b2800000| PB 0x00000000b2800000| Untracked 
| 297|0x00000000b2900000, 0x00000000b2a00000, 0x00000000b2a00000|100%| O|  |TAMS 0x00000000b2900000| PB 0x00000000b2900000| Untracked 
| 298|0x00000000b2a00000, 0x00000000b2b00000, 0x00000000b2b00000|100%| O|  |TAMS 0x00000000b2a00000| PB 0x00000000b2a00000| Untracked 
| 299|0x00000000b2b00000, 0x00000000b2b00000, 0x00000000b2c00000|  0%| F|  |TAMS 0x00000000b2b00000| PB 0x00000000b2b00000| Untracked 
| 300|0x00000000b2c00000, 0x00000000b2c00000, 0x00000000b2d00000|  0%| F|  |TAMS 0x00000000b2c00000| PB 0x00000000b2c00000| Untracked 
| 301|0x00000000b2d00000, 0x00000000b2e00000, 0x00000000b2e00000|100%| O|  |TAMS 0x00000000b2d00000| PB 0x00000000b2d00000| Untracked 
| 302|0x00000000b2e00000, 0x00000000b2e00000, 0x00000000b2f00000|  0%| F|  |TAMS 0x00000000b2e00000| PB 0x00000000b2e00000| Untracked 
| 303|0x00000000b2f00000, 0x00000000b2f00000, 0x00000000b3000000|  0%| F|  |TAMS 0x00000000b2f00000| PB 0x00000000b2f00000| Untracked 
| 304|0x00000000b3000000, 0x00000000b3000000, 0x00000000b3100000|  0%| F|  |TAMS 0x00000000b3000000| PB 0x00000000b3000000| Untracked 
| 305|0x00000000b3100000, 0x00000000b3200000, 0x00000000b3200000|100%| O|  |TAMS 0x00000000b3100000| PB 0x00000000b3100000| Untracked 
| 306|0x00000000b3200000, 0x00000000b3300000, 0x00000000b3300000|100%| O|  |TAMS 0x00000000b3200000| PB 0x00000000b3200000| Untracked 
| 307|0x00000000b3300000, 0x00000000b3400000, 0x00000000b3400000|100%| O|Cm|TAMS 0x00000000b3300000| PB 0x00000000b3300000| Complete 
| 308|0x00000000b3400000, 0x00000000b3400000, 0x00000000b3500000|  0%| F|  |TAMS 0x00000000b3400000| PB 0x00000000b3400000| Untracked 
| 309|0x00000000b3500000, 0x00000000b3500000, 0x00000000b3600000|  0%| F|  |TAMS 0x00000000b3500000| PB 0x00000000b3500000| Untracked 
| 310|0x00000000b3600000, 0x00000000b3600000, 0x00000000b3700000|  0%| F|  |TAMS 0x00000000b3600000| PB 0x00000000b3600000| Untracked 
| 311|0x00000000b3700000, 0x00000000b3700000, 0x00000000b3800000|  0%| F|  |TAMS 0x00000000b3700000| PB 0x00000000b3700000| Untracked 
| 312|0x00000000b3800000, 0x00000000b3800000, 0x00000000b3900000|  0%| F|  |TAMS 0x00000000b3800000| PB 0x00000000b3800000| Untracked 
| 313|0x00000000b3900000, 0x00000000b3900000, 0x00000000b3a00000|  0%| F|  |TAMS 0x00000000b3900000| PB 0x00000000b3900000| Untracked 
| 314|0x00000000b3a00000, 0x00000000b3b00000, 0x00000000b3b00000|100%| O|Cm|TAMS 0x00000000b3a00000| PB 0x00000000b3a00000| Complete 
| 315|0x00000000b3b00000, 0x00000000b3c00000, 0x00000000b3c00000|100%| O|Cm|TAMS 0x00000000b3b00000| PB 0x00000000b3b00000| Complete 
| 316|0x00000000b3c00000, 0x00000000b3c00000, 0x00000000b3d00000|  0%| F|  |TAMS 0x00000000b3c00000| PB 0x00000000b3c00000| Untracked 
| 317|0x00000000b3d00000, 0x00000000b3d00000, 0x00000000b3e00000|  0%| F|  |TAMS 0x00000000b3d00000| PB 0x00000000b3d00000| Untracked 
| 318|0x00000000b3e00000, 0x00000000b3e00000, 0x00000000b3f00000|  0%| F|  |TAMS 0x00000000b3e00000| PB 0x00000000b3e00000| Untracked 
| 319|0x00000000b3f00000, 0x00000000b3f00000, 0x00000000b4000000|  0%| F|  |TAMS 0x00000000b3f00000| PB 0x00000000b3f00000| Untracked 
| 320|0x00000000b4000000, 0x00000000b4000000, 0x00000000b4100000|  0%| F|  |TAMS 0x00000000b4000000| PB 0x00000000b4000000| Untracked 
| 321|0x00000000b4100000, 0x00000000b4100000, 0x00000000b4200000|  0%| F|  |TAMS 0x00000000b4100000| PB 0x00000000b4100000| Untracked 
| 322|0x00000000b4200000, 0x00000000b4200000, 0x00000000b4300000|  0%| F|  |TAMS 0x00000000b4200000| PB 0x00000000b4200000| Untracked 
| 323|0x00000000b4300000, 0x00000000b4300000, 0x00000000b4400000|  0%| F|  |TAMS 0x00000000b4300000| PB 0x00000000b4300000| Untracked 
| 324|0x00000000b4400000, 0x00000000b4500000, 0x00000000b4500000|100%| O|  |TAMS 0x00000000b4400000| PB 0x00000000b4400000| Untracked 
| 325|0x00000000b4500000, 0x00000000b4500000, 0x00000000b4600000|  0%| F|  |TAMS 0x00000000b4500000| PB 0x00000000b4500000| Untracked 
| 326|0x00000000b4600000, 0x00000000b4700000, 0x00000000b4700000|100%|HS|  |TAMS 0x00000000b4600000| PB 0x00000000b4600000| Complete 
| 327|0x00000000b4700000, 0x00000000b4700000, 0x00000000b4800000|  0%| F|  |TAMS 0x00000000b4700000| PB 0x00000000b4700000| Untracked 
| 328|0x00000000b4800000, 0x00000000b4900000, 0x00000000b4900000|100%| O|  |TAMS 0x00000000b4800000| PB 0x00000000b4800000| Untracked 
| 329|0x00000000b4900000, 0x00000000b4900000, 0x00000000b4a00000|  0%| F|  |TAMS 0x00000000b4900000| PB 0x00000000b4900000| Untracked 
| 330|0x00000000b4a00000, 0x00000000b4b00000, 0x00000000b4b00000|100%|HS|  |TAMS 0x00000000b4a00000| PB 0x00000000b4a00000| Complete 
| 331|0x00000000b4b00000, 0x00000000b4c00000, 0x00000000b4c00000|100%|HC|  |TAMS 0x00000000b4b00000| PB 0x00000000b4b00000| Complete 
| 332|0x00000000b4c00000, 0x00000000b4d00000, 0x00000000b4d00000|100%| O|  |TAMS 0x00000000b4c00000| PB 0x00000000b4c00000| Untracked 
| 333|0x00000000b4d00000, 0x00000000b4d00000, 0x00000000b4e00000|  0%| F|  |TAMS 0x00000000b4d00000| PB 0x00000000b4d00000| Untracked 
| 334|0x00000000b4e00000, 0x00000000b4e00000, 0x00000000b4f00000|  0%| F|  |TAMS 0x00000000b4e00000| PB 0x00000000b4e00000| Untracked 
| 335|0x00000000b4f00000, 0x00000000b5000000, 0x00000000b5000000|100%| O|  |TAMS 0x00000000b4f00000| PB 0x00000000b4f00000| Untracked 
| 336|0x00000000b5000000, 0x00000000b5000000, 0x00000000b5100000|  0%| F|  |TAMS 0x00000000b5000000| PB 0x00000000b5000000| Untracked 
| 337|0x00000000b5100000, 0x00000000b5200000, 0x00000000b5200000|100%| O|  |TAMS 0x00000000b5100000| PB 0x00000000b5100000| Untracked 
| 338|0x00000000b5200000, 0x00000000b5300000, 0x00000000b5300000|100%| O|  |TAMS 0x00000000b5200000| PB 0x00000000b5200000| Untracked 
| 339|0x00000000b5300000, 0x00000000b5400000, 0x00000000b5400000|100%| O|  |TAMS 0x00000000b5300000| PB 0x00000000b5300000| Untracked 
| 340|0x00000000b5400000, 0x00000000b5500000, 0x00000000b5500000|100%| O|  |TAMS 0x00000000b5400000| PB 0x00000000b5400000| Untracked 
| 341|0x00000000b5500000, 0x00000000b5600000, 0x00000000b5600000|100%| O|  |TAMS 0x00000000b5500000| PB 0x00000000b5500000| Untracked 
| 342|0x00000000b5600000, 0x00000000b5700000, 0x00000000b5700000|100%| O|  |TAMS 0x00000000b5600000| PB 0x00000000b5600000| Untracked 
| 343|0x00000000b5700000, 0x00000000b5800000, 0x00000000b5800000|100%| O|  |TAMS 0x00000000b5700000| PB 0x00000000b5700000| Untracked 
| 344|0x00000000b5800000, 0x00000000b5900000, 0x00000000b5900000|100%| O|Cm|TAMS 0x00000000b5800000| PB 0x00000000b5800000| Complete 
| 345|0x00000000b5900000, 0x00000000b5a00000, 0x00000000b5a00000|100%| O|Cm|TAMS 0x00000000b5900000| PB 0x00000000b5900000| Complete 
| 346|0x00000000b5a00000, 0x00000000b5a00000, 0x00000000b5b00000|  0%| F|  |TAMS 0x00000000b5a00000| PB 0x00000000b5a00000| Untracked 
| 347|0x00000000b5b00000, 0x00000000b5c00000, 0x00000000b5c00000|100%| O|Cm|TAMS 0x00000000b5b00000| PB 0x00000000b5b00000| Complete 
| 348|0x00000000b5c00000, 0x00000000b5d00000, 0x00000000b5d00000|100%| O|Cm|TAMS 0x00000000b5c00000| PB 0x00000000b5c00000| Complete 
| 349|0x00000000b5d00000, 0x00000000b5e00000, 0x00000000b5e00000|100%|HS|  |TAMS 0x00000000b5d00000| PB 0x00000000b5d00000| Complete 
| 350|0x00000000b5e00000, 0x00000000b5f00000, 0x00000000b5f00000|100%|HC|  |TAMS 0x00000000b5e00000| PB 0x00000000b5e00000| Complete 
| 351|0x00000000b5f00000, 0x00000000b6000000, 0x00000000b6000000|100%| O|  |TAMS 0x00000000b5f00000| PB 0x00000000b5f00000| Untracked 
| 352|0x00000000b6000000, 0x00000000b6000000, 0x00000000b6100000|  0%| F|  |TAMS 0x00000000b6000000| PB 0x00000000b6000000| Untracked 
| 353|0x00000000b6100000, 0x00000000b6100000, 0x00000000b6200000|  0%| F|  |TAMS 0x00000000b6100000| PB 0x00000000b6100000| Untracked 
| 354|0x00000000b6200000, 0x00000000b6200000, 0x00000000b6300000|  0%| F|  |TAMS 0x00000000b6200000| PB 0x00000000b6200000| Untracked 
| 355|0x00000000b6300000, 0x00000000b6400000, 0x00000000b6400000|100%| O|  |TAMS 0x00000000b6300000| PB 0x00000000b6300000| Untracked 
| 356|0x00000000b6400000, 0x00000000b6500000, 0x00000000b6500000|100%| O|  |TAMS 0x00000000b6400000| PB 0x00000000b6400000| Untracked 
| 357|0x00000000b6500000, 0x00000000b6600000, 0x00000000b6600000|100%| O|  |TAMS 0x00000000b6500000| PB 0x00000000b6500000| Untracked 
| 358|0x00000000b6600000, 0x00000000b6700000, 0x00000000b6700000|100%| O|  |TAMS 0x00000000b6600000| PB 0x00000000b6600000| Untracked 
| 359|0x00000000b6700000, 0x00000000b6800000, 0x00000000b6800000|100%| O|  |TAMS 0x00000000b6700000| PB 0x00000000b6700000| Untracked 
| 360|0x00000000b6800000, 0x00000000b6800000, 0x00000000b6900000|  0%| F|  |TAMS 0x00000000b6800000| PB 0x00000000b6800000| Untracked 
| 361|0x00000000b6900000, 0x00000000b6900000, 0x00000000b6a00000|  0%| F|  |TAMS 0x00000000b6900000| PB 0x00000000b6900000| Untracked 
| 362|0x00000000b6a00000, 0x00000000b6a00000, 0x00000000b6b00000|  0%| F|  |TAMS 0x00000000b6a00000| PB 0x00000000b6a00000| Untracked 
| 363|0x00000000b6b00000, 0x00000000b6c00000, 0x00000000b6c00000|100%| O|  |TAMS 0x00000000b6b00000| PB 0x00000000b6b00000| Untracked 
| 364|0x00000000b6c00000, 0x00000000b6d00000, 0x00000000b6d00000|100%| O|  |TAMS 0x00000000b6c00000| PB 0x00000000b6c00000| Untracked 
| 365|0x00000000b6d00000, 0x00000000b6e00000, 0x00000000b6e00000|100%| O|  |TAMS 0x00000000b6d00000| PB 0x00000000b6d00000| Untracked 
| 366|0x00000000b6e00000, 0x00000000b6f00000, 0x00000000b6f00000|100%| O|  |TAMS 0x00000000b6e00000| PB 0x00000000b6e00000| Untracked 
| 367|0x00000000b6f00000, 0x00000000b7000000, 0x00000000b7000000|100%| O|  |TAMS 0x00000000b6f00000| PB 0x00000000b6f00000| Untracked 
| 368|0x00000000b7000000, 0x00000000b7100000, 0x00000000b7100000|100%| O|  |TAMS 0x00000000b7000000| PB 0x00000000b7000000| Untracked 
| 369|0x00000000b7100000, 0x00000000b7200000, 0x00000000b7200000|100%| O|  |TAMS 0x00000000b7100000| PB 0x00000000b7100000| Untracked 
| 370|0x00000000b7200000, 0x00000000b7300000, 0x00000000b7300000|100%| O|  |TAMS 0x00000000b7200000| PB 0x00000000b7200000| Untracked 
| 371|0x00000000b7300000, 0x00000000b7400000, 0x00000000b7400000|100%| O|  |TAMS 0x00000000b7300000| PB 0x00000000b7300000| Untracked 
| 372|0x00000000b7400000, 0x00000000b7500000, 0x00000000b7500000|100%| O|  |TAMS 0x00000000b7400000| PB 0x00000000b7400000| Untracked 
| 373|0x00000000b7500000, 0x00000000b7600000, 0x00000000b7600000|100%| O|  |TAMS 0x00000000b7500000| PB 0x00000000b7500000| Untracked 
| 374|0x00000000b7600000, 0x00000000b7700000, 0x00000000b7700000|100%| O|  |TAMS 0x00000000b7600000| PB 0x00000000b7600000| Untracked 
| 375|0x00000000b7700000, 0x00000000b7800000, 0x00000000b7800000|100%| O|  |TAMS 0x00000000b7700000| PB 0x00000000b7700000| Untracked 
| 376|0x00000000b7800000, 0x00000000b7900000, 0x00000000b7900000|100%| O|  |TAMS 0x00000000b7800000| PB 0x00000000b7800000| Untracked 
| 377|0x00000000b7900000, 0x00000000b7a00000, 0x00000000b7a00000|100%| O|  |TAMS 0x00000000b7900000| PB 0x00000000b7900000| Untracked 
| 378|0x00000000b7a00000, 0x00000000b7b00000, 0x00000000b7b00000|100%| O|  |TAMS 0x00000000b7a00000| PB 0x00000000b7a00000| Untracked 
| 379|0x00000000b7b00000, 0x00000000b7c00000, 0x00000000b7c00000|100%| O|  |TAMS 0x00000000b7b00000| PB 0x00000000b7b00000| Untracked 
| 380|0x00000000b7c00000, 0x00000000b7d00000, 0x00000000b7d00000|100%| O|  |TAMS 0x00000000b7c00000| PB 0x00000000b7c00000| Untracked 
| 381|0x00000000b7d00000, 0x00000000b7e00000, 0x00000000b7e00000|100%| O|  |TAMS 0x00000000b7d00000| PB 0x00000000b7d00000| Untracked 
| 382|0x00000000b7e00000, 0x00000000b7f00000, 0x00000000b7f00000|100%| O|  |TAMS 0x00000000b7e00000| PB 0x00000000b7e00000| Untracked 
| 383|0x00000000b7f00000, 0x00000000b8000000, 0x00000000b8000000|100%| O|  |TAMS 0x00000000b7f00000| PB 0x00000000b7f00000| Untracked 
| 384|0x00000000b8000000, 0x00000000b8100000, 0x00000000b8100000|100%| O|  |TAMS 0x00000000b8000000| PB 0x00000000b8000000| Untracked 
| 385|0x00000000b8100000, 0x00000000b8200000, 0x00000000b8200000|100%| O|  |TAMS 0x00000000b8100000| PB 0x00000000b8100000| Untracked 
| 386|0x00000000b8200000, 0x00000000b8300000, 0x00000000b8300000|100%| O|Cm|TAMS 0x00000000b8200000| PB 0x00000000b8200000| Complete 
| 387|0x00000000b8300000, 0x00000000b8400000, 0x00000000b8400000|100%| O|Cm|TAMS 0x00000000b8300000| PB 0x00000000b8300000| Complete 
| 388|0x00000000b8400000, 0x00000000b8500000, 0x00000000b8500000|100%| O|  |TAMS 0x00000000b8400000| PB 0x00000000b8400000| Untracked 
| 389|0x00000000b8500000, 0x00000000b8600000, 0x00000000b8600000|100%| O|  |TAMS 0x00000000b8500000| PB 0x00000000b8500000| Untracked 
| 390|0x00000000b8600000, 0x00000000b8700000, 0x00000000b8700000|100%| O|  |TAMS 0x00000000b8600000| PB 0x00000000b8600000| Untracked 
| 391|0x00000000b8700000, 0x00000000b8800000, 0x00000000b8800000|100%| O|  |TAMS 0x00000000b8700000| PB 0x00000000b8700000| Untracked 
| 392|0x00000000b8800000, 0x00000000b8900000, 0x00000000b8900000|100%| O|  |TAMS 0x00000000b8800000| PB 0x00000000b8800000| Untracked 
| 393|0x00000000b8900000, 0x00000000b8a00000, 0x00000000b8a00000|100%| O|  |TAMS 0x00000000b8900000| PB 0x00000000b8900000| Untracked 
| 394|0x00000000b8a00000, 0x00000000b8b00000, 0x00000000b8b00000|100%| O|  |TAMS 0x00000000b8a00000| PB 0x00000000b8a00000| Untracked 
| 395|0x00000000b8b00000, 0x00000000b8c00000, 0x00000000b8c00000|100%| O|  |TAMS 0x00000000b8b00000| PB 0x00000000b8b00000| Untracked 
| 396|0x00000000b8c00000, 0x00000000b8d00000, 0x00000000b8d00000|100%| O|  |TAMS 0x00000000b8c00000| PB 0x00000000b8c00000| Untracked 
| 397|0x00000000b8d00000, 0x00000000b8e00000, 0x00000000b8e00000|100%| O|  |TAMS 0x00000000b8d00000| PB 0x00000000b8d00000| Untracked 
| 398|0x00000000b8e00000, 0x00000000b8f00000, 0x00000000b8f00000|100%| O|  |TAMS 0x00000000b8e00000| PB 0x00000000b8e00000| Untracked 
| 399|0x00000000b8f00000, 0x00000000b9000000, 0x00000000b9000000|100%| O|  |TAMS 0x00000000b8f00000| PB 0x00000000b8f00000| Untracked 
| 400|0x00000000b9000000, 0x00000000b9100000, 0x00000000b9100000|100%| O|  |TAMS 0x00000000b9000000| PB 0x00000000b9000000| Untracked 
| 401|0x00000000b9100000, 0x00000000b9200000, 0x00000000b9200000|100%| O|  |TAMS 0x00000000b9100000| PB 0x00000000b9100000| Untracked 
| 402|0x00000000b9200000, 0x00000000b9300000, 0x00000000b9300000|100%| O|  |TAMS 0x00000000b9200000| PB 0x00000000b9200000| Untracked 
| 403|0x00000000b9300000, 0x00000000b9400000, 0x00000000b9400000|100%| O|  |TAMS 0x00000000b9300000| PB 0x00000000b9300000| Untracked 
| 404|0x00000000b9400000, 0x00000000b9500000, 0x00000000b9500000|100%| O|  |TAMS 0x00000000b9400000| PB 0x00000000b9400000| Untracked 
| 405|0x00000000b9500000, 0x00000000b9600000, 0x00000000b9600000|100%| O|  |TAMS 0x00000000b9500000| PB 0x00000000b9500000| Untracked 
| 406|0x00000000b9600000, 0x00000000b9700000, 0x00000000b9700000|100%| O|  |TAMS 0x00000000b9600000| PB 0x00000000b9600000| Untracked 
| 407|0x00000000b9700000, 0x00000000b9800000, 0x00000000b9800000|100%| O|  |TAMS 0x00000000b9700000| PB 0x00000000b9700000| Untracked 
| 408|0x00000000b9800000, 0x00000000b9900000, 0x00000000b9900000|100%| O|  |TAMS 0x00000000b9800000| PB 0x00000000b9800000| Untracked 
| 409|0x00000000b9900000, 0x00000000b9a00000, 0x00000000b9a00000|100%| O|  |TAMS 0x00000000b9900000| PB 0x00000000b9900000| Untracked 
| 410|0x00000000b9a00000, 0x00000000b9b00000, 0x00000000b9b00000|100%| O|  |TAMS 0x00000000b9a00000| PB 0x00000000b9a00000| Untracked 
| 411|0x00000000b9b00000, 0x00000000b9c00000, 0x00000000b9c00000|100%| O|  |TAMS 0x00000000b9b00000| PB 0x00000000b9b00000| Untracked 
| 412|0x00000000b9c00000, 0x00000000b9d00000, 0x00000000b9d00000|100%| O|  |TAMS 0x00000000b9c00000| PB 0x00000000b9c00000| Untracked 
| 413|0x00000000b9d00000, 0x00000000b9e00000, 0x00000000b9e00000|100%| O|  |TAMS 0x00000000b9d00000| PB 0x00000000b9d00000| Untracked 
| 414|0x00000000b9e00000, 0x00000000b9f00000, 0x00000000b9f00000|100%| O|  |TAMS 0x00000000b9e00000| PB 0x00000000b9e00000| Untracked 
| 415|0x00000000b9f00000, 0x00000000ba000000, 0x00000000ba000000|100%| O|  |TAMS 0x00000000b9f00000| PB 0x00000000b9f00000| Untracked 
| 416|0x00000000ba000000, 0x00000000ba100000, 0x00000000ba100000|100%| O|  |TAMS 0x00000000ba000000| PB 0x00000000ba000000| Untracked 
| 417|0x00000000ba100000, 0x00000000ba200000, 0x00000000ba200000|100%| O|  |TAMS 0x00000000ba100000| PB 0x00000000ba100000| Untracked 
| 418|0x00000000ba200000, 0x00000000ba300000, 0x00000000ba300000|100%| O|  |TAMS 0x00000000ba200000| PB 0x00000000ba200000| Untracked 
| 419|0x00000000ba300000, 0x00000000ba400000, 0x00000000ba400000|100%| O|  |TAMS 0x00000000ba300000| PB 0x00000000ba300000| Untracked 
| 420|0x00000000ba400000, 0x00000000ba500000, 0x00000000ba500000|100%| O|  |TAMS 0x00000000ba400000| PB 0x00000000ba400000| Untracked 
| 421|0x00000000ba500000, 0x00000000ba600000, 0x00000000ba600000|100%| O|  |TAMS 0x00000000ba500000| PB 0x00000000ba500000| Untracked 
| 422|0x00000000ba600000, 0x00000000ba700000, 0x00000000ba700000|100%| O|  |TAMS 0x00000000ba600000| PB 0x00000000ba600000| Untracked 
| 423|0x00000000ba700000, 0x00000000ba800000, 0x00000000ba800000|100%| O|  |TAMS 0x00000000ba700000| PB 0x00000000ba700000| Untracked 
| 424|0x00000000ba800000, 0x00000000ba900000, 0x00000000ba900000|100%| O|  |TAMS 0x00000000ba800000| PB 0x00000000ba800000| Untracked 
| 425|0x00000000ba900000, 0x00000000baa00000, 0x00000000baa00000|100%| O|  |TAMS 0x00000000ba900000| PB 0x00000000ba900000| Untracked 
| 426|0x00000000baa00000, 0x00000000bab00000, 0x00000000bab00000|100%| O|  |TAMS 0x00000000baa00000| PB 0x00000000baa00000| Untracked 
| 427|0x00000000bab00000, 0x00000000bac00000, 0x00000000bac00000|100%| O|  |TAMS 0x00000000bab00000| PB 0x00000000bab00000| Untracked 
| 428|0x00000000bac00000, 0x00000000bad00000, 0x00000000bad00000|100%| O|  |TAMS 0x00000000bac00000| PB 0x00000000bac00000| Untracked 
| 429|0x00000000bad00000, 0x00000000bae00000, 0x00000000bae00000|100%| O|  |TAMS 0x00000000bad00000| PB 0x00000000bad00000| Untracked 
| 430|0x00000000bae00000, 0x00000000baf00000, 0x00000000baf00000|100%| O|  |TAMS 0x00000000bae00000| PB 0x00000000bae00000| Untracked 
| 431|0x00000000baf00000, 0x00000000bb000000, 0x00000000bb000000|100%| O|  |TAMS 0x00000000baf00000| PB 0x00000000baf00000| Untracked 
| 432|0x00000000bb000000, 0x00000000bb100000, 0x00000000bb100000|100%| O|  |TAMS 0x00000000bb000000| PB 0x00000000bb000000| Untracked 
| 433|0x00000000bb100000, 0x00000000bb200000, 0x00000000bb200000|100%| O|  |TAMS 0x00000000bb100000| PB 0x00000000bb100000| Untracked 
| 434|0x00000000bb200000, 0x00000000bb300000, 0x00000000bb300000|100%| O|  |TAMS 0x00000000bb200000| PB 0x00000000bb200000| Untracked 
| 435|0x00000000bb300000, 0x00000000bb400000, 0x00000000bb400000|100%| O|  |TAMS 0x00000000bb300000| PB 0x00000000bb300000| Untracked 
| 436|0x00000000bb400000, 0x00000000bb500000, 0x00000000bb500000|100%| O|  |TAMS 0x00000000bb400000| PB 0x00000000bb400000| Untracked 
| 437|0x00000000bb500000, 0x00000000bb600000, 0x00000000bb600000|100%| O|  |TAMS 0x00000000bb500000| PB 0x00000000bb500000| Untracked 
| 438|0x00000000bb600000, 0x00000000bb700000, 0x00000000bb700000|100%| O|  |TAMS 0x00000000bb600000| PB 0x00000000bb600000| Untracked 
| 439|0x00000000bb700000, 0x00000000bb800000, 0x00000000bb800000|100%| O|  |TAMS 0x00000000bb700000| PB 0x00000000bb700000| Untracked 
| 440|0x00000000bb800000, 0x00000000bb900000, 0x00000000bb900000|100%| O|  |TAMS 0x00000000bb800000| PB 0x00000000bb800000| Untracked 
| 441|0x00000000bb900000, 0x00000000bba00000, 0x00000000bba00000|100%| O|  |TAMS 0x00000000bb900000| PB 0x00000000bb900000| Untracked 
| 442|0x00000000bba00000, 0x00000000bbb00000, 0x00000000bbb00000|100%| O|  |TAMS 0x00000000bba00000| PB 0x00000000bba00000| Untracked 
| 443|0x00000000bbb00000, 0x00000000bbc00000, 0x00000000bbc00000|100%| O|  |TAMS 0x00000000bbb00000| PB 0x00000000bbb00000| Untracked 
| 444|0x00000000bbc00000, 0x00000000bbd00000, 0x00000000bbd00000|100%| O|  |TAMS 0x00000000bbc00000| PB 0x00000000bbc00000| Untracked 
| 445|0x00000000bbd00000, 0x00000000bbe00000, 0x00000000bbe00000|100%| O|  |TAMS 0x00000000bbd00000| PB 0x00000000bbd00000| Untracked 
| 446|0x00000000bbe00000, 0x00000000bbf00000, 0x00000000bbf00000|100%| O|  |TAMS 0x00000000bbe00000| PB 0x00000000bbe00000| Untracked 
| 447|0x00000000bbf00000, 0x00000000bc000000, 0x00000000bc000000|100%| O|  |TAMS 0x00000000bbf00000| PB 0x00000000bbf00000| Untracked 
| 448|0x00000000bc000000, 0x00000000bc100000, 0x00000000bc100000|100%| O|  |TAMS 0x00000000bc000000| PB 0x00000000bc000000| Untracked 
| 449|0x00000000bc100000, 0x00000000bc200000, 0x00000000bc200000|100%| O|  |TAMS 0x00000000bc100000| PB 0x00000000bc100000| Untracked 
| 450|0x00000000bc200000, 0x00000000bc300000, 0x00000000bc300000|100%| O|  |TAMS 0x00000000bc200000| PB 0x00000000bc200000| Untracked 
| 451|0x00000000bc300000, 0x00000000bc400000, 0x00000000bc400000|100%| O|  |TAMS 0x00000000bc300000| PB 0x00000000bc300000| Untracked 
| 452|0x00000000bc400000, 0x00000000bc500000, 0x00000000bc500000|100%| O|  |TAMS 0x00000000bc400000| PB 0x00000000bc400000| Untracked 
| 453|0x00000000bc500000, 0x00000000bc600000, 0x00000000bc600000|100%| O|  |TAMS 0x00000000bc500000| PB 0x00000000bc500000| Untracked 
| 454|0x00000000bc600000, 0x00000000bc700000, 0x00000000bc700000|100%| O|  |TAMS 0x00000000bc600000| PB 0x00000000bc600000| Untracked 
| 455|0x00000000bc700000, 0x00000000bc800000, 0x00000000bc800000|100%| O|  |TAMS 0x00000000bc700000| PB 0x00000000bc700000| Untracked 
| 456|0x00000000bc800000, 0x00000000bc900000, 0x00000000bc900000|100%| O|  |TAMS 0x00000000bc800000| PB 0x00000000bc800000| Untracked 
| 457|0x00000000bc900000, 0x00000000bca00000, 0x00000000bca00000|100%| O|  |TAMS 0x00000000bc900000| PB 0x00000000bc900000| Untracked 
| 458|0x00000000bca00000, 0x00000000bcb00000, 0x00000000bcb00000|100%| O|  |TAMS 0x00000000bca00000| PB 0x00000000bca00000| Untracked 
| 459|0x00000000bcb00000, 0x00000000bcc00000, 0x00000000bcc00000|100%| O|  |TAMS 0x00000000bcb00000| PB 0x00000000bcb00000| Untracked 
| 460|0x00000000bcc00000, 0x00000000bcd00000, 0x00000000bcd00000|100%| O|  |TAMS 0x00000000bcc00000| PB 0x00000000bcc00000| Untracked 
| 461|0x00000000bcd00000, 0x00000000bce00000, 0x00000000bce00000|100%| O|  |TAMS 0x00000000bcd00000| PB 0x00000000bcd00000| Untracked 
| 462|0x00000000bce00000, 0x00000000bcf00000, 0x00000000bcf00000|100%| O|  |TAMS 0x00000000bce00000| PB 0x00000000bce00000| Untracked 
| 463|0x00000000bcf00000, 0x00000000bd000000, 0x00000000bd000000|100%| O|  |TAMS 0x00000000bcf00000| PB 0x00000000bcf00000| Untracked 
| 464|0x00000000bd000000, 0x00000000bd100000, 0x00000000bd100000|100%| O|  |TAMS 0x00000000bd000000| PB 0x00000000bd000000| Untracked 
| 465|0x00000000bd100000, 0x00000000bd200000, 0x00000000bd200000|100%| O|  |TAMS 0x00000000bd100000| PB 0x00000000bd100000| Untracked 
| 466|0x00000000bd200000, 0x00000000bd300000, 0x00000000bd300000|100%| O|  |TAMS 0x00000000bd200000| PB 0x00000000bd200000| Untracked 
| 467|0x00000000bd300000, 0x00000000bd400000, 0x00000000bd400000|100%| O|  |TAMS 0x00000000bd300000| PB 0x00000000bd300000| Untracked 
| 468|0x00000000bd400000, 0x00000000bd500000, 0x00000000bd500000|100%| O|  |TAMS 0x00000000bd400000| PB 0x00000000bd400000| Untracked 
| 469|0x00000000bd500000, 0x00000000bd600000, 0x00000000bd600000|100%| O|  |TAMS 0x00000000bd500000| PB 0x00000000bd500000| Untracked 
| 470|0x00000000bd600000, 0x00000000bd700000, 0x00000000bd700000|100%| O|  |TAMS 0x00000000bd600000| PB 0x00000000bd600000| Untracked 
| 471|0x00000000bd700000, 0x00000000bd800000, 0x00000000bd800000|100%| O|  |TAMS 0x00000000bd700000| PB 0x00000000bd700000| Untracked 
| 472|0x00000000bd800000, 0x00000000bd900000, 0x00000000bd900000|100%| O|  |TAMS 0x00000000bd800000| PB 0x00000000bd800000| Untracked 
| 473|0x00000000bd900000, 0x00000000bda00000, 0x00000000bda00000|100%| O|  |TAMS 0x00000000bd900000| PB 0x00000000bd900000| Untracked 
| 474|0x00000000bda00000, 0x00000000bdb00000, 0x00000000bdb00000|100%| O|  |TAMS 0x00000000bda00000| PB 0x00000000bda00000| Untracked 
| 475|0x00000000bdb00000, 0x00000000bdc00000, 0x00000000bdc00000|100%| O|  |TAMS 0x00000000bdb00000| PB 0x00000000bdb00000| Untracked 
| 476|0x00000000bdc00000, 0x00000000bdd00000, 0x00000000bdd00000|100%| O|  |TAMS 0x00000000bdc00000| PB 0x00000000bdc00000| Untracked 
| 477|0x00000000bdd00000, 0x00000000bde00000, 0x00000000bde00000|100%| O|  |TAMS 0x00000000bdd00000| PB 0x00000000bdd00000| Untracked 
| 478|0x00000000bde00000, 0x00000000bdf00000, 0x00000000bdf00000|100%| O|  |TAMS 0x00000000bde00000| PB 0x00000000bde00000| Untracked 
| 479|0x00000000bdf00000, 0x00000000be000000, 0x00000000be000000|100%| O|  |TAMS 0x00000000bdf00000| PB 0x00000000bdf00000| Untracked 
| 480|0x00000000be000000, 0x00000000be100000, 0x00000000be100000|100%| O|  |TAMS 0x00000000be000000| PB 0x00000000be000000| Untracked 
| 481|0x00000000be100000, 0x00000000be200000, 0x00000000be200000|100%| O|  |TAMS 0x00000000be100000| PB 0x00000000be100000| Untracked 
| 482|0x00000000be200000, 0x00000000be300000, 0x00000000be300000|100%| O|  |TAMS 0x00000000be200000| PB 0x00000000be200000| Untracked 
| 483|0x00000000be300000, 0x00000000be400000, 0x00000000be400000|100%| O|Cm|TAMS 0x00000000be300000| PB 0x00000000be300000| Complete 
| 484|0x00000000be400000, 0x00000000be500000, 0x00000000be500000|100%| O|  |TAMS 0x00000000be400000| PB 0x00000000be400000| Untracked 
| 485|0x00000000be500000, 0x00000000be600000, 0x00000000be600000|100%| O|  |TAMS 0x00000000be500000| PB 0x00000000be500000| Untracked 
| 486|0x00000000be600000, 0x00000000be700000, 0x00000000be700000|100%| O|  |TAMS 0x00000000be600000| PB 0x00000000be600000| Untracked 
| 487|0x00000000be700000, 0x00000000be800000, 0x00000000be800000|100%| O|  |TAMS 0x00000000be700000| PB 0x00000000be700000| Untracked 
| 488|0x00000000be800000, 0x00000000be900000, 0x00000000be900000|100%| O|  |TAMS 0x00000000be800000| PB 0x00000000be800000| Untracked 
| 489|0x00000000be900000, 0x00000000bea00000, 0x00000000bea00000|100%| O|  |TAMS 0x00000000be900000| PB 0x00000000be900000| Untracked 
| 490|0x00000000bea00000, 0x00000000beb00000, 0x00000000beb00000|100%| O|  |TAMS 0x00000000bea00000| PB 0x00000000bea00000| Untracked 
| 491|0x00000000beb00000, 0x00000000bec00000, 0x00000000bec00000|100%| O|  |TAMS 0x00000000beb00000| PB 0x00000000beb00000| Untracked 
| 492|0x00000000bec00000, 0x00000000bed00000, 0x00000000bed00000|100%| O|  |TAMS 0x00000000bec00000| PB 0x00000000bec00000| Untracked 
| 493|0x00000000bed00000, 0x00000000bee00000, 0x00000000bee00000|100%| O|  |TAMS 0x00000000bed00000| PB 0x00000000bed00000| Untracked 
| 494|0x00000000bee00000, 0x00000000bef00000, 0x00000000bef00000|100%| O|  |TAMS 0x00000000bee00000| PB 0x00000000bee00000| Untracked 
| 495|0x00000000bef00000, 0x00000000bf000000, 0x00000000bf000000|100%| O|  |TAMS 0x00000000bef00000| PB 0x00000000bef00000| Untracked 
| 496|0x00000000bf000000, 0x00000000bf100000, 0x00000000bf100000|100%| O|  |TAMS 0x00000000bf000000| PB 0x00000000bf000000| Untracked 
| 497|0x00000000bf100000, 0x00000000bf200000, 0x00000000bf200000|100%| O|  |TAMS 0x00000000bf100000| PB 0x00000000bf100000| Untracked 
| 498|0x00000000bf200000, 0x00000000bf300000, 0x00000000bf300000|100%| O|  |TAMS 0x00000000bf200000| PB 0x00000000bf200000| Untracked 
| 499|0x00000000bf300000, 0x00000000bf400000, 0x00000000bf400000|100%| O|  |TAMS 0x00000000bf300000| PB 0x00000000bf300000| Untracked 
| 500|0x00000000bf400000, 0x00000000bf500000, 0x00000000bf500000|100%| O|  |TAMS 0x00000000bf400000| PB 0x00000000bf400000| Untracked 
| 501|0x00000000bf500000, 0x00000000bf600000, 0x00000000bf600000|100%| O|  |TAMS 0x00000000bf500000| PB 0x00000000bf500000| Untracked 
| 502|0x00000000bf600000, 0x00000000bf700000, 0x00000000bf700000|100%| O|  |TAMS 0x00000000bf600000| PB 0x00000000bf600000| Untracked 
| 503|0x00000000bf700000, 0x00000000bf800000, 0x00000000bf800000|100%| O|  |TAMS 0x00000000bf700000| PB 0x00000000bf700000| Untracked 
| 504|0x00000000bf800000, 0x00000000bf900000, 0x00000000bf900000|100%| O|  |TAMS 0x00000000bf800000| PB 0x00000000bf800000| Untracked 
| 505|0x00000000bf900000, 0x00000000bfa00000, 0x00000000bfa00000|100%| O|  |TAMS 0x00000000bf900000| PB 0x00000000bf900000| Untracked 
| 506|0x00000000bfa00000, 0x00000000bfb00000, 0x00000000bfb00000|100%| O|  |TAMS 0x00000000bfa00000| PB 0x00000000bfa00000| Untracked 
| 507|0x00000000bfb00000, 0x00000000bfc00000, 0x00000000bfc00000|100%| O|  |TAMS 0x00000000bfb00000| PB 0x00000000bfb00000| Untracked 
| 508|0x00000000bfc00000, 0x00000000bfd00000, 0x00000000bfd00000|100%| O|  |TAMS 0x00000000bfc00000| PB 0x00000000bfc00000| Untracked 
| 509|0x00000000bfd00000, 0x00000000bfe00000, 0x00000000bfe00000|100%| O|  |TAMS 0x00000000bfd00000| PB 0x00000000bfd00000| Untracked 
| 510|0x00000000bfe00000, 0x00000000bff00000, 0x00000000bff00000|100%| O|  |TAMS 0x00000000bfe00000| PB 0x00000000bfe00000| Untracked 
| 511|0x00000000bff00000, 0x00000000c0000000, 0x00000000c0000000|100%| O|  |TAMS 0x00000000bff00000| PB 0x00000000bff00000| Untracked 
| 512|0x00000000c0000000, 0x00000000c0100000, 0x00000000c0100000|100%| O|  |TAMS 0x00000000c0000000| PB 0x00000000c0000000| Untracked 
| 513|0x00000000c0100000, 0x00000000c0200000, 0x00000000c0200000|100%| O|  |TAMS 0x00000000c0100000| PB 0x00000000c0100000| Untracked 
| 514|0x00000000c0200000, 0x00000000c0300000, 0x00000000c0300000|100%| O|  |TAMS 0x00000000c0200000| PB 0x00000000c0200000| Untracked 
| 515|0x00000000c0300000, 0x00000000c0400000, 0x00000000c0400000|100%| O|  |TAMS 0x00000000c0300000| PB 0x00000000c0300000| Untracked 
| 516|0x00000000c0400000, 0x00000000c0500000, 0x00000000c0500000|100%| O|  |TAMS 0x00000000c0400000| PB 0x00000000c0400000| Untracked 
| 517|0x00000000c0500000, 0x00000000c0600000, 0x00000000c0600000|100%| O|  |TAMS 0x00000000c0500000| PB 0x00000000c0500000| Untracked 
| 518|0x00000000c0600000, 0x00000000c0700000, 0x00000000c0700000|100%| O|  |TAMS 0x00000000c0600000| PB 0x00000000c0600000| Untracked 
| 519|0x00000000c0700000, 0x00000000c0800000, 0x00000000c0800000|100%| O|  |TAMS 0x00000000c0700000| PB 0x00000000c0700000| Untracked 
| 520|0x00000000c0800000, 0x00000000c0900000, 0x00000000c0900000|100%| O|  |TAMS 0x00000000c0800000| PB 0x00000000c0800000| Untracked 
| 521|0x00000000c0900000, 0x00000000c0a00000, 0x00000000c0a00000|100%| O|  |TAMS 0x00000000c0900000| PB 0x00000000c0900000| Untracked 
| 522|0x00000000c0a00000, 0x00000000c0b00000, 0x00000000c0b00000|100%| O|  |TAMS 0x00000000c0a00000| PB 0x00000000c0a00000| Untracked 
| 523|0x00000000c0b00000, 0x00000000c0c00000, 0x00000000c0c00000|100%| O|  |TAMS 0x00000000c0b00000| PB 0x00000000c0b00000| Untracked 
| 524|0x00000000c0c00000, 0x00000000c0d00000, 0x00000000c0d00000|100%| O|  |TAMS 0x00000000c0c00000| PB 0x00000000c0c00000| Untracked 
| 525|0x00000000c0d00000, 0x00000000c0e00000, 0x00000000c0e00000|100%| O|  |TAMS 0x00000000c0d00000| PB 0x00000000c0d00000| Untracked 
| 526|0x00000000c0e00000, 0x00000000c0f00000, 0x00000000c0f00000|100%| O|  |TAMS 0x00000000c0e00000| PB 0x00000000c0e00000| Untracked 
| 527|0x00000000c0f00000, 0x00000000c1000000, 0x00000000c1000000|100%| O|  |TAMS 0x00000000c0f00000| PB 0x00000000c0f00000| Untracked 
| 528|0x00000000c1000000, 0x00000000c1100000, 0x00000000c1100000|100%| O|  |TAMS 0x00000000c1000000| PB 0x00000000c1000000| Untracked 
| 529|0x00000000c1100000, 0x00000000c1200000, 0x00000000c1200000|100%| O|  |TAMS 0x00000000c1100000| PB 0x00000000c1100000| Untracked 
| 530|0x00000000c1200000, 0x00000000c1300000, 0x00000000c1300000|100%| O|  |TAMS 0x00000000c1200000| PB 0x00000000c1200000| Untracked 
| 531|0x00000000c1300000, 0x00000000c1400000, 0x00000000c1400000|100%| O|  |TAMS 0x00000000c1300000| PB 0x00000000c1300000| Untracked 
| 532|0x00000000c1400000, 0x00000000c1500000, 0x00000000c1500000|100%| O|  |TAMS 0x00000000c1400000| PB 0x00000000c1400000| Untracked 
| 533|0x00000000c1500000, 0x00000000c1600000, 0x00000000c1600000|100%| O|  |TAMS 0x00000000c1500000| PB 0x00000000c1500000| Untracked 
| 534|0x00000000c1600000, 0x00000000c1700000, 0x00000000c1700000|100%| O|  |TAMS 0x00000000c1600000| PB 0x00000000c1600000| Untracked 
| 535|0x00000000c1700000, 0x00000000c1800000, 0x00000000c1800000|100%| O|  |TAMS 0x00000000c1700000| PB 0x00000000c1700000| Untracked 
| 536|0x00000000c1800000, 0x00000000c1900000, 0x00000000c1900000|100%| O|  |TAMS 0x00000000c1800000| PB 0x00000000c1800000| Untracked 
| 537|0x00000000c1900000, 0x00000000c1a00000, 0x00000000c1a00000|100%| O|  |TAMS 0x00000000c1900000| PB 0x00000000c1900000| Untracked 
| 538|0x00000000c1a00000, 0x00000000c1b00000, 0x00000000c1b00000|100%| O|  |TAMS 0x00000000c1a00000| PB 0x00000000c1a00000| Untracked 
| 539|0x00000000c1b00000, 0x00000000c1c00000, 0x00000000c1c00000|100%| O|Cm|TAMS 0x00000000c1b00000| PB 0x00000000c1b00000| Complete 
| 540|0x00000000c1c00000, 0x00000000c1d00000, 0x00000000c1d00000|100%| O|Cm|TAMS 0x00000000c1c00000| PB 0x00000000c1c00000| Complete 
| 541|0x00000000c1d00000, 0x00000000c1e00000, 0x00000000c1e00000|100%| O|  |TAMS 0x00000000c1d00000| PB 0x00000000c1d00000| Untracked 
| 542|0x00000000c1e00000, 0x00000000c1f00000, 0x00000000c1f00000|100%| O|  |TAMS 0x00000000c1e00000| PB 0x00000000c1e00000| Untracked 
| 543|0x00000000c1f00000, 0x00000000c2000000, 0x00000000c2000000|100%| O|Cm|TAMS 0x00000000c1f00000| PB 0x00000000c1f00000| Complete 
| 544|0x00000000c2000000, 0x00000000c2100000, 0x00000000c2100000|100%| O|  |TAMS 0x00000000c2000000| PB 0x00000000c2000000| Untracked 
| 545|0x00000000c2100000, 0x00000000c2200000, 0x00000000c2200000|100%| O|  |TAMS 0x00000000c2100000| PB 0x00000000c2100000| Untracked 
| 546|0x00000000c2200000, 0x00000000c2300000, 0x00000000c2300000|100%| O|  |TAMS 0x00000000c2200000| PB 0x00000000c2200000| Untracked 
| 547|0x00000000c2300000, 0x00000000c2400000, 0x00000000c2400000|100%| O|  |TAMS 0x00000000c2300000| PB 0x00000000c2300000| Untracked 
| 548|0x00000000c2400000, 0x00000000c2500000, 0x00000000c2500000|100%| O|  |TAMS 0x00000000c2400000| PB 0x00000000c2400000| Untracked 
| 549|0x00000000c2500000, 0x00000000c2500000, 0x00000000c2600000|  0%| F|  |TAMS 0x00000000c2500000| PB 0x00000000c2500000| Untracked 
| 550|0x00000000c2600000, 0x00000000c2600000, 0x00000000c2700000|  0%| F|  |TAMS 0x00000000c2600000| PB 0x00000000c2600000| Untracked 
| 551|0x00000000c2700000, 0x00000000c2700000, 0x00000000c2800000|  0%| F|  |TAMS 0x00000000c2700000| PB 0x00000000c2700000| Untracked 
| 552|0x00000000c2800000, 0x00000000c2900000, 0x00000000c2900000|100%| O|  |TAMS 0x00000000c2800000| PB 0x00000000c2800000| Untracked 
| 553|0x00000000c2900000, 0x00000000c2a00000, 0x00000000c2a00000|100%| O|  |TAMS 0x00000000c2900000| PB 0x00000000c2900000| Untracked 
| 554|0x00000000c2a00000, 0x00000000c2b00000, 0x00000000c2b00000|100%| O|  |TAMS 0x00000000c2a00000| PB 0x00000000c2a00000| Untracked 
| 555|0x00000000c2b00000, 0x00000000c2c00000, 0x00000000c2c00000|100%| O|  |TAMS 0x00000000c2b00000| PB 0x00000000c2b00000| Untracked 
| 556|0x00000000c2c00000, 0x00000000c2d00000, 0x00000000c2d00000|100%| O|  |TAMS 0x00000000c2c00000| PB 0x00000000c2c00000| Untracked 
| 557|0x00000000c2d00000, 0x00000000c2e00000, 0x00000000c2e00000|100%| O|  |TAMS 0x00000000c2d00000| PB 0x00000000c2d00000| Untracked 
| 558|0x00000000c2e00000, 0x00000000c2f00000, 0x00000000c2f00000|100%| O|  |TAMS 0x00000000c2e00000| PB 0x00000000c2e00000| Untracked 
| 559|0x00000000c2f00000, 0x00000000c3000000, 0x00000000c3000000|100%| O|  |TAMS 0x00000000c2f00000| PB 0x00000000c2f00000| Untracked 
| 560|0x00000000c3000000, 0x00000000c3100000, 0x00000000c3100000|100%| O|  |TAMS 0x00000000c3000000| PB 0x00000000c3000000| Untracked 
| 561|0x00000000c3100000, 0x00000000c3200000, 0x00000000c3200000|100%| O|  |TAMS 0x00000000c3100000| PB 0x00000000c3100000| Untracked 
| 562|0x00000000c3200000, 0x00000000c3300000, 0x00000000c3300000|100%| O|  |TAMS 0x00000000c3200000| PB 0x00000000c3200000| Untracked 
| 563|0x00000000c3300000, 0x00000000c3400000, 0x00000000c3400000|100%| O|  |TAMS 0x00000000c3300000| PB 0x00000000c3300000| Untracked 
| 564|0x00000000c3400000, 0x00000000c3500000, 0x00000000c3500000|100%| O|  |TAMS 0x00000000c3400000| PB 0x00000000c3400000| Untracked 
| 565|0x00000000c3500000, 0x00000000c3600000, 0x00000000c3600000|100%| O|Cm|TAMS 0x00000000c3500000| PB 0x00000000c3500000| Complete 
| 566|0x00000000c3600000, 0x00000000c3700000, 0x00000000c3700000|100%| O|Cm|TAMS 0x00000000c3600000| PB 0x00000000c3600000| Complete 
| 567|0x00000000c3700000, 0x00000000c3800000, 0x00000000c3800000|100%| O|  |TAMS 0x00000000c3700000| PB 0x00000000c3700000| Untracked 
| 568|0x00000000c3800000, 0x00000000c3900000, 0x00000000c3900000|100%| O|  |TAMS 0x00000000c3800000| PB 0x00000000c3800000| Untracked 
| 569|0x00000000c3900000, 0x00000000c3a00000, 0x00000000c3a00000|100%| O|Cm|TAMS 0x00000000c3900000| PB 0x00000000c3900000| Complete 
| 570|0x00000000c3a00000, 0x00000000c3b00000, 0x00000000c3b00000|100%| O|Cm|TAMS 0x00000000c3a00000| PB 0x00000000c3a00000| Complete 
| 571|0x00000000c3b00000, 0x00000000c3c00000, 0x00000000c3c00000|100%| O|  |TAMS 0x00000000c3b00000| PB 0x00000000c3b00000| Untracked 
| 572|0x00000000c3c00000, 0x00000000c3d00000, 0x00000000c3d00000|100%| O|  |TAMS 0x00000000c3c00000| PB 0x00000000c3c00000| Untracked 
| 573|0x00000000c3d00000, 0x00000000c3d80000, 0x00000000c3e00000| 50%| O|  |TAMS 0x00000000c3d00000| PB 0x00000000c3d00000| Untracked 
| 574|0x00000000c3e00000, 0x00000000c3f00000, 0x00000000c3f00000|100%|HS|  |TAMS 0x00000000c3e00000| PB 0x00000000c3e00000| Complete 
| 575|0x00000000c3f00000, 0x00000000c4000000, 0x00000000c4000000|100%|HS|  |TAMS 0x00000000c3f00000| PB 0x00000000c3f00000| Complete 
| 576|0x00000000c4000000, 0x00000000c4000000, 0x00000000c4100000|  0%| F|  |TAMS 0x00000000c4000000| PB 0x00000000c4000000| Untracked 
| 577|0x00000000c4100000, 0x00000000c4100000, 0x00000000c4200000|  0%| F|  |TAMS 0x00000000c4100000| PB 0x00000000c4100000| Untracked 
| 578|0x00000000c4200000, 0x00000000c4200000, 0x00000000c4300000|  0%| F|  |TAMS 0x00000000c4200000| PB 0x00000000c4200000| Untracked 
| 579|0x00000000c4300000, 0x00000000c4300000, 0x00000000c4400000|  0%| F|  |TAMS 0x00000000c4300000| PB 0x00000000c4300000| Untracked 
| 580|0x00000000c4400000, 0x00000000c4400000, 0x00000000c4500000|  0%| F|  |TAMS 0x00000000c4400000| PB 0x00000000c4400000| Untracked 
| 581|0x00000000c4500000, 0x00000000c4500000, 0x00000000c4600000|  0%| F|  |TAMS 0x00000000c4500000| PB 0x00000000c4500000| Untracked 
| 582|0x00000000c4600000, 0x00000000c4600000, 0x00000000c4700000|  0%| F|  |TAMS 0x00000000c4600000| PB 0x00000000c4600000| Untracked 
| 583|0x00000000c4700000, 0x00000000c4700000, 0x00000000c4800000|  0%| F|  |TAMS 0x00000000c4700000| PB 0x00000000c4700000| Untracked 
| 584|0x00000000c4800000, 0x00000000c4800000, 0x00000000c4900000|  0%| F|  |TAMS 0x00000000c4800000| PB 0x00000000c4800000| Untracked 
| 585|0x00000000c4900000, 0x00000000c4900000, 0x00000000c4a00000|  0%| F|  |TAMS 0x00000000c4900000| PB 0x00000000c4900000| Untracked 
| 586|0x00000000c4a00000, 0x00000000c4a00000, 0x00000000c4b00000|  0%| F|  |TAMS 0x00000000c4a00000| PB 0x00000000c4a00000| Untracked 
| 587|0x00000000c4b00000, 0x00000000c4b00000, 0x00000000c4c00000|  0%| F|  |TAMS 0x00000000c4b00000| PB 0x00000000c4b00000| Untracked 
| 588|0x00000000c4c00000, 0x00000000c4c00000, 0x00000000c4d00000|  0%| F|  |TAMS 0x00000000c4c00000| PB 0x00000000c4c00000| Untracked 
| 589|0x00000000c4d00000, 0x00000000c4d00000, 0x00000000c4e00000|  0%| F|  |TAMS 0x00000000c4d00000| PB 0x00000000c4d00000| Untracked 
| 590|0x00000000c4e00000, 0x00000000c4e00000, 0x00000000c4f00000|  0%| F|  |TAMS 0x00000000c4e00000| PB 0x00000000c4e00000| Untracked 
| 591|0x00000000c4f00000, 0x00000000c4f00000, 0x00000000c5000000|  0%| F|  |TAMS 0x00000000c4f00000| PB 0x00000000c4f00000| Untracked 
| 592|0x00000000c5000000, 0x00000000c5000000, 0x00000000c5100000|  0%| F|  |TAMS 0x00000000c5000000| PB 0x00000000c5000000| Untracked 
| 593|0x00000000c5100000, 0x00000000c5100000, 0x00000000c5200000|  0%| F|  |TAMS 0x00000000c5100000| PB 0x00000000c5100000| Untracked 
| 594|0x00000000c5200000, 0x00000000c5200000, 0x00000000c5300000|  0%| F|  |TAMS 0x00000000c5200000| PB 0x00000000c5200000| Untracked 
| 595|0x00000000c5300000, 0x00000000c5300000, 0x00000000c5400000|  0%| F|  |TAMS 0x00000000c5300000| PB 0x00000000c5300000| Untracked 
| 596|0x00000000c5400000, 0x00000000c5400000, 0x00000000c5500000|  0%| F|  |TAMS 0x00000000c5400000| PB 0x00000000c5400000| Untracked 
| 597|0x00000000c5500000, 0x00000000c5500000, 0x00000000c5600000|  0%| F|  |TAMS 0x00000000c5500000| PB 0x00000000c5500000| Untracked 
| 598|0x00000000c5600000, 0x00000000c5600000, 0x00000000c5700000|  0%| F|  |TAMS 0x00000000c5600000| PB 0x00000000c5600000| Untracked 
| 599|0x00000000c5700000, 0x00000000c5700000, 0x00000000c5800000|  0%| F|  |TAMS 0x00000000c5700000| PB 0x00000000c5700000| Untracked 
| 600|0x00000000c5800000, 0x00000000c5800000, 0x00000000c5900000|  0%| F|  |TAMS 0x00000000c5800000| PB 0x00000000c5800000| Untracked 
| 601|0x00000000c5900000, 0x00000000c5900000, 0x00000000c5a00000|  0%| F|  |TAMS 0x00000000c5900000| PB 0x00000000c5900000| Untracked 
| 602|0x00000000c5a00000, 0x00000000c5a00000, 0x00000000c5b00000|  0%| F|  |TAMS 0x00000000c5a00000| PB 0x00000000c5a00000| Untracked 
| 603|0x00000000c5b00000, 0x00000000c5b00000, 0x00000000c5c00000|  0%| F|  |TAMS 0x00000000c5b00000| PB 0x00000000c5b00000| Untracked 
| 604|0x00000000c5c00000, 0x00000000c5c00000, 0x00000000c5d00000|  0%| F|  |TAMS 0x00000000c5c00000| PB 0x00000000c5c00000| Untracked 
| 605|0x00000000c5d00000, 0x00000000c5d00000, 0x00000000c5e00000|  0%| F|  |TAMS 0x00000000c5d00000| PB 0x00000000c5d00000| Untracked 
| 606|0x00000000c5e00000, 0x00000000c5e00000, 0x00000000c5f00000|  0%| F|  |TAMS 0x00000000c5e00000| PB 0x00000000c5e00000| Untracked 
| 607|0x00000000c5f00000, 0x00000000c5f00000, 0x00000000c6000000|  0%| F|  |TAMS 0x00000000c5f00000| PB 0x00000000c5f00000| Untracked 
| 608|0x00000000c6000000, 0x00000000c6000000, 0x00000000c6100000|  0%| F|  |TAMS 0x00000000c6000000| PB 0x00000000c6000000| Untracked 
| 609|0x00000000c6100000, 0x00000000c6100000, 0x00000000c6200000|  0%| F|  |TAMS 0x00000000c6100000| PB 0x00000000c6100000| Untracked 
| 610|0x00000000c6200000, 0x00000000c6200000, 0x00000000c6300000|  0%| F|  |TAMS 0x00000000c6200000| PB 0x00000000c6200000| Untracked 
| 611|0x00000000c6300000, 0x00000000c6300000, 0x00000000c6400000|  0%| F|  |TAMS 0x00000000c6300000| PB 0x00000000c6300000| Untracked 
| 612|0x00000000c6400000, 0x00000000c6400000, 0x00000000c6500000|  0%| F|  |TAMS 0x00000000c6400000| PB 0x00000000c6400000| Untracked 
| 613|0x00000000c6500000, 0x00000000c6500000, 0x00000000c6600000|  0%| F|  |TAMS 0x00000000c6500000| PB 0x00000000c6500000| Untracked 
| 614|0x00000000c6600000, 0x00000000c6600000, 0x00000000c6700000|  0%| F|  |TAMS 0x00000000c6600000| PB 0x00000000c6600000| Untracked 
| 615|0x00000000c6700000, 0x00000000c6700000, 0x00000000c6800000|  0%| F|  |TAMS 0x00000000c6700000| PB 0x00000000c6700000| Untracked 
| 616|0x00000000c6800000, 0x00000000c6800000, 0x00000000c6900000|  0%| F|  |TAMS 0x00000000c6800000| PB 0x00000000c6800000| Untracked 
| 617|0x00000000c6900000, 0x00000000c6900000, 0x00000000c6a00000|  0%| F|  |TAMS 0x00000000c6900000| PB 0x00000000c6900000| Untracked 
| 618|0x00000000c6a00000, 0x00000000c6a00000, 0x00000000c6b00000|  0%| F|  |TAMS 0x00000000c6a00000| PB 0x00000000c6a00000| Untracked 
| 619|0x00000000c6b00000, 0x00000000c6b00000, 0x00000000c6c00000|  0%| F|  |TAMS 0x00000000c6b00000| PB 0x00000000c6b00000| Untracked 
| 620|0x00000000c6c00000, 0x00000000c6c00000, 0x00000000c6d00000|  0%| F|  |TAMS 0x00000000c6c00000| PB 0x00000000c6c00000| Untracked 
| 621|0x00000000c6d00000, 0x00000000c6d00000, 0x00000000c6e00000|  0%| F|  |TAMS 0x00000000c6d00000| PB 0x00000000c6d00000| Untracked 
| 622|0x00000000c6e00000, 0x00000000c6e00000, 0x00000000c6f00000|  0%| F|  |TAMS 0x00000000c6e00000| PB 0x00000000c6e00000| Untracked 
| 623|0x00000000c6f00000, 0x00000000c6f00000, 0x00000000c7000000|  0%| F|  |TAMS 0x00000000c6f00000| PB 0x00000000c6f00000| Untracked 
| 624|0x00000000c7000000, 0x00000000c7000000, 0x00000000c7100000|  0%| F|  |TAMS 0x00000000c7000000| PB 0x00000000c7000000| Untracked 
| 625|0x00000000c7100000, 0x00000000c7100000, 0x00000000c7200000|  0%| F|  |TAMS 0x00000000c7100000| PB 0x00000000c7100000| Untracked 
| 626|0x00000000c7200000, 0x00000000c7200000, 0x00000000c7300000|  0%| F|  |TAMS 0x00000000c7200000| PB 0x00000000c7200000| Untracked 
| 627|0x00000000c7300000, 0x00000000c7300000, 0x00000000c7400000|  0%| F|  |TAMS 0x00000000c7300000| PB 0x00000000c7300000| Untracked 
| 628|0x00000000c7400000, 0x00000000c7400000, 0x00000000c7500000|  0%| F|  |TAMS 0x00000000c7400000| PB 0x00000000c7400000| Untracked 
| 629|0x00000000c7500000, 0x00000000c7500000, 0x00000000c7600000|  0%| F|  |TAMS 0x00000000c7500000| PB 0x00000000c7500000| Untracked 
| 630|0x00000000c7600000, 0x00000000c7600000, 0x00000000c7700000|  0%| F|  |TAMS 0x00000000c7600000| PB 0x00000000c7600000| Untracked 
| 631|0x00000000c7700000, 0x00000000c7700000, 0x00000000c7800000|  0%| F|  |TAMS 0x00000000c7700000| PB 0x00000000c7700000| Untracked 
| 632|0x00000000c7800000, 0x00000000c7800000, 0x00000000c7900000|  0%| F|  |TAMS 0x00000000c7800000| PB 0x00000000c7800000| Untracked 
| 633|0x00000000c7900000, 0x00000000c7900000, 0x00000000c7a00000|  0%| F|  |TAMS 0x00000000c7900000| PB 0x00000000c7900000| Untracked 
| 634|0x00000000c7a00000, 0x00000000c7a00000, 0x00000000c7b00000|  0%| F|  |TAMS 0x00000000c7a00000| PB 0x00000000c7a00000| Untracked 
| 635|0x00000000c7b00000, 0x00000000c7b00000, 0x00000000c7c00000|  0%| F|  |TAMS 0x00000000c7b00000| PB 0x00000000c7b00000| Untracked 
| 636|0x00000000c7c00000, 0x00000000c7c00000, 0x00000000c7d00000|  0%| F|  |TAMS 0x00000000c7c00000| PB 0x00000000c7c00000| Untracked 
| 637|0x00000000c7d00000, 0x00000000c7d00000, 0x00000000c7e00000|  0%| F|  |TAMS 0x00000000c7d00000| PB 0x00000000c7d00000| Untracked 
| 638|0x00000000c7e00000, 0x00000000c7e00000, 0x00000000c7f00000|  0%| F|  |TAMS 0x00000000c7e00000| PB 0x00000000c7e00000| Untracked 
| 639|0x00000000c7f00000, 0x00000000c7f00000, 0x00000000c8000000|  0%| F|  |TAMS 0x00000000c7f00000| PB 0x00000000c7f00000| Untracked 
| 640|0x00000000c8000000, 0x00000000c8000000, 0x00000000c8100000|  0%| F|  |TAMS 0x00000000c8000000| PB 0x00000000c8000000| Untracked 
| 641|0x00000000c8100000, 0x00000000c8100000, 0x00000000c8200000|  0%| F|  |TAMS 0x00000000c8100000| PB 0x00000000c8100000| Untracked 
| 642|0x00000000c8200000, 0x00000000c8200000, 0x00000000c8300000|  0%| F|  |TAMS 0x00000000c8200000| PB 0x00000000c8200000| Untracked 
| 643|0x00000000c8300000, 0x00000000c8300000, 0x00000000c8400000|  0%| F|  |TAMS 0x00000000c8300000| PB 0x00000000c8300000| Untracked 
| 644|0x00000000c8400000, 0x00000000c8400000, 0x00000000c8500000|  0%| F|  |TAMS 0x00000000c8400000| PB 0x00000000c8400000| Untracked 
| 645|0x00000000c8500000, 0x00000000c8500000, 0x00000000c8600000|  0%| F|  |TAMS 0x00000000c8500000| PB 0x00000000c8500000| Untracked 
| 646|0x00000000c8600000, 0x00000000c8600000, 0x00000000c8700000|  0%| F|  |TAMS 0x00000000c8600000| PB 0x00000000c8600000| Untracked 
| 647|0x00000000c8700000, 0x00000000c8700000, 0x00000000c8800000|  0%| F|  |TAMS 0x00000000c8700000| PB 0x00000000c8700000| Untracked 
| 648|0x00000000c8800000, 0x00000000c8800000, 0x00000000c8900000|  0%| F|  |TAMS 0x00000000c8800000| PB 0x00000000c8800000| Untracked 
| 649|0x00000000c8900000, 0x00000000c8900000, 0x00000000c8a00000|  0%| F|  |TAMS 0x00000000c8900000| PB 0x00000000c8900000| Untracked 
| 650|0x00000000c8a00000, 0x00000000c8a00000, 0x00000000c8b00000|  0%| F|  |TAMS 0x00000000c8a00000| PB 0x00000000c8a00000| Untracked 
| 651|0x00000000c8b00000, 0x00000000c8b00000, 0x00000000c8c00000|  0%| F|  |TAMS 0x00000000c8b00000| PB 0x00000000c8b00000| Untracked 
| 652|0x00000000c8c00000, 0x00000000c8c00000, 0x00000000c8d00000|  0%| F|  |TAMS 0x00000000c8c00000| PB 0x00000000c8c00000| Untracked 
| 653|0x00000000c8d00000, 0x00000000c8d00000, 0x00000000c8e00000|  0%| F|  |TAMS 0x00000000c8d00000| PB 0x00000000c8d00000| Untracked 
| 654|0x00000000c8e00000, 0x00000000c8e00000, 0x00000000c8f00000|  0%| F|  |TAMS 0x00000000c8e00000| PB 0x00000000c8e00000| Untracked 
| 655|0x00000000c8f00000, 0x00000000c8f00000, 0x00000000c9000000|  0%| F|  |TAMS 0x00000000c8f00000| PB 0x00000000c8f00000| Untracked 
| 656|0x00000000c9000000, 0x00000000c9000000, 0x00000000c9100000|  0%| F|  |TAMS 0x00000000c9000000| PB 0x00000000c9000000| Untracked 
| 657|0x00000000c9100000, 0x00000000c9100000, 0x00000000c9200000|  0%| F|  |TAMS 0x00000000c9100000| PB 0x00000000c9100000| Untracked 
| 658|0x00000000c9200000, 0x00000000c9200000, 0x00000000c9300000|  0%| F|  |TAMS 0x00000000c9200000| PB 0x00000000c9200000| Untracked 
| 659|0x00000000c9300000, 0x00000000c9300000, 0x00000000c9400000|  0%| F|  |TAMS 0x00000000c9300000| PB 0x00000000c9300000| Untracked 
| 660|0x00000000c9400000, 0x00000000c9400000, 0x00000000c9500000|  0%| F|  |TAMS 0x00000000c9400000| PB 0x00000000c9400000| Untracked 
| 661|0x00000000c9500000, 0x00000000c9500000, 0x00000000c9600000|  0%| F|  |TAMS 0x00000000c9500000| PB 0x00000000c9500000| Untracked 
| 662|0x00000000c9600000, 0x00000000c9600000, 0x00000000c9700000|  0%| F|  |TAMS 0x00000000c9600000| PB 0x00000000c9600000| Untracked 
| 663|0x00000000c9700000, 0x00000000c9700000, 0x00000000c9800000|  0%| F|  |TAMS 0x00000000c9700000| PB 0x00000000c9700000| Untracked 
| 664|0x00000000c9800000, 0x00000000c9800000, 0x00000000c9900000|  0%| F|  |TAMS 0x00000000c9800000| PB 0x00000000c9800000| Untracked 
| 665|0x00000000c9900000, 0x00000000c9900000, 0x00000000c9a00000|  0%| F|  |TAMS 0x00000000c9900000| PB 0x00000000c9900000| Untracked 
| 666|0x00000000c9a00000, 0x00000000c9a00000, 0x00000000c9b00000|  0%| F|  |TAMS 0x00000000c9a00000| PB 0x00000000c9a00000| Untracked 
| 667|0x00000000c9b00000, 0x00000000c9b00000, 0x00000000c9c00000|  0%| F|  |TAMS 0x00000000c9b00000| PB 0x00000000c9b00000| Untracked 
| 668|0x00000000c9c00000, 0x00000000c9c00000, 0x00000000c9d00000|  0%| F|  |TAMS 0x00000000c9c00000| PB 0x00000000c9c00000| Untracked 
| 669|0x00000000c9d00000, 0x00000000c9d00000, 0x00000000c9e00000|  0%| F|  |TAMS 0x00000000c9d00000| PB 0x00000000c9d00000| Untracked 
| 670|0x00000000c9e00000, 0x00000000c9e00000, 0x00000000c9f00000|  0%| F|  |TAMS 0x00000000c9e00000| PB 0x00000000c9e00000| Untracked 
| 671|0x00000000c9f00000, 0x00000000c9f00000, 0x00000000ca000000|  0%| F|  |TAMS 0x00000000c9f00000| PB 0x00000000c9f00000| Untracked 
| 672|0x00000000ca000000, 0x00000000ca000000, 0x00000000ca100000|  0%| F|  |TAMS 0x00000000ca000000| PB 0x00000000ca000000| Untracked 
| 673|0x00000000ca100000, 0x00000000ca100000, 0x00000000ca200000|  0%| F|  |TAMS 0x00000000ca100000| PB 0x00000000ca100000| Untracked 
| 674|0x00000000ca200000, 0x00000000ca200000, 0x00000000ca300000|  0%| F|  |TAMS 0x00000000ca200000| PB 0x00000000ca200000| Untracked 
| 675|0x00000000ca300000, 0x00000000ca300000, 0x00000000ca400000|  0%| F|  |TAMS 0x00000000ca300000| PB 0x00000000ca300000| Untracked 
| 676|0x00000000ca400000, 0x00000000ca400000, 0x00000000ca500000|  0%| F|  |TAMS 0x00000000ca400000| PB 0x00000000ca400000| Untracked 
| 677|0x00000000ca500000, 0x00000000ca500000, 0x00000000ca600000|  0%| F|  |TAMS 0x00000000ca500000| PB 0x00000000ca500000| Untracked 
| 678|0x00000000ca600000, 0x00000000ca600000, 0x00000000ca700000|  0%| F|  |TAMS 0x00000000ca600000| PB 0x00000000ca600000| Untracked 
| 679|0x00000000ca700000, 0x00000000ca700000, 0x00000000ca800000|  0%| F|  |TAMS 0x00000000ca700000| PB 0x00000000ca700000| Untracked 
| 680|0x00000000ca800000, 0x00000000ca800000, 0x00000000ca900000|  0%| F|  |TAMS 0x00000000ca800000| PB 0x00000000ca800000| Untracked 
| 681|0x00000000ca900000, 0x00000000ca900000, 0x00000000caa00000|  0%| F|  |TAMS 0x00000000ca900000| PB 0x00000000ca900000| Untracked 
| 682|0x00000000caa00000, 0x00000000caa00000, 0x00000000cab00000|  0%| F|  |TAMS 0x00000000caa00000| PB 0x00000000caa00000| Untracked 
| 683|0x00000000cab00000, 0x00000000cab00000, 0x00000000cac00000|  0%| F|  |TAMS 0x00000000cab00000| PB 0x00000000cab00000| Untracked 
| 684|0x00000000cac00000, 0x00000000cac00000, 0x00000000cad00000|  0%| F|  |TAMS 0x00000000cac00000| PB 0x00000000cac00000| Untracked 
| 685|0x00000000cad00000, 0x00000000cad00000, 0x00000000cae00000|  0%| F|  |TAMS 0x00000000cad00000| PB 0x00000000cad00000| Untracked 
| 686|0x00000000cae00000, 0x00000000cae00000, 0x00000000caf00000|  0%| F|  |TAMS 0x00000000cae00000| PB 0x00000000cae00000| Untracked 
| 687|0x00000000caf00000, 0x00000000caf00000, 0x00000000cb000000|  0%| F|  |TAMS 0x00000000caf00000| PB 0x00000000caf00000| Untracked 
| 688|0x00000000cb000000, 0x00000000cb000000, 0x00000000cb100000|  0%| F|  |TAMS 0x00000000cb000000| PB 0x00000000cb000000| Untracked 
| 689|0x00000000cb100000, 0x00000000cb100000, 0x00000000cb200000|  0%| F|  |TAMS 0x00000000cb100000| PB 0x00000000cb100000| Untracked 
| 690|0x00000000cb200000, 0x00000000cb200000, 0x00000000cb300000|  0%| F|  |TAMS 0x00000000cb200000| PB 0x00000000cb200000| Untracked 
| 691|0x00000000cb300000, 0x00000000cb300000, 0x00000000cb400000|  0%| F|  |TAMS 0x00000000cb300000| PB 0x00000000cb300000| Untracked 
| 692|0x00000000cb400000, 0x00000000cb400000, 0x00000000cb500000|  0%| F|  |TAMS 0x00000000cb400000| PB 0x00000000cb400000| Untracked 
| 693|0x00000000cb500000, 0x00000000cb500000, 0x00000000cb600000|  0%| F|  |TAMS 0x00000000cb500000| PB 0x00000000cb500000| Untracked 
| 694|0x00000000cb600000, 0x00000000cb600000, 0x00000000cb700000|  0%| F|  |TAMS 0x00000000cb600000| PB 0x00000000cb600000| Untracked 
| 695|0x00000000cb700000, 0x00000000cb700000, 0x00000000cb800000|  0%| F|  |TAMS 0x00000000cb700000| PB 0x00000000cb700000| Untracked 
| 696|0x00000000cb800000, 0x00000000cb800000, 0x00000000cb900000|  0%| F|  |TAMS 0x00000000cb800000| PB 0x00000000cb800000| Untracked 
| 697|0x00000000cb900000, 0x00000000cb900000, 0x00000000cba00000|  0%| F|  |TAMS 0x00000000cb900000| PB 0x00000000cb900000| Untracked 
| 698|0x00000000cba00000, 0x00000000cba00000, 0x00000000cbb00000|  0%| F|  |TAMS 0x00000000cba00000| PB 0x00000000cba00000| Untracked 
| 699|0x00000000cbb00000, 0x00000000cbb00000, 0x00000000cbc00000|  0%| F|  |TAMS 0x00000000cbb00000| PB 0x00000000cbb00000| Untracked 
| 700|0x00000000cbc00000, 0x00000000cbc00000, 0x00000000cbd00000|  0%| F|  |TAMS 0x00000000cbc00000| PB 0x00000000cbc00000| Untracked 
| 701|0x00000000cbd00000, 0x00000000cbd00000, 0x00000000cbe00000|  0%| F|  |TAMS 0x00000000cbd00000| PB 0x00000000cbd00000| Untracked 
| 702|0x00000000cbe00000, 0x00000000cbe00000, 0x00000000cbf00000|  0%| F|  |TAMS 0x00000000cbe00000| PB 0x00000000cbe00000| Untracked 
| 703|0x00000000cbf00000, 0x00000000cbf00000, 0x00000000cc000000|  0%| F|  |TAMS 0x00000000cbf00000| PB 0x00000000cbf00000| Untracked 
| 704|0x00000000cc000000, 0x00000000cc000000, 0x00000000cc100000|  0%| F|  |TAMS 0x00000000cc000000| PB 0x00000000cc000000| Untracked 
| 705|0x00000000cc100000, 0x00000000cc100000, 0x00000000cc200000|  0%| F|  |TAMS 0x00000000cc100000| PB 0x00000000cc100000| Untracked 
| 706|0x00000000cc200000, 0x00000000cc200000, 0x00000000cc300000|  0%| F|  |TAMS 0x00000000cc200000| PB 0x00000000cc200000| Untracked 
| 707|0x00000000cc300000, 0x00000000cc300000, 0x00000000cc400000|  0%| F|  |TAMS 0x00000000cc300000| PB 0x00000000cc300000| Untracked 
| 708|0x00000000cc400000, 0x00000000cc400000, 0x00000000cc500000|  0%| F|  |TAMS 0x00000000cc400000| PB 0x00000000cc400000| Untracked 
| 709|0x00000000cc500000, 0x00000000cc500000, 0x00000000cc600000|  0%| F|  |TAMS 0x00000000cc500000| PB 0x00000000cc500000| Untracked 
| 710|0x00000000cc600000, 0x00000000cc600000, 0x00000000cc700000|  0%| F|  |TAMS 0x00000000cc600000| PB 0x00000000cc600000| Untracked 
| 711|0x00000000cc700000, 0x00000000cc700000, 0x00000000cc800000|  0%| F|  |TAMS 0x00000000cc700000| PB 0x00000000cc700000| Untracked 
| 712|0x00000000cc800000, 0x00000000cc800000, 0x00000000cc900000|  0%| F|  |TAMS 0x00000000cc800000| PB 0x00000000cc800000| Untracked 
| 713|0x00000000cc900000, 0x00000000cc900000, 0x00000000cca00000|  0%| F|  |TAMS 0x00000000cc900000| PB 0x00000000cc900000| Untracked 
| 714|0x00000000cca00000, 0x00000000cca00000, 0x00000000ccb00000|  0%| F|  |TAMS 0x00000000cca00000| PB 0x00000000cca00000| Untracked 
| 715|0x00000000ccb00000, 0x00000000ccb00000, 0x00000000ccc00000|  0%| F|  |TAMS 0x00000000ccb00000| PB 0x00000000ccb00000| Untracked 
| 716|0x00000000ccc00000, 0x00000000ccc00000, 0x00000000ccd00000|  0%| F|  |TAMS 0x00000000ccc00000| PB 0x00000000ccc00000| Untracked 
| 717|0x00000000ccd00000, 0x00000000ccd00000, 0x00000000cce00000|  0%| F|  |TAMS 0x00000000ccd00000| PB 0x00000000ccd00000| Untracked 
| 718|0x00000000cce00000, 0x00000000cce00000, 0x00000000ccf00000|  0%| F|  |TAMS 0x00000000cce00000| PB 0x00000000cce00000| Untracked 
| 719|0x00000000ccf00000, 0x00000000ccf00000, 0x00000000cd000000|  0%| F|  |TAMS 0x00000000ccf00000| PB 0x00000000ccf00000| Untracked 
| 720|0x00000000cd000000, 0x00000000cd000000, 0x00000000cd100000|  0%| F|  |TAMS 0x00000000cd000000| PB 0x00000000cd000000| Untracked 
| 721|0x00000000cd100000, 0x00000000cd100000, 0x00000000cd200000|  0%| F|  |TAMS 0x00000000cd100000| PB 0x00000000cd100000| Untracked 
| 722|0x00000000cd200000, 0x00000000cd200000, 0x00000000cd300000|  0%| F|  |TAMS 0x00000000cd200000| PB 0x00000000cd200000| Untracked 
| 723|0x00000000cd300000, 0x00000000cd300000, 0x00000000cd400000|  0%| F|  |TAMS 0x00000000cd300000| PB 0x00000000cd300000| Untracked 
| 724|0x00000000cd400000, 0x00000000cd400000, 0x00000000cd500000|  0%| F|  |TAMS 0x00000000cd400000| PB 0x00000000cd400000| Untracked 
| 725|0x00000000cd500000, 0x00000000cd500000, 0x00000000cd600000|  0%| F|  |TAMS 0x00000000cd500000| PB 0x00000000cd500000| Untracked 
| 726|0x00000000cd600000, 0x00000000cd600000, 0x00000000cd700000|  0%| F|  |TAMS 0x00000000cd600000| PB 0x00000000cd600000| Untracked 
| 727|0x00000000cd700000, 0x00000000cd700000, 0x00000000cd800000|  0%| F|  |TAMS 0x00000000cd700000| PB 0x00000000cd700000| Untracked 
| 728|0x00000000cd800000, 0x00000000cd800000, 0x00000000cd900000|  0%| F|  |TAMS 0x00000000cd800000| PB 0x00000000cd800000| Untracked 
| 729|0x00000000cd900000, 0x00000000cd900000, 0x00000000cda00000|  0%| F|  |TAMS 0x00000000cd900000| PB 0x00000000cd900000| Untracked 
| 730|0x00000000cda00000, 0x00000000cda80000, 0x00000000cdb00000| 50%| E|  |TAMS 0x00000000cda00000| PB 0x00000000cda00000| Complete 
| 731|0x00000000cdb00000, 0x00000000cdc00000, 0x00000000cdc00000|100%| E|CS|TAMS 0x00000000cdb00000| PB 0x00000000cdb00000| Complete 
| 732|0x00000000cdc00000, 0x00000000cdd00000, 0x00000000cdd00000|100%| E|CS|TAMS 0x00000000cdc00000| PB 0x00000000cdc00000| Complete 
| 733|0x00000000cdd00000, 0x00000000cde00000, 0x00000000cde00000|100%| E|CS|TAMS 0x00000000cdd00000| PB 0x00000000cdd00000| Complete 
| 734|0x00000000cde00000, 0x00000000cdf00000, 0x00000000cdf00000|100%| E|CS|TAMS 0x00000000cde00000| PB 0x00000000cde00000| Complete 
| 735|0x00000000cdf00000, 0x00000000ce000000, 0x00000000ce000000|100%| E|CS|TAMS 0x00000000cdf00000| PB 0x00000000cdf00000| Complete 
| 736|0x00000000ce000000, 0x00000000ce100000, 0x00000000ce100000|100%| E|CS|TAMS 0x00000000ce000000| PB 0x00000000ce000000| Complete 
| 737|0x00000000ce100000, 0x00000000ce200000, 0x00000000ce200000|100%| E|CS|TAMS 0x00000000ce100000| PB 0x00000000ce100000| Complete 
| 738|0x00000000ce200000, 0x00000000ce300000, 0x00000000ce300000|100%| E|CS|TAMS 0x00000000ce200000| PB 0x00000000ce200000| Complete 
| 739|0x00000000ce300000, 0x00000000ce400000, 0x00000000ce400000|100%| E|CS|TAMS 0x00000000ce300000| PB 0x00000000ce300000| Complete 
| 740|0x00000000ce400000, 0x00000000ce500000, 0x00000000ce500000|100%| E|CS|TAMS 0x00000000ce400000| PB 0x00000000ce400000| Complete 
| 741|0x00000000ce500000, 0x00000000ce600000, 0x00000000ce600000|100%| E|  |TAMS 0x00000000ce500000| PB 0x00000000ce500000| Complete 
| 742|0x00000000ce600000, 0x00000000ce700000, 0x00000000ce700000|100%| E|CS|TAMS 0x00000000ce600000| PB 0x00000000ce600000| Complete 
| 743|0x00000000ce700000, 0x00000000ce800000, 0x00000000ce800000|100%| E|CS|TAMS 0x00000000ce700000| PB 0x00000000ce700000| Complete 
| 744|0x00000000ce800000, 0x00000000ce900000, 0x00000000ce900000|100%| E|CS|TAMS 0x00000000ce800000| PB 0x00000000ce800000| Complete 
| 745|0x00000000ce900000, 0x00000000cea00000, 0x00000000cea00000|100%| E|CS|TAMS 0x00000000ce900000| PB 0x00000000ce900000| Complete 
| 746|0x00000000cea00000, 0x00000000ceb00000, 0x00000000ceb00000|100%| E|CS|TAMS 0x00000000cea00000| PB 0x00000000cea00000| Complete 
| 747|0x00000000ceb00000, 0x00000000cec00000, 0x00000000cec00000|100%| E|CS|TAMS 0x00000000ceb00000| PB 0x00000000ceb00000| Complete 
| 748|0x00000000cec00000, 0x00000000ced00000, 0x00000000ced00000|100%| E|CS|TAMS 0x00000000cec00000| PB 0x00000000cec00000| Complete 
| 749|0x00000000ced00000, 0x00000000cee00000, 0x00000000cee00000|100%| E|CS|TAMS 0x00000000ced00000| PB 0x00000000ced00000| Complete 
| 750|0x00000000cee00000, 0x00000000cef00000, 0x00000000cef00000|100%| E|CS|TAMS 0x00000000cee00000| PB 0x00000000cee00000| Complete 
| 751|0x00000000cef00000, 0x00000000cf000000, 0x00000000cf000000|100%| E|CS|TAMS 0x00000000cef00000| PB 0x00000000cef00000| Complete 
| 752|0x00000000cf000000, 0x00000000cf100000, 0x00000000cf100000|100%| E|CS|TAMS 0x00000000cf000000| PB 0x00000000cf000000| Complete 
| 753|0x00000000cf100000, 0x00000000cf200000, 0x00000000cf200000|100%| E|CS|TAMS 0x00000000cf100000| PB 0x00000000cf100000| Complete 
| 754|0x00000000cf200000, 0x00000000cf300000, 0x00000000cf300000|100%| E|CS|TAMS 0x00000000cf200000| PB 0x00000000cf200000| Complete 
| 755|0x00000000cf300000, 0x00000000cf400000, 0x00000000cf400000|100%| E|CS|TAMS 0x00000000cf300000| PB 0x00000000cf300000| Complete 
| 756|0x00000000cf400000, 0x00000000cf500000, 0x00000000cf500000|100%| E|CS|TAMS 0x00000000cf400000| PB 0x00000000cf400000| Complete 
| 757|0x00000000cf500000, 0x00000000cf600000, 0x00000000cf600000|100%| E|CS|TAMS 0x00000000cf500000| PB 0x00000000cf500000| Complete 
| 758|0x00000000cf600000, 0x00000000cf700000, 0x00000000cf700000|100%| E|CS|TAMS 0x00000000cf600000| PB 0x00000000cf600000| Complete 
| 759|0x00000000cf700000, 0x00000000cf800000, 0x00000000cf800000|100%| E|CS|TAMS 0x00000000cf700000| PB 0x00000000cf700000| Complete 
| 760|0x00000000cf800000, 0x00000000cf900000, 0x00000000cf900000|100%| E|CS|TAMS 0x00000000cf800000| PB 0x00000000cf800000| Complete 
| 761|0x00000000cf900000, 0x00000000cfa00000, 0x00000000cfa00000|100%| E|CS|TAMS 0x00000000cf900000| PB 0x00000000cf900000| Complete 
| 762|0x00000000cfa00000, 0x00000000cfb00000, 0x00000000cfb00000|100%| E|CS|TAMS 0x00000000cfa00000| PB 0x00000000cfa00000| Complete 
| 763|0x00000000cfb00000, 0x00000000cfc00000, 0x00000000cfc00000|100%| E|CS|TAMS 0x00000000cfb00000| PB 0x00000000cfb00000| Complete 
| 764|0x00000000cfc00000, 0x00000000cfd00000, 0x00000000cfd00000|100%| E|CS|TAMS 0x00000000cfc00000| PB 0x00000000cfc00000| Complete 
| 765|0x00000000cfd00000, 0x00000000cfe00000, 0x00000000cfe00000|100%| E|CS|TAMS 0x00000000cfd00000| PB 0x00000000cfd00000| Complete 
| 766|0x00000000cfe00000, 0x00000000cff00000, 0x00000000cff00000|100%| E|CS|TAMS 0x00000000cfe00000| PB 0x00000000cfe00000| Complete 
| 767|0x00000000cff00000, 0x00000000d0000000, 0x00000000d0000000|100%| E|CS|TAMS 0x00000000cff00000| PB 0x00000000cff00000| Complete 
| 768|0x00000000d0000000, 0x00000000d0100000, 0x00000000d0100000|100%| E|CS|TAMS 0x00000000d0000000| PB 0x00000000d0000000| Complete 
| 769|0x00000000d0100000, 0x00000000d0200000, 0x00000000d0200000|100%| E|CS|TAMS 0x00000000d0100000| PB 0x00000000d0100000| Complete 
| 770|0x00000000d0200000, 0x00000000d0300000, 0x00000000d0300000|100%| E|CS|TAMS 0x00000000d0200000| PB 0x00000000d0200000| Complete 
| 771|0x00000000d0300000, 0x00000000d0400000, 0x00000000d0400000|100%| E|CS|TAMS 0x00000000d0300000| PB 0x00000000d0300000| Complete 
| 772|0x00000000d0400000, 0x00000000d0500000, 0x00000000d0500000|100%| E|CS|TAMS 0x00000000d0400000| PB 0x00000000d0400000| Complete 
| 773|0x00000000d0500000, 0x00000000d0600000, 0x00000000d0600000|100%| E|CS|TAMS 0x00000000d0500000| PB 0x00000000d0500000| Complete 
| 774|0x00000000d0600000, 0x00000000d0700000, 0x00000000d0700000|100%| E|CS|TAMS 0x00000000d0600000| PB 0x00000000d0600000| Complete 
| 775|0x00000000d0700000, 0x00000000d0800000, 0x00000000d0800000|100%| E|CS|TAMS 0x00000000d0700000| PB 0x00000000d0700000| Complete 
| 776|0x00000000d0800000, 0x00000000d0900000, 0x00000000d0900000|100%| E|CS|TAMS 0x00000000d0800000| PB 0x00000000d0800000| Complete 
| 777|0x00000000d0900000, 0x00000000d0a00000, 0x00000000d0a00000|100%| E|CS|TAMS 0x00000000d0900000| PB 0x00000000d0900000| Complete 
| 778|0x00000000d0a00000, 0x00000000d0b00000, 0x00000000d0b00000|100%| E|CS|TAMS 0x00000000d0a00000| PB 0x00000000d0a00000| Complete 
| 779|0x00000000d0b00000, 0x00000000d0c00000, 0x00000000d0c00000|100%| E|CS|TAMS 0x00000000d0b00000| PB 0x00000000d0b00000| Complete 
| 780|0x00000000d0c00000, 0x00000000d0d00000, 0x00000000d0d00000|100%| E|CS|TAMS 0x00000000d0c00000| PB 0x00000000d0c00000| Complete 
| 781|0x00000000d0d00000, 0x00000000d0e00000, 0x00000000d0e00000|100%| E|CS|TAMS 0x00000000d0d00000| PB 0x00000000d0d00000| Complete 
| 782|0x00000000d0e00000, 0x00000000d0f00000, 0x00000000d0f00000|100%| E|CS|TAMS 0x00000000d0e00000| PB 0x00000000d0e00000| Complete 
| 783|0x00000000d0f00000, 0x00000000d1000000, 0x00000000d1000000|100%| E|CS|TAMS 0x00000000d0f00000| PB 0x00000000d0f00000| Complete 
| 784|0x00000000d1000000, 0x00000000d1100000, 0x00000000d1100000|100%| E|CS|TAMS 0x00000000d1000000| PB 0x00000000d1000000| Complete 
| 785|0x00000000d1100000, 0x00000000d1200000, 0x00000000d1200000|100%| E|CS|TAMS 0x00000000d1100000| PB 0x00000000d1100000| Complete 
| 786|0x00000000d1200000, 0x00000000d1300000, 0x00000000d1300000|100%| E|CS|TAMS 0x00000000d1200000| PB 0x00000000d1200000| Complete 
| 787|0x00000000d1300000, 0x00000000d1400000, 0x00000000d1400000|100%| E|CS|TAMS 0x00000000d1300000| PB 0x00000000d1300000| Complete 
| 788|0x00000000d1400000, 0x00000000d1500000, 0x00000000d1500000|100%| E|CS|TAMS 0x00000000d1400000| PB 0x00000000d1400000| Complete 
| 789|0x00000000d1500000, 0x00000000d1600000, 0x00000000d1600000|100%| E|CS|TAMS 0x00000000d1500000| PB 0x00000000d1500000| Complete 
| 790|0x00000000d1600000, 0x00000000d1700000, 0x00000000d1700000|100%| E|CS|TAMS 0x00000000d1600000| PB 0x00000000d1600000| Complete 
| 791|0x00000000d1700000, 0x00000000d1800000, 0x00000000d1800000|100%| E|CS|TAMS 0x00000000d1700000| PB 0x00000000d1700000| Complete 
| 792|0x00000000d1800000, 0x00000000d1900000, 0x00000000d1900000|100%| E|CS|TAMS 0x00000000d1800000| PB 0x00000000d1800000| Complete 
| 793|0x00000000d1900000, 0x00000000d1a00000, 0x00000000d1a00000|100%| E|CS|TAMS 0x00000000d1900000| PB 0x00000000d1900000| Complete 
| 794|0x00000000d1a00000, 0x00000000d1b00000, 0x00000000d1b00000|100%| E|CS|TAMS 0x00000000d1a00000| PB 0x00000000d1a00000| Complete 
| 795|0x00000000d1b00000, 0x00000000d1c00000, 0x00000000d1c00000|100%| E|CS|TAMS 0x00000000d1b00000| PB 0x00000000d1b00000| Complete 
| 796|0x00000000d1c00000, 0x00000000d1d00000, 0x00000000d1d00000|100%| E|CS|TAMS 0x00000000d1c00000| PB 0x00000000d1c00000| Complete 
| 797|0x00000000d1d00000, 0x00000000d1e00000, 0x00000000d1e00000|100%| E|CS|TAMS 0x00000000d1d00000| PB 0x00000000d1d00000| Complete 
| 798|0x00000000d1e00000, 0x00000000d1f00000, 0x00000000d1f00000|100%| E|CS|TAMS 0x00000000d1e00000| PB 0x00000000d1e00000| Complete 
| 799|0x00000000d1f00000, 0x00000000d2000000, 0x00000000d2000000|100%| E|CS|TAMS 0x00000000d1f00000| PB 0x00000000d1f00000| Complete 
| 800|0x00000000d2000000, 0x00000000d2100000, 0x00000000d2100000|100%| E|CS|TAMS 0x00000000d2000000| PB 0x00000000d2000000| Complete 
| 801|0x00000000d2100000, 0x00000000d2200000, 0x00000000d2200000|100%| E|CS|TAMS 0x00000000d2100000| PB 0x00000000d2100000| Complete 
| 802|0x00000000d2200000, 0x00000000d2300000, 0x00000000d2300000|100%| E|CS|TAMS 0x00000000d2200000| PB 0x00000000d2200000| Complete 
| 803|0x00000000d2300000, 0x00000000d2400000, 0x00000000d2400000|100%| E|CS|TAMS 0x00000000d2300000| PB 0x00000000d2300000| Complete 
| 804|0x00000000d2400000, 0x00000000d2500000, 0x00000000d2500000|100%| E|CS|TAMS 0x00000000d2400000| PB 0x00000000d2400000| Complete 
| 805|0x00000000d2500000, 0x00000000d2600000, 0x00000000d2600000|100%| E|CS|TAMS 0x00000000d2500000| PB 0x00000000d2500000| Complete 
| 806|0x00000000d2600000, 0x00000000d2700000, 0x00000000d2700000|100%| E|CS|TAMS 0x00000000d2600000| PB 0x00000000d2600000| Complete 
| 807|0x00000000d2700000, 0x00000000d2800000, 0x00000000d2800000|100%| E|CS|TAMS 0x00000000d2700000| PB 0x00000000d2700000| Complete 
| 808|0x00000000d2800000, 0x00000000d2900000, 0x00000000d2900000|100%| E|CS|TAMS 0x00000000d2800000| PB 0x00000000d2800000| Complete 
| 809|0x00000000d2900000, 0x00000000d2a00000, 0x00000000d2a00000|100%| E|CS|TAMS 0x00000000d2900000| PB 0x00000000d2900000| Complete 
| 810|0x00000000d2a00000, 0x00000000d2b00000, 0x00000000d2b00000|100%| E|CS|TAMS 0x00000000d2a00000| PB 0x00000000d2a00000| Complete 
| 811|0x00000000d2b00000, 0x00000000d2c00000, 0x00000000d2c00000|100%| E|CS|TAMS 0x00000000d2b00000| PB 0x00000000d2b00000| Complete 
| 812|0x00000000d2c00000, 0x00000000d2d00000, 0x00000000d2d00000|100%| E|CS|TAMS 0x00000000d2c00000| PB 0x00000000d2c00000| Complete 
| 813|0x00000000d2d00000, 0x00000000d2e00000, 0x00000000d2e00000|100%| E|CS|TAMS 0x00000000d2d00000| PB 0x00000000d2d00000| Complete 
| 814|0x00000000d2e00000, 0x00000000d2f00000, 0x00000000d2f00000|100%| E|CS|TAMS 0x00000000d2e00000| PB 0x00000000d2e00000| Complete 
| 815|0x00000000d2f00000, 0x00000000d3000000, 0x00000000d3000000|100%| E|CS|TAMS 0x00000000d2f00000| PB 0x00000000d2f00000| Complete 
| 816|0x00000000d3000000, 0x00000000d3100000, 0x00000000d3100000|100%| E|CS|TAMS 0x00000000d3000000| PB 0x00000000d3000000| Complete 
| 817|0x00000000d3100000, 0x00000000d3200000, 0x00000000d3200000|100%| E|CS|TAMS 0x00000000d3100000| PB 0x00000000d3100000| Complete 
| 818|0x00000000d3200000, 0x00000000d3300000, 0x00000000d3300000|100%| E|CS|TAMS 0x00000000d3200000| PB 0x00000000d3200000| Complete 
| 819|0x00000000d3300000, 0x00000000d3400000, 0x00000000d3400000|100%| E|CS|TAMS 0x00000000d3300000| PB 0x00000000d3300000| Complete 
| 820|0x00000000d3400000, 0x00000000d3500000, 0x00000000d3500000|100%| E|CS|TAMS 0x00000000d3400000| PB 0x00000000d3400000| Complete 
| 821|0x00000000d3500000, 0x00000000d3600000, 0x00000000d3600000|100%| E|CS|TAMS 0x00000000d3500000| PB 0x00000000d3500000| Complete 
| 822|0x00000000d3600000, 0x00000000d3700000, 0x00000000d3700000|100%| E|CS|TAMS 0x00000000d3600000| PB 0x00000000d3600000| Complete 
| 823|0x00000000d3700000, 0x00000000d3800000, 0x00000000d3800000|100%| E|CS|TAMS 0x00000000d3700000| PB 0x00000000d3700000| Complete 
| 824|0x00000000d3800000, 0x00000000d3900000, 0x00000000d3900000|100%| E|CS|TAMS 0x00000000d3800000| PB 0x00000000d3800000| Complete 
| 825|0x00000000d3900000, 0x00000000d3a00000, 0x00000000d3a00000|100%| E|CS|TAMS 0x00000000d3900000| PB 0x00000000d3900000| Complete 
| 826|0x00000000d3a00000, 0x00000000d3b00000, 0x00000000d3b00000|100%| E|CS|TAMS 0x00000000d3a00000| PB 0x00000000d3a00000| Complete 
| 827|0x00000000d3b00000, 0x00000000d3c00000, 0x00000000d3c00000|100%| E|CS|TAMS 0x00000000d3b00000| PB 0x00000000d3b00000| Complete 
| 828|0x00000000d3c00000, 0x00000000d3d00000, 0x00000000d3d00000|100%| E|CS|TAMS 0x00000000d3c00000| PB 0x00000000d3c00000| Complete 
| 829|0x00000000d3d00000, 0x00000000d3e00000, 0x00000000d3e00000|100%| E|CS|TAMS 0x00000000d3d00000| PB 0x00000000d3d00000| Complete 
| 830|0x00000000d3e00000, 0x00000000d3f00000, 0x00000000d3f00000|100%| E|CS|TAMS 0x00000000d3e00000| PB 0x00000000d3e00000| Complete 
| 831|0x00000000d3f00000, 0x00000000d4000000, 0x00000000d4000000|100%| E|CS|TAMS 0x00000000d3f00000| PB 0x00000000d3f00000| Complete 
| 832|0x00000000d4000000, 0x00000000d4100000, 0x00000000d4100000|100%| E|CS|TAMS 0x00000000d4000000| PB 0x00000000d4000000| Complete 
| 833|0x00000000d4100000, 0x00000000d4200000, 0x00000000d4200000|100%| E|CS|TAMS 0x00000000d4100000| PB 0x00000000d4100000| Complete 
| 834|0x00000000d4200000, 0x00000000d4300000, 0x00000000d4300000|100%| E|CS|TAMS 0x00000000d4200000| PB 0x00000000d4200000| Complete 
| 835|0x00000000d4300000, 0x00000000d4400000, 0x00000000d4400000|100%| E|CS|TAMS 0x00000000d4300000| PB 0x00000000d4300000| Complete 
| 836|0x00000000d4400000, 0x00000000d4500000, 0x00000000d4500000|100%| E|CS|TAMS 0x00000000d4400000| PB 0x00000000d4400000| Complete 
| 837|0x00000000d4500000, 0x00000000d4600000, 0x00000000d4600000|100%| E|CS|TAMS 0x00000000d4500000| PB 0x00000000d4500000| Complete 
| 838|0x00000000d4600000, 0x00000000d4700000, 0x00000000d4700000|100%| E|CS|TAMS 0x00000000d4600000| PB 0x00000000d4600000| Complete 
| 839|0x00000000d4700000, 0x00000000d4800000, 0x00000000d4800000|100%| E|CS|TAMS 0x00000000d4700000| PB 0x00000000d4700000| Complete 
| 840|0x00000000d4800000, 0x00000000d4900000, 0x00000000d4900000|100%| E|CS|TAMS 0x00000000d4800000| PB 0x00000000d4800000| Complete 
| 841|0x00000000d4900000, 0x00000000d4a00000, 0x00000000d4a00000|100%| E|CS|TAMS 0x00000000d4900000| PB 0x00000000d4900000| Complete 
| 842|0x00000000d4a00000, 0x00000000d4b00000, 0x00000000d4b00000|100%| E|CS|TAMS 0x00000000d4a00000| PB 0x00000000d4a00000| Complete 
| 843|0x00000000d4b00000, 0x00000000d4c00000, 0x00000000d4c00000|100%| E|CS|TAMS 0x00000000d4b00000| PB 0x00000000d4b00000| Complete 
| 844|0x00000000d4c00000, 0x00000000d4d00000, 0x00000000d4d00000|100%| E|CS|TAMS 0x00000000d4c00000| PB 0x00000000d4c00000| Complete 
| 845|0x00000000d4d00000, 0x00000000d4e00000, 0x00000000d4e00000|100%| E|CS|TAMS 0x00000000d4d00000| PB 0x00000000d4d00000| Complete 
| 846|0x00000000d4e00000, 0x00000000d4f00000, 0x00000000d4f00000|100%| E|CS|TAMS 0x00000000d4e00000| PB 0x00000000d4e00000| Complete 
| 847|0x00000000d4f00000, 0x00000000d5000000, 0x00000000d5000000|100%| E|CS|TAMS 0x00000000d4f00000| PB 0x00000000d4f00000| Complete 
| 848|0x00000000d5000000, 0x00000000d5100000, 0x00000000d5100000|100%| E|CS|TAMS 0x00000000d5000000| PB 0x00000000d5000000| Complete 
| 849|0x00000000d5100000, 0x00000000d5200000, 0x00000000d5200000|100%| E|CS|TAMS 0x00000000d5100000| PB 0x00000000d5100000| Complete 
| 850|0x00000000d5200000, 0x00000000d5300000, 0x00000000d5300000|100%| E|CS|TAMS 0x00000000d5200000| PB 0x00000000d5200000| Complete 
| 851|0x00000000d5300000, 0x00000000d5400000, 0x00000000d5400000|100%| E|CS|TAMS 0x00000000d5300000| PB 0x00000000d5300000| Complete 
| 852|0x00000000d5400000, 0x00000000d5500000, 0x00000000d5500000|100%| E|CS|TAMS 0x00000000d5400000| PB 0x00000000d5400000| Complete 
| 853|0x00000000d5500000, 0x00000000d5600000, 0x00000000d5600000|100%| E|CS|TAMS 0x00000000d5500000| PB 0x00000000d5500000| Complete 
| 854|0x00000000d5600000, 0x00000000d5700000, 0x00000000d5700000|100%| E|CS|TAMS 0x00000000d5600000| PB 0x00000000d5600000| Complete 
| 855|0x00000000d5700000, 0x00000000d5800000, 0x00000000d5800000|100%| E|CS|TAMS 0x00000000d5700000| PB 0x00000000d5700000| Complete 
| 856|0x00000000d5800000, 0x00000000d5900000, 0x00000000d5900000|100%| E|CS|TAMS 0x00000000d5800000| PB 0x00000000d5800000| Complete 
| 857|0x00000000d5900000, 0x00000000d5a00000, 0x00000000d5a00000|100%| E|CS|TAMS 0x00000000d5900000| PB 0x00000000d5900000| Complete 
| 858|0x00000000d5a00000, 0x00000000d5b00000, 0x00000000d5b00000|100%| E|CS|TAMS 0x00000000d5a00000| PB 0x00000000d5a00000| Complete 
| 859|0x00000000d5b00000, 0x00000000d5c00000, 0x00000000d5c00000|100%| E|CS|TAMS 0x00000000d5b00000| PB 0x00000000d5b00000| Complete 
| 860|0x00000000d5c00000, 0x00000000d5d00000, 0x00000000d5d00000|100%| E|CS|TAMS 0x00000000d5c00000| PB 0x00000000d5c00000| Complete 
| 861|0x00000000d5d00000, 0x00000000d5e00000, 0x00000000d5e00000|100%| E|CS|TAMS 0x00000000d5d00000| PB 0x00000000d5d00000| Complete 
| 862|0x00000000d5e00000, 0x00000000d5f00000, 0x00000000d5f00000|100%| E|CS|TAMS 0x00000000d5e00000| PB 0x00000000d5e00000| Complete 
| 863|0x00000000d5f00000, 0x00000000d6000000, 0x00000000d6000000|100%| E|CS|TAMS 0x00000000d5f00000| PB 0x00000000d5f00000| Complete 
| 864|0x00000000d6000000, 0x00000000d6100000, 0x00000000d6100000|100%| E|CS|TAMS 0x00000000d6000000| PB 0x00000000d6000000| Complete 
| 865|0x00000000d6100000, 0x00000000d6200000, 0x00000000d6200000|100%| E|CS|TAMS 0x00000000d6100000| PB 0x00000000d6100000| Complete 
| 866|0x00000000d6200000, 0x00000000d6300000, 0x00000000d6300000|100%| E|CS|TAMS 0x00000000d6200000| PB 0x00000000d6200000| Complete 
| 867|0x00000000d6300000, 0x00000000d6400000, 0x00000000d6400000|100%| E|CS|TAMS 0x00000000d6300000| PB 0x00000000d6300000| Complete 
| 868|0x00000000d6400000, 0x00000000d6500000, 0x00000000d6500000|100%| E|CS|TAMS 0x00000000d6400000| PB 0x00000000d6400000| Complete 
| 869|0x00000000d6500000, 0x00000000d6600000, 0x00000000d6600000|100%| E|CS|TAMS 0x00000000d6500000| PB 0x00000000d6500000| Complete 
| 870|0x00000000d6600000, 0x00000000d6700000, 0x00000000d6700000|100%| E|CS|TAMS 0x00000000d6600000| PB 0x00000000d6600000| Complete 
| 871|0x00000000d6700000, 0x00000000d6800000, 0x00000000d6800000|100%| E|CS|TAMS 0x00000000d6700000| PB 0x00000000d6700000| Complete 
| 872|0x00000000d6800000, 0x00000000d6900000, 0x00000000d6900000|100%| E|CS|TAMS 0x00000000d6800000| PB 0x00000000d6800000| Complete 
| 873|0x00000000d6900000, 0x00000000d6a00000, 0x00000000d6a00000|100%| E|CS|TAMS 0x00000000d6900000| PB 0x00000000d6900000| Complete 
| 874|0x00000000d6a00000, 0x00000000d6b00000, 0x00000000d6b00000|100%| E|CS|TAMS 0x00000000d6a00000| PB 0x00000000d6a00000| Complete 
| 875|0x00000000d6b00000, 0x00000000d6c00000, 0x00000000d6c00000|100%| E|CS|TAMS 0x00000000d6b00000| PB 0x00000000d6b00000| Complete 
| 876|0x00000000d6c00000, 0x00000000d6d00000, 0x00000000d6d00000|100%| E|CS|TAMS 0x00000000d6c00000| PB 0x00000000d6c00000| Complete 
| 877|0x00000000d6d00000, 0x00000000d6e00000, 0x00000000d6e00000|100%| E|CS|TAMS 0x00000000d6d00000| PB 0x00000000d6d00000| Complete 
| 878|0x00000000d6e00000, 0x00000000d6f00000, 0x00000000d6f00000|100%| E|CS|TAMS 0x00000000d6e00000| PB 0x00000000d6e00000| Complete 
| 879|0x00000000d6f00000, 0x00000000d7000000, 0x00000000d7000000|100%| E|CS|TAMS 0x00000000d6f00000| PB 0x00000000d6f00000| Complete 
| 880|0x00000000d7000000, 0x00000000d7100000, 0x00000000d7100000|100%| E|CS|TAMS 0x00000000d7000000| PB 0x00000000d7000000| Complete 
| 881|0x00000000d7100000, 0x00000000d7200000, 0x00000000d7200000|100%| E|CS|TAMS 0x00000000d7100000| PB 0x00000000d7100000| Complete 
| 882|0x00000000d7200000, 0x00000000d7300000, 0x00000000d7300000|100%| E|CS|TAMS 0x00000000d7200000| PB 0x00000000d7200000| Complete 
| 883|0x00000000d7300000, 0x00000000d7400000, 0x00000000d7400000|100%| E|CS|TAMS 0x00000000d7300000| PB 0x00000000d7300000| Complete 
| 884|0x00000000d7400000, 0x00000000d7500000, 0x00000000d7500000|100%| E|CS|TAMS 0x00000000d7400000| PB 0x00000000d7400000| Complete 
| 885|0x00000000d7500000, 0x00000000d7600000, 0x00000000d7600000|100%| E|CS|TAMS 0x00000000d7500000| PB 0x00000000d7500000| Complete 
| 886|0x00000000d7600000, 0x00000000d7700000, 0x00000000d7700000|100%| E|CS|TAMS 0x00000000d7600000| PB 0x00000000d7600000| Complete 
| 887|0x00000000d7700000, 0x00000000d7800000, 0x00000000d7800000|100%| E|CS|TAMS 0x00000000d7700000| PB 0x00000000d7700000| Complete 
| 888|0x00000000d7800000, 0x00000000d7900000, 0x00000000d7900000|100%| E|CS|TAMS 0x00000000d7800000| PB 0x00000000d7800000| Complete 
| 889|0x00000000d7900000, 0x00000000d7a00000, 0x00000000d7a00000|100%| E|CS|TAMS 0x00000000d7900000| PB 0x00000000d7900000| Complete 
| 890|0x00000000d7a00000, 0x00000000d7b00000, 0x00000000d7b00000|100%| E|CS|TAMS 0x00000000d7a00000| PB 0x00000000d7a00000| Complete 
| 891|0x00000000d7b00000, 0x00000000d7c00000, 0x00000000d7c00000|100%| E|CS|TAMS 0x00000000d7b00000| PB 0x00000000d7b00000| Complete 
| 892|0x00000000d7c00000, 0x00000000d7d00000, 0x00000000d7d00000|100%| E|CS|TAMS 0x00000000d7c00000| PB 0x00000000d7c00000| Complete 
| 893|0x00000000d7d00000, 0x00000000d7d80000, 0x00000000d7e00000| 50%| S|CS|TAMS 0x00000000d7d00000| PB 0x00000000d7d00000| Complete 
| 894|0x00000000d7e00000, 0x00000000d7f00000, 0x00000000d7f00000|100%| S|CS|TAMS 0x00000000d7e00000| PB 0x00000000d7e00000| Complete 
| 895|0x00000000d7f00000, 0x00000000d8000000, 0x00000000d8000000|100%| S|CS|TAMS 0x00000000d7f00000| PB 0x00000000d7f00000| Complete 
| 896|0x00000000d8000000, 0x00000000d8100000, 0x00000000d8100000|100%| S|CS|TAMS 0x00000000d8000000| PB 0x00000000d8000000| Complete 
| 897|0x00000000d8100000, 0x00000000d8200000, 0x00000000d8200000|100%| S|CS|TAMS 0x00000000d8100000| PB 0x00000000d8100000| Complete 
| 898|0x00000000d8200000, 0x00000000d8300000, 0x00000000d8300000|100%| S|CS|TAMS 0x00000000d8200000| PB 0x00000000d8200000| Complete 
| 899|0x00000000d8300000, 0x00000000d8400000, 0x00000000d8400000|100%| S|CS|TAMS 0x00000000d8300000| PB 0x00000000d8300000| Complete 
| 900|0x00000000d8400000, 0x00000000d8500000, 0x00000000d8500000|100%| S|CS|TAMS 0x00000000d8400000| PB 0x00000000d8400000| Complete 
| 901|0x00000000d8500000, 0x00000000d8600000, 0x00000000d8600000|100%| S|CS|TAMS 0x00000000d8500000| PB 0x00000000d8500000| Complete 
| 902|0x00000000d8600000, 0x00000000d8700000, 0x00000000d8700000|100%| S|CS|TAMS 0x00000000d8600000| PB 0x00000000d8600000| Complete 
| 903|0x00000000d8700000, 0x00000000d8800000, 0x00000000d8800000|100%| S|CS|TAMS 0x00000000d8700000| PB 0x00000000d8700000| Complete 
| 904|0x00000000d8800000, 0x00000000d8900000, 0x00000000d8900000|100%| S|CS|TAMS 0x00000000d8800000| PB 0x00000000d8800000| Complete 
| 905|0x00000000d8900000, 0x00000000d8a00000, 0x00000000d8a00000|100%| S|CS|TAMS 0x00000000d8900000| PB 0x00000000d8900000| Complete 
| 906|0x00000000d8a00000, 0x00000000d8b00000, 0x00000000d8b00000|100%| S|CS|TAMS 0x00000000d8a00000| PB 0x00000000d8a00000| Complete 
| 907|0x00000000d8b00000, 0x00000000d8c00000, 0x00000000d8c00000|100%| E|CS|TAMS 0x00000000d8b00000| PB 0x00000000d8b00000| Complete 
| 908|0x00000000d8c00000, 0x00000000d8d00000, 0x00000000d8d00000|100%| E|CS|TAMS 0x00000000d8c00000| PB 0x00000000d8c00000| Complete 
| 909|0x00000000d8d00000, 0x00000000d8e00000, 0x00000000d8e00000|100%| E|CS|TAMS 0x00000000d8d00000| PB 0x00000000d8d00000| Complete 
| 910|0x00000000d8e00000, 0x00000000d8f00000, 0x00000000d8f00000|100%| E|CS|TAMS 0x00000000d8e00000| PB 0x00000000d8e00000| Complete 
| 911|0x00000000d8f00000, 0x00000000d9000000, 0x00000000d9000000|100%| E|CS|TAMS 0x00000000d8f00000| PB 0x00000000d8f00000| Complete 
| 912|0x00000000d9000000, 0x00000000d9100000, 0x00000000d9100000|100%| E|CS|TAMS 0x00000000d9000000| PB 0x00000000d9000000| Complete 
| 913|0x00000000d9100000, 0x00000000d9200000, 0x00000000d9200000|100%| E|CS|TAMS 0x00000000d9100000| PB 0x00000000d9100000| Complete 
| 914|0x00000000d9200000, 0x00000000d9300000, 0x00000000d9300000|100%| E|CS|TAMS 0x00000000d9200000| PB 0x00000000d9200000| Complete 
| 915|0x00000000d9300000, 0x00000000d9400000, 0x00000000d9400000|100%| E|CS|TAMS 0x00000000d9300000| PB 0x00000000d9300000| Complete 
| 916|0x00000000d9400000, 0x00000000d9500000, 0x00000000d9500000|100%| E|CS|TAMS 0x00000000d9400000| PB 0x00000000d9400000| Complete 
| 917|0x00000000d9500000, 0x00000000d9600000, 0x00000000d9600000|100%| E|CS|TAMS 0x00000000d9500000| PB 0x00000000d9500000| Complete 
| 918|0x00000000d9600000, 0x00000000d9700000, 0x00000000d9700000|100%| E|CS|TAMS 0x00000000d9600000| PB 0x00000000d9600000| Complete 
| 919|0x00000000d9700000, 0x00000000d9800000, 0x00000000d9800000|100%| E|CS|TAMS 0x00000000d9700000| PB 0x00000000d9700000| Complete 
| 920|0x00000000d9800000, 0x00000000d9900000, 0x00000000d9900000|100%| E|CS|TAMS 0x00000000d9800000| PB 0x00000000d9800000| Complete 
| 921|0x00000000d9900000, 0x00000000d9a00000, 0x00000000d9a00000|100%| E|CS|TAMS 0x00000000d9900000| PB 0x00000000d9900000| Complete 
| 922|0x00000000d9a00000, 0x00000000d9b00000, 0x00000000d9b00000|100%| E|CS|TAMS 0x00000000d9a00000| PB 0x00000000d9a00000| Complete 
| 923|0x00000000d9b00000, 0x00000000d9c00000, 0x00000000d9c00000|100%| E|CS|TAMS 0x00000000d9b00000| PB 0x00000000d9b00000| Complete 
| 924|0x00000000d9c00000, 0x00000000d9d00000, 0x00000000d9d00000|100%| E|CS|TAMS 0x00000000d9c00000| PB 0x00000000d9c00000| Complete 
|1489|0x00000000fd100000, 0x00000000fd200000, 0x00000000fd200000|100%| E|CS|TAMS 0x00000000fd100000| PB 0x00000000fd100000| Complete 
|1490|0x00000000fd200000, 0x00000000fd300000, 0x00000000fd300000|100%| E|CS|TAMS 0x00000000fd200000| PB 0x00000000fd200000| Complete 
|1491|0x00000000fd300000, 0x00000000fd400000, 0x00000000fd400000|100%| O|  |TAMS 0x00000000fd300000| PB 0x00000000fd300000| Untracked 
|1492|0x00000000fd400000, 0x00000000fd500000, 0x00000000fd500000|100%| O|  |TAMS 0x00000000fd400000| PB 0x00000000fd400000| Untracked 
|1493|0x00000000fd500000, 0x00000000fd600000, 0x00000000fd600000|100%| O|  |TAMS 0x00000000fd500000| PB 0x00000000fd500000| Untracked 
|1494|0x00000000fd600000, 0x00000000fd700000, 0x00000000fd700000|100%| E|CS|TAMS 0x00000000fd600000| PB 0x00000000fd600000| Complete 
|1495|0x00000000fd700000, 0x00000000fd800000, 0x00000000fd800000|100%| E|CS|TAMS 0x00000000fd700000| PB 0x00000000fd700000| Complete 
|1496|0x00000000fd800000, 0x00000000fd900000, 0x00000000fd900000|100%| O|Cm|TAMS 0x00000000fd800000| PB 0x00000000fd800000| Complete 
|1497|0x00000000fd900000, 0x00000000fda00000, 0x00000000fda00000|100%| E|CS|TAMS 0x00000000fd900000| PB 0x00000000fd900000| Complete 
|1498|0x00000000fda00000, 0x00000000fdb00000, 0x00000000fdb00000|100%| E|CS|TAMS 0x00000000fda00000| PB 0x00000000fda00000| Complete 
|1499|0x00000000fdb00000, 0x00000000fdc00000, 0x00000000fdc00000|100%| E|CS|TAMS 0x00000000fdb00000| PB 0x00000000fdb00000| Complete 
|1500|0x00000000fdc00000, 0x00000000fdd00000, 0x00000000fdd00000|100%| E|CS|TAMS 0x00000000fdc00000| PB 0x00000000fdc00000| Complete 
|1501|0x00000000fdd00000, 0x00000000fde00000, 0x00000000fde00000|100%| E|CS|TAMS 0x00000000fdd00000| PB 0x00000000fdd00000| Complete 
|1502|0x00000000fde00000, 0x00000000fdf00000, 0x00000000fdf00000|100%| E|CS|TAMS 0x00000000fde00000| PB 0x00000000fde00000| Complete 
|1503|0x00000000fdf00000, 0x00000000fe000000, 0x00000000fe000000|100%| O|  |TAMS 0x00000000fdf00000| PB 0x00000000fdf00000| Untracked 
|1504|0x00000000fe000000, 0x00000000fe100000, 0x00000000fe100000|100%| O|  |TAMS 0x00000000fe000000| PB 0x00000000fe000000| Untracked 
|1505|0x00000000fe100000, 0x00000000fe200000, 0x00000000fe200000|100%| O|  |TAMS 0x00000000fe100000| PB 0x00000000fe100000| Untracked 
|1506|0x00000000fe200000, 0x00000000fe300000, 0x00000000fe300000|100%| O|  |TAMS 0x00000000fe200000| PB 0x00000000fe200000| Untracked 
|1507|0x00000000fe300000, 0x00000000fe400000, 0x00000000fe400000|100%| O|  |TAMS 0x00000000fe300000| PB 0x00000000fe300000| Untracked 
|1508|0x00000000fe400000, 0x00000000fe500000, 0x00000000fe500000|100%| O|  |TAMS 0x00000000fe400000| PB 0x00000000fe400000| Untracked 
|1509|0x00000000fe500000, 0x00000000fe600000, 0x00000000fe600000|100%| O|  |TAMS 0x00000000fe500000| PB 0x00000000fe500000| Untracked 
|1510|0x00000000fe600000, 0x00000000fe700000, 0x00000000fe700000|100%| E|CS|TAMS 0x00000000fe600000| PB 0x00000000fe600000| Complete 
|1511|0x00000000fe700000, 0x00000000fe800000, 0x00000000fe800000|100%| O|  |TAMS 0x00000000fe700000| PB 0x00000000fe700000| Untracked 
|1512|0x00000000fe800000, 0x00000000fe900000, 0x00000000fe900000|100%| E|CS|TAMS 0x00000000fe800000| PB 0x00000000fe800000| Complete 
|1513|0x00000000fe900000, 0x00000000fea00000, 0x00000000fea00000|100%| O|  |TAMS 0x00000000fe900000| PB 0x00000000fe900000| Untracked 
|1514|0x00000000fea00000, 0x00000000feb00000, 0x00000000feb00000|100%| O|  |TAMS 0x00000000fea00000| PB 0x00000000fea00000| Untracked 
|1515|0x00000000feb00000, 0x00000000fec00000, 0x00000000fec00000|100%| E|CS|TAMS 0x00000000feb00000| PB 0x00000000feb00000| Complete 
|1516|0x00000000fec00000, 0x00000000fed00000, 0x00000000fed00000|100%| E|CS|TAMS 0x00000000fec00000| PB 0x00000000fec00000| Complete 
|1517|0x00000000fed00000, 0x00000000fee00000, 0x00000000fee00000|100%| O|Cm|TAMS 0x00000000fed00000| PB 0x00000000fed00000| Complete 
|1518|0x00000000fee00000, 0x00000000fef00000, 0x00000000fef00000|100%| O|  |TAMS 0x00000000fee00000| PB 0x00000000fee00000| Untracked 
|1519|0x00000000fef00000, 0x00000000ff000000, 0x00000000ff000000|100%| O|  |TAMS 0x00000000fef00000| PB 0x00000000fef00000| Untracked 
|1520|0x00000000ff000000, 0x00000000ff100000, 0x00000000ff100000|100%| O|  |TAMS 0x00000000ff000000| PB 0x00000000ff000000| Untracked 
|1521|0x00000000ff100000, 0x00000000ff200000, 0x00000000ff200000|100%| O|  |TAMS 0x00000000ff100000| PB 0x00000000ff100000| Untracked 
|1522|0x00000000ff200000, 0x00000000ff300000, 0x00000000ff300000|100%| O|  |TAMS 0x00000000ff200000| PB 0x00000000ff200000| Untracked 
|1523|0x00000000ff300000, 0x00000000ff400000, 0x00000000ff400000|100%| O|  |TAMS 0x00000000ff300000| PB 0x00000000ff300000| Untracked 
|1524|0x00000000ff400000, 0x00000000ff500000, 0x00000000ff500000|100%| O|  |TAMS 0x00000000ff400000| PB 0x00000000ff400000| Untracked 
|1525|0x00000000ff500000, 0x00000000ff600000, 0x00000000ff600000|100%| O|  |TAMS 0x00000000ff500000| PB 0x00000000ff500000| Untracked 
|1526|0x00000000ff600000, 0x00000000ff700000, 0x00000000ff700000|100%| O|  |TAMS 0x00000000ff600000| PB 0x00000000ff600000| Untracked 
|1527|0x00000000ff700000, 0x00000000ff800000, 0x00000000ff800000|100%| O|  |TAMS 0x00000000ff700000| PB 0x00000000ff700000| Untracked 
|1528|0x00000000ff800000, 0x00000000ff900000, 0x00000000ff900000|100%| O|  |TAMS 0x00000000ff800000| PB 0x00000000ff800000| Untracked 
|1529|0x00000000ff900000, 0x00000000ffa00000, 0x00000000ffa00000|100%| O|  |TAMS 0x00000000ff900000| PB 0x00000000ff900000| Untracked 
|1530|0x00000000ffa00000, 0x00000000ffb00000, 0x00000000ffb00000|100%| E|CS|TAMS 0x00000000ffa00000| PB 0x00000000ffa00000| Complete 
|1531|0x00000000ffb00000, 0x00000000ffc00000, 0x00000000ffc00000|100%| E|CS|TAMS 0x00000000ffb00000| PB 0x00000000ffb00000| Complete 
|1532|0x00000000ffc00000, 0x00000000ffd00000, 0x00000000ffd00000|100%| O|Cm|TAMS 0x00000000ffc00000| PB 0x00000000ffc00000| Complete 
|1533|0x00000000ffd00000, 0x00000000ffe00000, 0x00000000ffe00000|100%| E|CS|TAMS 0x00000000ffd00000| PB 0x00000000ffd00000| Complete 
|1534|0x00000000ffe00000, 0x00000000fff00000, 0x00000000fff00000|100%| E|CS|TAMS 0x00000000ffe00000| PB 0x00000000ffe00000| Complete 
|1535|0x00000000fff00000, 0x0000000100000000, 0x0000000100000000|100%| E|CS|TAMS 0x00000000fff00000| PB 0x00000000fff00000| Complete 

Card table byte_map: [0x0000017550fb0000,0x00000175512b0000] _byte_map_base: 0x0000017550ab0000

Marking Bits: (CMBitMap*) 0x000001753eb9d090
 Bits: [0x00000175512b0000, 0x0000017552ab0000)

Polling page: 0x000001753cad0000

Metaspace:

Usage:
  Non-class:    139.61 MB used.
      Class:     21.85 MB used.
       Both:    161.46 MB used.

Virtual space:
  Non-class space:      192.00 MB reserved,     141.19 MB ( 74%) committed,  3 nodes.
      Class space:      416.00 MB reserved,      23.31 MB (  6%) committed,  1 nodes.
             Both:      608.00 MB reserved,     164.50 MB ( 27%) committed. 

Chunk freelists:
   Non-Class:  2.76 MB
       Class:  8.70 MB
        Both:  11.46 MB

MaxMetaspaceSize: 512.00 MB
CompressedClassSpaceSize: 416.00 MB
Initial GC threshold: 21.00 MB
Current GC threshold: 274.00 MB
CDS: off
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 9.
num_arena_births: 5176.
num_arena_deaths: 0.
num_vsnodes_births: 4.
num_vsnodes_deaths: 0.
num_space_committed: 2631.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 9.
num_chunks_taken_from_freelist: 12704.
num_chunk_merges: 9.
num_chunk_splits: 8227.
num_chunks_enlarged: 5220.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=18086Kb max_used=18691Kb free=101913Kb
 bounds [0x0000017549780000, 0x000001754a9d0000, 0x0000017550cb0000]
CodeHeap 'profiled nmethods': size=120000Kb used=41563Kb max_used=43736Kb free=78436Kb
 bounds [0x0000017541cb0000, 0x0000017544770000, 0x00000175491e0000]
CodeHeap 'non-nmethods': size=5760Kb used=3011Kb max_used=3088Kb free=2748Kb
 bounds [0x00000175491e0000, 0x0000017549500000, 0x0000017549780000]
 total_blobs=22291 nmethods=21206 adapters=988
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 97.051 Thread 0x0000017555267c70 28511       4       com.android.tools.r8.internal.jW::a (120 bytes)
Event: 97.052 Thread 0x0000017555267c70 nmethod 28511 0x0000017549b0d310 code [0x0000017549b0d4a0, 0x0000017549b0d528]
Event: 97.052 Thread 0x0000017555267c70 28506       4       com.android.tools.r8.internal.Aa::q (2 bytes)
Event: 97.052 Thread 0x0000017555267c70 nmethod 28506 0x0000017549b0f210 code [0x0000017549b0f3a0, 0x0000017549b0f428]
Event: 97.052 Thread 0x0000017555267c70 28366       4       com.android.tools.r8.internal.e6::getOrDefault (12 bytes)
Event: 97.054 Thread 0x0000017555268310 28514       3       com.android.tools.r8.dex.n0$$Lambda/0x0000000101726770::accept (12 bytes)
Event: 97.054 Thread 0x0000017555268310 nmethod 28514 0x00000175424f6710 code [0x00000175424f68c0, 0x00000175424f6ae8]
Event: 97.055 Thread 0x0000017555267c70 nmethod 28366 0x0000017549995910 code [0x0000017549995ae0, 0x0000017549995dc0]
Event: 97.055 Thread 0x0000017555267c70 28512       4       com.android.tools.r8.internal.Jv::<init> (18 bytes)
Event: 97.058 Thread 0x0000017555267c70 nmethod 28512 0x0000017549829810 code [0x00000175498299a0, 0x0000017549829dd8]
Event: 97.058 Thread 0x0000017555267c70 28513       4       com.android.tools.r8.graph.B2::b (61 bytes)
Event: 97.060 Thread 0x0000017555268310 28517       2       com.android.tools.r8.internal.xm::a (1 bytes)
Event: 97.060 Thread 0x0000017555268310 nmethod 28517 0x0000017542b7bd90 code [0x0000017542b7bf20, 0x0000017542b7c028]
Event: 97.064 Thread 0x0000017555268310 28518       3       com.android.tools.r8.dex.n0$$Lambda/0x0000000101724670::apply (15 bytes)
Event: 97.064 Thread 0x0000017555268310 nmethod 28518 0x00000175424c1d10 code [0x00000175424c1ee0, 0x00000175424c22d0]
Event: 97.065 Thread 0x0000017555268310 28521       3       com.android.tools.r8.dex.n0$$Lambda/0x0000000101724b20::apply (15 bytes)
Event: 97.065 Thread 0x0000017555268310 nmethod 28521 0x00000175426ec610 code [0x00000175426ec7e0, 0x00000175426ecad8]
Event: 97.070 Thread 0x0000017555268310 28523       3       com.android.tools.r8.dex.s0::b (28 bytes)
Event: 97.070 Thread 0x0000017555268310 nmethod 28523 0x00000175426edf10 code [0x00000175426ee0e0, 0x00000175426ee3d8]
Event: 97.072 Thread 0x0000017555268310 28524       2       com.android.tools.r8.internal.iq::a (1 bytes)

GC Heap History (20 events):
Event: 82.932 GC heap before
{Heap before GC invocations=97 (full 0):
 garbage-first heap   total 540672K, used 532341K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 142 young (145408K), 19 survivors (19456K)
 Metaspace       used 151037K, committed 154048K, reserved 622592K
  class space    used 20483K, committed 21888K, reserved 425984K
}
Event: 83.042 GC heap after
{Heap after GC invocations=98 (full 0):
 garbage-first heap   total 589824K, used 412160K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 18 young (18432K), 18 survivors (18432K)
 Metaspace       used 151037K, committed 154048K, reserved 622592K
  class space    used 20483K, committed 21888K, reserved 425984K
}
Event: 83.502 GC heap before
{Heap before GC invocations=98 (full 0):
 garbage-first heap   total 589824K, used 451072K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 58 young (59392K), 18 survivors (18432K)
 Metaspace       used 151803K, committed 154816K, reserved 622592K
  class space    used 20599K, committed 22016K, reserved 425984K
}
Event: 83.534 GC heap after
{Heap after GC invocations=99 (full 0):
 garbage-first heap   total 589824K, used 423936K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 11 young (11264K), 11 survivors (11264K)
 Metaspace       used 151803K, committed 154816K, reserved 622592K
  class space    used 20599K, committed 22016K, reserved 425984K
}
Event: 84.607 GC heap before
{Heap before GC invocations=100 (full 0):
 garbage-first heap   total 715776K, used 532480K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 113 young (115712K), 11 survivors (11264K)
 Metaspace       used 152886K, committed 155904K, reserved 622592K
  class space    used 20746K, committed 22144K, reserved 425984K
}
Event: 84.662 GC heap after
{Heap after GC invocations=101 (full 0):
 garbage-first heap   total 715776K, used 456192K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 15 young (15360K), 15 survivors (15360K)
 Metaspace       used 152886K, committed 155904K, reserved 622592K
  class space    used 20746K, committed 22144K, reserved 425984K
}
Event: 86.669 GC heap before
{Heap before GC invocations=101 (full 0):
 garbage-first heap   total 715776K, used 613888K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 170 young (174080K), 15 survivors (15360K)
 Metaspace       used 156422K, committed 159488K, reserved 622592K
  class space    used 21355K, committed 22784K, reserved 425984K
}
Event: 86.717 GC heap after
{Heap after GC invocations=102 (full 0):
 garbage-first heap   total 715776K, used 479744K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 22 young (22528K), 22 survivors (22528K)
 Metaspace       used 156422K, committed 159488K, reserved 622592K
  class space    used 21355K, committed 22784K, reserved 425984K
}
Event: 88.354 GC heap before
{Heap before GC invocations=102 (full 0):
 garbage-first heap   total 715776K, used 628224K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 166 young (169984K), 22 survivors (22528K)
 Metaspace       used 157778K, committed 160896K, reserved 622592K
  class space    used 21563K, committed 23040K, reserved 425984K
}
Event: 88.434 GC heap after
{Heap after GC invocations=103 (full 0):
 garbage-first heap   total 720896K, used 542720K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 21 young (21504K), 21 survivors (21504K)
 Metaspace       used 157778K, committed 160896K, reserved 622592K
  class space    used 21563K, committed 23040K, reserved 425984K
}
Event: 88.875 GC heap before
{Heap before GC invocations=103 (full 0):
 garbage-first heap   total 720896K, used 641024K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 118 young (120832K), 21 survivors (21504K)
 Metaspace       used 158529K, committed 161664K, reserved 622592K
  class space    used 21659K, committed 23168K, reserved 425984K
}
Event: 88.940 GC heap after
{Heap after GC invocations=104 (full 0):
 garbage-first heap   total 720896K, used 592384K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 15 young (15360K), 15 survivors (15360K)
 Metaspace       used 158529K, committed 161664K, reserved 622592K
  class space    used 21659K, committed 23168K, reserved 425984K
}
Event: 89.857 GC heap before
{Heap before GC invocations=104 (full 0):
 garbage-first heap   total 985088K, used 653824K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 78 young (79872K), 15 survivors (15360K)
 Metaspace       used 159773K, committed 162816K, reserved 622592K
  class space    used 21843K, committed 23296K, reserved 425984K
}
Event: 89.903 GC heap after
{Heap after GC invocations=105 (full 0):
 garbage-first heap   total 985088K, used 619264K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 10 young (10240K), 10 survivors (10240K)
 Metaspace       used 159773K, committed 162816K, reserved 622592K
  class space    used 21843K, committed 23296K, reserved 425984K
}
Event: 93.240 GC heap before
{Heap before GC invocations=106 (full 0):
 garbage-first heap   total 985088K, used 813824K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 199 young (203776K), 10 survivors (10240K)
 Metaspace       used 163582K, committed 166656K, reserved 622592K
  class space    used 22152K, committed 23616K, reserved 425984K
}
Event: 93.283 GC heap after
{Heap after GC invocations=107 (full 0):
 garbage-first heap   total 985088K, used 634112K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 23 young (23552K), 23 survivors (23552K)
 Metaspace       used 163582K, committed 166656K, reserved 622592K
  class space    used 22152K, committed 23616K, reserved 425984K
}
Event: 94.732 GC heap before
{Heap before GC invocations=107 (full 0):
 garbage-first heap   total 985088K, used 858368K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 241 young (246784K), 23 survivors (23552K)
 Metaspace       used 164838K, committed 167936K, reserved 622592K
  class space    used 22308K, committed 23808K, reserved 425984K
}
Event: 94.771 GC heap after
{Heap after GC invocations=108 (full 0):
 garbage-first heap   total 985088K, used 641024K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 21 young (21504K), 21 survivors (21504K)
 Metaspace       used 164838K, committed 167936K, reserved 622592K
  class space    used 22308K, committed 23808K, reserved 425984K
}
Event: 94.854 GC heap before
{Heap before GC invocations=108 (full 0):
 garbage-first heap   total 985088K, used 654336K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 36 young (36864K), 21 survivors (21504K)
 Metaspace       used 164867K, committed 167936K, reserved 622592K
  class space    used 22311K, committed 23808K, reserved 425984K
}
Event: 94.872 GC heap after
{Heap after GC invocations=109 (full 0):
 garbage-first heap   total 985088K, used 642048K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 14 young (14336K), 14 survivors (14336K)
 Metaspace       used 164867K, committed 167936K, reserved 622592K
  class space    used 22311K, committed 23808K, reserved 425984K
}

Dll operation events (3 events):
Event: 0.007 Loaded shared library C:\Program Files\Android\Android Studio\jbr\bin\java.dll
Event: 0.013 Loaded shared library C:\Program Files\Android\Android Studio\jbr\bin\zip.dll
Event: 0.656 Loaded shared library C:\Program Files\Android\Android Studio\jbr\bin\verify.dll

Deoptimization events (20 events):
Event: 96.922 Thread 0x000001755eb7d460 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000017549977238 relative=0x0000000000000938
Event: 96.922 Thread 0x000001755eb7d460 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000017549977238 method=com.android.tools.r8.synthesis.I.a(Ljava/util/function/Function;Lcom/android/tools/r8/graph/N2;)Lcom/android/tools/r8/graph/b0; @ 15 c2
Event: 96.922 Thread 0x000001755eb7d460 DEOPT PACKING pc=0x0000017549977238 sp=0x0000005f908f8ed0
Event: 96.922 Thread 0x000001755eb7d460 DEOPT UNPACKING pc=0x00000175492346a2 sp=0x0000005f908f8e60 mode 2
Event: 96.933 Thread 0x000001755eb7d460 DEOPT PACKING pc=0x000001754410197d sp=0x0000005f908f9590
Event: 96.933 Thread 0x000001755eb7d460 DEOPT UNPACKING pc=0x0000017549234e42 sp=0x0000005f908f8c90 mode 0
Event: 96.936 Thread 0x000001755eb7d460 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000017549a875bc relative=0x000000000000011c
Event: 96.936 Thread 0x000001755eb7d460 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000017549a875bc method=com.android.tools.r8.graph.M2.b([B)Z @ 19 c2
Event: 96.936 Thread 0x000001755eb7d460 DEOPT PACKING pc=0x0000017549a875bc sp=0x0000005f908f9c30
Event: 96.936 Thread 0x000001755eb7d460 DEOPT UNPACKING pc=0x00000175492346a2 sp=0x0000005f908f9bc0 mode 2
Event: 96.939 Thread 0x000001755eb7d460 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000017549977238 relative=0x0000000000000938
Event: 96.939 Thread 0x000001755eb7d460 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000017549977238 method=com.android.tools.r8.synthesis.I.a(Ljava/util/function/Function;Lcom/android/tools/r8/graph/N2;)Lcom/android/tools/r8/graph/b0; @ 15 c2
Event: 96.939 Thread 0x000001755eb7d460 DEOPT PACKING pc=0x0000017549977238 sp=0x0000005f908f8ed0
Event: 96.939 Thread 0x000001755eb7d460 DEOPT UNPACKING pc=0x00000175492346a2 sp=0x0000005f908f8e60 mode 2
Event: 96.943 Thread 0x000001755eb7d460 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000001754a8fdeec relative=0x000000000000026c
Event: 96.943 Thread 0x000001755eb7d460 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001754a8fdeec method=com.android.tools.r8.internal.AG.contains(Ljava/lang/Object;)Z @ 122 c2
Event: 96.943 Thread 0x000001755eb7d460 DEOPT PACKING pc=0x000001754a8fdeec sp=0x0000005f908f9f10
Event: 96.943 Thread 0x000001755eb7d460 DEOPT UNPACKING pc=0x00000175492346a2 sp=0x0000005f908f9e88 mode 2
Event: 97.014 Thread 0x000001755eb7d460 DEOPT PACKING pc=0x000001754410197d sp=0x0000005f908f9590
Event: 97.014 Thread 0x000001755eb7d460 DEOPT UNPACKING pc=0x0000017549234e42 sp=0x0000005f908f8c90 mode 0

Classes loaded (20 events):
Event: 89.629 Loading class java/util/stream/SliceOps
Event: 89.629 Loading class java/util/stream/SliceOps done
Event: 89.629 Loading class java/util/stream/SliceOps$1
Event: 89.630 Loading class java/util/stream/SliceOps$1 done
Event: 89.664 Loading class java/util/stream/SliceOps$1$1
Event: 89.665 Loading class java/util/stream/SliceOps$1$1 done
Event: 92.192 Loading class java/nio/file/Files$2
Event: 92.194 Loading class java/nio/file/Files$2 done
Event: 92.204 Loading class java/nio/file/StandardCopyOption
Event: 92.205 Loading class java/nio/file/StandardCopyOption done
Event: 92.205 Loading class sun/nio/fs/WindowsFileCopy
Event: 92.206 Loading class sun/nio/fs/WindowsFileCopy done
Event: 94.668 Loading class java/nio/ByteBufferAsShortBufferL
Event: 94.670 Loading class java/nio/ByteBufferAsShortBufferL done
Event: 95.028 Loading class java/util/zip/Adler32
Event: 95.041 Loading class java/util/zip/Adler32 done
Event: 95.066 Loading class java/nio/file/Path$1
Event: 95.067 Loading class java/nio/file/Path$1 done
Event: 95.096 Loading class sun/nio/ch/ChannelOutputStream
Event: 95.100 Loading class sun/nio/ch/ChannelOutputStream done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 95.139 Thread 0x000001755eb79950 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d78debe0}> (0x00000000d78debe0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 95.141 Thread 0x000001755eb79950 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d78e6c80}> (0x00000000d78e6c80) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 95.144 Thread 0x000001755eb79950 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d78ef000}> (0x00000000d78ef000) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 95.160 Thread 0x000001755eb79950 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d78f8fb8}> (0x00000000d78f8fb8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 95.163 Thread 0x000001755eb79950 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d76c51c8}> (0x00000000d76c51c8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 95.167 Thread 0x000001755eb79950 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d76d0e88}> (0x00000000d76d0e88) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 95.170 Thread 0x000001755eb79950 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d76d7a88}> (0x00000000d76d7a88) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 95.253 Thread 0x000001755eb79950 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d76df328}> (0x00000000d76df328) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 95.256 Thread 0x000001755eb79950 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d76eb8c8}> (0x00000000d76eb8c8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 95.259 Thread 0x000001755eb79950 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d76f5a20}> (0x00000000d76f5a20) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 95.261 Thread 0x000001755eb79950 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d76fe538}> (0x00000000d76fe538) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 95.275 Thread 0x000001755eb79950 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d6ec91b0}> (0x00000000d6ec91b0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 96.035 Thread 0x000001755f42e040 Exception <a 'sun/nio/fs/WindowsException'{0x00000000fdd85f30}> (0x00000000fdd85f30) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 96.377 Thread 0x000001755f42e040 Exception <a 'sun/nio/fs/WindowsException'{0x00000000fdd90760}> (0x00000000fdd90760) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 96.607 Thread 0x000001755f42e040 Exception <a 'sun/nio/fs/WindowsException'{0x00000000fdd92ed8}> (0x00000000fdd92ed8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 96.721 Thread 0x000001755f42e040 Exception <a 'sun/nio/fs/WindowsException'{0x00000000fdd95658}> (0x00000000fdd95658) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 96.756 Thread 0x000001755f42e040 Exception <a 'sun/nio/fs/WindowsException'{0x00000000fdd97dc0}> (0x00000000fdd97dc0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 96.912 Thread 0x000001755eb7d460 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d06fd478}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecialIFC(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d06fd478) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 96.949 Thread 0x000001755f42e040 Exception <a 'sun/nio/fs/WindowsException'{0x00000000fdd9a560}> (0x00000000fdd9a560) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 97.048 Thread 0x000001755f42e040 Exception <a 'sun/nio/fs/WindowsException'{0x00000000fdd9cd38}> (0x00000000fdd9cd38) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 94.535 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 94.592 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 94.622 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 94.651 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 94.673 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 94.729 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 94.731 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause)
Event: 94.771 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause) done
Event: 94.774 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 94.786 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 94.839 Executing VM operation: G1TryInitiateConcMark (G1 Humongous Allocation)
Event: 94.872 Executing VM operation: G1TryInitiateConcMark (G1 Humongous Allocation) done
Event: 95.873 Executing VM operation: G1PauseRemark
Event: 95.917 Executing VM operation: G1PauseRemark done
Event: 96.312 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 96.558 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 96.597 Executing VM operation: G1PauseCleanup
Event: 96.605 Executing VM operation: G1PauseCleanup done
Event: 96.980 Executing VM operation: ICBufferFull
Event: 97.009 Executing VM operation: ICBufferFull done

Memory protections (0 events):
No events

Nmethod flushes (20 events):
Event: 95.914 Thread 0x0000017554f997b0 flushing  nmethod 0x0000017543a65510
Event: 95.914 Thread 0x0000017554f997b0 flushing  nmethod 0x0000017543a65990
Event: 95.914 Thread 0x0000017554f997b0 flushing  nmethod 0x0000017543a69390
Event: 95.914 Thread 0x0000017554f997b0 flushing  nmethod 0x0000017543aa3390
Event: 95.914 Thread 0x0000017554f997b0 flushing  nmethod 0x0000017543c36090
Event: 95.914 Thread 0x0000017554f997b0 flushing  nmethod 0x0000017543c36c90
Event: 95.914 Thread 0x0000017554f997b0 flushing  nmethod 0x0000017543d52f10
Event: 95.914 Thread 0x0000017554f997b0 flushing  nmethod 0x0000017543d53b90
Event: 95.915 Thread 0x0000017554f997b0 flushing  nmethod 0x0000017543d58b90
Event: 95.915 Thread 0x0000017554f997b0 flushing  nmethod 0x0000017543db9390
Event: 95.915 Thread 0x0000017554f997b0 flushing  nmethod 0x0000017543dbb490
Event: 95.915 Thread 0x0000017554f997b0 flushing  nmethod 0x0000017543dbf610
Event: 95.915 Thread 0x0000017554f997b0 flushing  nmethod 0x0000017543dc0190
Event: 95.915 Thread 0x0000017554f997b0 flushing  nmethod 0x0000017543de0d10
Event: 95.915 Thread 0x0000017554f997b0 flushing  nmethod 0x0000017543e5f010
Event: 95.915 Thread 0x0000017554f997b0 flushing  nmethod 0x0000017543e69010
Event: 95.915 Thread 0x0000017554f997b0 flushing  nmethod 0x0000017543e90890
Event: 95.915 Thread 0x0000017554f997b0 flushing  nmethod 0x0000017543e9ab10
Event: 95.915 Thread 0x0000017554f997b0 flushing  nmethod 0x0000017543ea8290
Event: 95.915 Thread 0x0000017554f997b0 flushing  nmethod 0x0000017543ec6390

Events (20 events):
Event: 79.998 Thread 0x000001755eb79950 Thread added: 0x000001755f42e040
Event: 80.101 Thread 0x0000017555268310 Thread added: 0x000001756556be60
Event: 80.488 Thread 0x000001755eb7c740 Thread added: 0x000001755f42cc90
Event: 80.647 Thread 0x000001755f42cc90 Thread added: 0x000001755f42f3f0
Event: 80.647 Thread 0x000001755f42cc90 Thread added: 0x000001755f4314c0
Event: 83.338 Thread 0x000001756556be60 Thread exited: 0x000001756556be60
Event: 83.559 Thread 0x0000017555268310 Thread added: 0x000001756556cc00
Event: 84.691 Thread 0x000001755f42f3f0 Thread added: 0x000001755cac35d0
Event: 84.692 Thread 0x000001755f42f3f0 Thread added: 0x000001755cac0e70
Event: 84.692 Thread 0x000001755f42f3f0 Thread added: 0x000001755cabfac0
Event: 84.730 Thread 0x000001755cac0e70 Thread added: 0x000001755cac0150
Event: 86.245 Thread 0x000001756556cc00 Thread exited: 0x000001756556cc00
Event: 86.313 Thread 0x0000017555268310 Thread added: 0x000001756556f4e0
Event: 87.148 Thread 0x000001755eb78c30 Thread exited: 0x000001755eb78c30
Event: 87.148 Thread 0x000001755eb77880 Thread exited: 0x000001755eb77880
Event: 87.240 Thread 0x000001755eb77f10 Thread exited: 0x000001755eb77f10
Event: 89.313 Thread 0x000001756556f4e0 Thread exited: 0x000001756556f4e0
Event: 89.709 Thread 0x0000017555268310 Thread added: 0x0000017565570950
Event: 89.906 Thread 0x0000017565570950 Thread exited: 0x0000017565570950
Event: 91.384 Thread 0x0000017555268310 Thread added: 0x000001756556be60


Dynamic libraries:
0x00007ff7b0e10000 - 0x00007ff7b0e1a000 	C:\Program Files\Android\Android Studio\jbr\bin\java.exe
0x00007ffef0c10000 - 0x00007ffef0e08000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ffef09c0000 - 0x00007ffef0a82000 	C:\Windows\System32\KERNEL32.DLL
0x00007ffeee760000 - 0x00007ffeeea56000 	C:\Windows\System32\KERNELBASE.dll
0x00007ffeee320000 - 0x00007ffeee420000 	C:\Windows\System32\ucrtbase.dll
0x00007ffeca2b0000 - 0x00007ffeca2cb000 	C:\Program Files\Android\Android Studio\jbr\bin\VCRUNTIME140.dll
0x00007ffecc820000 - 0x00007ffecc838000 	C:\Program Files\Android\Android Studio\jbr\bin\jli.dll
0x00007ffeef0b0000 - 0x00007ffeef24d000 	C:\Windows\System32\USER32.dll
0x00007ffeee580000 - 0x00007ffeee5a2000 	C:\Windows\System32\win32u.dll
0x00007ffee0760000 - 0x00007ffee09fa000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7\COMCTL32.dll
0x00007ffef0930000 - 0x00007ffef095b000 	C:\Windows\System32\GDI32.dll
0x00007ffeef5f0000 - 0x00007ffeef68e000 	C:\Windows\System32\msvcrt.dll
0x00007ffeee5b0000 - 0x00007ffeee6c9000 	C:\Windows\System32\gdi32full.dll
0x00007ffeeea90000 - 0x00007ffeeeb2d000 	C:\Windows\System32\msvcp_win.dll
0x00007ffeeedc0000 - 0x00007ffeeedef000 	C:\Windows\System32\IMM32.DLL
0x00007ffede140000 - 0x00007ffede14c000 	C:\Program Files\Android\Android Studio\jbr\bin\vcruntime140_1.dll
0x00007ffec0510000 - 0x00007ffec059d000 	C:\Program Files\Android\Android Studio\jbr\bin\msvcp140.dll
0x00007ffea3830000 - 0x00007ffea44ba000 	C:\Program Files\Android\Android Studio\jbr\bin\server\jvm.dll
0x00007ffeef530000 - 0x00007ffeef5e1000 	C:\Windows\System32\ADVAPI32.dll
0x00007ffeef010000 - 0x00007ffeef0af000 	C:\Windows\System32\sechost.dll
0x00007ffeeeee0000 - 0x00007ffeef003000 	C:\Windows\System32\RPCRT4.dll
0x00007ffeeea60000 - 0x00007ffeeea87000 	C:\Windows\System32\bcrypt.dll
0x00007ffeef4c0000 - 0x00007ffeef52b000 	C:\Windows\System32\WS2_32.dll
0x00007ffeee100000 - 0x00007ffeee14b000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ffee2be0000 - 0x00007ffee2c07000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ffee5700000 - 0x00007ffee570a000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ffeee0e0000 - 0x00007ffeee0f2000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ffeec100000 - 0x00007ffeec112000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ffedd730000 - 0x00007ffedd73a000 	C:\Program Files\Android\Android Studio\jbr\bin\jimage.dll
0x00007ffedfc10000 - 0x00007ffedfe11000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ffec4fb0000 - 0x00007ffec4fe4000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ffeee6d0000 - 0x00007ffeee752000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ffec2550000 - 0x00007ffec255e000 	C:\Program Files\Android\Android Studio\jbr\bin\instrument.dll
0x00007ffeca290000 - 0x00007ffeca2b0000 	C:\Program Files\Android\Android Studio\jbr\bin\java.dll
0x00007ffeca210000 - 0x00007ffeca228000 	C:\Program Files\Android\Android Studio\jbr\bin\zip.dll
0x00007ffef00d0000 - 0x00007ffef083e000 	C:\Windows\System32\SHELL32.dll
0x00007ffeec300000 - 0x00007ffeecaa4000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ffeefd70000 - 0x00007ffef00c3000 	C:\Windows\System32\combase.dll
0x00007ffeedb20000 - 0x00007ffeedb4b000 	C:\Windows\SYSTEM32\Wldp.dll
0x00007ffeeedf0000 - 0x00007ffeeeebd000 	C:\Windows\System32\OLEAUT32.dll
0x00007ffeeec30000 - 0x00007ffeeecdd000 	C:\Windows\System32\SHCORE.dll
0x00007ffeeece0000 - 0x00007ffeeed3b000 	C:\Windows\System32\shlwapi.dll
0x00007ffeee1d0000 - 0x00007ffeee1f4000 	C:\Windows\SYSTEM32\profapi.dll
0x00007ffedbb90000 - 0x00007ffedbba0000 	C:\Program Files\Android\Android Studio\jbr\bin\net.dll
0x00007ffee77d0000 - 0x00007ffee78da000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007ffeed900000 - 0x00007ffeed96a000 	C:\Windows\system32\mswsock.dll
0x00007ffec9a30000 - 0x00007ffec9a46000 	C:\Program Files\Android\Android Studio\jbr\bin\nio.dll
0x00007ffedb850000 - 0x00007ffedb860000 	C:\Program Files\Android\Android Studio\jbr\bin\verify.dll
0x00007ffebf790000 - 0x00007ffebf7b7000 	C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64\native-platform.dll
0x00007ffeb82a0000 - 0x00007ffeb8318000 	C:\Users\<USER>\.gradle\native\0.2.7\x86_64-windows-gnu\gradle-fileevents.dll
0x00007ffed0d90000 - 0x00007ffed0d99000 	C:\Program Files\Android\Android Studio\jbr\bin\management.dll
0x00007ffecd120000 - 0x00007ffecd12b000 	C:\Program Files\Android\Android Studio\jbr\bin\management_ext.dll
0x00007ffef0920000 - 0x00007ffef0928000 	C:\Windows\System32\PSAPI.DLL
0x00007ffeed600000 - 0x00007ffeed63b000 	C:\Windows\SYSTEM32\IPHLPAPI.DLL
0x00007ffeef8e0000 - 0x00007ffeef8e8000 	C:\Windows\System32\NSI.dll
0x00007ffecd050000 - 0x00007ffecd059000 	C:\Program Files\Android\Android Studio\jbr\bin\extnet.dll
0x00007ffeedeb0000 - 0x00007ffeedec8000 	C:\Windows\SYSTEM32\CRYPTSP.dll
0x00007ffeed240000 - 0x00007ffeed278000 	C:\Windows\system32\rsaenh.dll
0x00007ffeee150000 - 0x00007ffeee17e000 	C:\Windows\SYSTEM32\USERENV.dll
0x00007ffeeda90000 - 0x00007ffeeda9c000 	C:\Windows\SYSTEM32\CRYPTBASE.dll
0x00007ffec2500000 - 0x00007ffec2507000 	C:\Windows\system32\wshunix.dll
0x00007ffeed490000 - 0x00007ffeed4c3000 	C:\Windows\SYSTEM32\ntmarta.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Android\Android Studio\jbr\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7;C:\Program Files\Android\Android Studio\jbr\bin\server;C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64;C:\Users\<USER>\.gradle\native\0.2.7\x86_64-windows-gnu

VM Arguments:
jvm_args: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -XX:MaxMetaspaceSize=512m -XX:+HeapDumpOnOutOfMemoryError -Xmx1536m -Dfile.encoding=UTF-8 -Duser.country=BR -Duser.language=pt -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.14.1-bin\baw1sv0jfoi8rxs14qo3h49cs\gradle-8.14.1\lib\agents\gradle-instrumentation-agent-8.14.1.jar 
java_command: org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.14.1
java_class_path (initial): C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.14.1-bin\baw1sv0jfoi8rxs14qo3h49cs\gradle-8.14.1\lib\gradle-daemon-main-8.14.1.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 3                                         {product} {ergonomic}
   size_t CompressedClassSpaceSize                 = 436207616                                 {product} {ergonomic}
     uint ConcGCThreads                            = 1                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 4                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 1048576                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
     bool HeapDumpOnOutOfMemoryError               = true                                   {manageable} {command line}
   size_t InitialHeapSize                          = 134217728                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 1610612736                                {product} {command line}
   size_t MaxMetaspaceSize                         = 536870912                                 {product} {command line}
   size_t MaxNewSize                               = 965738496                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 1048576                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5832780                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122912730                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122912730                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 1610612736                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=C:\Program Files\Java\jdk-11.0.10
PATH=F:\PROJETO\React\EscolaSabatina\node_modules\.bin;F:\PROJETO\React\EscolaSabatina\node_modules\.bin;F:\PROJETO\React\node_modules\.bin;F:\PROJETO\node_modules\.bin;F:\node_modules\.bin;C:\Users\<USER>\AppData\Roaming\nvm\v20.10.0\node_modules\npm\node_modules\@npmcli\run-script\lib\node-gyp-bin;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\Microsoft SQL Server\130\Tools\Binn\;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn\;C:\Program Files\dotnet\;C:\Program Files (x86)\Microsoft SQL Server\150\DTS\Binn\;C:\Program Files\Azure Data Studio\bin;C:\Program Files\Git\cmd;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\110\Tools\Binn\;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\130\Tools\Binn\;C:\Program Files (x86)\Microsoft SQL Server\140\Tools\Binn\;C:\Program Files\Microsoft SQL Server\140\Tools\Binn\;C:\Program Files\Microsoft SQL Server\140\DTS\Binn\;C:\Program Files\Microsoft\Web Platform Installer\;C:\Program Files (x86)\dotnet\;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;C:\Program Files\Calibre2\;C:\Program Files\Java\jdk-11\bin;C:\Prog;C:\Program Files\Docker\Docker\resources\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\.local\share\solana\install\active_release\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\;C:\ProgramData\DockerDesktop\version-bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\Users\ADM\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\.dotnet\tools;C:\MinGW\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin
USERNAME=ADM
LANG=en_US.UTF-8
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 58 Stepping 9, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

JNI global refs:
JNI global refs: 28, weak refs: 27

JNI global refs memory usage: 835, weak refs: 1465

Process memory usage:
Resident Set Size: 1285028K (15% of 8357708K total physical memory with 347704K free physical memory)

OOME stack traces (most recent first):
Classloader memory used:
Loader org.gradle.internal.classloader.VisitableURLClassLoader$InstrumentingVisitableURLClassLoader: 63947K
Loader org.gradle.internal.classloader.VisitableURLClassLoader                         : 50839K
Loader bootstrap                                                                       : 31578K
Loader org.gradle.initialization.MixInLegacyTypesClassLoader                           : 14885K
Loader org.gradle.groovy.scripts.internal.DefaultScriptCompilationHandler$ScriptClassLoader: 2144K
Loader jdk.internal.loader.ClassLoaders$PlatformClassLoader                            : 540K
Loader jdk.internal.reflect.DelegatingClassLoader                                      : 520K
Loader org.gradle.internal.classloader.VisitableURLClassLoader                         : 340K
Loader org.codehaus.groovy.runtime.callsite.CallSiteClassLoader                        : 301K
Loader jdk.internal.loader.ClassLoaders$AppClassLoader                                 : 235K
Loader sun.reflect.misc.MethodUtil                                                     : 2952B

Classes loaded by more than one classloader:
Class Program                                                                         : loaded 12 times (x 68B)
Class Build_gradle$1                                                                  : loaded 10 times (x 74B)
Class Build_gradle                                                                    : loaded 10 times (x 126B)
Class org.gradle.kotlin.dsl.VersionCatalogAccessorsKt                                 : loaded 5 times (x 67B)
Class Build_gradle$4                                                                  : loaded 4 times (x 70B)
Class Build_gradle$6                                                                  : loaded 4 times (x 70B)
Class Build_gradle$2                                                                  : loaded 4 times (x 75B)
Class org.gradle.kotlin.dsl.Accessors96b3ii45gitqpy1kb3tvcvtxvKt                      : loaded 4 times (x 67B)
Class org.gradle.kotlin.dsl.ImplementationConfigurationAccessorsKt                    : loaded 4 times (x 67B)
Class org.gradle.kotlin.dsl.Accessors9osmdt78klrrxidc9srcw3tsqKt                      : loaded 4 times (x 67B)
Class Build_gradle$3                                                                  : loaded 4 times (x 70B)
Class Build_gradle$5                                                                  : loaded 4 times (x 70B)
Class org.gradle.kotlin.dsl.TestImplementationConfigurationAccessorsKt                : loaded 3 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.CInteropProcess                               : loaded 2 times (x 343B)
Class org.jetbrains.kotlin.gradle.internal.KotlinDomApiDependencyManagementKt         : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycleImpl$launch$coroutine$1 : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.NpmDependencyExtensionKt$defaultNpmDependencyDelegate$1: loaded 2 times (x 163B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.MissingNativeStdlibChecker: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.targets.ConfigureNonPackedKlibConsumingSideEffectKt : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.native.KotilnNativeConfigureBinariesSideEffectKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPlatformType$DisambiguationRule        : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPlatformType$Companion                 : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.internal.ConfigurationCacheStartParameterAccessorKt: loaded 2 times (x 67B)
Class com.google.common.io.FileWriteMode                                              : loaded 2 times (x 75B)
Class [Lcom.google.common.base.AbstractIterator$State;                                : loaded 2 times (x 65B)
Class com.google.common.base.CharMatcher$IsEither                                     : loaded 2 times (x 107B)
Class org.gradle.internal.classloader.ClassLoaderHierarchy                            : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.hierarchy.KotlinSourceSetTreeClassifierKt    : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.report.UsesBuildMetricsService                      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory$KotlinCompilationSourceSetsContainerFactory: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.utils.ConfigurationCacheOpaqueValueSource           : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.utils.MutableObservableSet                          : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinHierarchyDsl                              : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.targets.metadata.KotlinMetadataTargetConfiguratorKt$isNativeSourceSet$2: loaded 2 times (x 91B)
Class org.jetbrains.kotlin.gradle.targets.native.KonanPropertiesBuildService$Companion$registerIfAbsent$2$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.report.BuildReportType                              : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.statistics.metrics.StringAnonymizationPolicy$AllowedListAnonymizer: loaded 2 times (x 75B)
Class com.google.common.collect.ImmutableEnumSet                                      : loaded 2 times (x 142B)
Class com.google.common.util.concurrent.ExecutionError                                : loaded 2 times (x 78B)
Class com.google.common.collect.ImmutableList$1                                       : loaded 2 times (x 93B)
Class org.gradle.api.internal.classpath.ManifestUtil                                  : loaded 2 times (x 67B)
Class io.invertase.gradle.common.WithExtensions                                       : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.utils.FutureKt$future$1                             : loaded 2 times (x 91B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$androidLayoutResources$1        : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.report.GradleBuildMetricsReporter                   : loaded 2 times (x 94B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinMetadataCompilation                : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.sources.KotlinDependencyScope                : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig$1   : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationFriendPathsResolver: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$ANDROID_ARM64                     : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinNativeHostSpecificMetadataArtifactKt$KotlinNativeHostSpecificMetadataArtifact$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinLegacyMetadataArtifactKt$KotlinLegacyMetadataArtifact$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationSideEffect$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinUsages$KotlinMetadataDisambiguation: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.FlowActionBuildFusService$Companion$registerIfAbsentImpl$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$IntGradleProperty: loaded 2 times (x 72B)
Class com.google.common.io.ByteArrayDataOutput                                        : loaded 2 times (x 66B)
Class com.google.common.collect.RegularImmutableMap                                   : loaded 2 times (x 117B)
Class com.google.common.collect.ImmutableMap$MapViewOfValuesAsSingletonSets           : loaded 2 times (x 121B)
Class io.invertase.gradle.common.WithExtensions$getName                               : loaded 2 times (x 148B)
Class io.invertase.gradle.build.ProjectExtension                                      : loaded 2 times (x 91B)
Class org.jetbrains.kotlin.gradle.utils.GradleAttributesContainerUtilsKt$setAttributeProvider$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.internal.StdlibDependencyManagementKt$addStdlibDependency$1$1$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.AbstractKotlinCompile$compilerRunner$1        : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.CacheableTasksKt$cacheOnlyIfEnabledForKotlin$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.internal.transforms.BuildToolsApiClasspathEntrySnapshotTransform: loaded 2 times (x 78B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig$2   : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory$PreConfigure: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinProjectSetupActionKt$KotlinProjectSetupCoroutine$1$1: loaded 2 times (x 91B)
Class org.jetbrains.kotlin.gradle.utils.NativeCompilerDownloader$Companion$DEFAULT_KONAN_VERSION$2: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$WATCHOS_ARM32                     : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.targets.js.dsl.KotlinJsTargetDsl                    : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycleImplKt                  : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.DeprecatedKotlinNativeTargetsChecker$runChecks$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycleImpl$WhenMappings       : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinCommonCompilerToolOptionsHelper           : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinTargetComponent                        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradlePluginExtensionPointInternal     : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.statistics.metrics.StringOverridePolicy$OVERRIDE_VERSION_IF_NOT_SET: loaded 2 times (x 81B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinBuildStatsBeanService$Companion$initStatsService$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactories$Companion$getProvider$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$BooleanGradleProperty: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinProjectExtension                          : loaded 2 times (x 118B)
Class [Lorg.objectweb.asm.Symbol;                                                     : loaded 2 times (x 65B)
Class org.objectweb.asm.ClassVisitor                                                  : loaded 2 times (x 84B)
Class com.google.common.collect.UnmodifiableListIterator                              : loaded 2 times (x 91B)
Class Build_gradle$2$1$1                                                              : loaded 2 times (x 70B)
Class io.invertase.gradle.build.ReactNativeModule$_closure1                           : loaded 2 times (x 137B)
Class build_86psz0zg5ccmkipscnjtptl6f                                                 : loaded 2 times (x 176B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycle$ProjectConfigurationResult$Failure: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.sources.DefaultKotlinSourceSet$MetadataDependencyTransformation: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$Companion          : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain                    : loaded 2 times (x 73B)
Class org.jetbrains.kotlin.gradle.targets.native.toolchain.KotlinNativeBundleBuildService$Parameters: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.targets.native.toolchain.KotlinNativeBundleBuildService: loaded 2 times (x 73B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleBuildServices                    : loaded 2 times (x 76B)
Class [Lorg.jetbrains.kotlin.statistics.ValueAnonymizer;                              : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$MemoizedCallable$value$2: loaded 2 times (x 75B)
Class org.objectweb.asm.MethodTooLargeException                                       : loaded 2 times (x 79B)
Class com.google.common.base.Platform$JdkPatternCompiler                              : loaded 2 times (x 71B)
Class [Lcom.google.common.cache.CacheBuilder$OneWeigher;                              : loaded 2 times (x 65B)
Class com.google.common.base.Suppliers                                                : loaded 2 times (x 67B)
Class org.gradle.internal.classloader.DefaultClassLoaderFactory                       : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.gradle.plugin.PropertiesProvider$kotlinCompilerArgumentsLogLevel$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.compilerRunner.CompilerSystemPropertiesService$Parameters_Decorated: loaded 2 times (x 130B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinTargetWithTests                        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.ToolingDiagnosticRenderingOptions: loaded 2 times (x 68B)
Class [Lorg.jetbrains.kotlin.gradle.dsl.KotlinVersion;                                : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.UsesKotlinToolingDiagnosticsParameters: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.JsEnvironmentChecker    : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.NpmDependencyExtensionKt             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.metadata.KotlinMetadataTargetConfiguratorKt$special$$inlined$extrasStoredFuture$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.PropertiesProvider$classLoaderCacheTimeoutInSeconds$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.report.BuildMetricsService                          : loaded 2 times (x 82B)
Class org.jetbrains.kotlin.statistics.metrics.NumberAnonymizationPolicy               : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.statistics.metrics.StringAnonymizationPolicy               : loaded 2 times (x 74B)
Class com.google.common.io.ByteSource$ConcatenatedByteSource                          : loaded 2 times (x 81B)
Class com.google.common.base.CharMatcher$None                                         : loaded 2 times (x 108B)
Class org.gradle.internal.classloader.ClassLoaderVisitor                              : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.build.report.metrics.BuildPerformanceMetrics               : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationLanguageSettingsConfigurator: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycleKt$await$1              : loaded 2 times (x 84B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.KotlinCompilation;                         : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinTargetComponentWithPublication     : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCreateNativeCompileTasksSideEffectKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinTargetContainerWithJsPresetFunctions      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinProjectSetupActionKt                   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.report.ReportingSettings$Companion                  : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.report.ConfigureReporingKt                          : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.tooling.core.TypeUtils                                     : loaded 2 times (x 67B)
Class [Lcom.google.common.collect.ImmutableMapEntry;                                  : loaded 2 times (x 65B)
Class org.gradle.api.internal.classpath.EffectiveClassPath                            : loaded 2 times (x 86B)
Class org.jetbrains.kotlin.gradle.utils.CompatibilityKt                               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.DefaultKotlinCompilationConfigurationsContainer: loaded 2 times (x 81B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$ANDROID_ARM32                     : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.tasks.TasksProviderKt                               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.ToolingDiagnostic                : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.sources.DefaultKotlinSourceSetFactory        : loaded 2 times (x 76B)
Class com.google.common.collect.CollectSpliterators$1WithCharacteristics              : loaded 2 times (x 86B)
Class com.google.common.collect.ImmutableRangeSet                                     : loaded 2 times (x 111B)
Class com.google.common.collect.ImmutableEnumMap                                      : loaded 2 times (x 121B)
Class com.google.common.collect.ListMultimap                                          : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.incremental.ClasspathChanges                               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.buildtools.api.jvm.ClassSnapshotGranularity                : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.ExternalKotlinTargetApi                             : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.AndroidPluginWithoutAndroidTargetChecker$runChecks$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.AndroidSourceSetLayoutV1SourceSetsNotFoundChecker$runChecks$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinGradleProjectCheckersRunnerKt$launchKotlinGradleProjectCheckers$1$1: loaded 2 times (x 91B)
Class org.jetbrains.kotlin.konan.target.KonanTarget                                   : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.targets.CreateDefaultTestRunSideEffectKt            : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.utils.ProviderApiUtilsKt$mapOrNull$1                : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.targets.native.toolchain.KotlinNativeBundleBuildService$Companion$registerIfAbsent$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.statistics.metrics.BooleanAnonymizationPolicy              : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.statistics.metrics.BooleanOverridePolicy$OVERRIDE          : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.gradle.plugin.PropertiesProvider$kotlinExperimentalTryNext$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.fus.BuildUidService$Companion                       : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactoriesConfigurator$Companion$getProvider$1: loaded 2 times (x 70B)
Class org.objectweb.asm.AnnotationVisitor                                             : loaded 2 times (x 74B)
Class com.google.common.collect.Sets$1                                                : loaded 2 times (x 135B)
Class com.google.common.base.Splitter                                                 : loaded 2 times (x 68B)
Class org.gradle.api.internal.DefaultClassPathProvider                                : loaded 2 times (x 72B)
Class build_86psz0zg5ccmkipscnjtptl6f$_run_closure1                                   : loaded 2 times (x 135B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$1$3$classpathEntrySnapshotFiles$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.KaptGenerateStubsConfig$Companion$wireJavaAndKotlinOutputs$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycleCoroutineContextElement : loaded 2 times (x 80B)
Class org.jetbrains.kotlin.gradle.dsl.PlatformSourceSetConventionsChecker             : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.KotlinSourceSetTreeDependsOnMismatchChecker: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.targets.CreateDefaultCompilationsSideEffectKt       : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.SyncLanguageSettingsWithKotlinExtensionSetupActionKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.statistics.metrics.NumericalMetrics                        : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactories$Companion$getProvider$2$1: loaded 2 times (x 75B)
Class com.google.common.collect.Sets$2                                                : loaded 2 times (x 135B)
Class [Lorg.gradle.api.internal.ClassPathProvider;                                    : loaded 2 times (x 65B)
Class org.gradle.api.internal.ClassPathProvider                                       : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.ExecutedTaskMetrics               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.CheckKotlinGradlePluginConfigurationErrors$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinJvmCompile$DefaultImpls                 : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.CacheableTasksKt                              : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.konan.target.HostManager$targetValues$2                    : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.UnusedSourceSetsChecker$runChecks$1: loaded 2 times (x 84B)
Class [Lorg.jetbrains.kotlin.konan.target.Family;                                     : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.PeerNpmDependencyExtension           : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.dsl.AndroidMainSourceSetConventionsChecker          : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.WasmJsEnvironmentChecker$1: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.targets.native.internal.SetupKotlinNativePlatformDependenciesAndStdlibKt$SetupKotlinNativeStdlibAndPlatformDependenciesImport$1: loaded 2 times (x 91B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.NpmDependencyExtensionKt$AddNpmDependencyExtensionProjectSetupAction$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.FlowActionBuildFusService         : loaded 2 times (x 80B)
Class org.jetbrains.kotlin.build.report.metrics.GradleBuildPerformanceMetric          : loaded 2 times (x 88B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$NullableBooleanGradleProperty: loaded 2 times (x 72B)
Class [Lorg.objectweb.asm.AnnotationVisitor;                                          : loaded 2 times (x 65B)
Class com.google.common.collect.Sets$3                                                : loaded 2 times (x 135B)
Class com.google.common.cache.LocalCache$LocalLoadingCache                            : loaded 2 times (x 130B)
Class com.google.common.base.AbstractIterator$State                                   : loaded 2 times (x 75B)
Class org.gradle.internal.service.UnknownServiceException                             : loaded 2 times (x 79B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$2$1$1$1 : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$ScriptFilterSpec                : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginWrapper                          : loaded 2 times (x 82B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.ComposePluginSuggestApplyChecker$runChecks$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$TVOS_ARM64                        : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.DefaultPeerNpmDependencyExtension    : loaded 2 times (x 137B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycleKt                      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.WasmJsEnvironmentChecker$2: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.AbstractKotlinTarget                     : loaded 2 times (x 152B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesLoader$kotlinNativeVersion$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactoriesConfigurator$Inject: loaded 2 times (x 92B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinSingleTargetExtension                     : loaded 2 times (x 120B)
Class org.objectweb.asm.ModuleVisitor                                                 : loaded 2 times (x 77B)
Class com.google.common.io.ByteArrayDataInput                                         : loaded 2 times (x 66B)
Class com.google.common.collect.Sets$4                                                : loaded 2 times (x 135B)
Class com.google.common.cache.LocalCache$Strength$1                                   : loaded 2 times (x 77B)
Class build_6s7imafvclc6gm72hnzimmguy                                                 : loaded 2 times (x 181B)
Class io.invertase.gradle.build.ReactNativeProject                                    : loaded 2 times (x 87B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$2$1$1$2 : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinCompilationsKt$addSourcesToKotlinCompileTask$configureAction$2: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationTaskNamesContainer: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory$KotlinCompilerOptionsFactory: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildServiceKt$propertyWithDeprecatedName$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.NpmDependencyExtension               : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.LanguageSettingsBuilder                      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.WasmWasiEnvironmentChecker: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.WasmJsEnvironmentChecker$3: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.targets.ConfigureBuildSideEffectKt                  : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.BuildEventsListenerRegistryHolder$Companion  : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.BuildFusService$Companion$registerIfAbsentImpl$generalConfigurationMetricsProvider$1: loaded 2 times (x 71B)
Class [Lorg.jetbrains.kotlin.statistics.metrics.StringMetrics;                        : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$Inject   : loaded 2 times (x 92B)
Class com.google.common.util.concurrent.SettableFuture                                : loaded 2 times (x 110B)
Class com.google.common.cache.LocalCache$Strength$2                                   : loaded 2 times (x 77B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinJavaToolchain$JdkSetter                 : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.utils.ConfigurationCacheKt                          : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycle$ProjectConfigurationResult$Success: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.StdlibDependencyManagementKt$configureStdlibVersionAlignment$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.OverriddenKotlinNativeHomeChecker$allKonanMainSubdirectoriesExist$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.dsl.PlatformSourceSetConventionsChecker$CheckedPlatformInfo: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.WasmJsEnvironmentChecker$4: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinUsages$KotlinCinteropDisambiguation: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginInMultipleProjectsHolder         : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.report.BuildReportType$Companion                    : loaded 2 times (x 67B)
Class com.google.common.io.ByteStreams                                                : loaded 2 times (x 67B)
Class com.google.common.cache.LocalCache$Strength$3                                   : loaded 2 times (x 77B)
Class com.google.common.collect.MapDifference                                         : loaded 2 times (x 66B)
Class com.google.common.base.Splitter$1                                               : loaded 2 times (x 73B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompilerPluginData$InputsOutputsState   : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.sources.FragmentConsistencyChecks$optInAnnotationsCheck$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.AbstractKotlinCompilationToRunnableFiles : loaded 2 times (x 192B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinMetadataArtifactKt$KotlinMetadataArtifact$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinUsages$KotlinMetadataCompatibility : loaded 2 times (x 72B)
Class [Lorg.jetbrains.kotlin.gradle.dsl.JvmTarget;                                    : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.utils.ConfigurationsKt$maybeCreateResolvable$1      : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.StatisticsBuildFlowManager$Companion         : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.statistics.metrics.StringMetrics$Companion                 : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$property$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.tooling.core.Extras$Type                                   : loaded 2 times (x 68B)
Class org.objectweb.asm.FieldWriter                                                   : loaded 2 times (x 74B)
Class com.google.common.cache.CacheBuilder$NullListener                               : loaded 2 times (x 78B)
Class org.gradle.internal.classpath.TransformedClassPath                              : loaded 2 times (x 92B)
Class org.gradle.internal.classloader.InstrumentingClassLoader                        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.internal.StdlibDependencyManagementKt$configureStdlibVersionAlignment$1$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.BuildFinishedListenerService$Companion       : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.BuildFinishedListenerService                 : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.sources.FragmentConsistencyChecks$optInAnnotationsCheck$2: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.targets.NativeForwardImplementationToApiElementsSideEffectKt$special$$inlined$KotlinTargetSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.BuildFusServiceKt                 : loaded 2 times (x 67B)
Class com.google.common.collect.ImmutableRangeSet$AsSet                               : loaded 2 times (x 295B)
Class com.google.common.cache.CacheLoader$FunctionToCacheLoader                       : loaded 2 times (x 71B)
Class com.google.common.collect.Iterators$ArrayItr                                    : loaded 2 times (x 93B)
Class com.google.common.collect.ImmutableCollection$Builder                           : loaded 2 times (x 72B)
Class build_agr3p375fzty99zrloowe6ju6                                                 : loaded 2 times (x 177B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.BuildFusService$onFinish$2        : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$1$3$2   : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.AbstractKotlinCompileTool$sources$1           : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.TaskConfigAction                : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.OverriddenKotlinNativeHomeChecker$allKonanMainSubdirectoriesExist$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.AbstractKotlinPlugin$Companion               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinSourceSetTree                          : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycle$Stage                  : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCreateResourcesTaskSideEffectKt$KotlinCreateResourcesTaskSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.targets.jvm.KotlinJvmTargetTestFixturesSideEffectKt : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.sources.KotlinSourceSetFactory               : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.targets.native.toolchain.KotlinNativeBundleBuildService$Parameters_Decorated: loaded 2 times (x 139B)
Class org.jetbrains.kotlin.gradle.internal.ClassLoadersCachingBuildService$Companion  : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.IdeaKt$isInIdeaSync$1                      : loaded 2 times (x 70B)
Class com.google.common.collect.BaseImmutableMultimap                                 : loaded 2 times (x 119B)
Class com.google.common.base.Joiner                                                   : loaded 2 times (x 75B)
Class com.google.common.base.MoreObjects                                              : loaded 2 times (x 67B)
Class build_agr3p375fzty99zrloowe6ju6$_run_closure1                                   : loaded 2 times (x 135B)
Class [Lorg.jetbrains.kotlin.build.report.metrics.BuildAttribute;                     : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.utils.ProviderApiUtilsKt$mapOrNull$1$1              : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.hierarchy.KotlinSourceSetTreeClassifier$Default: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.InternalKotlinCompilationKt              : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig     : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationImpl$attributes$2: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.OverriddenKotlinNativeHomeChecker$StdlibExistenceCheckerValueSource: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.StatisticsBuildFlowManager$subscribeForBuildResultAndConfigurationTimeMetrics$1$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.build.report.metrics.GradleBuildTime                       : loaded 2 times (x 82B)
Class org.jetbrains.kotlin.gradle.utils.IsolatedKotlinClasspathClassCastException     : loaded 2 times (x 78B)
Class [Lorg.objectweb.asm.Type;                                                       : loaded 2 times (x 65B)
Class com.google.common.collect.Platform                                              : loaded 2 times (x 67B)
Class com.google.common.collect.UnmodifiableIterator                                  : loaded 2 times (x 76B)
Class org.gradle.internal.service.ServiceLocator                                      : loaded 2 times (x 66B)
Class io.invertase.gradle.common.PackageJson$getForProject                            : loaded 2 times (x 142B)
Class io.invertase.gradle.build.ProjectExtension$getVersion$1                         : loaded 2 times (x 148B)
Class org.jetbrains.kotlin.gradle.utils.GradleAttributesContainerUtilsKt$copyAttributeTo$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$DefaultJavaToolchainSetter$useAsConvention$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilerArgumentsProducer$CreateCompilerArgumentsContext: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.RenderReportedDiagnosticsKt      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.hierarchy.KotlinSourceSetTreeClassifierKt$classify$1: loaded 2 times (x 84B)
Class [Lorg.jetbrains.kotlin.konan.target.KonanTarget;                                : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.WasmSourceSetsNotFoundChecker: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.AndroidSourceSetLayoutV1SourceSetsNotFoundChecker$androidSourceSetRegex$2: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinBuildStatsLoggerService$Companion: loaded 2 times (x 67B)
Class com.google.common.base.Ascii                                                    : loaded 2 times (x 67B)
Class com.google.common.collect.SingletonImmutableBiMap                               : loaded 2 times (x 139B)
Class com.google.common.cache.LocalCache$1                                            : loaded 2 times (x 85B)
Class com.google.common.collect.AbstractMapEntry                                      : loaded 2 times (x 77B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilationWithResources               : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinTargetPreset                           : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.KotlinTargetAlreadyDeclaredChecker: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationProcessorSideEffectKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinUsages$KotlinJavaRuntimeJarsCompatibility: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.utils.FileUtilsKt$localProperties$1                 : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.dsl.HasConfigurableKotlinCompilerOptions            : loaded 2 times (x 66B)
Class com.google.common.io.ByteSource$ByteArrayByteSource                             : loaded 2 times (x 81B)
Class com.google.common.collect.Sets$ImprovedAbstractSet                              : loaded 2 times (x 131B)
Class com.google.common.collect.ImmutableList$Builder                                 : loaded 2 times (x 73B)
Class com.google.common.cache.LocalCache$2                                            : loaded 2 times (x 138B)
Class com.google.common.collect.AbstractMultimap                                      : loaded 2 times (x 119B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig$2$1 : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain_Decorated          : loaded 2 times (x 122B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinProjectConfigurationMetrics : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilationInfo$TCS                    : loaded 2 times (x 85B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.HasBinaries                              : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinCommonOptions                             : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCreateSourcesJarTaskSideEffectKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinJvmProjectExtension                       : loaded 2 times (x 143B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradlePluginExtensionPointInternalKt   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.native.toolchain.UsesKotlinNativeBundleBuildService: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilerArgumentsProducer              : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.build.report.metrics.ValueType                             : loaded 2 times (x 75B)
Class com.google.common.collect.ImmutableRangeSet$1                                   : loaded 2 times (x 205B)
Class com.google.common.collect.RegularImmutableAsList                                : loaded 2 times (x 212B)
Class org.gradle.kotlin.dsl.Accessorse8w47d3slt021lb0cbtcdsmobKt                      : loaded 2 times (x 67B)
Class build_1st6b4zcxn0lddyz8z0am5m89$_run_closure2                                   : loaded 2 times (x 136B)
Class org.jetbrains.kotlin.build.report.metrics.BuildTimes$Companion                  : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.tasks.ProducesKlib                         : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$1$classpathConfiguration$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.NpmDependencyExtensionDelegate       : loaded 2 times (x 163B)
Class org.jetbrains.kotlin.gradle.plugin.sources.FragmentConsistencyChecker           : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.targets.native.internal.SetupKotlinNativePlatformDependenciesAndStdlibKt$SetupKotlinNativePlatformDependenciesAndStdlib$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.dsl.DefaultToolchainSupport_Decorated               : loaded 2 times (x 120B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesLoader$jvmArgs$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.report.BuildMetricsService$Companion                : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompileTool                             : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.utils.SingleAction$PerformedActions                 : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactories$Parameters_Decorated: loaded 2 times (x 130B)
Class org.jetbrains.kotlin.gradle.plugin.PropertiesProvider$PropertyNames             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.utils.GradleUtilsKt                                 : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.HasProject                                   : loaded 2 times (x 66B)
Class com.google.common.base.Optional                                                 : loaded 2 times (x 76B)
Class com.google.common.collect.Range                                                 : loaded 2 times (x 83B)
Class com.google.common.util.concurrent.AbstractFuture$Trusted                        : loaded 2 times (x 66B)
Class com.google.common.collect.Iterators                                             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig$2$3 : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig         : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory$KotlinCompilationTaskNamesContainerFactory: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.internal.KotlinDependenciesManagementKt$CustomizeKotlinDependenciesSetupAction$1$coreLibrariesVersion$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.AndroidPublicationNotConfiguredChecker$runChecks$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.tooling.core.KotlinToolingVersion                          : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.CInteropInputChecker$runChecks$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.NativeVersionChecker    : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.ExperimentalKotlinGradlePluginApi                   : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesLoader          : loaded 2 times (x 82B)
Class [Lorg.jetbrains.kotlin.build.report.metrics.BuildTime;                          : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.statistics.metrics.NumberAnonymizationPolicy$SAFE          : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.BuildFusService                   : loaded 2 times (x 80B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinAndroidProjectExtension                   : loaded 2 times (x 135B)
Class com.google.common.io.ByteSink$AsCharSink                                        : loaded 2 times (x 76B)
Class com.google.common.util.concurrent.UncheckedExecutionException                   : loaded 2 times (x 78B)
Class com.google.common.collect.Lists$StringAsImmutableList                           : loaded 2 times (x 203B)
Class org.gradle.internal.classloader.FilteringClassLoader                            : loaded 2 times (x 101B)
Class org.jetbrains.kotlin.gradle.utils.ReportUtilsKt$addConfigurationMetrics$1       : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig$2$4 : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$scriptSources$1                 : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.build.report.metrics.BuildAttribute$Companion              : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.CInteropInputChecker$runChecks$2: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.KaptGenerateStubsConfig$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.KotlinTestDependencyManagementKt$maybeAddTestDependencyCapability$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.CheckKotlinGradlePluginConfigurationErrorsKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.NpmDependencyExtensionKt$AddNpmDependencyExtensionProjectSetupAction$1$WhenMappings: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinJvmAndroidCompilation              : loaded 2 times (x 194B)
Class org.jetbrains.kotlin.gradle.targets.CreateDefaultCompilationsSideEffectKt$CreateDefaultCompilationsSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.targets.native.internal.AddKotlinPlatformIntegersSupportLibraryKt$AddKotlinPlatformIntegersSupportLibrary$1: loaded 2 times (x 91B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.ApplyUserDefinedAttributesKt             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.tooling.core.TypeUtils$WhenMappings                        : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.utils.FutureImpl                                    : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesLoader$isToolchainEnabled$1: loaded 2 times (x 71B)
Class [Lorg.jetbrains.kotlin.statistics.metrics.NumericalMetrics;                     : loaded 2 times (x 65B)
Class org.gradle.internal.InternalTransformer                                         : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.tasks.CompileUsingKotlinDaemonWithNormalization     : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory$PostConfigure$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.KotlinDomApiDependencyManagementKt$configureKotlinDomApiDefaultDependency$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.DefaultDevNpmDependencyExtension     : loaded 2 times (x 141B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.HasKotlinDependencies;                     : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$Companion$wireJvmTargetToToolchain$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.JsLikeEnvironmentChecker: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPlatformType                           : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildServiceKt        : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleBuildServices$Companion          : loaded 2 times (x 67B)
Class org.objectweb.asm.ClassWriter                                                   : loaded 2 times (x 102B)
Class com.google.common.collect.AbstractMapBasedMultimap$KeySet$1                     : loaded 2 times (x 78B)
Class com.google.common.cache.CacheLoader$UnsupportedLoadingOperationException        : loaded 2 times (x 78B)
Class com.google.common.base.CharMatcher$And                                          : loaded 2 times (x 108B)
Class org.gradle.internal.classloader.ClasspathUtil$1                                 : loaded 2 times (x 72B)
Class build_agisdczp7kn8uyp03dp4zx2vi                                                 : loaded 2 times (x 180B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.TransformActionUsingKotlinToolingDiagnostics$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.BuildFinishedListenerService$Companion$registerIfAbsent$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.ToolingDiagnosticRenderingOptions$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinJvmCompilerOptionsHelper                  : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$Companion$wireJvmTargetToToolchain$2: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.ProjectLocalConfigurations$ProjectLocalDisambiguation: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.apple.swiftexport.internal.SwiftExportInitKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$NullableStringGradleProperty: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.targets.native.KonanPropertiesBuildService          : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleBuildServices$detectKotlinPluginLoadedInMultipleProjects$onRegister$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.internal.ConfigurationCacheStartParameterAccessor$Factory: loaded 2 times (x 66B)
Class org.objectweb.asm.FieldVisitor                                                  : loaded 2 times (x 73B)
Class com.google.common.base.Objects                                                  : loaded 2 times (x 67B)
Class com.google.common.collect.IndexedImmutableSet                                   : loaded 2 times (x 146B)
Class com.google.common.collect.ImmutableMap$1                                        : loaded 2 times (x 77B)
Class com.google.common.collect.BiMap                                                 : loaded 2 times (x 66B)
Class org.gradle.api.internal.classpath.DefaultModuleRegistry$DefaultModule           : loaded 2 times (x 82B)
Class build_agisdczp7kn8uyp03dp4zx2vi$_run_closure1                                   : loaded 2 times (x 135B)
Class org.jetbrains.kotlin.gradle.internal.transforms.BuildToolsApiClasspathEntrySnapshotTransform$Parameters$Inject: loaded 2 times (x 114B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.CheckKotlinGradlePluginConfigurationErrors_Decorated: loaded 2 times (x 361B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationSourceSetInclusion$DefaultAddSourcesToCompileTask$addSources$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.WasmSourceSetsNotFoundChecker$wasmSourceSetRegex$2: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationSideEffect: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradlePluginExtensionPoint             : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.tooling.core.ExtrasUtilsKt                                 : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.buildtools.api.ExperimentalBuildToolsApi                   : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.targets.native.KonanPropertiesBuildService$Companion$registerIfAbsent$2$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.internal.IsInIdeaSyncValueSource$Inject             : loaded 2 times (x 105B)
Class org.jetbrains.kotlin.gradle.utils.ResourceUtilsKt                               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginWrapperKt$kotlinPluginVersionFromResources$1: loaded 2 times (x 74B)
Class com.google.common.base.Absent                                                   : loaded 2 times (x 76B)
Class com.google.common.primitives.Ints                                               : loaded 2 times (x 67B)
Class com.google.common.collect.AbstractIterator                                      : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinTargetSoftwareComponentImplKt$KotlinTargetSoftwareComponent$1$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.UsesKotlinJavaToolchain$DefaultImpls          : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.build.report.metrics.BuildAttributeKind$Companion          : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.KaptGenerateStubsConfig$Companion$configureLibraries$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationSourceSetInclusion$DefaultAddSourcesToCompileTask$addSources$2: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.AbstractKotlinCompile                         : loaded 2 times (x 450B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilationOutput                      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.CheckKotlinGradlePluginConfigurationErrorsKt$addDependsOnFromTasksThatShouldFailWhenErrorsReported$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$TVOS_X64                          : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinToolingDiagnosticsSetupActionKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.native.KonanPropertiesBuildService$Parameters_Decorated: loaded 2 times (x 130B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleBuildServices$Parameters$Inject  : loaded 2 times (x 102B)
Class org.jetbrains.kotlin.gradle.fus.BuildUidService$Inject                          : loaded 2 times (x 96B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginWrapperKt$kotlinPluginVersionFromResources$1$1: loaded 2 times (x 67B)
Class com.google.common.math.MathPreconditions                                        : loaded 2 times (x 67B)
Class com.google.common.collect.Multiset                                              : loaded 2 times (x 66B)
Class com.google.common.collect.ArrayListMultimapGwtSerializationDependencies         : loaded 2 times (x 168B)
Class com.google.common.collect.ObjectArrays                                          : loaded 2 times (x 67B)
Class com.google.common.collect.AbstractIndexedListIterator                           : loaded 2 times (x 92B)
Class build_evl13vpxjidvxohlw1uueqdij                                                 : loaded 2 times (x 176B)
Class org.jetbrains.kotlin.gradle.plugin.attributes.KlibPackaging$Companion           : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.tooling.core.Extras$Entry                                  : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.sources.InternalKotlinSourceSetKt            : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.Kapt3GradleSubplugin$Kapt3SubpluginContext : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinTargetArtifact                      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinJvmCompilerOptionsDefault_Decorated       : loaded 2 times (x 164B)
Class org.jetbrains.kotlin.gradle.plugin.HasKotlinDependencies                        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.internal.ClassLoadersCachingBuildService$Parameters_Decorated: loaded 2 times (x 130B)
Class org.jetbrains.kotlin.gradle.targets.native.toolchain.KotlinNativeBundleBuildService$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.BuildEventsListenerRegistryHolder_Decorated  : loaded 2 times (x 115B)
Class org.jetbrains.kotlin.gradle.plugin.StatisticsBuildFlowManager$subscribeForBuildResultAndConfigurationTimeMetrics$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.MetricContainer                   : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.old.Pre232IdeaKotlinBuildStatsMXBean: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinBuildStatsServicesRegistry  : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactoriesConfigurator   : loaded 2 times (x 72B)
Class com.google.common.collect.ImmutableMapEntrySet                                  : loaded 2 times (x 147B)
Class org.gradle.api.internal.classpath.Module                                        : loaded 2 times (x 66B)
Class build_evl13vpxjidvxohlw1uueqdij$_run_closure1                                   : loaded 2 times (x 136B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$getToolsJarFromJvm$1$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$ClasspathSnapshotProperties_Decorated: loaded 2 times (x 133B)
Class org.jetbrains.kotlin.gradle.internal.transforms.BuildToolsApiClasspathEntrySnapshotTransform$Parameters_Decorated: loaded 2 times (x 151B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationSourceSetInclusion$Companion$includedSourceSets$2: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinToolingDiagnosticsSetupActionKt$KotlinToolingDiagnosticsSetupAction$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.NpmDirectoryDependencyWithExternalsExtension: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinProjectSetupAction                     : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleFinishBuildHandler$Companion     : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.IsInIdeaSyncValueSource                    : loaded 2 times (x 74B)
Class com.google.common.cache.Weigher                                                 : loaded 2 times (x 66B)
Class org.gradle.api.internal.classpath.UnknownModuleException                        : loaded 2 times (x 78B)
Class io.invertase.gradle.build.ReactNativeShared$_applyDefaultExcludes_closure2$_closure5: loaded 2 times (x 137B)
Class build_evl13vpxjidvxohlw1uueqdij$_run_closure2                                   : loaded 2 times (x 136B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.InternalKotlinTargetComponentKt          : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.InternalKotlinTarget$DefaultImpls        : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycleImpl$executeCurrentStageAndScheduleNext$3: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationRegisterInSourceSetsConfigurator$configure$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinToolingDiagnosticsSetupActionKt$KotlinToolingDiagnosticsSetupAction$1$2: loaded 2 times (x 91B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.NpmDependencyWithExternalsExtension  : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinUsageContext                       : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinTargetsContainer                       : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.compilerRunner.GradleCliCommonizerKt                       : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.utils.ConfigurationsKt                              : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.StatisticsBuildFlowManager                   : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.BuildFusService$Parameters        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.PluginWrappersKt                             : loaded 2 times (x 67B)
Class com.google.common.base.Stopwatch                                                : loaded 2 times (x 68B)
Class com.google.common.cache.LocalCache$Strength                                     : loaded 2 times (x 77B)
Class com.google.common.cache.Cache                                                   : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.build.report.metrics.BuildMetricsReporter                  : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.InternalKotlinTargetKt                   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinToolingDiagnosticsSetupActionKt$KotlinToolingDiagnosticsSetupAction$1$3: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.utils.NativeCompilerDownloader                      : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.targets.js.dsl.KotlinTargetWithNodeJsDsl            : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.sources.DefaultKotlinSourceSetKt$defaultSourceSetLanguageSettingsChecker$1: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.sources.InternalKotlinSourceSet              : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinJvmPlugin                              : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.SyncLanguageSettingsWithKotlinExtensionSetupActionKt$SyncLanguageSettingsWithKotlinExtensionSetupAction$1: loaded 2 times (x 91B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinGradlePluginPublicDsl                     : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativeProperties                : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.fus.internal.SynchronizedConfigurationMetrics       : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.statistics.BuildSessionLogger                              : loaded 2 times (x 73B)
Class org.jetbrains.kotlin.gradle.plugin.internal.ConfigurationCacheStartParameterAccessor: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.internal.diagnostics.GradleCompatibilityCheck$DefaultCurrentGradleVersionProvider: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinBaseExtension                             : loaded 2 times (x 66B)
Class com.google.common.util.concurrent.AbstractFuture$Cancellation                   : loaded 2 times (x 68B)
Class com.google.common.collect.ImmutableMap$Builder                                  : loaded 2 times (x 77B)
Class com.google.common.base.NullnessCasts                                            : loaded 2 times (x 67B)
Class io.invertase.gradle.build.ReactNativeShared                                     : loaded 2 times (x 89B)
Class org.jetbrains.kotlin.gradle.utils.FutureKt$future$2$1                           : loaded 2 times (x 91B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.statistics.plugins.ObservablePlugins;      : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.internal.BuildIdService$Companion$registerIfAbsent$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationDependencyConfigurationsFactoriesKt$WhenMappings: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$IOS_ARM64                         : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.targets.js.dsl.KotlinWasmTargetDsl                  : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.DisabledCinteropCommonizationInHmppProjectChecker$runChecks$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.plugin.sources.ConsistencyCheck                     : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.sources.DefaultKotlinSourceSetKt$defaultSourceSetLanguageSettingsChecker$2: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinCommonToolOptions                         : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.AbstractKotlinPlugin                         : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$Companion$wireJvmTargetToJvm$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.ComposePluginSuggestApplyChecker: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinMultiplatformExtension                    : loaded 2 times (x 705B)
Class org.jetbrains.kotlin.gradle.plugin.BuildFinishAndConfigurationTimeMetricsFlowAction: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.PropertiesProvider$booleanProvider$1         : loaded 2 times (x 70B)
Class com.google.common.base.Present                                                  : loaded 2 times (x 77B)
Class com.google.common.cache.RemovalListener                                         : loaded 2 times (x 66B)
Class org.gradle.api.internal.DefaultClassPathRegistry                                : loaded 2 times (x 72B)
Class build_6s7imafvclc6gm72hnzimmguy$_run_closure1                                   : loaded 2 times (x 136B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinTargetSoftwareComponentImplKt$KotlinTargetSoftwareComponent$1$1$configuration$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.DefaultPeerNpmDependencyExtension$delegate$1: loaded 2 times (x 163B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.NpmDependency                        : loaded 2 times (x 93B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.DefaultNpmDependencyExtension        : loaded 2 times (x 148B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.NpmDependency$Scope                  : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.sources.FragmentConsistencyChecks$unstableFeaturesCheck$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.sources.DefaultKotlinSourceSet               : loaded 2 times (x 137B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.AndroidSourceSetLayoutV1SourceSetsNotFoundChecker: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.utils.ConfigurationsKt$sam$org_gradle_api_Action$0  : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactories$Parameters    : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.internal.JavaExecTaskParametersCompatibility$Factory: loaded 2 times (x 66B)
Class com.google.common.primitives.IntsMethodsForWeb                                  : loaded 2 times (x 67B)
Class com.google.common.collect.Multimap                                              : loaded 2 times (x 66B)
Class org.gradle.api.internal.ClassPathRegistry                                       : loaded 2 times (x 66B)
Class org.gradle.internal.classpath.DefaultClassPath$ImmutableUniqueList              : loaded 2 times (x 195B)
Class build_9cu49dh6h7478zg7wrc3wnbxn                                                 : loaded 2 times (x 177B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilation$DefaultImpls               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.utils.ProviderApiUtilsKt$providerWithLazyConvention$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$Companion$predefinedTargets$2     : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.sources.FragmentConsistencyChecks$unstableFeaturesCheck$2: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycleImpl                    : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinToolingDiagnostics$WasmWasiEnvironmentNotChosenExplicitly: loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinNativeKlibArtifactKt$KotlinNativeKlibArtifact$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinCommonCompilerOptionsDefault              : loaded 2 times (x 92B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinCommonCompilerToolOptionsDefault          : loaded 2 times (x 80B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesLoader$isUseXcodeMessageStyleEnabled$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.internal.ClassLoadersCachingBuildService$Companion$registerIfAbsent$2$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinBuildStatisticsUtilsKt$collectGeneralConfigurationTimeMetrics$statisticOverhead$1$2: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.StatisticsBuildFlowManager_Decorated         : loaded 2 times (x 115B)
Class org.jetbrains.kotlin.gradle.report.BuildReportMode$Companion                    : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactories$VariantImplementationFactory: loaded 2 times (x 66B)
Class com.google.common.base.ExtraObjectsMethodsForWeb                                : loaded 2 times (x 67B)
Class io.invertase.gradle.common.Utilities$isGradleVersionLT                          : loaded 2 times (x 142B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.ApplyUserDefinedAttributesKt$UserDefinedAttributesSetupAction$1$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.compilerRunner.CompilerSystemPropertiesService             : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationImpl$1  : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.SemVer$Companion                     : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$LINUX_ARM64                       : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$LINUX_X64                         : loaded 2 times (x 74B)
Class [Lorg.jetbrains.kotlin.konan.target.Architecture;                               : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.targets.native.SetupEmbedAndSignAppleFrameworkTaskSideEffectKt$special$$inlined$KotlinTargetSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinUsages$KotlinCinteropCompatibility : loaded 2 times (x 73B)
Class org.jetbrains.kotlin.gradle.dsl.ExplicitApiMode                                 : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.utils.GradleAttributesContainerUtilsKt              : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactories$Companion     : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.internal.CustomPropertiesFileValueSource$Parameters_Decorated: loaded 2 times (x 130B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinTopLevelExtensionConfig                   : loaded 2 times (x 66B)
Class com.google.common.io.ByteSink                                                   : loaded 2 times (x 72B)
Class com.google.common.cache.CacheBuilder                                            : loaded 2 times (x 68B)
Class com.google.common.collect.SingletonImmutableList                                : loaded 2 times (x 203B)
Class com.google.common.collect.RegularImmutableMap$KeySet                            : loaded 2 times (x 146B)
Class org.gradle.internal.classpath.ClassPath                                         : loaded 2 times (x 66B)
Class org.gradle.api.UncheckedIOException                                             : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.gradle.incremental.IncrementalModuleInfoProvider           : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationImpl$2  : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.dsl.PlatformSourceSetConventionsChecker$runChecks$1 : loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationProcessorSideEffectKt$KotlinCompilationProcessorSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinJvmCompilerOptionsDefault                 : loaded 2 times (x 102B)
Class org.jetbrains.kotlin.gradle.internal.UsesClassLoadersCachingBuildService        : loaded 2 times (x 66B)
Class com.google.common.io.Files$FileByteSource                                       : loaded 2 times (x 81B)
Class com.google.common.collect.ImmutableList$ReverseImmutableList                    : loaded 2 times (x 204B)
Class com.google.common.base.JdkPattern                                               : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinStdlibConfigurationMetrics$collectMetrics$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.compilerRunner.KotlinCompilerArgumentsLogLevel$Companion   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.DefaultKotlinBasePlugin$addKotlinCompilerConfiguration$3$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.AbstractKotlinCompile$defaultKotlinJavaToolchain$1: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.daemon.common.MultiModuleICSettings                        : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.cli.common.arguments.Freezable                             : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.internal.transforms.BuildToolsApiClasspathEntrySnapshotTransform$Parameters: loaded 2 times (x 83B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilationInfoKt                      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile                                 : loaded 2 times (x 494B)
Class org.jetbrains.kotlin.gradle.plugin.AbstractKotlinPluginWrapper                  : loaded 2 times (x 82B)
Class org.jetbrains.kotlin.gradle.internal.StdlibDependencyManagementKt               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.PlatformSourceSetConventionsChecker$runChecks$2 : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$MINGW_X64                         : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.utils.ObservableSet                                 : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinToolingDiagnosticsCollectorKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.HierarchyAttributeContainer              : loaded 2 times (x 79B)
Class org.jetbrains.kotlin.gradle.tasks.UsesKotlinJavaToolchain                       : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.DefaultKotlinBasePlugin$apply$1              : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.statistics.metrics.StringOverridePolicy$OVERRIDE           : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$Params_Decorated: loaded 2 times (x 130B)
Class org.objectweb.asm.SymbolTable$Entry                                             : loaded 2 times (x 70B)
Class com.google.errorprone.annotations.DoNotMock                                     : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinTargetComponentWithCoordinatesAndPublication: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.incremental.UsesIncrementalModuleInfoBuildService   : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$ANDROID_X86                       : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.dsl.PlatformSourceSetConventionsChecker$runChecks$3 : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.RegisterKotlinPluginExtensionsKt             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.resources.resolve.KotlinTargetResourcesResolutionStrategy: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.utils.CompilerOptionsKt$configureExperimentalTryNext$1$1: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.utils.CompilerOptionsKt                             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.utils.SingleAction                                  : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.plugin.PropertiesProvider$Companion                 : loaded 2 times (x 67B)
Class org.objectweb.asm.Edge                                                          : loaded 2 times (x 68B)
Class com.google.common.base.Suppliers$SupplierOfInstance                             : loaded 2 times (x 75B)
Class com.google.common.collect.Iterators$1                                           : loaded 2 times (x 77B)
Class Build_gradle$2$1                                                                : loaded 2 times (x 70B)
Class build_9kn57pj55amg75ewdgalkhlsr                                                 : loaded 2 times (x 184B)
Class build_1st6b4zcxn0lddyz8z0am5m89                                                 : loaded 2 times (x 176B)
Class org.jetbrains.kotlin.gradle.internal.utils.CollectionsKt                        : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$defaultKotlinJavaToolchain$1    : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinJavaToolchain$JavaToolchainSetter       : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.utils.NativeCompilerDownloader$Companion            : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.PlatformSourceSetConventionsChecker$runChecks$4 : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.JsLikeEnvironmentChecker$runChecks$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.UsesKotlinToolingDiagnostics     : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.KonanHomeConflictDeclarationChecker: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinToolingDiagnostics$WasmJsEnvironmentNotChosenExplicitly: loaded 2 times (x 69B)
Class org.jetbrains.kotlin.tooling.core.ExtrasPropertyKt$extrasLazyProperty$1         : loaded 2 times (x 90B)
Class org.jetbrains.kotlin.statistics.metrics.BooleanAnonymizationPolicy$SAFE         : loaded 2 times (x 81B)
Class com.google.common.cache.LocalCache$ComputingValueReference                      : loaded 2 times (x 92B)
Class com.google.common.collect.SingletonImmutableSet                                 : loaded 2 times (x 142B)
Class com.google.common.collect.ImmutableMap$IteratorBasedImmutableMap                : loaded 2 times (x 121B)
Class io.invertase.gradle.build.ReactNativeModule$_applyAndroidVersions_closure2$_closure6: loaded 2 times (x 139B)
Class com.google.common.base.Equivalence$Identity                                     : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.DefaultKotlinCompilationSourceSetsContainer$allKotlinSourceSetsImpl$1$1: loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.DefaultKotlinCompilationSourceSetsContainer: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$TVOS_SIMULATOR_ARM64              : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$Companion                         : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycle$Stage$Companion        : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.DeprecatedKotlinNativeTargetsChecker: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.targets.jvm.KotlinJvmCompilationWireJavaSourcesSideEffectKt$KotlinJvmCompilationWireJavaSourcesSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.targets.native.ConfigureFrameworkExportSideEffectKt$special$$inlined$KotlinTargetSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.tooling.core.AbstractExtras                                : loaded 2 times (x 137B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesLoader$shouldUseEmbeddableCompilerJar$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.fus.BuildUidService$Companion$registerIfAbsent$1    : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.internal.diagnostics.AgpCompatibilityCheck$AndroidGradlePluginVersionProvider: loaded 2 times (x 66B)
Class com.google.common.cache.LocalCache$Segment                                      : loaded 2 times (x 150B)
Class org.jetbrains.kotlin.build.report.metrics.BuildTimes                            : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$ClasspathSnapshotProperties     : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.internal.StdlibDependencyManagementKt$addStdlibDependency$1$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$2$1     : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.TransformActionUsingKotlinToolingDiagnostics: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.dsl.PlatformSourceSetConventionsChecker$runChecks$6 : loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.plugin.sources.DefaultKotlinSourceSet_Decorated     : loaded 2 times (x 188B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinCompilationsKt                     : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.utils.GradleUtilsKt$registerClassLoaderScopedBuildService$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.targets.native.KonanPropertiesBuildService$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.statistics.metrics.StringOverridePolicy                    : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.DefaultKotlinBuildStatsBeanService: loaded 2 times (x 88B)
Class org.jetbrains.kotlin.gradle.plugin.PropertiesProvider$propertyWithDeprecatedValues$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.internal.diagnostics.AgpCompatibilityCheck$AndroidGradlePluginVersionProvider$Default: loaded 2 times (x 70B)
Class [Lorg.objectweb.asm.SymbolTable$Entry;                                          : loaded 2 times (x 65B)
Class [Lcom.google.common.base.Supplier;                                              : loaded 2 times (x 65B)
Class com.google.common.collect.SortedIterable                                        : loaded 2 times (x 66B)
Class com.google.common.cache.CacheLoader                                             : loaded 2 times (x 70B)
Class com.google.common.collect.Iterators$4                                           : loaded 2 times (x 78B)
Class com.google.common.collect.ImmutableBiMap                                        : loaded 2 times (x 139B)
Class com.google.common.collect.AbstractListMultimap                                  : loaded 2 times (x 168B)
Class org.gradle.internal.installation.GradleInstallation$1                           : loaded 2 times (x 71B)
Class org.gradle.api.internal.classpath.ModuleRegistry                                : loaded 2 times (x 66B)
Class io.invertase.gradle.build.ReactNativeModule$_applyReactNativeDependency_closure5: loaded 2 times (x 140B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycle$ProjectConfigurationResult: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.KotlinDependenciesManagementKt$configureDefaultVersionsResolutionStrategy$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.internal.StdlibDependencyManagementKt$configureStdlibDefaultDependency$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.utils.ConfigurationCacheOpaqueValue                 : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.model.builder.KotlinModelBuilder$Companion          : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCreateResourcesTaskSideEffectKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.native.internal.SetupKotlinNativePlatformDependenciesAndStdlibKt: loaded 2 times (x 67B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.mpp.resources.resolve.KotlinTargetResourcesResolutionStrategy;: loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinUsages                             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesLoader$Companion: loaded 2 times (x 67B)
Class [Lorg.jetbrains.kotlin.build.report.metrics.GradleBuildPerformanceMetric;       : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinBuildStatsBeanService$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.BuildFusService$Companion         : loaded 2 times (x 67B)
Class com.google.common.util.concurrent.internal.InternalFutureFailureAccess          : loaded 2 times (x 68B)
Class com.google.common.collect.Iterators$5                                           : loaded 2 times (x 78B)
Class com.google.common.collect.ImmutableSet$JdkBackedSetBuilderImpl                  : loaded 2 times (x 72B)
Class com.google.common.base.Splitter$SplittingIterator                               : loaded 2 times (x 80B)
Class build_6k1vuntnni446s9ebkvsvp2hk$_run_closure1                                   : loaded 2 times (x 135B)
Class org.jetbrains.kotlin.gradle.plugin.HasCompilerOptions                           : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.DefaultKotlinCompilationFriendPathsResolver$FriendArtifactResolver: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$WATCHOS_SIMULATOR_ARM64           : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.targets.js.ir.KotlinJsIrTarget                      : loaded 2 times (x 258B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.DefaultKotlinUsageContext                : loaded 2 times (x 104B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycleImpl$start$3            : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinJvmExtension                              : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinMultiplatformPluginWrapper             : loaded 2 times (x 82B)
Class [Lorg.jetbrains.kotlin.statistics.metrics.BooleanMetrics;                       : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$Companion: loaded 2 times (x 67B)
Class com.google.common.cache.LocalCache$LocalManualCache$1                           : loaded 2 times (x 71B)
Class com.google.common.base.CharMatcher$ForPredicate                                 : loaded 2 times (x 108B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationConfigurationsContainer: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.internal.StdlibDependencyManagementKt$addStdlibDependency$1: loaded 2 times (x 71B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycle$CoroutineStart;      : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.dsl.IosSourceSetConventionChecker                   : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinGradleProjectChecker       : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinMultiplatformSourceSetConventions         : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.sources.KotlinSourceSetFactory$Companion     : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.statistics.metrics.NumberOverridePolicy$SUM                : loaded 2 times (x 81B)
Class com.google.common.collect.ImmutableCollection                                   : loaded 2 times (x 121B)
Class com.google.common.base.CharMatcher$AnyOf                                        : loaded 2 times (x 108B)
Class org.gradle.api.internal.classpath.DefaultModuleRegistry                         : loaded 2 times (x 82B)
Class org.jetbrains.kotlin.gradle.plugin.internal.state.TaskExecutionResults          : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.compilerRunner.KotlinCompilerArgumentsLogLevel             : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.CompositePostConfigure: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.targets.native.internal.CInteropConfigurationsKt    : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.BuildFusService$Parameters$Inject : loaded 2 times (x 108B)
Class org.jetbrains.kotlin.build.report.metrics.GradleBuildTime$Companion             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.statistics.metrics.StatisticsValuesConsumer                : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.AndroidGradlePluginVersion$Companion         : loaded 2 times (x 67B)
Class com.google.common.util.concurrent.ListenableFuture                              : loaded 2 times (x 66B)
Class com.google.common.cache.ReferenceEntry                                          : loaded 2 times (x 66B)
Class com.google.common.collect.ImmutableSet$CachingAsList                            : loaded 2 times (x 143B)
Class com.google.common.base.Converter                                                : loaded 2 times (x 86B)
Class com.google.common.collect.Lists                                                 : loaded 2 times (x 67B)
Class io.invertase.gradle.build.ReactNativePlugin                                     : loaded 2 times (x 92B)
Class org.jetbrains.kotlin.gradle.plugin.DefaultKotlinBasePlugin$addKotlinCompilerConfiguration$2$1$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompilerExecutionStrategy$Companion     : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.CompilerPluginConfig                         : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.model.builder.KotlinModelBuilder                    : loaded 2 times (x 72B)
Class [Lorg.jetbrains.kotlin.tooling.core.HasMutableExtras;                           : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.GradleDeprecatedPropertyChecker: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinJsKlibArtifactKt$KotlinJsKlibArtifact$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.targets.CreateTargetConfigurationsSideEffectKt      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.ApplyUserDefinedAttributesKt$UserDefinedAttributesSetupAction$1: loaded 2 times (x 91B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinAndroidTarget                      : loaded 2 times (x 161B)
Class org.jetbrains.kotlin.gradle.plugin.DefaultKotlinBasePlugin$addKotlinCompilerConfiguration$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.NonSynchronizedMetricsContainer   : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.gradle.plugin.internal.DefaultCompatibilityConventionRegistrar: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.PropertiesProvider                           : loaded 2 times (x 69B)
Class com.google.common.util.concurrent.AbstractFuture                                : loaded 2 times (x 102B)
Class com.google.common.base.FunctionalEquivalence                                    : loaded 2 times (x 79B)
Class com.google.common.collect.Iterators$9                                           : loaded 2 times (x 77B)
Class com.google.common.collect.AbstractMapBasedMultimap                              : loaded 2 times (x 135B)
Class com.google.common.collect.ImmutableAsList                                       : loaded 2 times (x 205B)
Class io.invertase.gradle.build.ReactNativeModule                                     : loaded 2 times (x 89B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.MppSourcesJarKt$sourcesJarTask$1         : loaded 2 times (x 91B)
Class [Lorg.jetbrains.kotlin.gradle.dsl.jvm.JvmTargetValidationMode;                  : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$javaVersion$1      : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory$KotlinCompilationOutputFactory: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.konan.target.TargetSupportException                        : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.gradle.internal.IsInIdeaEnvironmentValueSource             : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilationToRunnableFiles             : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.tooling.BuildKotlinToolingMetadataTaskKt            : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.ProjectLocalConfigurations$setupAttributesMatchingStrategy$1$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.targets.native.toolchain.KotlinNativeBundleBuildService$Companion$registerIfAbsent$2$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.statistics.BuildSessionLogger$Companion                    : loaded 2 times (x 67B)
Class org.objectweb.asm.RecordComponentVisitor                                        : loaded 2 times (x 73B)
Class com.google.common.cache.CacheLoader$SupplierToCacheLoader                       : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.MppSourcesJarKt                          : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.SemverKt                             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.StdlibDependencyManagementKt$isStdlibAddedByUser$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinCompilationsKt$sam$java_util_concurrent_Callable$0: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompilerPluginData                      : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.compilerRunner.CompilerSystemPropertiesService$Companion$registerIfAbsent$2$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilationInfo                        : loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.tasks.BaseKotlinCompile                             : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.internal.UsesBuildIdProviderService          : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationSourceSetInclusion$include$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory$KotlinCompilationDependencyConfigurationsFactory: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinProjectExtension$jvmToolchain$1           : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycleCoroutineContextElement$Key: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinToolingDiagnosticsSetupActionKt$KotlinToolingDiagnosticsSetupAction$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.tooling.core.ExtrasPropertyKt                              : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinProjectExtensionKt                        : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.ClassLoadersCachingBuildService$Companion$registerIfAbsent$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleBuildServices$Parameters_Decorated: loaded 2 times (x 130B)
Class org.jetbrains.kotlin.gradle.plugin.DefaultKotlinBasePlugin$addKotlinCompilerConfiguration$3: loaded 2 times (x 71B)
Class [Lorg.jetbrains.kotlin.statistics.metrics.IMetricContainerFactory;              : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.internal.IdeaKt                                     : loaded 2 times (x 67B)
Class com.google.common.collect.Maps$EntryTransformer                                 : loaded 2 times (x 66B)
Class org.gradle.internal.service.ServiceLookupException                              : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.gradle.internal.StdlibDependencyManagementKt$isStdlibAddedByUser$2: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.tasks.GradleCompileTaskProvider_Decorated           : loaded 2 times (x 117B)
Class org.jetbrains.kotlin.gradle.plugin.hierarchy.KotlinSourceSetTreeClassifierKt$sourceSetTreeClassifier$2: loaded 2 times (x 74B)
Class [Lorg.jetbrains.kotlin.tooling.core.KotlinToolingVersion$Maturity;              : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.konan.target.HostManager                                   : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.NpmDirectoryDependencyExtension      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinUsageContext$MavenScope            : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.targets.native.KotilnNativeConfigureBinariesSideEffectKt$special$$inlined$KotlinTargetSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinProjectSetupAction$Companion           : loaded 2 times (x 67B)
Class com.google.common.collect.ImmutableSortedSet                                    : loaded 2 times (x 295B)
Class [Lcom.google.common.cache.LocalCache$EntryFactory;                              : loaded 2 times (x 65B)
Class org.gradle.internal.classloader.ClassLoaderFactory                              : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$currentJvmJdkToolsJar$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.SubpluginEnvironment                         : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.internal.StdlibDependencyManagementKt$addStdlibDependency$1$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.sources.AbstractKotlinSourceSet              : loaded 2 times (x 137B)
Class org.jetbrains.kotlin.gradle.tooling.BuildKotlinToolingMetadataTaskKt$RegisterBuildKotlinToolingMetadataTask$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.compilerRunner.GradleCliCommonizerKt$maybeCreateCommonizerClasspathConfiguration$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesLoader$nativeCacheKind$1: loaded 2 times (x 71B)
Class [Lorg.jetbrains.kotlin.build.report.metrics.ValueType;                          : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.internal.diagnostics.AgpCompatibilityCheck          : loaded 2 times (x 67B)
Class com.google.common.collect.ImmutableMap$SerializedForm                           : loaded 2 times (x 69B)
Class com.google.common.cache.LocalCache$LocalManualCache                             : loaded 2 times (x 95B)
Class com.google.common.base.CharMatcher$IsNot                                        : loaded 2 times (x 107B)
Class com.google.common.base.CommonPattern                                            : loaded 2 times (x 70B)
Class org.gradle.internal.service.CachingServiceLocator                               : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.gradle.dsl.jvm.JvmTargetValidationMode                     : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.BuildFinishedListenerService$Companion$registerIfAbsent$2$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinTargetConfiguratorKt$WhenMappings      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.KotlinTestDependencyManagementKt$configureKotlinTestDependency$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.tooling.core.KotlinToolingVersionKt                        : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.OverriddenKotlinNativeHomeChecker$StdlibExistenceCheckerValueSource$Params_Decorated: loaded 2 times (x 130B)
Class org.jetbrains.kotlin.project.model.LanguageSettings                             : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycleKt$kotlinPluginLifecycle$2: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.targets.native.KonanPropertiesBuildService$Parameters: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.BuildFusService$Companion$registerIfAbsent$1$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.internal.CompatibilityConventionRegistrar$Factory: loaded 2 times (x 66B)
Class com.google.common.collect.AbstractMapBasedMultimap$KeySet                       : loaded 2 times (x 133B)
Class com.google.common.collect.RegularImmutableSortedSet                             : loaded 2 times (x 295B)
Class com.google.common.math.IntMath                                                  : loaded 2 times (x 67B)
Class io.invertase.gradle.build.ReactNativeShared$_applyPackageVersion_closure3$_closure6: loaded 2 times (x 140B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinMetadataTarget                     : loaded 2 times (x 163B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.CheckKotlinGradlePluginConfigurationErrorsKt$locateOrRegisterCheckKotlinGradlePluginErrorsTask$taskProvider$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.InternalKotlinCompilation$DefaultImpls   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationSourceSetInclusion$DefaultAddSourcesToCompileTask: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.internal.KotlinTestDependencyManagementKt$configureKotlinTestDependency$2: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.dsl.PlatformSourceSetConventionsChecker$runChecks$$inlined$CheckedPlatformInfo$default$1: loaded 2 times (x 74B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycle$Stage;               : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinTargetArtifact$Companion            : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCreateLifecycleTasksSideEffectKt$KotlinCreateLifecycleTasksSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.targets.metadata.KotlinMetadataTargetConfiguratorKt$metadataCompilationsCreated$2: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinGradlePluginDsl                           : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.tooling.core.MutableExtras                                 : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleBuildServices$detectKotlinPluginLoadedInMultipleProjects$onRegister$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.BuildFinishAndConfigurationTimeMetricsFlowAction$Parameters_Decorated: loaded 2 times (x 139B)
Class org.jetbrains.kotlin.gradle.plugin.BuildFinishAndConfigurationTimeMetricsFlowAction$Parameters: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.build.report.metrics.BuildTime                             : loaded 2 times (x 66B)
Class com.google.common.collect.ImmutableMapEntry                                     : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.CheckKotlinGradlePluginConfigurationErrorsKt$locateOrRegisterCheckKotlinGradlePluginErrorsTask$taskProvider$1$2: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.tasks.CompileUsingKotlinDaemon                      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinWithJavaTarget                     : loaded 2 times (x 162B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCreateNativeCompileTasksSideEffectKt$special$$inlined$KotlinCompilationSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.targets.KotlinTargetSideEffect$Companion            : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinSingleJavaTargetExtension                 : loaded 2 times (x 120B)
Class org.jetbrains.kotlin.gradle.utils.StoredPropertyKt$extrasStoredProperty$1       : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycle$IllegalLifecycleException: loaded 2 times (x 78B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesLoader$userProvidedNativeHome$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleFinishBuildHandler               : loaded 2 times (x 68B)
Class [Lorg.jetbrains.kotlin.build.report.metrics.BuildPerformanceMetric;             : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.statistics.metrics.StringOverridePolicy$CONCAT             : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinTopLevelExtension                         : loaded 2 times (x 66B)
Class com.google.common.collect.ImmutableMultimap                                     : loaded 2 times (x 143B)
Class com.google.common.base.Equivalence                                              : loaded 2 times (x 78B)
Class com.google.common.collect.ImmutableMapEntry$NonTerminalImmutableMapEntry        : loaded 2 times (x 81B)
Class com.google.common.base.Function                                                 : loaded 2 times (x 66B)
Class io.invertase.gradle.build.ReactNativeModule$_applyAndroidVersions_closure2      : loaded 2 times (x 139B)
Class org.jetbrains.kotlin.gradle.plugin.ProjectLocalConfigurationsKt                 : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.internal.KotlinDomApiDependencyManagementKt$addKotlinDomApiDependency$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.KotlinTargetAlreadyDeclaredChecker$runChecks$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinTargetWithBinaries                 : loaded 2 times (x 159B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.KotlinSourceSet;                           : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.JsEnvironmentChecker$1  : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinToolingDiagnostics$JsLikeEnvironmentNotChosenExplicitly: loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinTargetsContainerWithPresets            : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.utils.StoredLazyProperty$getValue$1                 : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.statistics.metrics.IMetricContainerFactory                 : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.statistics.MetricValueValidationFailed                     : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.gradle.plugin.PropertiesProvider$localProperties$2         : loaded 2 times (x 75B)
Class com.google.common.collect.IndexedImmutableSet$1                                 : loaded 2 times (x 208B)
Class com.google.common.cache.CacheBuilder$OneWeigher                                 : loaded 2 times (x 78B)
Class com.google.common.collect.RegularImmutableList                                  : loaded 2 times (x 208B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.DefaultKotlinCompilationFriendPathsResolver$resolveFriendPaths$1$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.build.report.metrics.BuildMetricsReporterImpl              : loaded 2 times (x 94B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.KaptGenerateStubsConfig         : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$1$classpathConfiguration$jvmToolchain$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.SemVer                               : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesLoader$konanDataDirProperty$1$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.JsEnvironmentChecker$2  : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.utils.WhenPluginEnabledKt$whenPluginsEnabled$1      : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.AbstractKotlinCompileTool                     : loaded 2 times (x 362B)
Class org.jetbrains.kotlin.gradle.internal.report.BuildScanApi                        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.FlowActionBuildFusService$Companion$registerIfAbsentImpl$1$1: loaded 2 times (x 70B)
Class [Lorg.jetbrains.kotlin.gradle.report.BuildReportType;                           : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinBuildStatsConfiguration     : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$localProperties$2: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinVariantWithCoordinates             : loaded 2 times (x 104B)
Class org.gradle.internal.classloader.ClasspathUtil                                   : loaded 2 times (x 67B)
Class io.invertase.gradle.build.ProjectExtension$getVersionsRoot$2                    : loaded 2 times (x 148B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinCompile                                   : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.DefaultKotlinCompilationFriendPathsResolver$DefaultFriendArtifactResolver: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.InternalGradlePropertiesUsageChecker$runChecks$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.utils.StringUtilsKt$lowerCamelCaseName$1            : loaded 2 times (x 121B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.JsEnvironmentChecker$3  : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinLegacyMetadataArtifactKt            : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.JvmTarget$Companion                             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesLoader$downloadFromMaven$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.internal.DefaultConfigurationCacheStartParameterAccessor_Decorated: loaded 2 times (x 121B)
Class org.jetbrains.kotlin.gradle.plugin.internal.DefaultConfigurationCacheStartParameterAccessor: loaded 2 times (x 73B)
Class org.jetbrains.kotlin.tooling.core.HasExtras                                     : loaded 2 times (x 66B)
Class com.google.common.util.concurrent.Uninterruptibles                              : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilationInfo$TCS$friendPaths$1      : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.fus.internal.SynchronizedConfigurationMetrics$add$1 : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.sources.DefaultLanguageSettingsBuilder$freeCompilerArgsProvider$1: loaded 2 times (x 91B)
Class org.jetbrains.kotlin.konan.target.HostManager$Companion                         : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.DevNpmDependencyExtension            : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.PreHmppDependenciesUsageChecker: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.targets.CreateArtifactsSideEffectKt                 : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.AbstractKotlinMultiplatformPluginWrapper     : loaded 2 times (x 82B)
Class org.jetbrains.kotlin.gradle.utils.GradleConfigurationUtilsKt$addGradlePluginMetadataAttributes$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.internal.DefaultProjectIsolationStartParameterAccessor: loaded 2 times (x 73B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinAndroidExtension                          : loaded 2 times (x 66B)
Class com.google.common.io.CharSource                                                 : loaded 2 times (x 81B)
Class com.google.common.collect.Maps                                                  : loaded 2 times (x 67B)
Class org.gradle.api.specs.Spec                                                       : loaded 2 times (x 66B)
Class io.invertase.gradle.build.ReactNativeShared$_applyPackageVersion_closure3       : loaded 2 times (x 140B)
Class org.jetbrains.kotlin.gradle.fus.internal.SynchronizedConfigurationMetrics$add$2 : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$disableMultiModuleIC$2          : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.GradleCompileTaskProviderKt                   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.SubpluginEnvironment$Companion               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.ApplyUserDefinedAttributesKt$UserDefinedAttributesSetupAction$1$1$3: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationImpl$Params: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.konan.target.UtilsKt                                       : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tooling.BuildKotlinToolingMetadataTask              : loaded 2 times (x 313B)
Class org.jetbrains.kotlin.gradle.plugin.internal.CompatibilityConventionRegistrarKt  : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinJvmJarArtifactKt                    : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.CreateCInteropTasksSideEffectKt$special$$inlined$KotlinCompilationSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.FinalizeConfigurationFusMetricActionKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.resources.resolve.KotlinTargetResourcesResolutionStrategy$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.native.toolchain.KotlinNativeBundleBuildService$Companion$registerIfAbsent$2$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.build.report.metrics.GradleBuildPerformanceMetric$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinBuildStatsBeanService       : loaded 2 times (x 83B)
Class org.jetbrains.kotlin.gradle.plugin.internal.MavenPublicationComponentAccessor$Factory: loaded 2 times (x 66B)
Class build_6t0aaf1vxqnq7aqxdnrvcooc8                                                 : loaded 2 times (x 176B)
Class [Lcom.google.common.collect.ImmutableEntry;                                     : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig$1$1 : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$kotlinOptions$1                 : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativeProperties$Companion      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.utils.StringUtilsKt                                 : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.ConfigureBuildSideEffectKt$ConfigureBuildSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.tooling.core.ExtrasLazyProperty                            : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.utils.CompletableFuture                             : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.build.report.metrics.BuildPerformanceMetric                : loaded 2 times (x 66B)
Class [Lorg.jetbrains.kotlin.statistics.metrics.StringOverridePolicy;                 : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.internal.JavaExecTaskParametersCompatibility : loaded 2 times (x 66B)
Class com.google.common.cache.LocalCache$LoadingValueReference                        : loaded 2 times (x 92B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig$getClasspathSnapshotDir$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig$1$2 : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.ConfigurationMetricsBuildFusParameters: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfigKt$explicitApiMode$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.OverriddenKotlinNativeHomeChecker$StdlibExistenceCheckerValueSource$Params$Inject: loaded 2 times (x 102B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.CommonMainOrTestWithDependsOnChecker: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinNativeHostSpecificMetadataArtifactKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCreateLifecycleTasksSideEffectKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinTarget                                 : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.utils.ConfigurationsKt$createResolvable$1           : loaded 2 times (x 74B)
Class [Lorg.jetbrains.kotlin.statistics.metrics.BooleanAnonymizationPolicy;           : loaded 2 times (x 65B)
Class com.google.common.collect.CollectSpliterators                                   : loaded 2 times (x 67B)
Class com.google.common.base.Platform                                                 : loaded 2 times (x 67B)
Class com.google.common.base.PairwiseEquivalence                                      : loaded 2 times (x 79B)
Class org.jetbrains.kotlin.gradle.internal.KotlinDependenciesManagementKt$allNonProjectDependencies$1: loaded 2 times (x 70B)
Class [Lorg.jetbrains.kotlin.build.report.metrics.BuildAttributeKind;                 : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.tasks.K2MultiplatformStructure                      : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.compilerRunner.CompilerSystemPropertiesService$Parameters  : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationSourceSetsContainer: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinNativeKlibArtifactKt                : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.ProjectLocalConfigurations                   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinUsages$KotlinUsagesDisambiguation  : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.tooling.core.Extras$Key$Companion                          : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.tooling.core.Extras$Key                                    : loaded 2 times (x 68B)
Class [Lorg.objectweb.asm.AnnotationWriter;                                           : loaded 2 times (x 65B)
Class com.google.common.collect.Maps$8                                                : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.gradle.plugin.PropertiesProvider$booleanPropertyWithDeprecatedValues$1: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig$1$4 : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.CheckKotlinGradlePluginConfigurationErrors: loaded 2 times (x 313B)
Class org.jetbrains.kotlin.gradle.plugin.sources.DefaultKotlinSourceSetKt             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinJvmOptions                                : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.HierarchyAttributeContainer$1            : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycle                        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinJsPluginWrapper                        : loaded 2 times (x 82B)
Class org.jetbrains.kotlin.gradle.dsl.ToolchainSupport                                : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.tooling.core.Extras                                        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.InternalKotlinTarget                     : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.statistics.metrics.NumberAnonymizationPolicy$RANDOM_10_PERCENT: loaded 2 times (x 81B)
Class org.jetbrains.kotlin.statistics.metrics.StringMetrics                           : loaded 2 times (x 76B)
Class com.google.common.collect.ImmutableRangeSet$Builder                             : loaded 2 times (x 73B)
Class com.google.common.collect.Sets$SetView                                          : loaded 2 times (x 134B)
Class com.google.common.cache.LocalCache$StrongValueReference                         : loaded 2 times (x 86B)
Class org.jetbrains.kotlin.gradle.fus.internal.SynchronizedConfigurationMetrics$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.CompilerPluginOptions                         : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinTargetSoftwareComponentImpl        : loaded 2 times (x 86B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.DefaultKotlinUsageContext$PublishOnlyIf  : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig$1$5 : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinCommonCompilerOptionsHelper               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinJvmPlugin$Companion                    : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinToolingDiagnostics         : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinMetadataArtifactKt                  : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.CreateArtifactsSideEffectKt$CreateArtifactsSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$property$1$valueFromGradleAndLocalProperties$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.PropertiesProvider$useClasspathSnapshot$1    : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginWrapperKt$kotlinToolingVersion$2 : loaded 2 times (x 74B)
Class org.objectweb.asm.Type                                                          : loaded 2 times (x 68B)
Class com.google.common.graph.SuccessorsFunction                                      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleBuildServices$Inject             : loaded 2 times (x 96B)
Class org.gradle.internal.classloader.VisitableURLClassLoader$InstrumentingVisitableURLClassLoader: loaded 2 times (x 119B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.DefaultKotlinUsageContext$1              : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig$1$6 : loaded 2 times (x 71B)
Class [Lorg.jetbrains.kotlin.gradle.tasks.KotlinCompilerExecutionStrategy;            : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.tasks.FailedCompilationException                    : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationImpl$associateWith$2: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.hierarchy.KotlinSourceSetTreeClassifier      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationAssociator: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.konan.target.UtilsKt$evaluatePresetName$1                  : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinNativeTarget                       : loaded 2 times (x 169B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.KotlinSourceSetTreeDependsOnMismatchChecker$runChecks$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.PreHmppDependenciesUsageChecker$runChecks$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.utils.ProjectExtensionsKt                           : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinSourceSetTree$Companion                : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinGradleProjectChecker$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleBuildServices$Parameters         : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.BuildFusService$Companion$registerIfAbsent$1$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.statistics.fileloggers.MetricsContainer$Companion          : loaded 2 times (x 67B)
Class org.objectweb.asm.ByteVector                                                    : loaded 2 times (x 75B)
Class com.google.common.io.Files$FileByteSink                                         : loaded 2 times (x 73B)
Class [Lcom.google.common.cache.CacheBuilder$NullListener;                            : loaded 2 times (x 65B)
Class com.google.common.math.IntMath$1                                                : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.ClassLoadersCachingBuildService$Companion$registerIfAbsent$2$1$1: loaded 2 times (x 71B)
Class org.gradle.internal.classloader.VisitableURLClassLoader$Spec                    : loaded 2 times (x 70B)
Class org.gradle.internal.classpath.DefaultClassPath                                  : loaded 2 times (x 86B)
Class io.invertase.gradle.build.ProjectExtension$getOption$3                          : loaded 2 times (x 148B)
Class org.jetbrains.kotlin.gradle.plugin.DefaultKotlinBasePlugin$addKotlinCompilerConfiguration$2$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinSoftwareComponentKt                : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationSourceSetInclusion$AddSourcesToCompileTask: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.konan.target.Architecture                                  : loaded 2 times (x 75B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.HasProject;                                : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.utils.MutableObservableSetImpl                      : loaded 2 times (x 159B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinTasksProvider                           : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinJsCompilerTypeHolder                   : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.dsl.DefaultToolchainSupport$wireToolchainToTasks$1  : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinCommonCompilerOptions                     : loaded 2 times (x 66B)
Class [Lorg.jetbrains.kotlin.build.report.metrics.GradleBuildTime;                    : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$MemoizedCallable: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.internal.DefaultMavenPublicationComponentAccessorFactory: loaded 2 times (x 72B)
Class org.objectweb.asm.Symbol                                                        : loaded 2 times (x 69B)
Class com.google.common.base.AbstractIterator                                         : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.targets.native.KonanPropertiesBuildService$Companion$registerIfAbsent$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.AbstractKotlinCompile$friendPathsSet$1        : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.build.report.metrics.BuildAttributes                       : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.cocoapods.KotlinCocoapodsPlugin$Companion    : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationImpl    : loaded 2 times (x 136B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory$PostConfigure;: loaded 2 times (x 65B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$MACOS_X64                         : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.konan.util.Named                                           : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinSourceSet$Companion                    : loaded 2 times (x 67B)
Class [Lorg.jetbrains.kotlin.gradle.report.BuildReportMode;                           : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.statistics.metrics.BooleanOverridePolicy                   : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.gradle.plugin.AbstractKotlinAndroidPluginWrapper           : loaded 2 times (x 82B)
Class com.google.common.io.Closer                                                     : loaded 2 times (x 74B)
Class com.google.common.collect.RegularImmutableMap$Values                            : loaded 2 times (x 203B)
Class com.google.common.collect.ImmutableSet                                          : loaded 2 times (x 141B)
Class com.google.common.base.CharMatcher$BitSetMatcher                                : loaded 2 times (x 108B)
Class Build_gradle$7                                                                  : loaded 2 times (x 70B)
Class org.gradle.internal.classloader.ClassLoaderSpec                                 : loaded 2 times (x 67B)
Class io.invertase.gradle.build.ProjectExtension$getSharedInstance                    : loaded 2 times (x 142B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinStdlibConfigurationMetrics  : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$DefaultJavaToolchainSetter: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.build.report.metrics.BuildAttributeKind                    : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinCompilationsKt$moduleNameForCompilation$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.sources.KotlinDependencyScopeKt              : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.TransformActionUsingKotlinToolingDiagnostics$Parameters: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.compilerRunner.CompilerSystemPropertiesService$Companion$registerIfAbsent$2$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinCompilationsKt$addSourcesToKotlinCompileTask$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.internal.KotlinDependenciesManagementKt$configureDefaultVersionsResolutionStrategy$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.utils.KotlinExtensionUtilsKt$forAllTargets$1        : loaded 2 times (x 91B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.CheckKotlinGradlePluginConfigurationErrorsKt$locateOrRegisterCheckKotlinGradlePluginErrorsTask$taskProvider$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.OverriddenKotlinNativeHomeChecker$StdlibExistenceCheckerValueSource$Params: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.dsl.PlatformSourceSetConventionsChecker$nativeTargetPresets$1$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tooling.BuildKotlinToolingMetadataTaskKt$buildKotlinToolingMetadataTask$1: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.internal.Kapt3GradleSubplugin$Companion             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCreateCompilationArchiveTaskKt$KotlinRegisterCompilationArchiveTasksExtension$1: loaded 2 times (x 70B)
Class [Lorg.jetbrains.kotlin.statistics.metrics.NumberOverridePolicy;                 : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$GradleProperty: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.utils.StoredPropertyKt$projectStoredProperty$1      : loaded 2 times (x 74B)
Class org.objectweb.asm.AnnotationWriter                                              : loaded 2 times (x 75B)
Class com.google.common.io.ByteSource$EmptyByteSource                                 : loaded 2 times (x 81B)
Class com.google.common.cache.CacheStats                                              : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.K2MultiplatformStructure$RefinesEdge          : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.SyncLanguageSettingsWithKotlinExtensionSetupActionKt$SyncLanguageSettingsWithKotlinExtensionSetupAction$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.TaskConfigAction$configureTask$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.DuplicateSourceSetChecker$runChecks$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.ToolingDiagnostic$Severity       : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.dsl.JvmTarget                                       : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.internal.ClassLoadersCachingBuildService            : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactories$Inject        : loaded 2 times (x 102B)
Class org.jetbrains.kotlin.gradle.plugin.internal.DefaultJavaExecTaskParametersCompatibility$Factory: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactoriesConfigurator$Companion: loaded 2 times (x 67B)
Class org.objectweb.asm.RecordComponentWriter                                         : loaded 2 times (x 74B)
Class settings_4rq5cjrcdj0mscfkcpdclh2wk$_run_closure1                                : loaded 2 times (x 135B)
Class com.google.common.io.LineProcessor                                              : loaded 2 times (x 66B)
Class com.google.common.io.Closer$Suppressor                                          : loaded 2 times (x 66B)
Class com.google.common.util.concurrent.AbstractFuture$Listener                       : loaded 2 times (x 68B)
Class com.google.common.base.CharMatcher$Or                                           : loaded 2 times (x 108B)
Class io.invertase.gradle.build.ReactNativeShared$_applyDefaultExcludes_closure2      : loaded 2 times (x 137B)
Class [Lorg.jetbrains.kotlin.compilerRunner.KotlinCompilerArgumentsLogLevel;          : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile_Decorated                       : loaded 2 times (x 595B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.SyncLanguageSettingsWithKotlinExtensionSetupActionKt$SyncLanguageSettingsWithKotlinExtensionSetupAction$1$2: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.internal.BuildIdService$Companion            : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.OverriddenKotlinNativeHomeChecker$StdlibExistenceCheckerValueSource$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.js.dsl.KotlinWasmSubTargetContainerDsl      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.WasmWasiEnvironmentChecker$1: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.DisabledCinteropCommonizationInHmppProjectChecker: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.NoKotlinTargetsDeclaredChecker: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.targets.jvm.KotlinJvmTargetTestFixturesSideEffectKt$ConfigureJavaTestFixturesSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.utils.StoredPropertyStorage                         : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinSourceSetContainer                     : loaded 2 times (x 66B)
Class org.objectweb.asm.Attribute                                                     : loaded 2 times (x 73B)
Class com.google.common.base.Supplier                                                 : loaded 2 times (x 66B)
Class org.gradle.internal.classloader.VisitableURLClassLoader                         : loaded 2 times (x 113B)
Class build_eupcmxgtu9q3uy0v8rg9z8i7j$_run_closure1                                   : loaded 2 times (x 136B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.plugins.ObservablePlugins         : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.sources.InternalKotlinSourceSetKt$awaitPlatformCompilations$1: loaded 2 times (x 84B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.sources.KotlinDependencyScope;             : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$configureCommonParameters$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.ConfigurationNaming: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.DefaultKotlinCompilationPostConfigureKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$WATCHOS_X64                       : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinGradleProjectCheckersRunnerKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.IdeaKt$isInIdeaEnvironment$1               : loaded 2 times (x 70B)
Class [Lorg.jetbrains.kotlin.tooling.core.HasExtras;                                  : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.IncorrectCompileOnlyDependenciesChecker: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.WasmWasiEnvironmentChecker$2: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinLegacyCompatibilityMetadataArtifactKt$KotlinLegacyCompatibilityMetadataArtifact$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.dsl.DefaultToolchainSupport$wireToolchainToTasks$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.utils.SingleActionPerProject                        : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.plugin.DefaultKotlinBasePlugin                      : loaded 2 times (x 78B)
Class com.google.common.collect.ImmutableList$SerializedForm                          : loaded 2 times (x 69B)
Class Settings_gradle                                                                 : loaded 2 times (x 124B)
Class org.gradle.internal.Cast                                                        : loaded 2 times (x 67B)
Class org.gradle.api.Action                                                           : loaded 2 times (x 66B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.mpp.KotlinUsageContext$MavenScope;         : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$configureCommonParameters$2: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationCompilerOptionsFromTargetConfigurator: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.internal.IdeaKt$isInIdeaEnvironment$2               : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.utils.FailuresKt$ideaSyncClasspathModeUtil$1        : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinToolingDiagnosticsCollector$Inject: loaded 2 times (x 92B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinJsKlibArtifactKt                    : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinTargetContainerWithWasmPresetFunctions    : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.GradleUtilsKt                                : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService          : loaded 2 times (x 72B)
Class org.objectweb.asm.Handler                                                       : loaded 2 times (x 68B)
Class com.google.common.base.Strings                                                  : loaded 2 times (x 67B)
Class com.google.common.collect.Sets                                                  : loaded 2 times (x 67B)
Class com.google.common.base.Ticker                                                   : loaded 2 times (x 68B)
Class com.google.common.base.CharMatcher$FastMatcher                                  : loaded 2 times (x 107B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinTarget$DefaultImpls                    : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$javaSources$1                   : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.incremental.IncrementalCompilationFeatures                 : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinToolingDiagnosticsCollector: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCreateCompilationArchiveTaskKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.tooling.core.MutableExtrasImpl                             : loaded 2 times (x 161B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinBuildStatsMXBean            : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.utils.FileUtilsKt                                   : loaded 2 times (x 67B)
Class [Lcom.google.common.cache.LocalCache$Segment;                                   : loaded 2 times (x 65B)
Class [Lcom.google.common.cache.LocalCache$Strength;                                  : loaded 2 times (x 65B)
Class com.google.common.cache.CacheBuilder$1                                          : loaded 2 times (x 81B)
Class com.google.common.collect.ImmutableMap                                          : loaded 2 times (x 116B)
Class com.google.common.base.Predicate                                                : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.AndroidPublicationNotConfiguredChecker: loaded 2 times (x 70B)
Class org.gradle.internal.classpath.DefaultClassPath$ImmutableUniqueList$Builder      : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinCompilationFactory$DefaultImpls    : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.InternalGradlePropertiesUsageChecker: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.CreateCInteropTasksSideEffectKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.metadata.KotlinMetadataTargetConfiguratorKt : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.testing.internal.KotlinTestsRegistry$Companion      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinCommonCompilerToolOptions                 : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$Companion$registerIfAbsent$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.internal.DefaultConfigurationCacheStartParameterAccessorVariantFactory: loaded 2 times (x 72B)
Class com.google.common.collect.AbstractRangeSet                                      : loaded 2 times (x 110B)
Class com.google.common.cache.LocalCache                                              : loaded 2 times (x 183B)
Class com.google.common.cache.CacheBuilder$2                                          : loaded 2 times (x 75B)
Class com.google.common.base.CharMatcher$NegatedFastMatcher                           : loaded 2 times (x 109B)
Class org.jetbrains.kotlin.build.report.metrics.BuildPerformanceMetrics$Companion     : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.cli.common.arguments.K2JVMCompilerArguments                : loaded 2 times (x 77B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.DefaultKotlinCompilationSourceSetsContainer$source$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.internal.KotlinTestDependencyManagementKt           : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.NativeVersionChecker$runChecks$nativeVersion$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$ANDROID_X64                       : loaded 2 times (x 74B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.sources.ConsistencyCheck;                  : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.FlowActionBuildFusService$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.report.ReportingSettings                            : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.statistics.metrics.StringAnonymizationPolicy$ComponentVersionAnonymizer: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinBasePluginWrapper                      : loaded 2 times (x 82B)
Class com.google.common.cache.CacheBuilder$3                                          : loaded 2 times (x 68B)
Class [Lcom.google.common.collect.AbstractMapEntry;                                   : loaded 2 times (x 65B)
Class com.google.common.collect.ImmutableEntry                                        : loaded 2 times (x 78B)
Class com.google.common.collect.ArrayListMultimap                                     : loaded 2 times (x 168B)
Class org.gradle.api.GradleException                                                  : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinToolingDiagnostics$JsEnvironmentNotChosenExplicitly: loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.plugin.ProjectLocalConfigurations$ProjectLocalCompatibility: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinJvmCompilerOptions                        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.internal.DefaultMavenPublicationComponentAccessor: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.internal.DefaultProjectIsolationStartParameterAccessor$Factory: loaded 2 times (x 72B)
Class com.google.common.base.Suppliers$MemoizingSupplier                              : loaded 2 times (x 75B)
Class com.google.common.collect.CollectPreconditions                                  : loaded 2 times (x 67B)
Class io.invertase.gradle.common.Utilities                                            : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.buildtools.api.SourcesChanges                              : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycleImpl$launch$1           : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.konan.target.HostManager$Companion$targetAliases$2         : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.utils.Completable                                   : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.DefaultKotlinBasePlugin$addKotlinCompilerConfiguration$2$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinBuildStatsServicesRegistry$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.internal.DefaultCompatibilityConventionRegistrar$Factory: loaded 2 times (x 72B)
Class [Lcom.google.common.io.FileWriteMode;                                           : loaded 2 times (x 65B)
Class com.google.common.collect.SortedMapDifference                                   : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$DefaultJdkSetter   : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.cli.common.arguments.CommonCompilerArguments               : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.tooling.core.KotlinToolingVersion$Maturity                 : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.IncorrectCompileOnlyDependenciesChecker$runChecks$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinOnlyTarget                         : loaded 2 times (x 155B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.BaseNpmDependencyExtension           : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinVersion$Companion                         : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.InternalKotlinGradlePluginApi                       : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.targets.ConfigureNonPackedKlibConsumingSideEffectKt$ConfigureNonPackedKlibConsumingSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.dsl.DefaultToolchainSupport                         : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.internal.CustomPropertiesFileValueSource     : loaded 2 times (x 78B)
Class com.google.common.io.Files                                                      : loaded 2 times (x 67B)
Class com.google.common.cache.LocalCache$EntryFactory                                 : loaded 2 times (x 79B)
Class org.jetbrains.kotlin.gradle.tasks.TaskOutputsBackup                             : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.cocoapods.KotlinCocoapodsPlugin              : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.internal.CompilerOptionsDslHelpersKt                : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfigKt   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.StdlibDependencyManagementKt$configureStdlibVersionAlignment$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinProjectExtension$awaitSourceSets$1        : loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycle$CoroutineStart         : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.konan.target.Family                                        : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.TasksProviderKt$sam$org_gradle_api_Action$0   : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tooling.BuildKotlinToolingMetadataTask$FromKotlinExtension: loaded 2 times (x 313B)
Class org.jetbrains.kotlin.gradle.utils.FailuresKt                                    : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.native.internal.CInteropConfigurationsKt$special$$inlined$KotlinTargetSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesLoader$konanDataDirProperty$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.BuildFusService$Parameters_Decorated: loaded 2 times (x 139B)
Class org.jetbrains.kotlin.gradle.plugin.PropertiesProvider$keepIncrementalCompilationCachesInMemory$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$Params$Inject: loaded 2 times (x 102B)
Class org.jetbrains.kotlin.tooling.core.HasMutableExtras                              : loaded 2 times (x 66B)
Class com.google.common.cache.LocalCache$AbstractReferenceEntry                       : loaded 2 times (x 103B)
Class com.google.common.base.CharMatcher$Whitespace                                   : loaded 2 times (x 108B)
Class org.jetbrains.kotlin.gradle.utils.Completable$await$1                           : loaded 2 times (x 84B)
Class org.gradle.internal.IoActions                                                   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.Kapt3KotlinGradleSubpluginKt               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinJvmCompile                                : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$LINUX_ARM32_HFP                   : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.internal.Kapt3GradleSubplugin                       : loaded 2 times (x 79B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.CInteropInputChecker    : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.targets.native.internal.CInteropConfigurationsKt$special$$inlined$KotlinTargetSideEffect$2: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.FinalizeConfigurationFusMetricActionKt$FinalizeConfigurationFusMetricAction$1: loaded 2 times (x 91B)
Class org.jetbrains.kotlin.gradle.internal.KotlinDependenciesManagementKt$CustomizeKotlinDependenciesSetupAction$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradlePluginExtensionPointInternal$registeredExtensions$2: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.statistics.metrics.BooleanOverridePolicy$OR                : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.gradle.plugin.UsesVariantImplementationFactories           : loaded 2 times (x 66B)
Class org.objectweb.asm.SymbolTable                                                   : loaded 2 times (x 68B)
Class com.google.common.collect.ImmutableRangeSet$ComplementRanges                    : loaded 2 times (x 203B)
Class com.google.common.cache.CacheLoader$InvalidCacheLoadException                   : loaded 2 times (x 78B)
Class com.google.common.collect.ImmutableSet$RegularSetBuilderImpl                    : loaded 2 times (x 73B)
Class org.gradle.internal.classloader.SystemClassLoaderSpec                           : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.CacheableTasksKt$cacheOnlyIfEnabledForKotlin$1$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$1$1     : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory$PostConfigure: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.utils.KotlinExtensionUtilsKt                        : loaded 2 times (x 67B)
Class [Lorg.jetbrains.kotlin.konan.util.Named;                                        : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.NoKotlinTargetsDeclaredChecker$runChecks$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.AbstractKotlinTarget$kotlinComponents$2  : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPlatformType$CompatibilityRule         : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesLoader$actualNativeHomeDirectory$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesKt              : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.BuildEventsListenerRegistryHolder            : loaded 2 times (x 68B)
Class [Lorg.jetbrains.kotlin.statistics.metrics.BooleanOverridePolicy;                : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.internal.CustomPropertiesFileValueSource$Parameters$Inject: loaded 2 times (x 102B)
Class com.google.common.cache.LocalCache$StrongEntry                                  : loaded 2 times (x 104B)
Class com.google.common.util.concurrent.AbstractFuture$TrustedFuture                  : loaded 2 times (x 110B)
Class com.google.common.cache.LoadingCache                                            : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinTargetSoftwareComponentImplKt$KotlinTargetSoftwareComponent$1: loaded 2 times (x 91B)
Class org.jetbrains.kotlin.gradle.tasks.GradleCompileTaskProvider                     : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$registerBuildToolsApiTransformations$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfigKt$sam$org_gradle_api_Action$0: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.internal.BuildIdService                      : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationDependencyConfigurationsFactoriesKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.DefaultKotlinCompilationFriendPathsResolver: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.WasmSourceSetsNotFoundChecker$runChecks$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$IOS_SIMULATOR_ARM64               : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.targets.jvm.KotlinJvmCompilationWireJavaSourcesSideEffectKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCreateCompilationArchiveTaskKt$KotlinCreateCompilationArchivesTask$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinVersion                                   : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesLoader$actualNativeHomeDirectory$2: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.utils.StoredPropertyKt                              : loaded 2 times (x 67B)
Class org.objectweb.asm.MethodWriter                                                  : loaded 2 times (x 102B)
Class com.google.common.io.ByteStreams$LimitedInputStream                             : loaded 2 times (x 88B)
Class com.google.common.base.Suppliers$NonSerializableMemoizingSupplier               : loaded 2 times (x 75B)
Class [Lcom.google.common.cache.Weigher;                                              : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.internal.KotlinDependenciesManagementKt$configureDefaultVersionsResolutionStrategy$1$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.internal.build.SourcesUtilsKt                       : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.sources.KotlinDependencyScope$Companion      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$1$3     : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$registerBuildToolsApiTransformations$2: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinJvmCompile                              : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationSourceSetsContainerKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycleKt$launchInStage$1      : loaded 2 times (x 91B)
Class org.jetbrains.kotlin.gradle.targets.NativeForwardImplementationToApiElementsSideEffectKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesLoader$actualNativeHomeDirectory$3: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.report.BuildReportMode                              : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.statistics.metrics.StringAnonymizationPolicy$RegexControlled: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.statistics.ValueAnonymizer                                 : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactories$Companion$getProvider$2$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.internal.ProjectIsolationStartParameterAccessor$Factory: loaded 2 times (x 66B)
Class Build_gradle$6$1                                                                : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$disableMultiModuleIC$2$1        : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$javaExecutable$1   : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.build.report.metrics.BuildAttribute                        : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.internal.transforms.BuildToolsApiClasspathEntrySnapshotTransform$Inject1: loaded 2 times (x 109B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationRegisterInSourceSetsConfigurator: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.DefaultKotlinCompilationTaskNamesContainerFactory: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.targets.native.internal.CommonizerTasksKt           : loaded 2 times (x 67B)
Class [Lorg.jetbrains.kotlin.gradle.targets.js.npm.NpmDependency$Scope;               : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilerPluginSupportPlugin            : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.AbstractKotlinTarget$components$2        : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.targets.CreateNonPackedKlibVariantsSideEffectKt$CreateNonPackedKlibVariantsSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinUsages$setupAttributesMatchingStrategy$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.PropertiesProvider$preciseCompilationResultsBackup$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.internal.diagnostics.AgpCompatibilityCheck$runAgpCompatibilityCheckIfAgpIsApplied$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.utils.AndroidPluginIdsKt                            : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinBasePlugin                             : loaded 2 times (x 66B)
Class com.google.common.collect.RegularImmutableBiMap                                 : loaded 2 times (x 144B)
Class com.google.common.cache.LocalCache$ValueReference                               : loaded 2 times (x 66B)
Class com.google.common.base.CharMatcher$Negated                                      : loaded 2 times (x 109B)
Class com.google.common.base.CharMatcher$Is                                           : loaded 2 times (x 107B)
Class org.jetbrains.kotlin.gradle.tasks.internal.KotlinJvmOptionsCompat               : loaded 2 times (x 99B)
Class org.jetbrains.kotlin.cli.common.arguments.CommonToolArguments                   : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.utils.FutureKt                                      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.native.internal.PlatformLibrariesGenerator$GeneratedPlatformLibrariesService: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.logging.GradleLoggingUtilsKt                        : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinAndroidPluginWrapper                   : loaded 2 times (x 82B)
Class com.google.common.hash.PrimitiveSink                                            : loaded 2 times (x 66B)
Class com.google.common.util.concurrent.AbstractFuture$Failure                        : loaded 2 times (x 68B)
Class com.google.common.base.PatternCompiler                                          : loaded 2 times (x 66B)
Class com.google.common.base.Equivalence$Equals                                       : loaded 2 times (x 78B)
Class com.google.common.collect.Hashing                                               : loaded 2 times (x 67B)
Class com.google.common.base.Preconditions                                            : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.compilerRunner.GradleCompilerRunner$Companion              : loaded 2 times (x 67B)
Class [Lorg.jetbrains.kotlin.gradle.dsl.PlatformSourceSetConventionsChecker$CheckedPlatformInfo;: loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinGradleProjectCheckerContext: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.DuplicateSourceSetChecker: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.ToolingDiagnosticFactory         : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.targets.KotlinTargetSideEffect                      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinTargetContainerWithNativeShortcuts        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.DefaultKotlinBasePlugin$setupAttributeMatchingStrategy$1$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.statistics.metrics.NumericalMetrics$Companion              : loaded 2 times (x 67B)
Class [Lorg.jetbrains.kotlin.statistics.metrics.NumberAnonymizationPolicy;            : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.statistics.metrics.NumberOverridePolicy$OVERRIDE           : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$property$1: loaded 2 times (x 75B)
Class org.objectweb.asm.Label                                                         : loaded 2 times (x 69B)
Class org.objectweb.asm.CurrentFrame                                                  : loaded 2 times (x 69B)
Class org.objectweb.asm.ClassTooLargeException                                        : loaded 2 times (x 79B)
Class com.google.common.io.CharSink                                                   : loaded 2 times (x 75B)
Class com.google.common.collect.ImmutableSetMultimap                                  : loaded 2 times (x 176B)
Class com.google.common.base.Joiner$1                                                 : loaded 2 times (x 76B)
Class com.google.common.collect.PeekingIterator                                       : loaded 2 times (x 66B)
Class com.google.common.base.CharMatcher                                              : loaded 2 times (x 107B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig$2$2$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.build.report.metrics.BuildAttributes$Companion             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompilationTask                         : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory$ProcessResourcesTaskNameFactory: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$property$2: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.internal.KotlinDependenciesManagementKt             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.DefaultKotlinBasePlugin$setupAttributeMatchingStrategy$1$2: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.targets.native.internal.PlatformLibrariesGenerator$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinBuildStatsLoggerService     : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.internal.CustomPropertiesFileValueSource$Inject: loaded 2 times (x 109B)
Class org.objectweb.asm.Frame                                                         : loaded 2 times (x 69B)
Class org.objectweb.asm.MethodVisitor                                                 : loaded 2 times (x 101B)
Class com.google.common.io.ByteSource$AsCharSource                                    : loaded 2 times (x 82B)
Class com.google.common.collect.Maps$IteratorBasedAbstractMap                         : loaded 2 times (x 121B)
Class com.google.common.collect.RangeGwtSerializationDependencies                     : loaded 2 times (x 67B)
Class com.google.common.base.Joiner$2                                                 : loaded 2 times (x 75B)
Class com.google.common.cache.LocalCache$EntryFactory$1                               : loaded 2 times (x 79B)
Class [Lcom.google.common.cache.RemovalListener;                                      : loaded 2 times (x 65B)
Class com.google.common.base.CharMatcher$1                                            : loaded 2 times (x 109B)
Class com.google.common.base.CharMatcher$NamedFastMatcher                             : loaded 2 times (x 108B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.InternalKotlinTargetKt$special$$inlined$extrasStoredFuture$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.compilerRunner.UsesCompilerSystemPropertiesService         : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationSourceSetInclusion$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$WATCHOS_DEVICE_ARM64              : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.MissingNativeStdlibChecker$runChecks$1: loaded 2 times (x 84B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.sources.android.AndroidVariantType;        : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.sources.android.AndroidVariantType           : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinTargetSoftwareComponent            : loaded 2 times (x 85B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinVariant                            : loaded 2 times (x 97B)
Class org.jetbrains.kotlin.gradle.targets.native.ConfigureFrameworkExportSideEffectKt : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.utils.WhenPluginEnabledKt                           : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.tooling.core.ExtrasProperty                                : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactories               : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.internal.diagnostics.KotlinCompilerEmbeddableCheck  : loaded 2 times (x 67B)
Class com.google.common.collect.RangeSet                                              : loaded 2 times (x 66B)
Class com.google.common.cache.LocalCache$EntryFactory$2                               : loaded 2 times (x 79B)
Class org.gradle.api.internal.classpath.GlobalCacheRootsProvider                      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.internal.StdlibDependencyManagementKt$WhenMappings  : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.tooling.core.Extras$Entry$Companion                        : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.js.dsl.KotlinWasmWasiTargetDsl              : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.DisabledNativeTargetsChecker: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCreateSourcesJarTaskSideEffectKt$KotlinCreateSourcesJarTaskSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.internal.CompilerArgumentAware                      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.utils.ReportUtilsKt                                 : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.utils.StoredLazyProperty                            : loaded 2 times (x 71B)
Class org.objectweb.asm.ModuleWriter                                                  : loaded 2 times (x 78B)
Class com.google.common.collect.CollectCollectors                                     : loaded 2 times (x 67B)
Class com.google.common.cache.LocalCache$EntryFactory$3                               : loaded 2 times (x 79B)
Class com.google.common.base.Ticker$1                                                 : loaded 2 times (x 68B)
Class com.google.common.collect.ImmutableMapEntrySet$RegularEntrySet                  : loaded 2 times (x 147B)
Class com.google.common.collect.Maps$BiMapConverter                                   : loaded 2 times (x 86B)
Class org.jetbrains.kotlin.gradle.tasks.K2MultiplatformStructure_Decorated            : loaded 2 times (x 131B)
Class org.jetbrains.kotlin.compilerRunner.GradleCompilerRunner                        : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.dsl.AndroidMainSourceSetConventionsChecker$runChecks$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.plugin.sources.FragmentConsistencyChecks            : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.InternalKotlinTargetComponent            : loaded 2 times (x 89B)
Class org.jetbrains.kotlin.gradle.targets.CreateNonPackedKlibVariantsSideEffectKt     : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.CreateDefaultTestRunSideEffectKt$special$$inlined$KotlinTargetSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.utils.ProviderApiUtilsKt                            : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.native.UsesKonanPropertiesBuildService      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesLoader$forceDisableRunningInProcess$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.utils.PersistentCachesKt                            : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.tasks.TaskWithLocalState                   : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinBuildStatisticsUtilsKt      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.statistics.metrics.BooleanMetrics                          : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$Params   : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.internal.diagnostics.GradleCompatibilityCheck       : loaded 2 times (x 67B)
Class com.google.common.collect.SetMultimap                                           : loaded 2 times (x 66B)
Class com.google.common.cache.LocalCache$EntryFactory$4                               : loaded 2 times (x 79B)
Class com.google.common.collect.ImmutableSet$Builder                                  : loaded 2 times (x 81B)
Class com.google.common.base.CharMatcher$InRange                                      : loaded 2 times (x 107B)
Class org.jetbrains.kotlin.gradle.plugin.sources.KotlinDependencyScopeKt$WhenMappings : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.K2MultiplatformCompilationTask                : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.OverriddenKotlinNativeHomeChecker$StdlibExistenceCheckerValueSource$Inject: loaded 2 times (x 105B)
Class org.jetbrains.kotlin.gradle.utils.ConfigurationsKt$maybeCreateDependencyScope$1 : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.sources.FragmentConsistencyChecks$languageVersionCheck$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.DecoratedKotlinCompilation               : loaded 2 times (x 187B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.ExperimentalTryNextUsageChecker: loaded 2 times (x 70B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.diagnostics.ToolingDiagnostic$Severity;    : loaded 2 times (x 65B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.KotlinPlatformType;                        : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.UsesBuildFusService               : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.old.Pre232IdeaKotlinBuildStatsBeanService: loaded 2 times (x 91B)
Class org.jetbrains.kotlin.statistics.metrics.NumberOverridePolicy$AVERAGE            : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactories$Parameters$Inject: loaded 2 times (x 102B)
Class org.jetbrains.kotlin.gradle.internal.diagnostics.GradleCompatibilityCheck$CurrentGradleVersionProvider: loaded 2 times (x 66B)
Class com.google.common.io.ByteStreams$1                                              : loaded 2 times (x 81B)
Class com.google.common.cache.LocalCache$EntryFactory$5                               : loaded 2 times (x 79B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.MppSourcesJarKt$sourcesJarTaskNamed$result$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$1       : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.UsesBuildFinishedListenerService             : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.ConfigurationNaming$Default: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationSourceSetInclusion: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.targets.jvm.KotlinJvmTarget                         : loaded 2 times (x 168B)
Class org.jetbrains.kotlin.gradle.targets.js.dsl.KotlinWasmJsTargetDsl                : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.DisabledNativeTargetsChecker$runChecks$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycleImpl$await$2$1          : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.sources.DefaultLanguageSettingsBuilder       : loaded 2 times (x 96B)
Class org.jetbrains.kotlin.gradle.plugin.sources.FragmentConsistencyChecks$languageVersionCheck$2: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.targets.CreateTargetConfigurationsSideEffectKt$CreateTargetConfigurationsSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinTargetContainerWithPresetFunctions        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinPublishingDsl                             : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.utils.GradleConfigurationUtilsKt                    : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.internal.CustomPropertiesFileValueSource$Parameters: loaded 2 times (x 66B)
Class com.google.common.cache.LocalCache$EntryFactory$6                               : loaded 2 times (x 79B)
Class org.jetbrains.kotlin.compilerRunner.CompilerSystemPropertiesService$Companion   : loaded 2 times (x 67B)
Class org.gradle.internal.installation.CurrentGradleInstallationLocator               : loaded 2 times (x 67B)
Class build_9kn57pj55amg75ewdgalkhlsr$_run_closure1                                   : loaded 2 times (x 136B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$getToolsJarFromJvm$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.build.report.metrics.GcMetrics                             : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.UsesKotlinToolingDiagnosticsKt   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$2       : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.BuildFinishedListenerService$Companion$registerIfAbsent$2$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$IOS_X64                           : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.internal.IsInIdeaEnvironmentValueSource$Inject      : loaded 2 times (x 96B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.InternalKotlinCompilation                : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.utils.WhenEvaluatedKt                               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.native.internal.AddKotlinPlatformIntegersSupportLibraryKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.AbstractKotlinJsPluginWrapper                : loaded 2 times (x 82B)
Class org.jetbrains.kotlin.gradle.plugin.internal.MavenPublicationComponentAccessor   : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.fus.BuildUidService                                 : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginWrapperKt                        : loaded 2 times (x 67B)
Class com.google.common.io.ByteSource$SlicedByteSource                                : loaded 2 times (x 81B)
Class com.google.common.collect.Maps$KeySet                                           : loaded 2 times (x 133B)
Class com.google.common.cache.LocalCache$EntryFactory$7                               : loaded 2 times (x 79B)
Class com.google.common.collect.ImmutableSet$EmptySetBuilderImpl                      : loaded 2 times (x 72B)
Class com.google.common.collect.ImmutableList                                         : loaded 2 times (x 202B)
Class org.gradle.internal.service.DefaultServiceLocator                               : loaded 2 times (x 79B)
Class build_agr3p375fzty99zrloowe6ju6$_run_closure1$_closure4                         : loaded 2 times (x 135B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$scriptExtensions$1              : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.K2MultiplatformStructure$Fragment             : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.compilerRunner.CompilerSystemPropertiesService$Companion$registerIfAbsent$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.util.capitalizeDecapitalize.CapitalizeDecapitalizeKt       : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.WasmJsEnvironmentChecker: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.AndroidPluginWithoutAndroidTargetChecker: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.FlowActionBuildFusService$getConfigurationTimeMetrics$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.SynchronizedMetricsContainer      : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.statistics.metrics.NumberOverridePolicy                    : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.statistics.metrics.StringAnonymizationPolicy$AllowedListAnonymizer$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.internal.DefaultProjectIsolationStartParameterAccessor_Decorated: loaded 2 times (x 121B)
Class org.jetbrains.kotlin.gradle.plugin.internal.ProjectIsolationStartParameterAccessorKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.testing.internal.KotlinTestsRegistry                : loaded 2 times (x 68B)
Class com.google.common.cache.LocalCache$EntryFactory$8                               : loaded 2 times (x 79B)
Class com.google.common.collect.ImmutableSet$SetBuilderImpl                           : loaded 2 times (x 72B)
Class org.gradle.internal.installation.CurrentGradleInstallation                      : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.InternalKotlinCompilationKt$awaitAllKotlinSourceSets$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompilerExecutionStrategy               : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.FusMetrics                        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.sources.DefaultKotlinSourceSet$special$$inlined$Iterable$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory$KotlinCompilerOptionsFactory$Options: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilation                            : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinTargetConfiguratorKt                   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.native.internal.PlatformLibrariesGenerator  : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.tooling.core.Extras$Type$Companion                         : loaded 2 times (x 67B)
Class com.google.common.cache.CacheLoader$1                                           : loaded 2 times (x 71B)
Class com.google.common.collect.ImmutableList$SubList                                 : loaded 2 times (x 204B)
Class org.gradle.util.internal.GUtil                                                  : loaded 2 times (x 67B)
Class build_9cu49dh6h7478zg7wrc3wnbxn$_run_closure1                                   : loaded 2 times (x 136B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.InternalKotlinTargetKt$isSourcesPublishableFuture$2: loaded 2 times (x 91B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinCompilationFactory                 : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.dsl.IosSourceSetConventionChecker$runChecks$1       : loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.AbstractKotlinCompilation                : loaded 2 times (x 187B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.UnusedSourceSetsChecker : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.utils.Future                                        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinBuildStatsConfiguration$Companion: loaded 2 times (x 67B)
Class settings_4rq5cjrcdj0mscfkcpdclh2wk                                              : loaded 2 times (x 175B)
Class com.google.common.io.ByteSource                                                 : loaded 2 times (x 80B)
Class com.google.common.cache.AbstractCache$StatsCounter                              : loaded 2 times (x 66B)
Class build_9cu49dh6h7478zg7wrc3wnbxn$_run_closure2                                   : loaded 2 times (x 136B)
Class io.invertase.gradle.common.PackageJson                                          : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.gradle.plugin.attributes.KlibPackaging                     : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinTargetSoftwareComponentImplKt      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.js.dsl.KotlinJsSubTargetContainerDsl        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.tooling.core.MutableExtrasImpl$Companion                   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.ClassLoadersCachingBuildService$Parameters : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleBuildServices$Companion$registerIfAbsent$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.statistics.fileloggers.MetricsContainer                    : loaded 2 times (x 73B)
Class org.jetbrains.kotlin.gradle.plugin.internal.ProjectIsolationStartParameterAccessor: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.utils.StoredProperty                                : loaded 2 times (x 66B)
Class com.google.common.collect.Iterators$MergingIterator                             : loaded 2 times (x 77B)
Class com.google.common.collect.RegularImmutableSet                                   : loaded 2 times (x 144B)
Class org.gradle.internal.installation.GradleInstallation                             : loaded 2 times (x 71B)
Class build_eupcmxgtu9q3uy0v8rg9z8i7j                                                 : loaded 2 times (x 187B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinJvmCompile$DefaultImpls                   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory$create$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinJavaToolchain                           : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.FlowActionBuildFusService$Inject  : loaded 2 times (x 100B)
Class org.jetbrains.kotlin.statistics.metrics.BooleanMetrics$Companion                : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.internal.CompatibilityConventionRegistrar    : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.AndroidGradlePluginVersion                   : loaded 2 times (x 71B)
Class com.google.common.collect.Iterables                                             : loaded 2 times (x 67B)
Class com.google.common.base.Splitter$Strategy                                        : loaded 2 times (x 66B)
Class build_6k1vuntnni446s9ebkvsvp2hk                                                 : loaded 2 times (x 179B)
Class io.invertase.gradle.build.ProjectExtension$setProject$0                         : loaded 2 times (x 148B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinJvmCompilation                     : loaded 2 times (x 196B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilation$Companion                  : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$WATCHOS_ARM64                     : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$MACOS_ARM64                       : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.OverriddenKotlinNativeHomeChecker: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinJvmJarArtifactKt$KotlinJvmJarArtifact$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinLegacyCompatibilityMetadataArtifactKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.native.SetupEmbedAndSignAppleFrameworkTaskSideEffectKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinProjectSetupActionKt$KotlinProjectSetupCoroutine$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.dsl.ToolchainSupport$Companion                      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinSourceSet                              : loaded 2 times (x 66B)

---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
OS uptime: 14 days 22:03 hours
Hyper-V role detected

CPU: total 4 (initial active 4) (4 cores per cpu, 1 threads per core) family 6 model 58 stepping 9 microcode 0x21, cx8, cmov, fxsr, mmx, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, tsc, tscinvbit, avx, aes, erms, clmul, vzeroupper, clflush, hv, rdtscp, f16c
Processor Information for all 4 processors :
  Max Mhz: 3101, Current Mhz: 3101, Mhz Limit: 3101

Memory: 4k page, system-wide physical 8161M (336M free)
TotalPageFile size 32737M (AvailPageFile size 0M)
current process WorkingSet (physical memory assigned to process): 1257M, peak: 1262M
current process commit charge ("private bytes"): 1488M, peak: 1499M

vm_info: OpenJDK 64-Bit Server VM (21.0.5+-13047016-b750.29) for windows-amd64 JRE (21.0.5+-13047016-b750.29), built on 2025-02-11T21:12:39Z by "builder" with MS VC++ 16.10 / 16.11 (VS2019)

END.
