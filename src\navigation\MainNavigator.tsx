import React, { useEffect } from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createStackNavigator } from '@react-navigation/stack';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import InterstitialAdService from '../services/InterstitialAdService';
import { AD_CONFIG } from '../config/ads';
import { useTheme } from '../context/ThemeContext';

// Importação das telas (ajuste os caminhos se necessário)
import HomeScreen from '../screens/HomeScreen';
import LessonScreen from '../screens/LessonScreen';
import BibleScreen from '../screens/BibleScreen';
import SettingsScreen from '../screens/SettingsScreen';
import NotesScreen from '../screens/NotesScreen';
import EditNoteScreen from '../screens/EditNoteScreen';
import VideosScreen from '../screens/VideosScreen';
// import VideoPlayerScreen from '../screens/VideoPlayerScreen'; // Não usado mais - vídeos abrem no navegador

const Tab = createBottomTabNavigator();
const Stack = createStackNavigator();

function HomeStack() {
  return (
    <Stack.Navigator>
      <Stack.Screen name="Home" component={HomeScreen} options={{ headerShown: false }} />
      <Stack.Screen name="Videos" component={VideosScreen} options={{ headerShown: false }} />
      {/* <Stack.Screen name="VideoPlayer" component={VideoPlayerScreen} options={{ headerShown: false, presentation: 'modal' }} /> */}
    </Stack.Navigator>
  );
}

function LessonStack() {
  return (
    <Stack.Navigator>
      <Stack.Screen name="Comentários" component={LessonScreen} options={{ headerShown: false }} />
    </Stack.Navigator>
  );
}

function BibleStack() {
  return (
    <Stack.Navigator>
      <Stack.Screen name="Bíblia" component={BibleScreen} options={{ headerShown: false }} />
    </Stack.Navigator>
  );
}

function NotesStack() {
  return (
    <Stack.Navigator>
      <Stack.Screen name="Minhas Anotações" component={NotesScreen} options={{ headerShown: false }} />
      <Stack.Screen name="EditNote" component={EditNoteScreen} options={{ headerShown: false }} />
    </Stack.Navigator>
  );
}

function SettingsStack() {
  return (
    <Stack.Navigator>
      <Stack.Screen name="Mais" component={SettingsScreen} options={{ headerShown: false }} />
    </Stack.Navigator>
  );
}

const MainNavigator = () => {
  const { colors } = useTheme();
  const adService = InterstitialAdService.getInstance();
  let tabPressCount = 0;

  useEffect(() => {
    // Pré-carregar anúncio quando o navigator monta
    adService.preloadAd();
  }, []);

  const handleTabPress = async () => {
    tabPressCount++;

    // Mostrar anúncio baseado na configuração
    if (tabPressCount % AD_CONFIG.TAB_PRESS_AD_FREQUENCY === 0) {
      await adService.showAd();
    }
  };

  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        headerShown: false,
        tabBarStyle: {
          backgroundColor: colors.surface,
          borderTopColor: colors.border,
        },
        tabBarActiveTintColor: colors.primary,
        tabBarInactiveTintColor: colors.textSecondary,
        tabBarIcon: ({ color, size }) => {
          let iconName = 'home';
          if (route.name === 'Home') iconName = 'home';
          else if (route.name === 'Comentários') iconName = 'book-open-variant';
          else if (route.name === 'Bíblia') iconName = 'book';
          else if (route.name === 'Anotações') iconName = 'notebook-outline';
          else if (route.name === 'Mais') iconName = 'cog-outline';
          return <Icon name={iconName} size={size} color={color} />;
        },
      })}
      screenListeners={{
        tabPress: handleTabPress,
      }}
    >
      <Tab.Screen name="Home" component={HomeStack} />
      <Tab.Screen name="Comentários" component={LessonStack} />
      <Tab.Screen name="Bíblia" component={BibleStack} />
      <Tab.Screen name="Anotações" component={NotesStack} />
      <Tab.Screen name="Mais" component={SettingsStack} />
    </Tab.Navigator>
  );
};

export default MainNavigator; 