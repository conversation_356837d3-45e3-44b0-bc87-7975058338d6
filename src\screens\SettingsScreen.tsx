import React from 'react';
import { View, Text, StyleSheet, SafeAreaView, TouchableOpacity, Switch } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { fonts } from '../theme/fonts';
import AdBanner from '../components/AdBanner';
import { BannerAdSize } from 'react-native-google-mobile-ads';
import { useTheme } from '../context/ThemeContext';

const SettingsScreen = () => {
  const { theme, colors, toggleTheme } = useTheme();

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Header */}
      <View style={[styles.header, { borderBottomColor: colors.border }]}>
        <Text style={[styles.headerTitle, { color: colors.text }]}>Mais</Text>
      </View>

      {/* Banner de Anúncio */}
      <AdBanner
        size={BannerAdSize.BANNER}
        style={{ marginVertical: 8 }}
      />

      {/* Configurações */}
      <View style={styles.settingsContainer}>
        {/* Tema Dark/Light */}
        <TouchableOpacity
          style={[styles.settingItem, { backgroundColor: colors.card, borderBottomColor: colors.border }]}
          onPress={toggleTheme}
        >
          <View style={styles.settingLeft}>
            <View style={[styles.iconContainer, { backgroundColor: colors.primary + '20' }]}>
              <Icon
                name={theme === 'dark' ? 'weather-night' : 'weather-sunny'}
                size={24}
                color={colors.primary}
              />
            </View>
            <View style={styles.settingTextContainer}>
              <Text style={[styles.settingTitle, { color: colors.text }]}>
                Tema {theme === 'dark' ? 'Escuro' : 'Claro'}
              </Text>
              <Text style={[styles.settingSubtitle, { color: colors.textSecondary }]}>
                {theme === 'dark' ? 'Modo escuro ativado' : 'Modo claro ativado'}
              </Text>
            </View>
          </View>
          <Switch
            value={theme === 'dark'}
            onValueChange={toggleTheme}
            trackColor={{ false: colors.border, true: colors.primary + '40' }}
            thumbColor={theme === 'dark' ? colors.primary : colors.textSecondary}
          />
        </TouchableOpacity>

        {/* Outras configurações futuras */}
        <View style={[styles.settingItem, { backgroundColor: colors.card, borderBottomColor: colors.border }]}>
          <View style={styles.settingLeft}>
            <View style={[styles.iconContainer, { backgroundColor: colors.accentBlue + '20' }]}>
              <Icon name="cog" size={24} color={colors.accentBlue} />
            </View>
            <View style={styles.settingTextContainer}>
              <Text style={[styles.settingTitle, { color: colors.text }]}>
                Outras Configurações
              </Text>
              <Text style={[styles.settingSubtitle, { color: colors.textSecondary }]}>
                Em desenvolvimento...
              </Text>
            </View>
          </View>
          <Icon name="chevron-right" size={24} color={colors.textSecondary} />
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 20,
    paddingTop: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  headerTitle: {
    fontFamily: fonts.bold,
    fontSize: 24,
  },
  settingsContainer: {
    flex: 1,
    paddingTop: 16,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    marginHorizontal: 16,
    marginBottom: 8,
    borderRadius: 12,
  },
  settingLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  settingTextContainer: {
    flex: 1,
  },
  settingTitle: {
    fontFamily: fonts.bold,
    fontSize: 16,
    marginBottom: 4,
  },
  settingSubtitle: {
    fontFamily: fonts.regular,
    fontSize: 14,
  },
});

export default SettingsScreen;