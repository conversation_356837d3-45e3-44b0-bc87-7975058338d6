import { api } from './api';
import { Licao } from '../models/Licao';
import { LicaoDia } from '../models/LicaoDia';

export async function listarLicoes(token: string, trimestreGuid: string): Promise<Licao[]> {
  const response = await api.post('/licao/listar', trimestreGuid, {
    headers: { Authorization: `Bearer ${token}` },
  });
  return response.data;
}

export async function getLicaoDaSemana(token: string, trimestreGuid: string): Promise<Licao | null> {
  const licoes = await listarLicoes(token, trimestreGuid);
  if (licoes.length === 0) return null;
  
  // Calcular a semana atual do ano (1-52)
  const hoje = new Date();
  const inicioAno = new Date(hoje.getFullYear(), 0, 1);
  const diasPassados = Math.floor((hoje.getTime() - inicioAno.getTime()) / (1000 * 60 * 60 * 24));
  const semanaAtual = Math.ceil((diasPassados + inicioAno.getDay() + 1) / 7);
  
  // Encontrar a lição correspondente à semana atual
  // Assumindo que as lições estão ordenadas por semana
  const licaoIndex = (semanaAtual - 1) % licoes.length;
  const licao = licoes[licaoIndex];
  if (licao) return licao;
  // Se não encontrar, retorna a última lição do trimestre
  return licoes[licoes.length - 1];
}

export async function listarLicaoDia(token: string, guidLicao: string): Promise<LicaoDia[]> {
  const response = await api.post('/licao/listarlicaodia', guidLicao, {
    headers: { Authorization: `Bearer ${token}` },
  });
  return response.data;
} 