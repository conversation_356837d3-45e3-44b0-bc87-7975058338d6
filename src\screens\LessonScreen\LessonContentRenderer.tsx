import React from 'react';
import RenderHTML, { defaultSystemFonts } from 'react-native-render-html';
import { useWindowDimensions } from 'react-native';
import { fonts } from '../../theme/fonts';
import { useTheme } from '../../context/ThemeContext';

interface LessonContentRendererProps {
  html: string;
  onLinkPress?: (event: any, href: string) => void;
}

const LessonContentRenderer: React.FC<LessonContentRendererProps> = ({ html, onLinkPress: _onLinkPress }) => {
  const { width } = useWindowDimensions();
  const { colors, theme } = useTheme();

  // Debug das cores do renderer
  console.log('📝 LessonContentRenderer - Tema:', theme, 'Cor do texto:', colors.text);

  const tagsStyles = {
    h1: {
      fontSize: fonts.size.xlarge,
      fontWeight: 'bold' as const,
      color: colors.primary,
      marginVertical: 8,
      fontFamily: fonts.bold,
    },
    h2: {
      fontSize: fonts.size.large,
      fontWeight: 'bold' as const,
      color: colors.secondary,
      marginVertical: 6,
      fontFamily: fonts.bold,
    },
    p: {
      fontSize: fonts.size.medium,
      color: colors.text,
      marginVertical: 4,
      lineHeight: 24,
      fontFamily: fonts.regular,
    },
    i: {
      fontStyle: 'italic' as const,
      color: colors.accent,
    },
  };

  const baseStyle = {
    fontSize: fonts.size.medium,
    color: colors.text,
    fontFamily: fonts.regular,
    lineHeight: 24,
    backgroundColor: colors.background,
  };

  // Aplicar cores do tema no HTML
  const themedHtml = html
    .replace(/style="background-color:\s*#f0f0f0/g, `style="background-color: ${colors.surface}`)
    .replace(/style="color:\s*#000000/g, `style="color: ${colors.text}`)
    .replace(/style="color:\s*#FFFFFF/g, `style="color: ${colors.text}`)
    .replace(/background-color:\s*#f0f0f0/g, `background-color: ${colors.surface}`)
    .replace(/color:\s*#000000/g, `color: ${colors.text}`)
    .replace(/color:\s*#FFFFFF/g, `color: ${colors.text}`);

  return (
    <RenderHTML
      contentWidth={width}
      source={{ html: themedHtml }}
      baseStyle={baseStyle}
      tagsStyles={tagsStyles}
      systemFonts={[...defaultSystemFonts, fonts.regular, fonts.bold]}
    />
  );
};

export default LessonContentRenderer; 