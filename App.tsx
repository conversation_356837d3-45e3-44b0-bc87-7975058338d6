/**
 * Sample React Native App
 * https://github.com/facebook/react-native
 *
 * @format
 */

import React, { useState, useEffect } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import MainNavigator from './src/navigation/MainNavigator';
import { AppProvider } from './src/context/AppContext';
import { AuthProvider } from './src/context/AuthContext';
import { ThemeProvider } from './src/context/ThemeContext';
import { LessonProvider } from './src/context/LessonContext';
import SplashScreen from './src/screens/SplashScreen';
import FirebaseMessagingService from './src/services/FirebaseMessagingService';
import InterstitialAdService from './src/services/InterstitialAdService';
import mobileAds from 'react-native-google-mobile-ads';

const App = () => {
  const [showSplash, setShowSplash] = useState(true);

  useEffect(() => {
    // Inicializar serviços
    const initializeServices = async () => {
      try {
        // Inicializar AdMob
        await mobileAds().initialize();
        console.log('🎯 AdMob inicializado com sucesso');

        // Aguardar um pouco antes de inicializar Interstitial Ads
        setTimeout(() => {
          try {
            InterstitialAdService.getInstance();
            console.log('🎯 Interstitial Ads inicializados');
          } catch (error) {
            console.error('❌ Erro ao inicializar Interstitial Ads:', error);
          }
        }, 2000);

        // Inicializar Firebase Messaging
        const messagingService = FirebaseMessagingService.getInstance();
        await messagingService.initialize();

        // Inscrever em tópicos padrão
        await messagingService.subscribeToTopic('escola_sabatina_geral');
        await messagingService.subscribeToTopic('licoes_semanais');

      } catch (error) {
        console.error('❌ Erro ao inicializar serviços:', error);
      }
    };

    initializeServices();
  }, []);

  if (showSplash) {
    return <SplashScreen onFinish={() => setShowSplash(false)} />;
  }

  return (
    <ThemeProvider>
      <AuthProvider>
        <LessonProvider>
          <AppProvider>
            <NavigationContainer>
              <MainNavigator />
            </NavigationContainer>
          </AppProvider>
        </LessonProvider>
      </AuthProvider>
    </ThemeProvider>
  );
};

export default App;
