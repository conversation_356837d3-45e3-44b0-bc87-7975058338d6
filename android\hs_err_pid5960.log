#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 115343360 bytes. Error detail: G1 virtual space
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (os_windows.cpp:3828), pid=5960, tid=15076
#
# JRE version: OpenJDK Runtime Environment (21.0.5) (build 21.0.5+-13047016-b750.29)
# Java VM: OpenJDK 64-Bit Server VM (21.0.5+-13047016-b750.29, mixed mode, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -XX:MaxMetaspaceSize=512m -XX:+HeapDumpOnOutOfMemoryError -Xmx1536m -Dfile.encoding=UTF-8 -Duser.country=BR -Duser.language=pt -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.14.1-bin\baw1sv0jfoi8rxs14qo3h49cs\gradle-8.14.1\lib\agents\gradle-instrumentation-agent-8.14.1.jar org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.14.1

Host: Intel(R) Xeon(R) CPU E3-1220 V2 @ 3.10GHz, 4 cores, 7G,  Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
Time: Wed Aug  6 14:20:30 2025 Hora oficial do Brasil elapsed time: 479.330628 seconds (0d 0h 7m 59s)

---------------  T H R E A D  ---------------

Current thread (0x00000294bca74d40):  VMThread "VM Thread"          [id=15076, stack(0x000000e348600000,0x000000e348700000) (1024K)]

Stack: [0x000000e348600000,0x000000e348700000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6cfb29]
V  [jvm.dll+0x85df93]
V  [jvm.dll+0x8604ee]
V  [jvm.dll+0x860bd3]
V  [jvm.dll+0x27e6b6]
V  [jvm.dll+0x6cc385]
V  [jvm.dll+0x6c078a]
V  [jvm.dll+0x35525b]
V  [jvm.dll+0x35ceb6]
V  [jvm.dll+0x3aedd6]
V  [jvm.dll+0x3af0a8]
V  [jvm.dll+0x32792c]
V  [jvm.dll+0x32a336]
V  [jvm.dll+0x33426d]
V  [jvm.dll+0x36b8e5]
V  [jvm.dll+0x866958]
V  [jvm.dll+0x867cd4]
V  [jvm.dll+0x868210]
V  [jvm.dll+0x8684a3]
V  [jvm.dll+0x806368]
V  [jvm.dll+0x6ce3fd]
C  [ucrtbase.dll+0x21bb2]
C  [KERNEL32.DLL+0x17374]
C  [ntdll.dll+0x4cc91]

VM_Operation (0x000000e3481ff700): G1PauseRemark, mode: safepoint, requested by thread 0x00000294a6657700


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00000294c7ae2410, length=113, elements={
0x00000294a44b61a0, 0x00000294c1041940, 0x00000294c1042650, 0x00000294c106fe60,
0x00000294c10708b0, 0x00000294c1044760, 0x00000294c10451b0, 0x00000294c1079250,
0x00000294c1099cf0, 0x00000294bc9e5a10, 0x00000294c13f9470, 0x00000294c20aaca0,
0x00000294c2c95160, 0x00000294c2005070, 0x00000294c2d32940, 0x00000294c2d31c20,
0x00000294c2d32fd0, 0x00000294c55da420, 0x00000294c55daab0, 0x00000294c55d9d90,
0x00000294c55d9070, 0x00000294c5a776f0, 0x00000294c14ee780, 0x00000294c23b1d10,
0x00000294c23b23a0, 0x00000294cf4f1890, 0x00000294cf4eeaa0, 0x00000294c5dbc020,
0x00000294c5dbc6b0, 0x00000294c5dc2920, 0x00000294c5dbcd40, 0x00000294c5dbda60,
0x00000294c5dbd3d0, 0x00000294c5dbe0f0, 0x00000294c5dbee10, 0x00000294c6e078f0,
0x00000294c6e07f80, 0x00000294c6e08ca0, 0x00000294c6e09330, 0x00000294c6e0a050,
0x00000294c6e0c120, 0x00000294c6e0a6e0, 0x00000294c2d31590, 0x00000294c2d322b0,
0x00000294c2d301e0, 0x00000294c2d33660, 0x00000294c2d30870, 0x00000294c2d30f00,
0x00000294c23aad80, 0x00000294c23adb70, 0x00000294c23afc40, 0x00000294c23ac130,
0x00000294c55db140, 0x00000294c55dbe60, 0x00000294c55dc4f0, 0x00000294c55d9700,
0x00000294ceca5e70, 0x00000294ceca3da0, 0x00000294ceca7220, 0x00000294ceca3080,
0x00000294cecaa010, 0x00000294ceca6500, 0x00000294ceca5150, 0x00000294ceca57e0,
0x00000294ceca92f0, 0x00000294ceca4ac0, 0x00000294ceca6b90, 0x00000294ceca7f40,
0x00000294ceca78b0, 0x00000294ceca9980, 0x00000294ceca85d0, 0x00000294ceca8c60,
0x00000294cecaa6a0, 0x00000294cecaad30, 0x00000294cecab3c0, 0x00000294ceca4430,
0x00000294cecace00, 0x00000294cecb1cc0, 0x00000294cecafbf0, 0x00000294cecb0280,
0x00000294cecb2350, 0x00000294cecb0910, 0x00000294cecb0fa0, 0x00000294cecb1630,
0x00000294cecaf560, 0x00000294cecaba50, 0x00000294cecae1b0, 0x00000294cecaeed0,
0x00000294cf4f25b0, 0x00000294c5a769d0, 0x00000294c5a75620, 0x00000294c5a78aa0,
0x00000294c5a74f90, 0x00000294d03e8380, 0x00000294d03e4870, 0x00000294d03e5590,
0x00000294d03eaae0, 0x00000294c5a77d80, 0x00000294c5dbb990, 0x00000294c23b02d0,
0x00000294c6e0ba90, 0x00000294d5264c80, 0x00000294d526a860, 0x00000294d5266d50,
0x00000294d5268790, 0x00000294d526aef0, 0x00000294d526d650, 0x00000294d5271e80,
0x00000294cf90b440, 0x00000294d52673e0, 0x00000294d5266030, 0x00000294d52694b0,
0x00000294d5269b40
}

Java Threads: ( => current thread )
  0x00000294a44b61a0 JavaThread "main"                              [_thread_blocked, id=4016, stack(0x000000e347f00000,0x000000e348000000) (1024K)]
  0x00000294c1041940 JavaThread "Reference Handler"          daemon [_thread_blocked, id=10540, stack(0x000000e348800000,0x000000e348900000) (1024K)]
  0x00000294c1042650 JavaThread "Finalizer"                  daemon [_thread_blocked, id=8080, stack(0x000000e348900000,0x000000e348a00000) (1024K)]
  0x00000294c106fe60 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=9992, stack(0x000000e348a00000,0x000000e348b00000) (1024K)]
  0x00000294c10708b0 JavaThread "Attach Listener"            daemon [_thread_blocked, id=1256, stack(0x000000e348b00000,0x000000e348c00000) (1024K)]
  0x00000294c1044760 JavaThread "Service Thread"             daemon [_thread_blocked, id=3864, stack(0x000000e348c00000,0x000000e348d00000) (1024K)]
  0x00000294c10451b0 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=11180, stack(0x000000e348d00000,0x000000e348e00000) (1024K)]
  0x00000294c1079250 JavaThread "C2 CompilerThread0"         daemon [_thread_blocked, id=1264, stack(0x000000e348e00000,0x000000e348f00000) (1024K)]
  0x00000294c1099cf0 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=13360, stack(0x000000e348f00000,0x000000e349000000) (1024K)]
  0x00000294bc9e5a10 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=12968, stack(0x000000e349000000,0x000000e349100000) (1024K)]
  0x00000294c13f9470 JavaThread "Notification Thread"        daemon [_thread_blocked, id=12956, stack(0x000000e349100000,0x000000e349200000) (1024K)]
  0x00000294c20aaca0 JavaThread "Daemon health stats"               [_thread_blocked, id=10108, stack(0x000000e349400000,0x000000e349500000) (1024K)]
  0x00000294c2c95160 JavaThread "Incoming local TCP Connector on port 56875"        [_thread_in_native, id=17016, stack(0x000000e349700000,0x000000e349800000) (1024K)]
  0x00000294c2005070 JavaThread "Daemon periodic checks"            [_thread_blocked, id=4264, stack(0x000000e349800000,0x000000e349900000) (1024K)]
  0x00000294c2d32940 JavaThread "Cache worker for journal cache (C:\Users\<USER>\.gradle\caches\journal-1)"        [_thread_blocked, id=10532, stack(0x000000e34a000000,0x000000e34a100000) (1024K)]
  0x00000294c2d31c20 JavaThread "File lock request listener"        [_thread_in_native, id=2484, stack(0x000000e34a100000,0x000000e34a200000) (1024K)]
  0x00000294c2d32fd0 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\.gradle\caches\8.14.1\fileHashes)"        [_thread_blocked, id=2584, stack(0x000000e34a200000,0x000000e34a300000) (1024K)]
  0x00000294c55da420 JavaThread "File watcher server"        daemon [_thread_blocked, id=14536, stack(0x000000e34a500000,0x000000e34a600000) (1024K)]
  0x00000294c55daab0 JavaThread "File watcher consumer"      daemon [_thread_blocked, id=12280, stack(0x000000e34a600000,0x000000e34a700000) (1024K)]
  0x00000294c55d9d90 JavaThread "jar transforms"                    [_thread_blocked, id=15784, stack(0x000000e34a700000,0x000000e34a800000) (1024K)]
  0x00000294c55d9070 JavaThread "Cache worker for file content cache (C:\Users\<USER>\.gradle\caches\8.14.1\fileContent)"        [_thread_blocked, id=4368, stack(0x000000e34a900000,0x000000e34aa00000) (1024K)]
  0x00000294c5a776f0 JavaThread "Memory manager"                    [_thread_blocked, id=9880, stack(0x000000e349600000,0x000000e349700000) (1024K)]
  0x00000294c14ee780 JavaThread "jar transforms Thread 2"           [_thread_blocked, id=1196, stack(0x000000e34df00000,0x000000e34e000000) (1024K)]
  0x00000294c23b1d10 JavaThread "jar transforms Thread 3"           [_thread_blocked, id=16732, stack(0x000000e347d00000,0x000000e347e00000) (1024K)]
  0x00000294c23b23a0 JavaThread "jar transforms Thread 4"           [_thread_blocked, id=10780, stack(0x000000e34e400000,0x000000e34e500000) (1024K)]
  0x00000294cf4f1890 JavaThread "Daemon Thread 2"                   [_thread_blocked, id=12988, stack(0x000000e347e00000,0x000000e347f00000) (1024K)]
  0x00000294cf4eeaa0 JavaThread "Daemon worker Thread 2"            [_thread_blocked, id=12016, stack(0x000000e349a00000,0x000000e349b00000) (1024K)]
  0x00000294c5dbc020 JavaThread "Handler for socket connection from /127.0.0.1:56875 to /127.0.0.1:57018"        [_thread_in_native, id=11184, stack(0x000000e348700000,0x000000e348800000) (1024K)]
  0x00000294c5dbc6b0 JavaThread "Cancel handler"                    [_thread_blocked, id=12704, stack(0x000000e349900000,0x000000e349a00000) (1024K)]
  0x00000294c5dc2920 JavaThread "Asynchronous log dispatcher for DefaultDaemonConnection: socket connection from /127.0.0.1:56875 to /127.0.0.1:57018"        [_thread_blocked, id=14248, stack(0x000000e349b00000,0x000000e349c00000) (1024K)]
  0x00000294c5dbcd40 JavaThread "Stdin handler"                     [_thread_blocked, id=5928, stack(0x000000e349d00000,0x000000e349e00000) (1024K)]
  0x00000294c5dbda60 JavaThread "Daemon client event forwarder"        [_thread_blocked, id=5096, stack(0x000000e349e00000,0x000000e349f00000) (1024K)]
  0x00000294c5dbd3d0 JavaThread "Cache worker for file hash cache (F:\PROJETO\React\EscolaSabatina\android\.gradle\8.14.1\fileHashes)"        [_thread_blocked, id=16416, stack(0x000000e349f00000,0x000000e34a000000) (1024K)]
  0x00000294c5dbe0f0 JavaThread "Cache worker for Build Output Cleanup Cache (F:\PROJETO\React\EscolaSabatina\android\.gradle\buildOutputCleanup)"        [_thread_blocked, id=15476, stack(0x000000e34a300000,0x000000e34a400000) (1024K)]
  0x00000294c5dbee10 JavaThread "Cache worker for checksums cache (F:\PROJETO\React\EscolaSabatina\android\.gradle\8.14.1\checksums)"        [_thread_blocked, id=5380, stack(0x000000e34a400000,0x000000e34a500000) (1024K)]
  0x00000294c6e078f0 JavaThread "Cache worker for cache directory md-supplier (C:\Users\<USER>\.gradle\caches\8.14.1\md-supplier)"        [_thread_blocked, id=12064, stack(0x000000e34a800000,0x000000e34a900000) (1024K)]
  0x00000294c6e07f80 JavaThread "Cache worker for cache directory md-rule (C:\Users\<USER>\.gradle\caches\8.14.1\md-rule)"        [_thread_blocked, id=7264, stack(0x000000e34aa00000,0x000000e34ab00000) (1024K)]
  0x00000294c6e08ca0 JavaThread "Cache worker for Build Output Cleanup Cache (F:\PROJETO\React\EscolaSabatina\node_modules\@react-native\gradle-plugin\.gradle\buildOutputCleanup)"        [_thread_blocked, id=9884, stack(0x000000e34ab00000,0x000000e34ac00000) (1024K)]
  0x00000294c6e09330 JavaThread "Unconstrained build operations"        [_thread_blocked, id=16224, stack(0x000000e34ac00000,0x000000e34ad00000) (1024K)]
  0x00000294c6e0a050 JavaThread "Unconstrained build operations Thread 2"        [_thread_blocked, id=16164, stack(0x000000e34ad00000,0x000000e34ae00000) (1024K)]
  0x00000294c6e0c120 JavaThread "Unconstrained build operations Thread 3"        [_thread_blocked, id=6308, stack(0x000000e34ae00000,0x000000e34af00000) (1024K)]
  0x00000294c6e0a6e0 JavaThread "Unconstrained build operations Thread 4"        [_thread_blocked, id=12768, stack(0x000000e34af00000,0x000000e34b000000) (1024K)]
  0x00000294c2d31590 JavaThread "Unconstrained build operations Thread 5"        [_thread_blocked, id=8664, stack(0x000000e34b000000,0x000000e34b100000) (1024K)]
  0x00000294c2d322b0 JavaThread "Unconstrained build operations Thread 6"        [_thread_blocked, id=11956, stack(0x000000e34b100000,0x000000e34b200000) (1024K)]
  0x00000294c2d301e0 JavaThread "Unconstrained build operations Thread 7"        [_thread_blocked, id=10676, stack(0x000000e34b200000,0x000000e34b300000) (1024K)]
  0x00000294c2d33660 JavaThread "Unconstrained build operations Thread 8"        [_thread_blocked, id=5224, stack(0x000000e34b300000,0x000000e34b400000) (1024K)]
  0x00000294c2d30870 JavaThread "Unconstrained build operations Thread 9"        [_thread_blocked, id=2396, stack(0x000000e34b400000,0x000000e34b500000) (1024K)]
  0x00000294c2d30f00 JavaThread "Unconstrained build operations Thread 10"        [_thread_blocked, id=13284, stack(0x000000e34b500000,0x000000e34b600000) (1024K)]
  0x00000294c23aad80 JavaThread "build event listener"              [_thread_blocked, id=8964, stack(0x000000e34b600000,0x000000e34b700000) (1024K)]
  0x00000294c23adb70 JavaThread "included builds"                   [_thread_blocked, id=6712, stack(0x000000e34bc00000,0x000000e34bd00000) (1024K)]
  0x00000294c23afc40 JavaThread "Execution worker"                  [_thread_blocked, id=11388, stack(0x000000e34bd00000,0x000000e34be00000) (1024K)]
  0x00000294c23ac130 JavaThread "Execution worker Thread 2"         [_thread_blocked, id=6060, stack(0x000000e34be00000,0x000000e34bf00000) (1024K)]
  0x00000294c55db140 JavaThread "Execution worker Thread 3"         [_thread_blocked, id=14036, stack(0x000000e34bf00000,0x000000e34c000000) (1024K)]
  0x00000294c55dbe60 JavaThread "Cache worker for execution history cache (F:\PROJETO\React\EscolaSabatina\node_modules\@react-native\gradle-plugin\.gradle\8.14.1\executionHistory)"        [_thread_blocked, id=724, stack(0x000000e34c000000,0x000000e34c100000) (1024K)]
  0x00000294c55dc4f0 JavaThread "Unconstrained build operations Thread 11"        [_thread_blocked, id=10000, stack(0x000000e34c100000,0x000000e34c200000) (1024K)]
  0x00000294c55d9700 JavaThread "Unconstrained build operations Thread 12"        [_thread_blocked, id=7916, stack(0x000000e34c300000,0x000000e34c400000) (1024K)]
  0x00000294ceca5e70 JavaThread "Unconstrained build operations Thread 13"        [_thread_blocked, id=4276, stack(0x000000e34c400000,0x000000e34c500000) (1024K)]
  0x00000294ceca3da0 JavaThread "Unconstrained build operations Thread 14"        [_thread_blocked, id=6420, stack(0x000000e34c500000,0x000000e34c600000) (1024K)]
  0x00000294ceca7220 JavaThread "Unconstrained build operations Thread 15"        [_thread_blocked, id=12984, stack(0x000000e34c600000,0x000000e34c700000) (1024K)]
  0x00000294ceca3080 JavaThread "Unconstrained build operations Thread 16"        [_thread_blocked, id=11276, stack(0x000000e34c700000,0x000000e34c800000) (1024K)]
  0x00000294cecaa010 JavaThread "Unconstrained build operations Thread 17"        [_thread_blocked, id=7712, stack(0x000000e34c800000,0x000000e34c900000) (1024K)]
  0x00000294ceca6500 JavaThread "Unconstrained build operations Thread 18"        [_thread_blocked, id=2600, stack(0x000000e34c900000,0x000000e34ca00000) (1024K)]
  0x00000294ceca5150 JavaThread "Unconstrained build operations Thread 19"        [_thread_blocked, id=3428, stack(0x000000e34ca00000,0x000000e34cb00000) (1024K)]
  0x00000294ceca57e0 JavaThread "Unconstrained build operations Thread 20"        [_thread_blocked, id=8108, stack(0x000000e34cb00000,0x000000e34cc00000) (1024K)]
  0x00000294ceca92f0 JavaThread "Unconstrained build operations Thread 21"        [_thread_blocked, id=9896, stack(0x000000e34cc00000,0x000000e34cd00000) (1024K)]
  0x00000294ceca4ac0 JavaThread "Unconstrained build operations Thread 22"        [_thread_blocked, id=12952, stack(0x000000e34cd00000,0x000000e34ce00000) (1024K)]
  0x00000294ceca6b90 JavaThread "Unconstrained build operations Thread 23"        [_thread_blocked, id=1680, stack(0x000000e34ce00000,0x000000e34cf00000) (1024K)]
  0x00000294ceca7f40 JavaThread "Unconstrained build operations Thread 24"        [_thread_blocked, id=7832, stack(0x000000e34cf00000,0x000000e34d000000) (1024K)]
  0x00000294ceca78b0 JavaThread "Unconstrained build operations Thread 25"        [_thread_blocked, id=14716, stack(0x000000e34d000000,0x000000e34d100000) (1024K)]
  0x00000294ceca9980 JavaThread "Unconstrained build operations Thread 26"        [_thread_blocked, id=7624, stack(0x000000e34d100000,0x000000e34d200000) (1024K)]
  0x00000294ceca85d0 JavaThread "Unconstrained build operations Thread 27"        [_thread_blocked, id=14440, stack(0x000000e34d200000,0x000000e34d300000) (1024K)]
  0x00000294ceca8c60 JavaThread "Unconstrained build operations Thread 28"        [_thread_blocked, id=5920, stack(0x000000e34d300000,0x000000e34d400000) (1024K)]
  0x00000294cecaa6a0 JavaThread "Unconstrained build operations Thread 29"        [_thread_blocked, id=11072, stack(0x000000e34d400000,0x000000e34d500000) (1024K)]
  0x00000294cecaad30 JavaThread "Unconstrained build operations Thread 30"        [_thread_blocked, id=3420, stack(0x000000e34d500000,0x000000e34d600000) (1024K)]
  0x00000294cecab3c0 JavaThread "Unconstrained build operations Thread 31"        [_thread_blocked, id=9768, stack(0x000000e34d600000,0x000000e34d700000) (1024K)]
  0x00000294ceca4430 JavaThread "Unconstrained build operations Thread 32"        [_thread_blocked, id=17280, stack(0x000000e34d700000,0x000000e34d800000) (1024K)]
  0x00000294cecace00 JavaThread "build event listener"              [_thread_blocked, id=12720, stack(0x000000e34d800000,0x000000e34d900000) (1024K)]
  0x00000294cecb1cc0 JavaThread "Problems report writer"            [_thread_blocked, id=15548, stack(0x000000e34d900000,0x000000e34da00000) (1024K)]
  0x00000294cecafbf0 JavaThread "Unconstrained build operations Thread 33"        [_thread_blocked, id=12688, stack(0x000000e34da00000,0x000000e34db00000) (1024K)]
  0x00000294cecb0280 JavaThread "Unconstrained build operations Thread 34"        [_thread_blocked, id=13632, stack(0x000000e34db00000,0x000000e34dc00000) (1024K)]
  0x00000294cecb2350 JavaThread "Unconstrained build operations Thread 35"        [_thread_blocked, id=15468, stack(0x000000e34dc00000,0x000000e34dd00000) (1024K)]
  0x00000294cecb0910 JavaThread "Unconstrained build operations Thread 36"        [_thread_blocked, id=12328, stack(0x000000e34dd00000,0x000000e34de00000) (1024K)]
  0x00000294cecb0fa0 JavaThread "Unconstrained build operations Thread 37"        [_thread_blocked, id=17356, stack(0x000000e34de00000,0x000000e34df00000) (1024K)]
  0x00000294cecb1630 JavaThread "Unconstrained build operations Thread 38"        [_thread_blocked, id=17224, stack(0x000000e34e000000,0x000000e34e100000) (1024K)]
  0x00000294cecaf560 JavaThread "Unconstrained build operations Thread 39"        [_thread_blocked, id=2376, stack(0x000000e34e500000,0x000000e34e600000) (1024K)]
  0x00000294cecaba50 JavaThread "Unconstrained build operations Thread 40"        [_thread_blocked, id=9716, stack(0x000000e34e600000,0x000000e34e700000) (1024K)]
  0x00000294cecae1b0 JavaThread "Cache worker for execution history cache (F:\PROJETO\React\EscolaSabatina\android\.gradle\8.14.1\executionHistory)"        [_thread_blocked, id=1524, stack(0x000000e34e900000,0x000000e34ea00000) (1024K)]
  0x00000294cecaeed0 JavaThread "WorkerExecutor Queue Thread 2"        [_thread_blocked, id=15536, stack(0x000000e34eb00000,0x000000e34ec00000) (1024K)]
  0x00000294cf4f25b0 JavaThread "WorkerExecutor Queue Thread 3"        [_thread_blocked, id=9996, stack(0x000000e34ec00000,0x000000e34ed00000) (1024K)]
  0x00000294c5a769d0 JavaThread "pool-7-thread-1"                   [_thread_blocked, id=1676, stack(0x000000e34ee00000,0x000000e34ef00000) (1024K)]
  0x00000294c5a75620 JavaThread "stderr"                            [_thread_in_native, id=12152, stack(0x000000e34ef00000,0x000000e34f000000) (1024K)]
  0x00000294c5a78aa0 JavaThread "stdout"                            [_thread_in_native, id=5924, stack(0x000000e34f000000,0x000000e34f100000) (1024K)]
  0x00000294c5a74f90 JavaThread "Cache worker for Java compile cache (C:\Users\<USER>\.gradle\caches\8.14.1\javaCompile)"        [_thread_blocked, id=1504, stack(0x000000e34f100000,0x000000e34f200000) (1024K)]
  0x00000294d03e8380 JavaThread "Build operations"                  [_thread_blocked, id=14168, stack(0x000000e34f200000,0x000000e34f300000) (1024K)]
  0x00000294d03e4870 JavaThread "Build operations Thread 2"         [_thread_blocked, id=17268, stack(0x000000e34f300000,0x000000e34f400000) (1024K)]
  0x00000294d03e5590 JavaThread "Build operations Thread 3"         [_thread_blocked, id=12788, stack(0x000000e34f400000,0x000000e34f500000) (1024K)]
  0x00000294d03eaae0 JavaThread "Build operations Thread 4"         [_thread_blocked, id=5372, stack(0x000000e34ea00000,0x000000e34eb00000) (1024K)]
  0x00000294c5a77d80 JavaThread "RMI Scheduler(0)"           daemon [_thread_blocked, id=6168, stack(0x000000e34ba00000,0x000000e34bb00000) (1024K)]
  0x00000294c5dbb990 JavaThread "RMI GC Daemon"              daemon [_thread_blocked, id=8072, stack(0x000000e34f600000,0x000000e34f700000) (1024K)]
  0x00000294c23b02d0 JavaThread "RMI TCP Accept-0"           daemon [_thread_in_native, id=13736, stack(0x000000e34f700000,0x000000e34f800000) (1024K)]
  0x00000294c6e0ba90 JavaThread "RMI Reaper"                        [_thread_blocked, id=6972, stack(0x000000e34f800000,0x000000e34f900000) (1024K)]
  0x00000294d5264c80 JavaThread "RMI TCP Connection(idle)"   daemon [_thread_blocked, id=3200, stack(0x000000e34fa00000,0x000000e34fb00000) (1024K)]
  0x00000294d526a860 JavaThread "Exec process Thread 7"             [_thread_blocked, id=15868, stack(0x000000e34b700000,0x000000e34b800000) (1024K)]
  0x00000294d5266d50 JavaThread "Exec process Thread 8"             [_thread_blocked, id=10180, stack(0x000000e34e200000,0x000000e34e300000) (1024K)]
  0x00000294d5268790 JavaThread "Exec process Thread 9"             [_thread_blocked, id=1344, stack(0x000000e34e300000,0x000000e34e400000) (1024K)]
  0x00000294d526aef0 JavaThread "Exec process Thread 10"            [_thread_blocked, id=12548, stack(0x000000e350000000,0x000000e350100000) (1024K)]
  0x00000294d526d650 JavaThread "WorkerExecutor Queue Thread 7"        [_thread_blocked, id=11120, stack(0x000000e34b800000,0x000000e34b900000) (1024K)]
  0x00000294d5271e80 JavaThread "WorkerExecutor Queue Thread 8"        [_thread_blocked, id=16640, stack(0x000000e349c00000,0x000000e349d00000) (1024K)]
  0x00000294cf90b440 JavaThread "C2 CompilerThread1"         daemon [_thread_blocked, id=14772, stack(0x000000e34b900000,0x000000e34ba00000) (1024K)]
  0x00000294d52673e0 JavaThread "ForkJoinPool-1-worker-1"    daemon [_thread_blocked, id=12524, stack(0x000000e34bb00000,0x000000e34bc00000) (1024K)]
  0x00000294d5266030 JavaThread "ForkJoinPool-1-worker-2"    daemon [_thread_blocked, id=15056, stack(0x000000e34e100000,0x000000e34e200000) (1024K)]
  0x00000294d52694b0 JavaThread "ForkJoinPool-1-worker-3"    daemon [_thread_blocked, id=6336, stack(0x000000e34e700000,0x000000e34e800000) (1024K)]
  0x00000294d5269b40 JavaThread "ForkJoinPool-1-worker-4"    daemon [_thread_blocked, id=9548, stack(0x000000e34e800000,0x000000e34e900000) (1024K)]
Total: 113

Other Threads:
=>0x00000294bca74d40 VMThread "VM Thread"                           [id=15076, stack(0x000000e348600000,0x000000e348700000) (1024K)]
  0x00000294bc9e4d00 WatcherThread "VM Periodic Task Thread"        [id=3560, stack(0x000000e348500000,0x000000e348600000) (1024K)]
  0x00000294a664a5b0 WorkerThread "GC Thread#0"                     [id=1556, stack(0x000000e348000000,0x000000e348100000) (1024K)]
  0x00000294c1448a80 WorkerThread "GC Thread#1"                     [id=12580, stack(0x000000e349200000,0x000000e349300000) (1024K)]
  0x00000294c1448e20 WorkerThread "GC Thread#2"                     [id=14620, stack(0x000000e349300000,0x000000e349400000) (1024K)]
  0x00000294c1f1c260 WorkerThread "GC Thread#3"                     [id=16780, stack(0x000000e349500000,0x000000e349600000) (1024K)]
  0x00000294a6657700 ConcurrentGCThread "G1 Main Marker"            [id=10048, stack(0x000000e348100000,0x000000e348200000) (1024K)]
  0x00000294a665b9c0 WorkerThread "G1 Conc#0"                       [id=15500, stack(0x000000e348200000,0x000000e348300000) (1024K)]
  0x00000294a66ce4d0 ConcurrentGCThread "G1 Refine#0"               [id=16688, stack(0x000000e348300000,0x000000e348400000) (1024K)]
  0x00000294bc8a80e0 ConcurrentGCThread "G1 Service"                [id=1424, stack(0x000000e348400000,0x000000e348500000) (1024K)]
Total: 10

Threads with active compile tasks:
C2 CompilerThread0  479372 57528       4       com.android.tools.r8.graph.I2::a (285 bytes)
C2 CompilerThread1  479372 57515       4       com.android.tools.r8.dex.t0::a (43 bytes)
Total: 2

VM state: at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007ffea43dd360] Threads_lock - owner thread: 0x00000294bca74d40
[0x00007ffea43dd460] Heap_lock - owner thread: 0x00000294a6657700

Heap address: 0x00000000a0000000, size: 1536 MB, Compressed Oops mode: 32-bit

CDS archive(s) not mapped
Compressed class space mapped at: 0x0000000100000000-0x000000011a000000, reserved size: 436207616
Narrow klass base: 0x0000000000000000, Narrow klass shift: 3, Narrow klass range: 0x11a000000

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 5 size 8 Array Of Cards #cards 12 size 40 Howl #buckets 4 coarsen threshold 1843 Howl Bitmap #cards 512 size 80 coarsen threshold 460 Card regions per heap region 1 cards per card region 2048
 CPUs: 4 total, 4 available
 Memory: 8161M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Heap Region Size: 1M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 128M
 Heap Max Capacity: 1536M
 Pre-touch: Disabled
 Parallel Workers: 4
 Concurrent Workers: 1
 Concurrent Refinement Workers: 4
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 1029120K, used 754688K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 81 young (82944K), 12 survivors (12288K)
 Metaspace       used 218155K, committed 221632K, reserved 622592K
  class space    used 28058K, committed 29760K, reserved 425984K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom
|   0|0x00000000a0000000, 0x00000000a0100000, 0x00000000a0100000|100%|HS|  |TAMS 0x00000000a0100000| PB 0x00000000a0000000| Complete 
|   1|0x00000000a0100000, 0x00000000a0200000, 0x00000000a0200000|100%|HC|  |TAMS 0x00000000a0200000| PB 0x00000000a0100000| Complete 
|   2|0x00000000a0200000, 0x00000000a0300000, 0x00000000a0300000|100%|HC|  |TAMS 0x00000000a0300000| PB 0x00000000a0200000| Complete 
|   3|0x00000000a0300000, 0x00000000a0400000, 0x00000000a0400000|100%| O|  |TAMS 0x00000000a0400000| PB 0x00000000a0400000| Updating 
|   4|0x00000000a0400000, 0x00000000a0500000, 0x00000000a0500000|100%| O|  |TAMS 0x00000000a0500000| PB 0x00000000a0500000| Untracked 
|   5|0x00000000a0500000, 0x00000000a0600000, 0x00000000a0600000|100%| O|  |TAMS 0x00000000a0600000| PB 0x00000000a0600000| Untracked 
|   6|0x00000000a0600000, 0x00000000a0700000, 0x00000000a0700000|100%| O|  |TAMS 0x00000000a0700000| PB 0x00000000a0700000| Untracked 
|   7|0x00000000a0700000, 0x00000000a0800000, 0x00000000a0800000|100%|HS|  |TAMS 0x00000000a0800000| PB 0x00000000a0700000| Complete 
|   8|0x00000000a0800000, 0x00000000a0900000, 0x00000000a0900000|100%| O|  |TAMS 0x00000000a0900000| PB 0x00000000a0900000| Untracked 
|   9|0x00000000a0900000, 0x00000000a0a00000, 0x00000000a0a00000|100%| O|  |TAMS 0x00000000a0a00000| PB 0x00000000a0a00000| Untracked 
|  10|0x00000000a0a00000, 0x00000000a0b00000, 0x00000000a0b00000|100%| O|  |TAMS 0x00000000a0b00000| PB 0x00000000a0b00000| Untracked 
|  11|0x00000000a0b00000, 0x00000000a0c00000, 0x00000000a0c00000|100%| O|  |TAMS 0x00000000a0c00000| PB 0x00000000a0c00000| Untracked 
|  12|0x00000000a0c00000, 0x00000000a0d00000, 0x00000000a0d00000|100%| O|  |TAMS 0x00000000a0d00000| PB 0x00000000a0d00000| Updating 
|  13|0x00000000a0d00000, 0x00000000a0e00000, 0x00000000a0e00000|100%| O|  |TAMS 0x00000000a0e00000| PB 0x00000000a0e00000| Untracked 
|  14|0x00000000a0e00000, 0x00000000a0f00000, 0x00000000a0f00000|100%| O|  |TAMS 0x00000000a0f00000| PB 0x00000000a0f00000| Untracked 
|  15|0x00000000a0f00000, 0x00000000a1000000, 0x00000000a1000000|100%| O|  |TAMS 0x00000000a1000000| PB 0x00000000a1000000| Untracked 
|  16|0x00000000a1000000, 0x00000000a1100000, 0x00000000a1100000|100%| O|  |TAMS 0x00000000a1100000| PB 0x00000000a1100000| Untracked 
|  17|0x00000000a1100000, 0x00000000a1200000, 0x00000000a1200000|100%| O|  |TAMS 0x00000000a1200000| PB 0x00000000a1200000| Untracked 
|  18|0x00000000a1200000, 0x00000000a1300000, 0x00000000a1300000|100%| O|  |TAMS 0x00000000a1300000| PB 0x00000000a1300000| Untracked 
|  19|0x00000000a1300000, 0x00000000a1400000, 0x00000000a1400000|100%| O|  |TAMS 0x00000000a1400000| PB 0x00000000a1400000| Untracked 
|  20|0x00000000a1400000, 0x00000000a1500000, 0x00000000a1500000|100%| O|  |TAMS 0x00000000a1500000| PB 0x00000000a1500000| Updating 
|  21|0x00000000a1500000, 0x00000000a1600000, 0x00000000a1600000|100%| O|  |TAMS 0x00000000a1600000| PB 0x00000000a1600000| Untracked 
|  22|0x00000000a1600000, 0x00000000a1700000, 0x00000000a1700000|100%| O|  |TAMS 0x00000000a1700000| PB 0x00000000a1700000| Untracked 
|  23|0x00000000a1700000, 0x00000000a1800000, 0x00000000a1800000|100%| O|  |TAMS 0x00000000a1800000| PB 0x00000000a1800000| Updating 
|  24|0x00000000a1800000, 0x00000000a1900000, 0x00000000a1900000|100%| O|  |TAMS 0x00000000a1900000| PB 0x00000000a1900000| Updating 
|  25|0x00000000a1900000, 0x00000000a1a00000, 0x00000000a1a00000|100%| O|  |TAMS 0x00000000a1a00000| PB 0x00000000a1a00000| Untracked 
|  26|0x00000000a1a00000, 0x00000000a1b00000, 0x00000000a1b00000|100%| O|  |TAMS 0x00000000a1b00000| PB 0x00000000a1b00000| Untracked 
|  27|0x00000000a1b00000, 0x00000000a1c00000, 0x00000000a1c00000|100%| O|  |TAMS 0x00000000a1c00000| PB 0x00000000a1c00000| Untracked 
|  28|0x00000000a1c00000, 0x00000000a1d00000, 0x00000000a1d00000|100%| O|  |TAMS 0x00000000a1d00000| PB 0x00000000a1d00000| Untracked 
|  29|0x00000000a1d00000, 0x00000000a1e00000, 0x00000000a1e00000|100%| O|  |TAMS 0x00000000a1e00000| PB 0x00000000a1e00000| Updating 
|  30|0x00000000a1e00000, 0x00000000a1f00000, 0x00000000a1f00000|100%| O|  |TAMS 0x00000000a1f00000| PB 0x00000000a1f00000| Untracked 
|  31|0x00000000a1f00000, 0x00000000a2000000, 0x00000000a2000000|100%| O|  |TAMS 0x00000000a2000000| PB 0x00000000a2000000| Updating 
|  32|0x00000000a2000000, 0x00000000a2100000, 0x00000000a2100000|100%| O|  |TAMS 0x00000000a2100000| PB 0x00000000a2100000| Updating 
|  33|0x00000000a2100000, 0x00000000a2200000, 0x00000000a2200000|100%| O|  |TAMS 0x00000000a2200000| PB 0x00000000a2200000| Updating 
|  34|0x00000000a2200000, 0x00000000a2300000, 0x00000000a2300000|100%| O|  |TAMS 0x00000000a2300000| PB 0x00000000a2300000| Updating 
|  35|0x00000000a2300000, 0x00000000a2400000, 0x00000000a2400000|100%| O|  |TAMS 0x00000000a2400000| PB 0x00000000a2400000| Updating 
|  36|0x00000000a2400000, 0x00000000a2500000, 0x00000000a2500000|100%| O|  |TAMS 0x00000000a2500000| PB 0x00000000a2500000| Updating 
|  37|0x00000000a2500000, 0x00000000a2600000, 0x00000000a2600000|100%| O|  |TAMS 0x00000000a2600000| PB 0x00000000a2600000| Updating 
|  38|0x00000000a2600000, 0x00000000a2700000, 0x00000000a2700000|100%| O|  |TAMS 0x00000000a2700000| PB 0x00000000a2700000| Updating 
|  39|0x00000000a2700000, 0x00000000a2800000, 0x00000000a2800000|100%| O|  |TAMS 0x00000000a2800000| PB 0x00000000a2800000| Updating 
|  40|0x00000000a2800000, 0x00000000a2900000, 0x00000000a2900000|100%| O|  |TAMS 0x00000000a2900000| PB 0x00000000a2900000| Untracked 
|  41|0x00000000a2900000, 0x00000000a2a00000, 0x00000000a2a00000|100%| O|  |TAMS 0x00000000a2a00000| PB 0x00000000a2a00000| Updating 
|  42|0x00000000a2a00000, 0x00000000a2b00000, 0x00000000a2b00000|100%| O|  |TAMS 0x00000000a2b00000| PB 0x00000000a2b00000| Untracked 
|  43|0x00000000a2b00000, 0x00000000a2c00000, 0x00000000a2c00000|100%| O|  |TAMS 0x00000000a2c00000| PB 0x00000000a2c00000| Updating 
|  44|0x00000000a2c00000, 0x00000000a2d00000, 0x00000000a2d00000|100%| O|  |TAMS 0x00000000a2d00000| PB 0x00000000a2d00000| Updating 
|  45|0x00000000a2d00000, 0x00000000a2e00000, 0x00000000a2e00000|100%| O|  |TAMS 0x00000000a2e00000| PB 0x00000000a2e00000| Untracked 
|  46|0x00000000a2e00000, 0x00000000a2f00000, 0x00000000a2f00000|100%| O|  |TAMS 0x00000000a2f00000| PB 0x00000000a2f00000| Untracked 
|  47|0x00000000a2f00000, 0x00000000a3000000, 0x00000000a3000000|100%|HS|  |TAMS 0x00000000a3000000| PB 0x00000000a2f00000| Complete 
|  48|0x00000000a3000000, 0x00000000a3100000, 0x00000000a3100000|100%|HC|  |TAMS 0x00000000a3100000| PB 0x00000000a3000000| Complete 
|  49|0x00000000a3100000, 0x00000000a3200000, 0x00000000a3200000|100%|HC|  |TAMS 0x00000000a3200000| PB 0x00000000a3100000| Complete 
|  50|0x00000000a3200000, 0x00000000a3300000, 0x00000000a3300000|100%|HS|  |TAMS 0x00000000a3300000| PB 0x00000000a3200000| Complete 
|  51|0x00000000a3300000, 0x00000000a3400000, 0x00000000a3400000|100%|HC|  |TAMS 0x00000000a3400000| PB 0x00000000a3300000| Complete 
|  52|0x00000000a3400000, 0x00000000a3500000, 0x00000000a3500000|100%|HC|  |TAMS 0x00000000a3500000| PB 0x00000000a3400000| Complete 
|  53|0x00000000a3500000, 0x00000000a3600000, 0x00000000a3600000|100%| O|  |TAMS 0x00000000a3600000| PB 0x00000000a3600000| Updating 
|  54|0x00000000a3600000, 0x00000000a3700000, 0x00000000a3700000|100%| O|  |TAMS 0x00000000a3700000| PB 0x00000000a3700000| Untracked 
|  55|0x00000000a3700000, 0x00000000a3800000, 0x00000000a3800000|100%|HS|  |TAMS 0x00000000a3800000| PB 0x00000000a3700000| Complete 
|  56|0x00000000a3800000, 0x00000000a3900000, 0x00000000a3900000|100%| O|  |TAMS 0x00000000a3900000| PB 0x00000000a3900000| Updating 
|  57|0x00000000a3900000, 0x00000000a3a00000, 0x00000000a3a00000|100%| O|  |TAMS 0x00000000a3a00000| PB 0x00000000a3a00000| Updating 
|  58|0x00000000a3a00000, 0x00000000a3b00000, 0x00000000a3b00000|100%| O|  |TAMS 0x00000000a3b00000| PB 0x00000000a3b00000| Untracked 
|  59|0x00000000a3b00000, 0x00000000a3c00000, 0x00000000a3c00000|100%| O|  |TAMS 0x00000000a3c00000| PB 0x00000000a3c00000| Updating 
|  60|0x00000000a3c00000, 0x00000000a3d00000, 0x00000000a3d00000|100%| O|  |TAMS 0x00000000a3d00000| PB 0x00000000a3d00000| Updating 
|  61|0x00000000a3d00000, 0x00000000a3e00000, 0x00000000a3e00000|100%| O|  |TAMS 0x00000000a3e00000| PB 0x00000000a3e00000| Updating 
|  62|0x00000000a3e00000, 0x00000000a3f00000, 0x00000000a3f00000|100%| O|  |TAMS 0x00000000a3f00000| PB 0x00000000a3f00000| Untracked 
|  63|0x00000000a3f00000, 0x00000000a4000000, 0x00000000a4000000|100%| O|  |TAMS 0x00000000a4000000| PB 0x00000000a4000000| Updating 
|  64|0x00000000a4000000, 0x00000000a4100000, 0x00000000a4100000|100%| O|  |TAMS 0x00000000a4100000| PB 0x00000000a4100000| Untracked 
|  65|0x00000000a4100000, 0x00000000a4200000, 0x00000000a4200000|100%| O|  |TAMS 0x00000000a4200000| PB 0x00000000a4200000| Updating 
|  66|0x00000000a4200000, 0x00000000a4300000, 0x00000000a4300000|100%|HS|  |TAMS 0x00000000a4300000| PB 0x00000000a4200000| Complete 
|  67|0x00000000a4300000, 0x00000000a4400000, 0x00000000a4400000|100%| O|  |TAMS 0x00000000a4400000| PB 0x00000000a4400000| Updating 
|  68|0x00000000a4400000, 0x00000000a4500000, 0x00000000a4500000|100%|HS|  |TAMS 0x00000000a4500000| PB 0x00000000a4400000| Complete 
|  69|0x00000000a4500000, 0x00000000a4600000, 0x00000000a4600000|100%| O|  |TAMS 0x00000000a4600000| PB 0x00000000a4600000| Updating 
|  70|0x00000000a4600000, 0x00000000a4700000, 0x00000000a4700000|100%| O|  |TAMS 0x00000000a4700000| PB 0x00000000a4700000| Updating 
|  71|0x00000000a4700000, 0x00000000a4800000, 0x00000000a4800000|100%|HS|  |TAMS 0x00000000a4800000| PB 0x00000000a4700000| Complete 
|  72|0x00000000a4800000, 0x00000000a4900000, 0x00000000a4900000|100%| O|  |TAMS 0x00000000a4900000| PB 0x00000000a4900000| Updating 
|  73|0x00000000a4900000, 0x00000000a4a00000, 0x00000000a4a00000|100%| O|  |TAMS 0x00000000a4a00000| PB 0x00000000a4a00000| Updating 
|  74|0x00000000a4a00000, 0x00000000a4b00000, 0x00000000a4b00000|100%| O|  |TAMS 0x00000000a4b00000| PB 0x00000000a4b00000| Updating 
|  75|0x00000000a4b00000, 0x00000000a4c00000, 0x00000000a4c00000|100%| O|  |TAMS 0x00000000a4c00000| PB 0x00000000a4c00000| Untracked 
|  76|0x00000000a4c00000, 0x00000000a4d00000, 0x00000000a4d00000|100%| O|  |TAMS 0x00000000a4d00000| PB 0x00000000a4d00000| Untracked 
|  77|0x00000000a4d00000, 0x00000000a4e00000, 0x00000000a4e00000|100%| O|  |TAMS 0x00000000a4e00000| PB 0x00000000a4e00000| Untracked 
|  78|0x00000000a4e00000, 0x00000000a4f00000, 0x00000000a4f00000|100%| O|  |TAMS 0x00000000a4f00000| PB 0x00000000a4f00000| Untracked 
|  79|0x00000000a4f00000, 0x00000000a5000000, 0x00000000a5000000|100%| O|  |TAMS 0x00000000a5000000| PB 0x00000000a5000000| Updating 
|  80|0x00000000a5000000, 0x00000000a5100000, 0x00000000a5100000|100%| O|  |TAMS 0x00000000a5100000| PB 0x00000000a5100000| Updating 
|  81|0x00000000a5100000, 0x00000000a5200000, 0x00000000a5200000|100%|HS|  |TAMS 0x00000000a5200000| PB 0x00000000a5100000| Complete 
|  82|0x00000000a5200000, 0x00000000a5300000, 0x00000000a5300000|100%|HS|  |TAMS 0x00000000a5300000| PB 0x00000000a5200000| Complete 
|  83|0x00000000a5300000, 0x00000000a5400000, 0x00000000a5400000|100%| O|  |TAMS 0x00000000a5400000| PB 0x00000000a5400000| Updating 
|  84|0x00000000a5400000, 0x00000000a5500000, 0x00000000a5500000|100%| O|  |TAMS 0x00000000a5500000| PB 0x00000000a5500000| Untracked 
|  85|0x00000000a5500000, 0x00000000a5600000, 0x00000000a5600000|100%| O|  |TAMS 0x00000000a5600000| PB 0x00000000a5600000| Untracked 
|  86|0x00000000a5600000, 0x00000000a5700000, 0x00000000a5700000|100%| O|  |TAMS 0x00000000a5700000| PB 0x00000000a5700000| Updating 
|  87|0x00000000a5700000, 0x00000000a5800000, 0x00000000a5800000|100%| O|  |TAMS 0x00000000a5800000| PB 0x00000000a5800000| Untracked 
|  88|0x00000000a5800000, 0x00000000a5900000, 0x00000000a5900000|100%| O|  |TAMS 0x00000000a5900000| PB 0x00000000a5900000| Untracked 
|  89|0x00000000a5900000, 0x00000000a5a00000, 0x00000000a5a00000|100%| O|  |TAMS 0x00000000a5a00000| PB 0x00000000a5a00000| Untracked 
|  90|0x00000000a5a00000, 0x00000000a5b00000, 0x00000000a5b00000|100%| O|  |TAMS 0x00000000a5b00000| PB 0x00000000a5b00000| Updating 
|  91|0x00000000a5b00000, 0x00000000a5c00000, 0x00000000a5c00000|100%| O|  |TAMS 0x00000000a5c00000| PB 0x00000000a5c00000| Untracked 
|  92|0x00000000a5c00000, 0x00000000a5d00000, 0x00000000a5d00000|100%|HS|  |TAMS 0x00000000a5d00000| PB 0x00000000a5c00000| Complete 
|  93|0x00000000a5d00000, 0x00000000a5e00000, 0x00000000a5e00000|100%| O|  |TAMS 0x00000000a5e00000| PB 0x00000000a5e00000| Updating 
|  94|0x00000000a5e00000, 0x00000000a5f00000, 0x00000000a5f00000|100%| O|  |TAMS 0x00000000a5f00000| PB 0x00000000a5f00000| Untracked 
|  95|0x00000000a5f00000, 0x00000000a6000000, 0x00000000a6000000|100%| O|  |TAMS 0x00000000a6000000| PB 0x00000000a6000000| Untracked 
|  96|0x00000000a6000000, 0x00000000a6100000, 0x00000000a6100000|100%| O|  |TAMS 0x00000000a6100000| PB 0x00000000a6100000| Updating 
|  97|0x00000000a6100000, 0x00000000a6200000, 0x00000000a6200000|100%| O|  |TAMS 0x00000000a6200000| PB 0x00000000a6200000| Updating 
|  98|0x00000000a6200000, 0x00000000a6300000, 0x00000000a6300000|100%| O|  |TAMS 0x00000000a6300000| PB 0x00000000a6300000| Updating 
|  99|0x00000000a6300000, 0x00000000a6400000, 0x00000000a6400000|100%| O|  |TAMS 0x00000000a6400000| PB 0x00000000a6400000| Updating 
| 100|0x00000000a6400000, 0x00000000a6500000, 0x00000000a6500000|100%| O|  |TAMS 0x00000000a6500000| PB 0x00000000a6500000| Updating 
| 101|0x00000000a6500000, 0x00000000a6600000, 0x00000000a6600000|100%|HS|  |TAMS 0x00000000a6600000| PB 0x00000000a6500000| Complete 
| 102|0x00000000a6600000, 0x00000000a6700000, 0x00000000a6700000|100%|HC|  |TAMS 0x00000000a6700000| PB 0x00000000a6600000| Complete 
| 103|0x00000000a6700000, 0x00000000a6800000, 0x00000000a6800000|100%|HC|  |TAMS 0x00000000a6800000| PB 0x00000000a6700000| Complete 
| 104|0x00000000a6800000, 0x00000000a6900000, 0x00000000a6900000|100%| O|  |TAMS 0x00000000a6900000| PB 0x00000000a6900000| Untracked 
| 105|0x00000000a6900000, 0x00000000a6a00000, 0x00000000a6a00000|100%| O|  |TAMS 0x00000000a6a00000| PB 0x00000000a6a00000| Updating 
| 106|0x00000000a6a00000, 0x00000000a6b00000, 0x00000000a6b00000|100%| O|  |TAMS 0x00000000a6b00000| PB 0x00000000a6b00000| Untracked 
| 107|0x00000000a6b00000, 0x00000000a6c00000, 0x00000000a6c00000|100%| O|  |TAMS 0x00000000a6c00000| PB 0x00000000a6c00000| Updating 
| 108|0x00000000a6c00000, 0x00000000a6d00000, 0x00000000a6d00000|100%| O|  |TAMS 0x00000000a6d00000| PB 0x00000000a6d00000| Untracked 
| 109|0x00000000a6d00000, 0x00000000a6e00000, 0x00000000a6e00000|100%| O|  |TAMS 0x00000000a6e00000| PB 0x00000000a6e00000| Untracked 
| 110|0x00000000a6e00000, 0x00000000a6f00000, 0x00000000a6f00000|100%|HS|  |TAMS 0x00000000a6f00000| PB 0x00000000a6e00000| Complete 
| 111|0x00000000a6f00000, 0x00000000a7000000, 0x00000000a7000000|100%| O|  |TAMS 0x00000000a7000000| PB 0x00000000a7000000| Updating 
| 112|0x00000000a7000000, 0x00000000a7100000, 0x00000000a7100000|100%| O|  |TAMS 0x00000000a7100000| PB 0x00000000a7100000| Untracked 
| 113|0x00000000a7100000, 0x00000000a7200000, 0x00000000a7200000|100%| O|  |TAMS 0x00000000a7200000| PB 0x00000000a7200000| Untracked 
| 114|0x00000000a7200000, 0x00000000a7300000, 0x00000000a7300000|100%| O|  |TAMS 0x00000000a7300000| PB 0x00000000a7300000| Updating 
| 115|0x00000000a7300000, 0x00000000a7400000, 0x00000000a7400000|100%| O|  |TAMS 0x00000000a7400000| PB 0x00000000a7400000| Untracked 
| 116|0x00000000a7400000, 0x00000000a7500000, 0x00000000a7500000|100%| O|  |TAMS 0x00000000a7400000| PB 0x00000000a7400000| Untracked 
| 117|0x00000000a7500000, 0x00000000a7600000, 0x00000000a7600000|100%|HS|  |TAMS 0x00000000a7600000| PB 0x00000000a7500000| Complete 
| 118|0x00000000a7600000, 0x00000000a7700000, 0x00000000a7700000|100%|HS|  |TAMS 0x00000000a7700000| PB 0x00000000a7600000| Complete 
| 119|0x00000000a7700000, 0x00000000a7800000, 0x00000000a7800000|100%|HS|  |TAMS 0x00000000a7800000| PB 0x00000000a7700000| Complete 
| 120|0x00000000a7800000, 0x00000000a7900000, 0x00000000a7900000|100%| O|  |TAMS 0x00000000a7900000| PB 0x00000000a7900000| Updating 
| 121|0x00000000a7900000, 0x00000000a7a00000, 0x00000000a7a00000|100%| O|  |TAMS 0x00000000a7a00000| PB 0x00000000a7a00000| Untracked 
| 122|0x00000000a7a00000, 0x00000000a7b00000, 0x00000000a7b00000|100%| O|  |TAMS 0x00000000a7b00000| PB 0x00000000a7b00000| Untracked 
| 123|0x00000000a7b00000, 0x00000000a7c00000, 0x00000000a7c00000|100%| O|  |TAMS 0x00000000a7c00000| PB 0x00000000a7c00000| Untracked 
| 124|0x00000000a7c00000, 0x00000000a7d00000, 0x00000000a7d00000|100%| O|  |TAMS 0x00000000a7d00000| PB 0x00000000a7d00000| Untracked 
| 125|0x00000000a7d00000, 0x00000000a7e00000, 0x00000000a7e00000|100%| O|  |TAMS 0x00000000a7e00000| PB 0x00000000a7e00000| Untracked 
| 126|0x00000000a7e00000, 0x00000000a7f00000, 0x00000000a7f00000|100%|HS|  |TAMS 0x00000000a7f00000| PB 0x00000000a7e00000| Complete 
| 127|0x00000000a7f00000, 0x00000000a8000000, 0x00000000a8000000|100%| O|  |TAMS 0x00000000a8000000| PB 0x00000000a8000000| Untracked 
| 128|0x00000000a8000000, 0x00000000a8100000, 0x00000000a8100000|100%| O|  |TAMS 0x00000000a8100000| PB 0x00000000a8100000| Untracked 
| 129|0x00000000a8100000, 0x00000000a8200000, 0x00000000a8200000|100%| O|  |TAMS 0x00000000a8200000| PB 0x00000000a8200000| Untracked 
| 130|0x00000000a8200000, 0x00000000a8300000, 0x00000000a8300000|100%| O|  |TAMS 0x00000000a8300000| PB 0x00000000a8300000| Untracked 
| 131|0x00000000a8300000, 0x00000000a8400000, 0x00000000a8400000|100%|HS|  |TAMS 0x00000000a8400000| PB 0x00000000a8300000| Complete 
| 132|0x00000000a8400000, 0x00000000a8500000, 0x00000000a8500000|100%| O|  |TAMS 0x00000000a8500000| PB 0x00000000a8500000| Untracked 
| 133|0x00000000a8500000, 0x00000000a8600000, 0x00000000a8600000|100%| O|  |TAMS 0x00000000a8600000| PB 0x00000000a8600000| Updating 
| 134|0x00000000a8600000, 0x00000000a8700000, 0x00000000a8700000|100%| O|  |TAMS 0x00000000a8700000| PB 0x00000000a8700000| Untracked 
| 135|0x00000000a8700000, 0x00000000a8800000, 0x00000000a8800000|100%| O|  |TAMS 0x00000000a8700000| PB 0x00000000a8700000| Untracked 
| 136|0x00000000a8800000, 0x00000000a8900000, 0x00000000a8900000|100%|HS|  |TAMS 0x00000000a8900000| PB 0x00000000a8800000| Complete 
| 137|0x00000000a8900000, 0x00000000a8a00000, 0x00000000a8a00000|100%| O|  |TAMS 0x00000000a8a00000| PB 0x00000000a8a00000| Untracked 
| 138|0x00000000a8a00000, 0x00000000a8b00000, 0x00000000a8b00000|100%| O|  |TAMS 0x00000000a8b00000| PB 0x00000000a8b00000| Updating 
| 139|0x00000000a8b00000, 0x00000000a8c00000, 0x00000000a8c00000|100%| O|  |TAMS 0x00000000a8c00000| PB 0x00000000a8c00000| Updating 
| 140|0x00000000a8c00000, 0x00000000a8d00000, 0x00000000a8d00000|100%| O|  |TAMS 0x00000000a8d00000| PB 0x00000000a8d00000| Untracked 
| 141|0x00000000a8d00000, 0x00000000a8e00000, 0x00000000a8e00000|100%| O|  |TAMS 0x00000000a8e00000| PB 0x00000000a8e00000| Updating 
| 142|0x00000000a8e00000, 0x00000000a8f00000, 0x00000000a8f00000|100%| O|  |TAMS 0x00000000a8f00000| PB 0x00000000a8f00000| Untracked 
| 143|0x00000000a8f00000, 0x00000000a9000000, 0x00000000a9000000|100%| O|  |TAMS 0x00000000a9000000| PB 0x00000000a9000000| Untracked 
| 144|0x00000000a9000000, 0x00000000a9100000, 0x00000000a9100000|100%| O|  |TAMS 0x00000000a9100000| PB 0x00000000a9100000| Untracked 
| 145|0x00000000a9100000, 0x00000000a9200000, 0x00000000a9200000|100%| O|  |TAMS 0x00000000a9200000| PB 0x00000000a9200000| Untracked 
| 146|0x00000000a9200000, 0x00000000a9300000, 0x00000000a9300000|100%| O|  |TAMS 0x00000000a9300000| PB 0x00000000a9300000| Untracked 
| 147|0x00000000a9300000, 0x00000000a9400000, 0x00000000a9400000|100%| O|  |TAMS 0x00000000a9400000| PB 0x00000000a9400000| Untracked 
| 148|0x00000000a9400000, 0x00000000a9500000, 0x00000000a9500000|100%| O|  |TAMS 0x00000000a9500000| PB 0x00000000a9500000| Untracked 
| 149|0x00000000a9500000, 0x00000000a9600000, 0x00000000a9600000|100%| O|  |TAMS 0x00000000a9600000| PB 0x00000000a9600000| Untracked 
| 150|0x00000000a9600000, 0x00000000a9700000, 0x00000000a9700000|100%| O|  |TAMS 0x00000000a9700000| PB 0x00000000a9700000| Untracked 
| 151|0x00000000a9700000, 0x00000000a9800000, 0x00000000a9800000|100%| O|  |TAMS 0x00000000a9800000| PB 0x00000000a9800000| Untracked 
| 152|0x00000000a9800000, 0x00000000a9900000, 0x00000000a9900000|100%| O|  |TAMS 0x00000000a9900000| PB 0x00000000a9900000| Untracked 
| 153|0x00000000a9900000, 0x00000000a9a00000, 0x00000000a9a00000|100%| O|  |TAMS 0x00000000a9a00000| PB 0x00000000a9a00000| Untracked 
| 154|0x00000000a9a00000, 0x00000000a9b00000, 0x00000000a9b00000|100%| O|  |TAMS 0x00000000a9b00000| PB 0x00000000a9b00000| Untracked 
| 155|0x00000000a9b00000, 0x00000000a9c00000, 0x00000000a9c00000|100%| O|  |TAMS 0x00000000a9c00000| PB 0x00000000a9c00000| Updating 
| 156|0x00000000a9c00000, 0x00000000a9d00000, 0x00000000a9d00000|100%| O|  |TAMS 0x00000000a9d00000| PB 0x00000000a9d00000| Updating 
| 157|0x00000000a9d00000, 0x00000000a9e00000, 0x00000000a9e00000|100%| O|  |TAMS 0x00000000a9e00000| PB 0x00000000a9e00000| Untracked 
| 158|0x00000000a9e00000, 0x00000000a9f00000, 0x00000000a9f00000|100%| O|  |TAMS 0x00000000a9f00000| PB 0x00000000a9f00000| Updating 
| 159|0x00000000a9f00000, 0x00000000aa000000, 0x00000000aa000000|100%| O|  |TAMS 0x00000000aa000000| PB 0x00000000aa000000| Untracked 
| 160|0x00000000aa000000, 0x00000000aa100000, 0x00000000aa100000|100%| O|  |TAMS 0x00000000aa100000| PB 0x00000000aa100000| Updating 
| 161|0x00000000aa100000, 0x00000000aa200000, 0x00000000aa200000|100%| O|  |TAMS 0x00000000aa200000| PB 0x00000000aa200000| Untracked 
| 162|0x00000000aa200000, 0x00000000aa300000, 0x00000000aa300000|100%| O|  |TAMS 0x00000000aa300000| PB 0x00000000aa300000| Untracked 
| 163|0x00000000aa300000, 0x00000000aa400000, 0x00000000aa400000|100%| O|  |TAMS 0x00000000aa400000| PB 0x00000000aa400000| Untracked 
| 164|0x00000000aa400000, 0x00000000aa500000, 0x00000000aa500000|100%| O|  |TAMS 0x00000000aa500000| PB 0x00000000aa500000| Untracked 
| 165|0x00000000aa500000, 0x00000000aa600000, 0x00000000aa600000|100%| O|  |TAMS 0x00000000aa600000| PB 0x00000000aa600000| Untracked 
| 166|0x00000000aa600000, 0x00000000aa700000, 0x00000000aa700000|100%| O|  |TAMS 0x00000000aa700000| PB 0x00000000aa700000| Untracked 
| 167|0x00000000aa700000, 0x00000000aa800000, 0x00000000aa800000|100%| O|  |TAMS 0x00000000aa800000| PB 0x00000000aa800000| Untracked 
| 168|0x00000000aa800000, 0x00000000aa900000, 0x00000000aa900000|100%| O|  |TAMS 0x00000000aa900000| PB 0x00000000aa900000| Untracked 
| 169|0x00000000aa900000, 0x00000000aaa00000, 0x00000000aaa00000|100%| O|  |TAMS 0x00000000aaa00000| PB 0x00000000aaa00000| Untracked 
| 170|0x00000000aaa00000, 0x00000000aab00000, 0x00000000aab00000|100%| O|  |TAMS 0x00000000aab00000| PB 0x00000000aab00000| Untracked 
| 171|0x00000000aab00000, 0x00000000aac00000, 0x00000000aac00000|100%| O|  |TAMS 0x00000000aac00000| PB 0x00000000aac00000| Untracked 
| 172|0x00000000aac00000, 0x00000000aad00000, 0x00000000aad00000|100%| O|  |TAMS 0x00000000aad00000| PB 0x00000000aad00000| Untracked 
| 173|0x00000000aad00000, 0x00000000aae00000, 0x00000000aae00000|100%| O|  |TAMS 0x00000000aae00000| PB 0x00000000aae00000| Untracked 
| 174|0x00000000aae00000, 0x00000000aaf00000, 0x00000000aaf00000|100%| O|  |TAMS 0x00000000aaf00000| PB 0x00000000aaf00000| Untracked 
| 175|0x00000000aaf00000, 0x00000000ab000000, 0x00000000ab000000|100%| O|  |TAMS 0x00000000ab000000| PB 0x00000000ab000000| Untracked 
| 176|0x00000000ab000000, 0x00000000ab100000, 0x00000000ab100000|100%| O|  |TAMS 0x00000000ab100000| PB 0x00000000ab100000| Untracked 
| 177|0x00000000ab100000, 0x00000000ab200000, 0x00000000ab200000|100%| O|  |TAMS 0x00000000ab200000| PB 0x00000000ab200000| Untracked 
| 178|0x00000000ab200000, 0x00000000ab300000, 0x00000000ab300000|100%| O|  |TAMS 0x00000000ab300000| PB 0x00000000ab300000| Untracked 
| 179|0x00000000ab300000, 0x00000000ab400000, 0x00000000ab400000|100%| O|  |TAMS 0x00000000ab400000| PB 0x00000000ab400000| Untracked 
| 180|0x00000000ab400000, 0x00000000ab500000, 0x00000000ab500000|100%| O|  |TAMS 0x00000000ab500000| PB 0x00000000ab500000| Untracked 
| 181|0x00000000ab500000, 0x00000000ab600000, 0x00000000ab600000|100%| O|  |TAMS 0x00000000ab600000| PB 0x00000000ab600000| Updating 
| 182|0x00000000ab600000, 0x00000000ab700000, 0x00000000ab700000|100%| O|  |TAMS 0x00000000ab700000| PB 0x00000000ab700000| Untracked 
| 183|0x00000000ab700000, 0x00000000ab800000, 0x00000000ab800000|100%| O|  |TAMS 0x00000000ab800000| PB 0x00000000ab800000| Untracked 
| 184|0x00000000ab800000, 0x00000000ab900000, 0x00000000ab900000|100%| O|  |TAMS 0x00000000ab900000| PB 0x00000000ab900000| Untracked 
| 185|0x00000000ab900000, 0x00000000aba00000, 0x00000000aba00000|100%| O|  |TAMS 0x00000000aba00000| PB 0x00000000aba00000| Untracked 
| 186|0x00000000aba00000, 0x00000000abb00000, 0x00000000abb00000|100%| O|  |TAMS 0x00000000abb00000| PB 0x00000000abb00000| Untracked 
| 187|0x00000000abb00000, 0x00000000abc00000, 0x00000000abc00000|100%| O|  |TAMS 0x00000000abc00000| PB 0x00000000abc00000| Updating 
| 188|0x00000000abc00000, 0x00000000abd00000, 0x00000000abd00000|100%| O|  |TAMS 0x00000000abd00000| PB 0x00000000abd00000| Untracked 
| 189|0x00000000abd00000, 0x00000000abe00000, 0x00000000abe00000|100%| O|  |TAMS 0x00000000abe00000| PB 0x00000000abe00000| Untracked 
| 190|0x00000000abe00000, 0x00000000abf00000, 0x00000000abf00000|100%| O|  |TAMS 0x00000000abf00000| PB 0x00000000abf00000| Untracked 
| 191|0x00000000abf00000, 0x00000000ac000000, 0x00000000ac000000|100%| O|  |TAMS 0x00000000ac000000| PB 0x00000000ac000000| Updating 
| 192|0x00000000ac000000, 0x00000000ac100000, 0x00000000ac100000|100%| O|  |TAMS 0x00000000ac100000| PB 0x00000000ac100000| Untracked 
| 193|0x00000000ac100000, 0x00000000ac200000, 0x00000000ac200000|100%|HS|  |TAMS 0x00000000ac200000| PB 0x00000000ac100000| Complete 
| 194|0x00000000ac200000, 0x00000000ac300000, 0x00000000ac300000|100%|HS|  |TAMS 0x00000000ac300000| PB 0x00000000ac200000| Complete 
| 195|0x00000000ac300000, 0x00000000ac400000, 0x00000000ac400000|100%| O|  |TAMS 0x00000000ac400000| PB 0x00000000ac400000| Untracked 
| 196|0x00000000ac400000, 0x00000000ac500000, 0x00000000ac500000|100%| O|  |TAMS 0x00000000ac500000| PB 0x00000000ac500000| Untracked 
| 197|0x00000000ac500000, 0x00000000ac600000, 0x00000000ac600000|100%| O|  |TAMS 0x00000000ac600000| PB 0x00000000ac600000| Untracked 
| 198|0x00000000ac600000, 0x00000000ac700000, 0x00000000ac700000|100%| O|  |TAMS 0x00000000ac700000| PB 0x00000000ac700000| Untracked 
| 199|0x00000000ac700000, 0x00000000ac800000, 0x00000000ac800000|100%| O|  |TAMS 0x00000000ac700000| PB 0x00000000ac700000| Untracked 
| 200|0x00000000ac800000, 0x00000000ac900000, 0x00000000ac900000|100%| O|  |TAMS 0x00000000ac900000| PB 0x00000000ac900000| Untracked 
| 201|0x00000000ac900000, 0x00000000aca00000, 0x00000000aca00000|100%| O|  |TAMS 0x00000000aca00000| PB 0x00000000aca00000| Untracked 
| 202|0x00000000aca00000, 0x00000000acb00000, 0x00000000acb00000|100%| O|  |TAMS 0x00000000acb00000| PB 0x00000000acb00000| Untracked 
| 203|0x00000000acb00000, 0x00000000acc00000, 0x00000000acc00000|100%| O|  |TAMS 0x00000000acc00000| PB 0x00000000acc00000| Untracked 
| 204|0x00000000acc00000, 0x00000000acd00000, 0x00000000acd00000|100%| O|  |TAMS 0x00000000acd00000| PB 0x00000000acd00000| Untracked 
| 205|0x00000000acd00000, 0x00000000ace00000, 0x00000000ace00000|100%| O|  |TAMS 0x00000000ace00000| PB 0x00000000ace00000| Untracked 
| 206|0x00000000ace00000, 0x00000000acf00000, 0x00000000acf00000|100%| O|  |TAMS 0x00000000acf00000| PB 0x00000000acf00000| Untracked 
| 207|0x00000000acf00000, 0x00000000ad000000, 0x00000000ad000000|100%| O|  |TAMS 0x00000000ad000000| PB 0x00000000ad000000| Untracked 
| 208|0x00000000ad000000, 0x00000000ad100000, 0x00000000ad100000|100%| O|  |TAMS 0x00000000ad100000| PB 0x00000000ad100000| Updating 
| 209|0x00000000ad100000, 0x00000000ad200000, 0x00000000ad200000|100%| O|  |TAMS 0x00000000ad200000| PB 0x00000000ad200000| Untracked 
| 210|0x00000000ad200000, 0x00000000ad300000, 0x00000000ad300000|100%| O|  |TAMS 0x00000000ad300000| PB 0x00000000ad300000| Untracked 
| 211|0x00000000ad300000, 0x00000000ad400000, 0x00000000ad400000|100%| O|  |TAMS 0x00000000ad400000| PB 0x00000000ad400000| Untracked 
| 212|0x00000000ad400000, 0x00000000ad500000, 0x00000000ad500000|100%| O|  |TAMS 0x00000000ad500000| PB 0x00000000ad500000| Untracked 
| 213|0x00000000ad500000, 0x00000000ad600000, 0x00000000ad600000|100%| O|  |TAMS 0x00000000ad600000| PB 0x00000000ad600000| Updating 
| 214|0x00000000ad600000, 0x00000000ad700000, 0x00000000ad700000|100%| O|  |TAMS 0x00000000ad700000| PB 0x00000000ad700000| Untracked 
| 215|0x00000000ad700000, 0x00000000ad800000, 0x00000000ad800000|100%| O|  |TAMS 0x00000000ad800000| PB 0x00000000ad800000| Untracked 
| 216|0x00000000ad800000, 0x00000000ad900000, 0x00000000ad900000|100%| O|  |TAMS 0x00000000ad900000| PB 0x00000000ad900000| Untracked 
| 217|0x00000000ad900000, 0x00000000ada00000, 0x00000000ada00000|100%| O|  |TAMS 0x00000000ada00000| PB 0x00000000ada00000| Untracked 
| 218|0x00000000ada00000, 0x00000000adb00000, 0x00000000adb00000|100%| O|  |TAMS 0x00000000adb00000| PB 0x00000000adb00000| Updating 
| 219|0x00000000adb00000, 0x00000000adc00000, 0x00000000adc00000|100%| O|  |TAMS 0x00000000adc00000| PB 0x00000000adc00000| Untracked 
| 220|0x00000000adc00000, 0x00000000add00000, 0x00000000add00000|100%|HS|  |TAMS 0x00000000add00000| PB 0x00000000adc00000| Complete 
| 221|0x00000000add00000, 0x00000000ade00000, 0x00000000ade00000|100%| O|  |TAMS 0x00000000ade00000| PB 0x00000000ade00000| Untracked 
| 222|0x00000000ade00000, 0x00000000adf00000, 0x00000000adf00000|100%|HS|  |TAMS 0x00000000adf00000| PB 0x00000000ade00000| Complete 
| 223|0x00000000adf00000, 0x00000000ae000000, 0x00000000ae000000|100%|HC|  |TAMS 0x00000000ae000000| PB 0x00000000adf00000| Complete 
| 224|0x00000000ae000000, 0x00000000ae100000, 0x00000000ae100000|100%| O|  |TAMS 0x00000000ae100000| PB 0x00000000ae100000| Updating 
| 225|0x00000000ae100000, 0x00000000ae200000, 0x00000000ae200000|100%| O|  |TAMS 0x00000000ae200000| PB 0x00000000ae200000| Untracked 
| 226|0x00000000ae200000, 0x00000000ae300000, 0x00000000ae300000|100%| O|  |TAMS 0x00000000ae300000| PB 0x00000000ae300000| Untracked 
| 227|0x00000000ae300000, 0x00000000ae400000, 0x00000000ae400000|100%| O|  |TAMS 0x00000000ae400000| PB 0x00000000ae400000| Untracked 
| 228|0x00000000ae400000, 0x00000000ae500000, 0x00000000ae500000|100%| O|  |TAMS 0x00000000ae500000| PB 0x00000000ae500000| Untracked 
| 229|0x00000000ae500000, 0x00000000ae600000, 0x00000000ae600000|100%| O|  |TAMS 0x00000000ae600000| PB 0x00000000ae600000| Untracked 
| 230|0x00000000ae600000, 0x00000000ae700000, 0x00000000ae700000|100%| O|  |TAMS 0x00000000ae700000| PB 0x00000000ae700000| Untracked 
| 231|0x00000000ae700000, 0x00000000ae800000, 0x00000000ae800000|100%| O|  |TAMS 0x00000000ae800000| PB 0x00000000ae800000| Untracked 
| 232|0x00000000ae800000, 0x00000000ae900000, 0x00000000ae900000|100%| O|  |TAMS 0x00000000ae900000| PB 0x00000000ae900000| Untracked 
| 233|0x00000000ae900000, 0x00000000aea00000, 0x00000000aea00000|100%| O|  |TAMS 0x00000000aea00000| PB 0x00000000aea00000| Untracked 
| 234|0x00000000aea00000, 0x00000000aeb00000, 0x00000000aeb00000|100%| O|  |TAMS 0x00000000aeb00000| PB 0x00000000aeb00000| Updating 
| 235|0x00000000aeb00000, 0x00000000aec00000, 0x00000000aec00000|100%| O|  |TAMS 0x00000000aec00000| PB 0x00000000aec00000| Untracked 
| 236|0x00000000aec00000, 0x00000000aed00000, 0x00000000aed00000|100%| O|  |TAMS 0x00000000aed00000| PB 0x00000000aed00000| Untracked 
| 237|0x00000000aed00000, 0x00000000aee00000, 0x00000000aee00000|100%| O|  |TAMS 0x00000000aee00000| PB 0x00000000aee00000| Untracked 
| 238|0x00000000aee00000, 0x00000000aef00000, 0x00000000aef00000|100%| O|  |TAMS 0x00000000aef00000| PB 0x00000000aef00000| Untracked 
| 239|0x00000000aef00000, 0x00000000af000000, 0x00000000af000000|100%| O|  |TAMS 0x00000000af000000| PB 0x00000000af000000| Untracked 
| 240|0x00000000af000000, 0x00000000af100000, 0x00000000af100000|100%| O|  |TAMS 0x00000000af100000| PB 0x00000000af100000| Untracked 
| 241|0x00000000af100000, 0x00000000af200000, 0x00000000af200000|100%| O|  |TAMS 0x00000000af200000| PB 0x00000000af200000| Untracked 
| 242|0x00000000af200000, 0x00000000af300000, 0x00000000af300000|100%| O|  |TAMS 0x00000000af300000| PB 0x00000000af300000| Untracked 
| 243|0x00000000af300000, 0x00000000af400000, 0x00000000af400000|100%| O|  |TAMS 0x00000000af400000| PB 0x00000000af400000| Updating 
| 244|0x00000000af400000, 0x00000000af500000, 0x00000000af500000|100%| O|  |TAMS 0x00000000af500000| PB 0x00000000af500000| Untracked 
| 245|0x00000000af500000, 0x00000000af600000, 0x00000000af600000|100%| O|  |TAMS 0x00000000af600000| PB 0x00000000af600000| Untracked 
| 246|0x00000000af600000, 0x00000000af700000, 0x00000000af700000|100%| O|  |TAMS 0x00000000af700000| PB 0x00000000af700000| Untracked 
| 247|0x00000000af700000, 0x00000000af800000, 0x00000000af800000|100%| O|  |TAMS 0x00000000af800000| PB 0x00000000af800000| Untracked 
| 248|0x00000000af800000, 0x00000000af900000, 0x00000000af900000|100%|HS|  |TAMS 0x00000000af900000| PB 0x00000000af800000| Complete 
| 249|0x00000000af900000, 0x00000000afa00000, 0x00000000afa00000|100%|HC|  |TAMS 0x00000000afa00000| PB 0x00000000af900000| Complete 
| 250|0x00000000afa00000, 0x00000000afb00000, 0x00000000afb00000|100%|HC|  |TAMS 0x00000000afb00000| PB 0x00000000afa00000| Complete 
| 251|0x00000000afb00000, 0x00000000afc00000, 0x00000000afc00000|100%| O|  |TAMS 0x00000000afc00000| PB 0x00000000afc00000| Untracked 
| 252|0x00000000afc00000, 0x00000000afd00000, 0x00000000afd00000|100%| O|  |TAMS 0x00000000afd00000| PB 0x00000000afd00000| Untracked 
| 253|0x00000000afd00000, 0x00000000afe00000, 0x00000000afe00000|100%| O|  |TAMS 0x00000000afe00000| PB 0x00000000afe00000| Untracked 
| 254|0x00000000afe00000, 0x00000000aff00000, 0x00000000aff00000|100%| O|  |TAMS 0x00000000afe00000| PB 0x00000000afe00000| Untracked 
| 255|0x00000000aff00000, 0x00000000b0000000, 0x00000000b0000000|100%| O|  |TAMS 0x00000000b0000000| PB 0x00000000b0000000| Untracked 
| 256|0x00000000b0000000, 0x00000000b0100000, 0x00000000b0100000|100%| O|  |TAMS 0x00000000b0100000| PB 0x00000000b0100000| Untracked 
| 257|0x00000000b0100000, 0x00000000b0200000, 0x00000000b0200000|100%| O|  |TAMS 0x00000000b0200000| PB 0x00000000b0200000| Untracked 
| 258|0x00000000b0200000, 0x00000000b0300000, 0x00000000b0300000|100%| O|  |TAMS 0x00000000b0300000| PB 0x00000000b0300000| Updating 
| 259|0x00000000b0300000, 0x00000000b0400000, 0x00000000b0400000|100%| O|  |TAMS 0x00000000b0400000| PB 0x00000000b0400000| Untracked 
| 260|0x00000000b0400000, 0x00000000b0500000, 0x00000000b0500000|100%| O|  |TAMS 0x00000000b0500000| PB 0x00000000b0500000| Untracked 
| 261|0x00000000b0500000, 0x00000000b0600000, 0x00000000b0600000|100%| O|  |TAMS 0x00000000b0600000| PB 0x00000000b0600000| Untracked 
| 262|0x00000000b0600000, 0x00000000b0700000, 0x00000000b0700000|100%| O|  |TAMS 0x00000000b0700000| PB 0x00000000b0700000| Untracked 
| 263|0x00000000b0700000, 0x00000000b0800000, 0x00000000b0800000|100%| O|  |TAMS 0x00000000b0800000| PB 0x00000000b0800000| Updating 
| 264|0x00000000b0800000, 0x00000000b0900000, 0x00000000b0900000|100%| O|  |TAMS 0x00000000b0900000| PB 0x00000000b0900000| Untracked 
| 265|0x00000000b0900000, 0x00000000b0a00000, 0x00000000b0a00000|100%| O|  |TAMS 0x00000000b0a00000| PB 0x00000000b0a00000| Untracked 
| 266|0x00000000b0a00000, 0x00000000b0b00000, 0x00000000b0b00000|100%| O|  |TAMS 0x00000000b0b00000| PB 0x00000000b0b00000| Untracked 
| 267|0x00000000b0b00000, 0x00000000b0c00000, 0x00000000b0c00000|100%| O|  |TAMS 0x00000000b0c00000| PB 0x00000000b0c00000| Untracked 
| 268|0x00000000b0c00000, 0x00000000b0d00000, 0x00000000b0d00000|100%| O|  |TAMS 0x00000000b0d00000| PB 0x00000000b0d00000| Untracked 
| 269|0x00000000b0d00000, 0x00000000b0e00000, 0x00000000b0e00000|100%| O|  |TAMS 0x00000000b0e00000| PB 0x00000000b0e00000| Untracked 
| 270|0x00000000b0e00000, 0x00000000b0f00000, 0x00000000b0f00000|100%| O|  |TAMS 0x00000000b0f00000| PB 0x00000000b0f00000| Untracked 
| 271|0x00000000b0f00000, 0x00000000b1000000, 0x00000000b1000000|100%| O|  |TAMS 0x00000000b1000000| PB 0x00000000b1000000| Untracked 
| 272|0x00000000b1000000, 0x00000000b1100000, 0x00000000b1100000|100%| O|  |TAMS 0x00000000b1100000| PB 0x00000000b1100000| Untracked 
| 273|0x00000000b1100000, 0x00000000b1200000, 0x00000000b1200000|100%| O|  |TAMS 0x00000000b1200000| PB 0x00000000b1200000| Untracked 
| 274|0x00000000b1200000, 0x00000000b1300000, 0x00000000b1300000|100%| O|  |TAMS 0x00000000b1300000| PB 0x00000000b1300000| Untracked 
| 275|0x00000000b1300000, 0x00000000b1400000, 0x00000000b1400000|100%| O|  |TAMS 0x00000000b1400000| PB 0x00000000b1400000| Untracked 
| 276|0x00000000b1400000, 0x00000000b1500000, 0x00000000b1500000|100%| O|  |TAMS 0x00000000b1500000| PB 0x00000000b1500000| Untracked 
| 277|0x00000000b1500000, 0x00000000b1600000, 0x00000000b1600000|100%| O|  |TAMS 0x00000000b1600000| PB 0x00000000b1600000| Updating 
| 278|0x00000000b1600000, 0x00000000b1700000, 0x00000000b1700000|100%| O|  |TAMS 0x00000000b1700000| PB 0x00000000b1700000| Untracked 
| 279|0x00000000b1700000, 0x00000000b1800000, 0x00000000b1800000|100%| O|  |TAMS 0x00000000b1800000| PB 0x00000000b1800000| Untracked 
| 280|0x00000000b1800000, 0x00000000b1900000, 0x00000000b1900000|100%| O|  |TAMS 0x00000000b1800000| PB 0x00000000b1800000| Untracked 
| 281|0x00000000b1900000, 0x00000000b1a00000, 0x00000000b1a00000|100%| O|  |TAMS 0x00000000b1a00000| PB 0x00000000b1a00000| Untracked 
| 282|0x00000000b1a00000, 0x00000000b1b00000, 0x00000000b1b00000|100%| O|  |TAMS 0x00000000b1b00000| PB 0x00000000b1b00000| Updating 
| 283|0x00000000b1b00000, 0x00000000b1c00000, 0x00000000b1c00000|100%| O|  |TAMS 0x00000000b1c00000| PB 0x00000000b1c00000| Untracked 
| 284|0x00000000b1c00000, 0x00000000b1d00000, 0x00000000b1d00000|100%| O|  |TAMS 0x00000000b1d00000| PB 0x00000000b1d00000| Untracked 
| 285|0x00000000b1d00000, 0x00000000b1e00000, 0x00000000b1e00000|100%| O|  |TAMS 0x00000000b1e00000| PB 0x00000000b1e00000| Untracked 
| 286|0x00000000b1e00000, 0x00000000b1f00000, 0x00000000b1f00000|100%| O|  |TAMS 0x00000000b1f00000| PB 0x00000000b1f00000| Untracked 
| 287|0x00000000b1f00000, 0x00000000b2000000, 0x00000000b2000000|100%| O|  |TAMS 0x00000000b2000000| PB 0x00000000b2000000| Untracked 
| 288|0x00000000b2000000, 0x00000000b2100000, 0x00000000b2100000|100%| O|  |TAMS 0x00000000b2100000| PB 0x00000000b2100000| Updating 
| 289|0x00000000b2100000, 0x00000000b2200000, 0x00000000b2200000|100%| O|  |TAMS 0x00000000b2200000| PB 0x00000000b2200000| Untracked 
| 290|0x00000000b2200000, 0x00000000b2300000, 0x00000000b2300000|100%| O|  |TAMS 0x00000000b2300000| PB 0x00000000b2300000| Untracked 
| 291|0x00000000b2300000, 0x00000000b2400000, 0x00000000b2400000|100%| O|  |TAMS 0x00000000b2400000| PB 0x00000000b2400000| Updating 
| 292|0x00000000b2400000, 0x00000000b2500000, 0x00000000b2500000|100%| O|  |TAMS 0x00000000b2500000| PB 0x00000000b2500000| Untracked 
| 293|0x00000000b2500000, 0x00000000b2600000, 0x00000000b2600000|100%| O|  |TAMS 0x00000000b2500000| PB 0x00000000b2500000| Untracked 
| 294|0x00000000b2600000, 0x00000000b2700000, 0x00000000b2700000|100%| O|  |TAMS 0x00000000b2700000| PB 0x00000000b2700000| Updating 
| 295|0x00000000b2700000, 0x00000000b2800000, 0x00000000b2800000|100%| O|  |TAMS 0x00000000b2700000| PB 0x00000000b2700000| Untracked 
| 296|0x00000000b2800000, 0x00000000b2900000, 0x00000000b2900000|100%| O|  |TAMS 0x00000000b2900000| PB 0x00000000b2900000| Updating 
| 297|0x00000000b2900000, 0x00000000b2a00000, 0x00000000b2a00000|100%| O|  |TAMS 0x00000000b2a00000| PB 0x00000000b2a00000| Updating 
| 298|0x00000000b2a00000, 0x00000000b2b00000, 0x00000000b2b00000|100%| O|  |TAMS 0x00000000b2b00000| PB 0x00000000b2b00000| Updating 
| 299|0x00000000b2b00000, 0x00000000b2c00000, 0x00000000b2c00000|100%| O|  |TAMS 0x00000000b2c00000| PB 0x00000000b2c00000| Untracked 
| 300|0x00000000b2c00000, 0x00000000b2d00000, 0x00000000b2d00000|100%| O|  |TAMS 0x00000000b2d00000| PB 0x00000000b2d00000| Updating 
| 301|0x00000000b2d00000, 0x00000000b2e00000, 0x00000000b2e00000|100%| O|  |TAMS 0x00000000b2e00000| PB 0x00000000b2e00000| Untracked 
| 302|0x00000000b2e00000, 0x00000000b2f00000, 0x00000000b2f00000|100%| O|  |TAMS 0x00000000b2f00000| PB 0x00000000b2f00000| Updating 
| 303|0x00000000b2f00000, 0x00000000b3000000, 0x00000000b3000000|100%| O|  |TAMS 0x00000000b3000000| PB 0x00000000b3000000| Untracked 
| 304|0x00000000b3000000, 0x00000000b3100000, 0x00000000b3100000|100%| O|  |TAMS 0x00000000b3100000| PB 0x00000000b3100000| Untracked 
| 305|0x00000000b3100000, 0x00000000b3200000, 0x00000000b3200000|100%|HS|  |TAMS 0x00000000b3200000| PB 0x00000000b3100000| Complete 
| 306|0x00000000b3200000, 0x00000000b3300000, 0x00000000b3300000|100%| O|  |TAMS 0x00000000b3200000| PB 0x00000000b3200000| Untracked 
| 307|0x00000000b3300000, 0x00000000b3400000, 0x00000000b3400000|100%| O|  |TAMS 0x00000000b3400000| PB 0x00000000b3400000| Untracked 
| 308|0x00000000b3400000, 0x00000000b3500000, 0x00000000b3500000|100%| O|  |TAMS 0x00000000b3500000| PB 0x00000000b3500000| Untracked 
| 309|0x00000000b3500000, 0x00000000b3600000, 0x00000000b3600000|100%| O|  |TAMS 0x00000000b3600000| PB 0x00000000b3600000| Updating 
| 310|0x00000000b3600000, 0x00000000b3700000, 0x00000000b3700000|100%| O|  |TAMS 0x00000000b3700000| PB 0x00000000b3700000| Untracked 
| 311|0x00000000b3700000, 0x00000000b3800000, 0x00000000b3800000|100%| O|  |TAMS 0x00000000b3800000| PB 0x00000000b3800000| Untracked 
| 312|0x00000000b3800000, 0x00000000b3900000, 0x00000000b3900000|100%| O|  |TAMS 0x00000000b3900000| PB 0x00000000b3900000| Untracked 
| 313|0x00000000b3900000, 0x00000000b3a00000, 0x00000000b3a00000|100%| O|  |TAMS 0x00000000b3a00000| PB 0x00000000b3a00000| Untracked 
| 314|0x00000000b3a00000, 0x00000000b3b00000, 0x00000000b3b00000|100%| O|  |TAMS 0x00000000b3a00000| PB 0x00000000b3a00000| Untracked 
| 315|0x00000000b3b00000, 0x00000000b3c00000, 0x00000000b3c00000|100%| O|  |TAMS 0x00000000b3b00000| PB 0x00000000b3b00000| Untracked 
| 316|0x00000000b3c00000, 0x00000000b3d00000, 0x00000000b3d00000|100%| O|  |TAMS 0x00000000b3d00000| PB 0x00000000b3d00000| Untracked 
| 317|0x00000000b3d00000, 0x00000000b3e00000, 0x00000000b3e00000|100%|HS|  |TAMS 0x00000000b3e00000| PB 0x00000000b3d00000| Complete 
| 318|0x00000000b3e00000, 0x00000000b3f00000, 0x00000000b3f00000|100%| O|  |TAMS 0x00000000b3f00000| PB 0x00000000b3f00000| Updating 
| 319|0x00000000b3f00000, 0x00000000b4000000, 0x00000000b4000000|100%| O|  |TAMS 0x00000000b3f00000| PB 0x00000000b3f00000| Untracked 
| 320|0x00000000b4000000, 0x00000000b4100000, 0x00000000b4100000|100%|HS|  |TAMS 0x00000000b4100000| PB 0x00000000b4000000| Complete 
| 321|0x00000000b4100000, 0x00000000b4200000, 0x00000000b4200000|100%|HC|  |TAMS 0x00000000b4200000| PB 0x00000000b4100000| Complete 
| 322|0x00000000b4200000, 0x00000000b4300000, 0x00000000b4300000|100%| O|  |TAMS 0x00000000b4300000| PB 0x00000000b4300000| Untracked 
| 323|0x00000000b4300000, 0x00000000b4400000, 0x00000000b4400000|100%| O|  |TAMS 0x00000000b4400000| PB 0x00000000b4400000| Updating 
| 324|0x00000000b4400000, 0x00000000b4500000, 0x00000000b4500000|100%| O|  |TAMS 0x00000000b4500000| PB 0x00000000b4500000| Untracked 
| 325|0x00000000b4500000, 0x00000000b4600000, 0x00000000b4600000|100%| O|  |TAMS 0x00000000b4600000| PB 0x00000000b4600000| Untracked 
| 326|0x00000000b4600000, 0x00000000b4700000, 0x00000000b4700000|100%| O|  |TAMS 0x00000000b4700000| PB 0x00000000b4700000| Updating 
| 327|0x00000000b4700000, 0x00000000b4800000, 0x00000000b4800000|100%| O|  |TAMS 0x00000000b4800000| PB 0x00000000b4800000| Untracked 
| 328|0x00000000b4800000, 0x00000000b4900000, 0x00000000b4900000|100%| O|  |TAMS 0x00000000b4900000| PB 0x00000000b4900000| Untracked 
| 329|0x00000000b4900000, 0x00000000b4a00000, 0x00000000b4a00000|100%| O|  |TAMS 0x00000000b4a00000| PB 0x00000000b4a00000| Untracked 
| 330|0x00000000b4a00000, 0x00000000b4b00000, 0x00000000b4b00000|100%| O|  |TAMS 0x00000000b4b00000| PB 0x00000000b4b00000| Untracked 
| 331|0x00000000b4b00000, 0x00000000b4c00000, 0x00000000b4c00000|100%| O|  |TAMS 0x00000000b4c00000| PB 0x00000000b4c00000| Untracked 
| 332|0x00000000b4c00000, 0x00000000b4d00000, 0x00000000b4d00000|100%| O|  |TAMS 0x00000000b4d00000| PB 0x00000000b4d00000| Untracked 
| 333|0x00000000b4d00000, 0x00000000b4e00000, 0x00000000b4e00000|100%| O|  |TAMS 0x00000000b4e00000| PB 0x00000000b4e00000| Untracked 
| 334|0x00000000b4e00000, 0x00000000b4f00000, 0x00000000b4f00000|100%| O|  |TAMS 0x00000000b4f00000| PB 0x00000000b4f00000| Untracked 
| 335|0x00000000b4f00000, 0x00000000b5000000, 0x00000000b5000000|100%| O|  |TAMS 0x00000000b5000000| PB 0x00000000b5000000| Untracked 
| 336|0x00000000b5000000, 0x00000000b5100000, 0x00000000b5100000|100%|HS|  |TAMS 0x00000000b5100000| PB 0x00000000b5000000| Complete 
| 337|0x00000000b5100000, 0x00000000b5200000, 0x00000000b5200000|100%| O|  |TAMS 0x00000000b5200000| PB 0x00000000b5200000| Untracked 
| 338|0x00000000b5200000, 0x00000000b5300000, 0x00000000b5300000|100%| O|  |TAMS 0x00000000b5200000| PB 0x00000000b5200000| Untracked 
| 339|0x00000000b5300000, 0x00000000b5400000, 0x00000000b5400000|100%|HS|  |TAMS 0x00000000b5400000| PB 0x00000000b5300000| Complete 
| 340|0x00000000b5400000, 0x00000000b5500000, 0x00000000b5500000|100%|HS|  |TAMS 0x00000000b5500000| PB 0x00000000b5400000| Complete 
| 341|0x00000000b5500000, 0x00000000b5600000, 0x00000000b5600000|100%| O|  |TAMS 0x00000000b5500000| PB 0x00000000b5500000| Untracked 
| 342|0x00000000b5600000, 0x00000000b5700000, 0x00000000b5700000|100%| O|  |TAMS 0x00000000b5700000| PB 0x00000000b5700000| Updating 
| 343|0x00000000b5700000, 0x00000000b5800000, 0x00000000b5800000|100%| O|  |TAMS 0x00000000b5800000| PB 0x00000000b5800000| Updating 
| 344|0x00000000b5800000, 0x00000000b5900000, 0x00000000b5900000|100%| O|  |TAMS 0x00000000b5900000| PB 0x00000000b5900000| Updating 
| 345|0x00000000b5900000, 0x00000000b5a00000, 0x00000000b5a00000|100%| O|  |TAMS 0x00000000b5a00000| PB 0x00000000b5a00000| Untracked 
| 346|0x00000000b5a00000, 0x00000000b5b00000, 0x00000000b5b00000|100%|HS|  |TAMS 0x00000000b5b00000| PB 0x00000000b5a00000| Complete 
| 347|0x00000000b5b00000, 0x00000000b5c00000, 0x00000000b5c00000|100%| O|  |TAMS 0x00000000b5c00000| PB 0x00000000b5c00000| Untracked 
| 348|0x00000000b5c00000, 0x00000000b5d00000, 0x00000000b5d00000|100%| O|  |TAMS 0x00000000b5d00000| PB 0x00000000b5d00000| Updating 
| 349|0x00000000b5d00000, 0x00000000b5e00000, 0x00000000b5e00000|100%| O|  |TAMS 0x00000000b5e00000| PB 0x00000000b5e00000| Untracked 
| 350|0x00000000b5e00000, 0x00000000b5f00000, 0x00000000b5f00000|100%| O|  |TAMS 0x00000000b5f00000| PB 0x00000000b5f00000| Untracked 
| 351|0x00000000b5f00000, 0x00000000b6000000, 0x00000000b6000000|100%| O|  |TAMS 0x00000000b6000000| PB 0x00000000b6000000| Untracked 
| 352|0x00000000b6000000, 0x00000000b6100000, 0x00000000b6100000|100%| O|  |TAMS 0x00000000b6100000| PB 0x00000000b6100000| Updating 
| 353|0x00000000b6100000, 0x00000000b6200000, 0x00000000b6200000|100%| O|  |TAMS 0x00000000b6200000| PB 0x00000000b6200000| Untracked 
| 354|0x00000000b6200000, 0x00000000b6300000, 0x00000000b6300000|100%| O|  |TAMS 0x00000000b6300000| PB 0x00000000b6300000| Updating 
| 355|0x00000000b6300000, 0x00000000b6400000, 0x00000000b6400000|100%| O|  |TAMS 0x00000000b6400000| PB 0x00000000b6400000| Untracked 
| 356|0x00000000b6400000, 0x00000000b6500000, 0x00000000b6500000|100%| O|  |TAMS 0x00000000b6500000| PB 0x00000000b6500000| Untracked 
| 357|0x00000000b6500000, 0x00000000b6600000, 0x00000000b6600000|100%| O|  |TAMS 0x00000000b6600000| PB 0x00000000b6600000| Untracked 
| 358|0x00000000b6600000, 0x00000000b6700000, 0x00000000b6700000|100%| O|  |TAMS 0x00000000b6700000| PB 0x00000000b6700000| Untracked 
| 359|0x00000000b6700000, 0x00000000b6800000, 0x00000000b6800000|100%| O|  |TAMS 0x00000000b6800000| PB 0x00000000b6800000| Updating 
| 360|0x00000000b6800000, 0x00000000b6900000, 0x00000000b6900000|100%| O|  |TAMS 0x00000000b6900000| PB 0x00000000b6900000| Untracked 
| 361|0x00000000b6900000, 0x00000000b6a00000, 0x00000000b6a00000|100%| O|  |TAMS 0x00000000b6a00000| PB 0x00000000b6a00000| Untracked 
| 362|0x00000000b6a00000, 0x00000000b6b00000, 0x00000000b6b00000|100%| O|  |TAMS 0x00000000b6b00000| PB 0x00000000b6b00000| Untracked 
| 363|0x00000000b6b00000, 0x00000000b6c00000, 0x00000000b6c00000|100%| O|  |TAMS 0x00000000b6c00000| PB 0x00000000b6c00000| Untracked 
| 364|0x00000000b6c00000, 0x00000000b6d00000, 0x00000000b6d00000|100%| O|  |TAMS 0x00000000b6d00000| PB 0x00000000b6d00000| Untracked 
| 365|0x00000000b6d00000, 0x00000000b6e00000, 0x00000000b6e00000|100%| O|  |TAMS 0x00000000b6e00000| PB 0x00000000b6e00000| Untracked 
| 366|0x00000000b6e00000, 0x00000000b6f00000, 0x00000000b6f00000|100%| O|  |TAMS 0x00000000b6f00000| PB 0x00000000b6f00000| Untracked 
| 367|0x00000000b6f00000, 0x00000000b7000000, 0x00000000b7000000|100%| O|  |TAMS 0x00000000b7000000| PB 0x00000000b7000000| Updating 
| 368|0x00000000b7000000, 0x00000000b7100000, 0x00000000b7100000|100%| O|  |TAMS 0x00000000b7000000| PB 0x00000000b7000000| Untracked 
| 369|0x00000000b7100000, 0x00000000b7200000, 0x00000000b7200000|100%| O|  |TAMS 0x00000000b7200000| PB 0x00000000b7200000| Updating 
| 370|0x00000000b7200000, 0x00000000b7300000, 0x00000000b7300000|100%| O|  |TAMS 0x00000000b7300000| PB 0x00000000b7300000| Untracked 
| 371|0x00000000b7300000, 0x00000000b7400000, 0x00000000b7400000|100%|HS|  |TAMS 0x00000000b7400000| PB 0x00000000b7300000| Complete 
| 372|0x00000000b7400000, 0x00000000b7500000, 0x00000000b7500000|100%| O|  |TAMS 0x00000000b7500000| PB 0x00000000b7500000| Untracked 
| 373|0x00000000b7500000, 0x00000000b7600000, 0x00000000b7600000|100%| O|  |TAMS 0x00000000b7600000| PB 0x00000000b7600000| Untracked 
| 374|0x00000000b7600000, 0x00000000b7700000, 0x00000000b7700000|100%| O|  |TAMS 0x00000000b7700000| PB 0x00000000b7700000| Untracked 
| 375|0x00000000b7700000, 0x00000000b7800000, 0x00000000b7800000|100%| O|  |TAMS 0x00000000b7800000| PB 0x00000000b7800000| Untracked 
| 376|0x00000000b7800000, 0x00000000b7900000, 0x00000000b7900000|100%| O|  |TAMS 0x00000000b7900000| PB 0x00000000b7900000| Untracked 
| 377|0x00000000b7900000, 0x00000000b7a00000, 0x00000000b7a00000|100%| O|  |TAMS 0x00000000b7a00000| PB 0x00000000b7a00000| Untracked 
| 378|0x00000000b7a00000, 0x00000000b7b00000, 0x00000000b7b00000|100%| O|  |TAMS 0x00000000b7b00000| PB 0x00000000b7b00000| Updating 
| 379|0x00000000b7b00000, 0x00000000b7c00000, 0x00000000b7c00000|100%| O|  |TAMS 0x00000000b7c00000| PB 0x00000000b7c00000| Updating 
| 380|0x00000000b7c00000, 0x00000000b7d00000, 0x00000000b7d00000|100%| O|  |TAMS 0x00000000b7d00000| PB 0x00000000b7d00000| Updating 
| 381|0x00000000b7d00000, 0x00000000b7e00000, 0x00000000b7e00000|100%|HS|  |TAMS 0x00000000b7e00000| PB 0x00000000b7d00000| Complete 
| 382|0x00000000b7e00000, 0x00000000b7f00000, 0x00000000b7f00000|100%|HC|  |TAMS 0x00000000b7f00000| PB 0x00000000b7e00000| Complete 
| 383|0x00000000b7f00000, 0x00000000b8000000, 0x00000000b8000000|100%| O|  |TAMS 0x00000000b8000000| PB 0x00000000b8000000| Updating 
| 384|0x00000000b8000000, 0x00000000b8100000, 0x00000000b8100000|100%| O|  |TAMS 0x00000000b8100000| PB 0x00000000b8100000| Untracked 
| 385|0x00000000b8100000, 0x00000000b8200000, 0x00000000b8200000|100%| O|  |TAMS 0x00000000b8200000| PB 0x00000000b8200000| Untracked 
| 386|0x00000000b8200000, 0x00000000b8300000, 0x00000000b8300000|100%| O|  |TAMS 0x00000000b8300000| PB 0x00000000b8300000| Untracked 
| 387|0x00000000b8300000, 0x00000000b8400000, 0x00000000b8400000|100%|HS|  |TAMS 0x00000000b8400000| PB 0x00000000b8300000| Complete 
| 388|0x00000000b8400000, 0x00000000b8500000, 0x00000000b8500000|100%|HS|  |TAMS 0x00000000b8500000| PB 0x00000000b8400000| Complete 
| 389|0x00000000b8500000, 0x00000000b8600000, 0x00000000b8600000|100%|HC|  |TAMS 0x00000000b8600000| PB 0x00000000b8500000| Complete 
| 390|0x00000000b8600000, 0x00000000b8700000, 0x00000000b8700000|100%|HS|  |TAMS 0x00000000b8700000| PB 0x00000000b8600000| Complete 
| 391|0x00000000b8700000, 0x00000000b8800000, 0x00000000b8800000|100%|HC|  |TAMS 0x00000000b8800000| PB 0x00000000b8700000| Complete 
| 392|0x00000000b8800000, 0x00000000b8900000, 0x00000000b8900000|100%| O|  |TAMS 0x00000000b8900000| PB 0x00000000b8900000| Updating 
| 393|0x00000000b8900000, 0x00000000b8a00000, 0x00000000b8a00000|100%| O|  |TAMS 0x00000000b8a00000| PB 0x00000000b8a00000| Updating 
| 394|0x00000000b8a00000, 0x00000000b8b00000, 0x00000000b8b00000|100%|HS|  |TAMS 0x00000000b8b00000| PB 0x00000000b8a00000| Complete 
| 395|0x00000000b8b00000, 0x00000000b8c00000, 0x00000000b8c00000|100%|HC|  |TAMS 0x00000000b8c00000| PB 0x00000000b8b00000| Complete 
| 396|0x00000000b8c00000, 0x00000000b8d00000, 0x00000000b8d00000|100%| O|  |TAMS 0x00000000b8c00000| PB 0x00000000b8c00000| Untracked 
| 397|0x00000000b8d00000, 0x00000000b8e00000, 0x00000000b8e00000|100%| O|  |TAMS 0x00000000b8e00000| PB 0x00000000b8e00000| Untracked 
| 398|0x00000000b8e00000, 0x00000000b8f00000, 0x00000000b8f00000|100%|HS|  |TAMS 0x00000000b8f00000| PB 0x00000000b8e00000| Complete 
| 399|0x00000000b8f00000, 0x00000000b9000000, 0x00000000b9000000|100%|HC|  |TAMS 0x00000000b9000000| PB 0x00000000b8f00000| Complete 
| 400|0x00000000b9000000, 0x00000000b9100000, 0x00000000b9100000|100%| O|  |TAMS 0x00000000b9100000| PB 0x00000000b9100000| Updating 
| 401|0x00000000b9100000, 0x00000000b9200000, 0x00000000b9200000|100%|HS|  |TAMS 0x00000000b9200000| PB 0x00000000b9100000| Complete 
| 402|0x00000000b9200000, 0x00000000b9300000, 0x00000000b9300000|100%|HC|  |TAMS 0x00000000b9300000| PB 0x00000000b9200000| Complete 
| 403|0x00000000b9300000, 0x00000000b9400000, 0x00000000b9400000|100%| O|  |TAMS 0x00000000b9400000| PB 0x00000000b9400000| Untracked 
| 404|0x00000000b9400000, 0x00000000b9500000, 0x00000000b9500000|100%| O|  |TAMS 0x00000000b9500000| PB 0x00000000b9500000| Updating 
| 405|0x00000000b9500000, 0x00000000b9600000, 0x00000000b9600000|100%| O|  |TAMS 0x00000000b9600000| PB 0x00000000b9600000| Untracked 
| 406|0x00000000b9600000, 0x00000000b9700000, 0x00000000b9700000|100%| O|  |TAMS 0x00000000b9700000| PB 0x00000000b9700000| Updating 
| 407|0x00000000b9700000, 0x00000000b9800000, 0x00000000b9800000|100%| O|  |TAMS 0x00000000b9800000| PB 0x00000000b9800000| Untracked 
| 408|0x00000000b9800000, 0x00000000b9900000, 0x00000000b9900000|100%| O|  |TAMS 0x00000000b9900000| PB 0x00000000b9900000| Untracked 
| 409|0x00000000b9900000, 0x00000000b9a00000, 0x00000000b9a00000|100%| O|  |TAMS 0x00000000b9a00000| PB 0x00000000b9a00000| Updating 
| 410|0x00000000b9a00000, 0x00000000b9b00000, 0x00000000b9b00000|100%| O|  |TAMS 0x00000000b9b00000| PB 0x00000000b9b00000| Updating 
| 411|0x00000000b9b00000, 0x00000000b9c00000, 0x00000000b9c00000|100%|HS|  |TAMS 0x00000000b9c00000| PB 0x00000000b9b00000| Complete 
| 412|0x00000000b9c00000, 0x00000000b9d00000, 0x00000000b9d00000|100%|HC|  |TAMS 0x00000000b9d00000| PB 0x00000000b9c00000| Complete 
| 413|0x00000000b9d00000, 0x00000000b9e00000, 0x00000000b9e00000|100%|HC|  |TAMS 0x00000000b9e00000| PB 0x00000000b9d00000| Complete 
| 414|0x00000000b9e00000, 0x00000000b9f00000, 0x00000000b9f00000|100%| O|  |TAMS 0x00000000b9f00000| PB 0x00000000b9f00000| Untracked 
| 415|0x00000000b9f00000, 0x00000000ba000000, 0x00000000ba000000|100%| O|  |TAMS 0x00000000ba000000| PB 0x00000000ba000000| Updating 
| 416|0x00000000ba000000, 0x00000000ba100000, 0x00000000ba100000|100%|HS|  |TAMS 0x00000000ba100000| PB 0x00000000ba000000| Complete 
| 417|0x00000000ba100000, 0x00000000ba200000, 0x00000000ba200000|100%|HC|  |TAMS 0x00000000ba200000| PB 0x00000000ba100000| Complete 
| 418|0x00000000ba200000, 0x00000000ba300000, 0x00000000ba300000|100%|HC|  |TAMS 0x00000000ba300000| PB 0x00000000ba200000| Complete 
| 419|0x00000000ba300000, 0x00000000ba400000, 0x00000000ba400000|100%|HC|  |TAMS 0x00000000ba400000| PB 0x00000000ba300000| Complete 
| 420|0x00000000ba400000, 0x00000000ba500000, 0x00000000ba500000|100%|HS|  |TAMS 0x00000000ba500000| PB 0x00000000ba400000| Complete 
| 421|0x00000000ba500000, 0x00000000ba600000, 0x00000000ba600000|100%|HC|  |TAMS 0x00000000ba600000| PB 0x00000000ba500000| Complete 
| 422|0x00000000ba600000, 0x00000000ba700000, 0x00000000ba700000|100%|HC|  |TAMS 0x00000000ba700000| PB 0x00000000ba600000| Complete 
| 423|0x00000000ba700000, 0x00000000ba800000, 0x00000000ba800000|100%|HS|  |TAMS 0x00000000ba800000| PB 0x00000000ba700000| Complete 
| 424|0x00000000ba800000, 0x00000000ba900000, 0x00000000ba900000|100%|HC|  |TAMS 0x00000000ba900000| PB 0x00000000ba800000| Complete 
| 425|0x00000000ba900000, 0x00000000baa00000, 0x00000000baa00000|100%|HC|  |TAMS 0x00000000baa00000| PB 0x00000000ba900000| Complete 
| 426|0x00000000baa00000, 0x00000000baa00000, 0x00000000bab00000|  0%| F|  |TAMS 0x00000000baa00000| PB 0x00000000baa00000| Untracked 
| 427|0x00000000bab00000, 0x00000000bac00000, 0x00000000bac00000|100%| O|  |TAMS 0x00000000bac00000| PB 0x00000000bac00000| Updating 
| 428|0x00000000bac00000, 0x00000000bad00000, 0x00000000bad00000|100%| O|  |TAMS 0x00000000bad00000| PB 0x00000000bad00000| Untracked 
| 429|0x00000000bad00000, 0x00000000bae00000, 0x00000000bae00000|100%| O|  |TAMS 0x00000000bae00000| PB 0x00000000bae00000| Untracked 
| 430|0x00000000bae00000, 0x00000000baf00000, 0x00000000baf00000|100%| O|  |TAMS 0x00000000baf00000| PB 0x00000000baf00000| Updating 
| 431|0x00000000baf00000, 0x00000000bb000000, 0x00000000bb000000|100%| O|  |TAMS 0x00000000bb000000| PB 0x00000000bb000000| Untracked 
| 432|0x00000000bb000000, 0x00000000bb100000, 0x00000000bb100000|100%| O|  |TAMS 0x00000000bb100000| PB 0x00000000bb100000| Untracked 
| 433|0x00000000bb100000, 0x00000000bb200000, 0x00000000bb200000|100%| O|  |TAMS 0x00000000bb200000| PB 0x00000000bb200000| Updating 
| 434|0x00000000bb200000, 0x00000000bb300000, 0x00000000bb300000|100%| O|  |TAMS 0x00000000bb300000| PB 0x00000000bb300000| Updating 
| 435|0x00000000bb300000, 0x00000000bb400000, 0x00000000bb400000|100%| O|  |TAMS 0x00000000bb400000| PB 0x00000000bb400000| Updating 
| 436|0x00000000bb400000, 0x00000000bb400000, 0x00000000bb500000|  0%| F|  |TAMS 0x00000000bb400000| PB 0x00000000bb400000| Untracked 
| 437|0x00000000bb500000, 0x00000000bb500000, 0x00000000bb600000|  0%| F|  |TAMS 0x00000000bb500000| PB 0x00000000bb500000| Untracked 
| 438|0x00000000bb600000, 0x00000000bb600000, 0x00000000bb700000|  0%| F|  |TAMS 0x00000000bb600000| PB 0x00000000bb600000| Untracked 
| 439|0x00000000bb700000, 0x00000000bb800000, 0x00000000bb800000|100%| O|  |TAMS 0x00000000bb800000| PB 0x00000000bb800000| Updating 
| 440|0x00000000bb800000, 0x00000000bb900000, 0x00000000bb900000|100%| O|  |TAMS 0x00000000bb900000| PB 0x00000000bb900000| Untracked 
| 441|0x00000000bb900000, 0x00000000bba00000, 0x00000000bba00000|100%| O|  |TAMS 0x00000000bba00000| PB 0x00000000bba00000| Untracked 
| 442|0x00000000bba00000, 0x00000000bbb00000, 0x00000000bbb00000|100%| O|  |TAMS 0x00000000bbb00000| PB 0x00000000bbb00000| Untracked 
| 443|0x00000000bbb00000, 0x00000000bbb00000, 0x00000000bbc00000|  0%| F|  |TAMS 0x00000000bbb00000| PB 0x00000000bbb00000| Untracked 
| 444|0x00000000bbc00000, 0x00000000bbc00000, 0x00000000bbd00000|  0%| F|  |TAMS 0x00000000bbc00000| PB 0x00000000bbc00000| Untracked 
| 445|0x00000000bbd00000, 0x00000000bbe00000, 0x00000000bbe00000|100%| O|  |TAMS 0x00000000bbe00000| PB 0x00000000bbe00000| Untracked 
| 446|0x00000000bbe00000, 0x00000000bbf00000, 0x00000000bbf00000|100%| O|  |TAMS 0x00000000bbf00000| PB 0x00000000bbf00000| Updating 
| 447|0x00000000bbf00000, 0x00000000bc000000, 0x00000000bc000000|100%| O|  |TAMS 0x00000000bc000000| PB 0x00000000bc000000| Updating 
| 448|0x00000000bc000000, 0x00000000bc100000, 0x00000000bc100000|100%| O|  |TAMS 0x00000000bc100000| PB 0x00000000bc100000| Updating 
| 449|0x00000000bc100000, 0x00000000bc200000, 0x00000000bc200000|100%| O|  |TAMS 0x00000000bc200000| PB 0x00000000bc200000| Updating 
| 450|0x00000000bc200000, 0x00000000bc300000, 0x00000000bc300000|100%| O|  |TAMS 0x00000000bc300000| PB 0x00000000bc300000| Updating 
| 451|0x00000000bc300000, 0x00000000bc400000, 0x00000000bc400000|100%| O|  |TAMS 0x00000000bc400000| PB 0x00000000bc400000| Updating 
| 452|0x00000000bc400000, 0x00000000bc500000, 0x00000000bc500000|100%| O|  |TAMS 0x00000000bc500000| PB 0x00000000bc500000| Updating 
| 453|0x00000000bc500000, 0x00000000bc600000, 0x00000000bc600000|100%| O|  |TAMS 0x00000000bc500000| PB 0x00000000bc500000| Untracked 
| 454|0x00000000bc600000, 0x00000000bc700000, 0x00000000bc700000|100%| O|  |TAMS 0x00000000bc700000| PB 0x00000000bc700000| Updating 
| 455|0x00000000bc700000, 0x00000000bc800000, 0x00000000bc800000|100%| O|  |TAMS 0x00000000bc800000| PB 0x00000000bc800000| Updating 
| 456|0x00000000bc800000, 0x00000000bc900000, 0x00000000bc900000|100%| O|  |TAMS 0x00000000bc900000| PB 0x00000000bc900000| Updating 
| 457|0x00000000bc900000, 0x00000000bca00000, 0x00000000bca00000|100%| O|  |TAMS 0x00000000bca00000| PB 0x00000000bca00000| Updating 
| 458|0x00000000bca00000, 0x00000000bca00000, 0x00000000bcb00000|  0%| F|  |TAMS 0x00000000bca00000| PB 0x00000000bca00000| Untracked 
| 459|0x00000000bcb00000, 0x00000000bcc00000, 0x00000000bcc00000|100%| O|  |TAMS 0x00000000bcc00000| PB 0x00000000bcc00000| Updating 
| 460|0x00000000bcc00000, 0x00000000bcd00000, 0x00000000bcd00000|100%| O|  |TAMS 0x00000000bcd00000| PB 0x00000000bcd00000| Updating 
| 461|0x00000000bcd00000, 0x00000000bce00000, 0x00000000bce00000|100%| O|  |TAMS 0x00000000bce00000| PB 0x00000000bce00000| Updating 
| 462|0x00000000bce00000, 0x00000000bcf00000, 0x00000000bcf00000|100%| O|  |TAMS 0x00000000bcf00000| PB 0x00000000bcf00000| Updating 
| 463|0x00000000bcf00000, 0x00000000bd000000, 0x00000000bd000000|100%| O|  |TAMS 0x00000000bd000000| PB 0x00000000bd000000| Updating 
| 464|0x00000000bd000000, 0x00000000bd100000, 0x00000000bd100000|100%| O|  |TAMS 0x00000000bd100000| PB 0x00000000bd100000| Updating 
| 465|0x00000000bd100000, 0x00000000bd200000, 0x00000000bd200000|100%| O|  |TAMS 0x00000000bd200000| PB 0x00000000bd200000| Updating 
| 466|0x00000000bd200000, 0x00000000bd300000, 0x00000000bd300000|100%| O|  |TAMS 0x00000000bd300000| PB 0x00000000bd300000| Updating 
| 467|0x00000000bd300000, 0x00000000bd400000, 0x00000000bd400000|100%| O|  |TAMS 0x00000000bd400000| PB 0x00000000bd400000| Untracked 
| 468|0x00000000bd400000, 0x00000000bd500000, 0x00000000bd500000|100%| O|  |TAMS 0x00000000bd500000| PB 0x00000000bd500000| Untracked 
| 469|0x00000000bd500000, 0x00000000bd500000, 0x00000000bd600000|  0%| F|  |TAMS 0x00000000bd500000| PB 0x00000000bd500000| Untracked 
| 470|0x00000000bd600000, 0x00000000bd600000, 0x00000000bd700000|  0%| F|  |TAMS 0x00000000bd600000| PB 0x00000000bd600000| Untracked 
| 471|0x00000000bd700000, 0x00000000bd700000, 0x00000000bd800000|  0%| F|  |TAMS 0x00000000bd700000| PB 0x00000000bd700000| Untracked 
| 472|0x00000000bd800000, 0x00000000bd800000, 0x00000000bd900000|  0%| F|  |TAMS 0x00000000bd800000| PB 0x00000000bd800000| Untracked 
| 473|0x00000000bd900000, 0x00000000bda00000, 0x00000000bda00000|100%| O|  |TAMS 0x00000000bda00000| PB 0x00000000bda00000| Untracked 
| 474|0x00000000bda00000, 0x00000000bda00000, 0x00000000bdb00000|  0%| F|  |TAMS 0x00000000bda00000| PB 0x00000000bda00000| Untracked 
| 475|0x00000000bdb00000, 0x00000000bdc00000, 0x00000000bdc00000|100%| O|  |TAMS 0x00000000bdc00000| PB 0x00000000bdc00000| Updating 
| 476|0x00000000bdc00000, 0x00000000bdc00000, 0x00000000bdd00000|  0%| F|  |TAMS 0x00000000bdc00000| PB 0x00000000bdc00000| Untracked 
| 477|0x00000000bdd00000, 0x00000000bde00000, 0x00000000bde00000|100%| O|  |TAMS 0x00000000bde00000| PB 0x00000000bde00000| Untracked 
| 478|0x00000000bde00000, 0x00000000bde00000, 0x00000000bdf00000|  0%| F|  |TAMS 0x00000000bde00000| PB 0x00000000bde00000| Untracked 
| 479|0x00000000bdf00000, 0x00000000be000000, 0x00000000be000000|100%| O|  |TAMS 0x00000000be000000| PB 0x00000000be000000| Untracked 
| 480|0x00000000be000000, 0x00000000be000000, 0x00000000be100000|  0%| F|  |TAMS 0x00000000be000000| PB 0x00000000be000000| Untracked 
| 481|0x00000000be100000, 0x00000000be200000, 0x00000000be200000|100%| O|  |TAMS 0x00000000be200000| PB 0x00000000be200000| Untracked 
| 482|0x00000000be200000, 0x00000000be300000, 0x00000000be300000|100%| O|  |TAMS 0x00000000be300000| PB 0x00000000be300000| Untracked 
| 483|0x00000000be300000, 0x00000000be400000, 0x00000000be400000|100%| O|  |TAMS 0x00000000be400000| PB 0x00000000be400000| Untracked 
| 484|0x00000000be400000, 0x00000000be400000, 0x00000000be500000|  0%| F|  |TAMS 0x00000000be400000| PB 0x00000000be400000| Untracked 
| 485|0x00000000be500000, 0x00000000be500000, 0x00000000be600000|  0%| F|  |TAMS 0x00000000be500000| PB 0x00000000be500000| Untracked 
| 486|0x00000000be600000, 0x00000000be700000, 0x00000000be700000|100%| O|  |TAMS 0x00000000be700000| PB 0x00000000be700000| Untracked 
| 487|0x00000000be700000, 0x00000000be700000, 0x00000000be800000|  0%| F|  |TAMS 0x00000000be700000| PB 0x00000000be700000| Untracked 
| 488|0x00000000be800000, 0x00000000be900000, 0x00000000be900000|100%| O|  |TAMS 0x00000000be900000| PB 0x00000000be900000| Untracked 
| 489|0x00000000be900000, 0x00000000bea00000, 0x00000000bea00000|100%| O|  |TAMS 0x00000000bea00000| PB 0x00000000bea00000| Untracked 
| 490|0x00000000bea00000, 0x00000000beb00000, 0x00000000beb00000|100%| O|  |TAMS 0x00000000beb00000| PB 0x00000000beb00000| Untracked 
| 491|0x00000000beb00000, 0x00000000beb00000, 0x00000000bec00000|  0%| F|  |TAMS 0x00000000beb00000| PB 0x00000000beb00000| Untracked 
| 492|0x00000000bec00000, 0x00000000bec00000, 0x00000000bed00000|  0%| F|  |TAMS 0x00000000bec00000| PB 0x00000000bec00000| Untracked 
| 493|0x00000000bed00000, 0x00000000bee00000, 0x00000000bee00000|100%| O|  |TAMS 0x00000000bee00000| PB 0x00000000bee00000| Untracked 
| 494|0x00000000bee00000, 0x00000000bef00000, 0x00000000bef00000|100%| O|  |TAMS 0x00000000bef00000| PB 0x00000000bef00000| Untracked 
| 495|0x00000000bef00000, 0x00000000bf000000, 0x00000000bf000000|100%| O|  |TAMS 0x00000000bf000000| PB 0x00000000bf000000| Untracked 
| 496|0x00000000bf000000, 0x00000000bf000000, 0x00000000bf100000|  0%| F|  |TAMS 0x00000000bf000000| PB 0x00000000bf000000| Untracked 
| 497|0x00000000bf100000, 0x00000000bf100000, 0x00000000bf200000|  0%| F|  |TAMS 0x00000000bf100000| PB 0x00000000bf100000| Untracked 
| 498|0x00000000bf200000, 0x00000000bf300000, 0x00000000bf300000|100%| O|  |TAMS 0x00000000bf300000| PB 0x00000000bf300000| Untracked 
| 499|0x00000000bf300000, 0x00000000bf400000, 0x00000000bf400000|100%| O|  |TAMS 0x00000000bf400000| PB 0x00000000bf400000| Untracked 
| 500|0x00000000bf400000, 0x00000000bf500000, 0x00000000bf500000|100%| O|  |TAMS 0x00000000bf500000| PB 0x00000000bf500000| Untracked 
| 501|0x00000000bf500000, 0x00000000bf500000, 0x00000000bf600000|  0%| F|  |TAMS 0x00000000bf500000| PB 0x00000000bf500000| Untracked 
| 502|0x00000000bf600000, 0x00000000bf600000, 0x00000000bf700000|  0%| F|  |TAMS 0x00000000bf600000| PB 0x00000000bf600000| Untracked 
| 503|0x00000000bf700000, 0x00000000bf800000, 0x00000000bf800000|100%| O|  |TAMS 0x00000000bf800000| PB 0x00000000bf800000| Untracked 
| 504|0x00000000bf800000, 0x00000000bf800000, 0x00000000bf900000|  0%| F|  |TAMS 0x00000000bf800000| PB 0x00000000bf800000| Untracked 
| 505|0x00000000bf900000, 0x00000000bfa00000, 0x00000000bfa00000|100%| O|  |TAMS 0x00000000bfa00000| PB 0x00000000bfa00000| Untracked 
| 506|0x00000000bfa00000, 0x00000000bfb00000, 0x00000000bfb00000|100%| O|  |TAMS 0x00000000bfb00000| PB 0x00000000bfb00000| Untracked 
| 507|0x00000000bfb00000, 0x00000000bfc00000, 0x00000000bfc00000|100%| O|  |TAMS 0x00000000bfc00000| PB 0x00000000bfc00000| Untracked 
| 508|0x00000000bfc00000, 0x00000000bfd00000, 0x00000000bfd00000|100%| O|  |TAMS 0x00000000bfd00000| PB 0x00000000bfd00000| Untracked 
| 509|0x00000000bfd00000, 0x00000000bfe00000, 0x00000000bfe00000|100%| O|  |TAMS 0x00000000bfe00000| PB 0x00000000bfe00000| Untracked 
| 510|0x00000000bfe00000, 0x00000000bff00000, 0x00000000bff00000|100%| O|  |TAMS 0x00000000bff00000| PB 0x00000000bff00000| Untracked 
| 511|0x00000000bff00000, 0x00000000bff00000, 0x00000000c0000000|  0%| F|  |TAMS 0x00000000bff00000| PB 0x00000000bff00000| Untracked 
| 512|0x00000000c0000000, 0x00000000c0000000, 0x00000000c0100000|  0%| F|  |TAMS 0x00000000c0000000| PB 0x00000000c0000000| Untracked 
| 513|0x00000000c0100000, 0x00000000c0100000, 0x00000000c0200000|  0%| F|  |TAMS 0x00000000c0100000| PB 0x00000000c0100000| Untracked 
| 514|0x00000000c0200000, 0x00000000c0300000, 0x00000000c0300000|100%| O|  |TAMS 0x00000000c0300000| PB 0x00000000c0300000| Untracked 
| 515|0x00000000c0300000, 0x00000000c0400000, 0x00000000c0400000|100%| O|  |TAMS 0x00000000c0400000| PB 0x00000000c0400000| Untracked 
| 516|0x00000000c0400000, 0x00000000c0500000, 0x00000000c0500000|100%| O|  |TAMS 0x00000000c0500000| PB 0x00000000c0500000| Untracked 
| 517|0x00000000c0500000, 0x00000000c0600000, 0x00000000c0600000|100%| O|  |TAMS 0x00000000c0600000| PB 0x00000000c0600000| Untracked 
| 518|0x00000000c0600000, 0x00000000c0700000, 0x00000000c0700000|100%| O|  |TAMS 0x00000000c0700000| PB 0x00000000c0700000| Untracked 
| 519|0x00000000c0700000, 0x00000000c0800000, 0x00000000c0800000|100%| O|  |TAMS 0x00000000c0800000| PB 0x00000000c0800000| Untracked 
| 520|0x00000000c0800000, 0x00000000c0800000, 0x00000000c0900000|  0%| F|  |TAMS 0x00000000c0800000| PB 0x00000000c0800000| Untracked 
| 521|0x00000000c0900000, 0x00000000c0900000, 0x00000000c0a00000|  0%| F|  |TAMS 0x00000000c0900000| PB 0x00000000c0900000| Untracked 
| 522|0x00000000c0a00000, 0x00000000c0a00000, 0x00000000c0b00000|  0%| F|  |TAMS 0x00000000c0a00000| PB 0x00000000c0a00000| Untracked 
| 523|0x00000000c0b00000, 0x00000000c0c00000, 0x00000000c0c00000|100%| O|  |TAMS 0x00000000c0c00000| PB 0x00000000c0c00000| Untracked 
| 524|0x00000000c0c00000, 0x00000000c0d00000, 0x00000000c0d00000|100%| O|  |TAMS 0x00000000c0d00000| PB 0x00000000c0d00000| Untracked 
| 525|0x00000000c0d00000, 0x00000000c0e00000, 0x00000000c0e00000|100%| O|  |TAMS 0x00000000c0e00000| PB 0x00000000c0e00000| Untracked 
| 526|0x00000000c0e00000, 0x00000000c0f00000, 0x00000000c0f00000|100%| O|  |TAMS 0x00000000c0f00000| PB 0x00000000c0f00000| Untracked 
| 527|0x00000000c0f00000, 0x00000000c1000000, 0x00000000c1000000|100%| O|  |TAMS 0x00000000c1000000| PB 0x00000000c1000000| Untracked 
| 528|0x00000000c1000000, 0x00000000c1100000, 0x00000000c1100000|100%| O|  |TAMS 0x00000000c1100000| PB 0x00000000c1100000| Untracked 
| 529|0x00000000c1100000, 0x00000000c1200000, 0x00000000c1200000|100%| O|  |TAMS 0x00000000c1200000| PB 0x00000000c1200000| Untracked 
| 530|0x00000000c1200000, 0x00000000c1300000, 0x00000000c1300000|100%| O|  |TAMS 0x00000000c1300000| PB 0x00000000c1300000| Untracked 
| 531|0x00000000c1300000, 0x00000000c1400000, 0x00000000c1400000|100%| O|  |TAMS 0x00000000c1400000| PB 0x00000000c1400000| Untracked 
| 532|0x00000000c1400000, 0x00000000c1500000, 0x00000000c1500000|100%| O|  |TAMS 0x00000000c1500000| PB 0x00000000c1500000| Untracked 
| 533|0x00000000c1500000, 0x00000000c1600000, 0x00000000c1600000|100%| O|  |TAMS 0x00000000c1600000| PB 0x00000000c1600000| Untracked 
| 534|0x00000000c1600000, 0x00000000c1700000, 0x00000000c1700000|100%| O|  |TAMS 0x00000000c1700000| PB 0x00000000c1700000| Untracked 
| 535|0x00000000c1700000, 0x00000000c1800000, 0x00000000c1800000|100%| O|  |TAMS 0x00000000c1800000| PB 0x00000000c1800000| Untracked 
| 536|0x00000000c1800000, 0x00000000c1900000, 0x00000000c1900000|100%| O|  |TAMS 0x00000000c1900000| PB 0x00000000c1900000| Untracked 
| 537|0x00000000c1900000, 0x00000000c1a00000, 0x00000000c1a00000|100%| O|  |TAMS 0x00000000c1a00000| PB 0x00000000c1a00000| Untracked 
| 538|0x00000000c1a00000, 0x00000000c1b00000, 0x00000000c1b00000|100%| O|  |TAMS 0x00000000c1b00000| PB 0x00000000c1b00000| Untracked 
| 539|0x00000000c1b00000, 0x00000000c1c00000, 0x00000000c1c00000|100%| O|  |TAMS 0x00000000c1c00000| PB 0x00000000c1c00000| Untracked 
| 540|0x00000000c1c00000, 0x00000000c1d00000, 0x00000000c1d00000|100%| O|  |TAMS 0x00000000c1d00000| PB 0x00000000c1d00000| Untracked 
| 541|0x00000000c1d00000, 0x00000000c1e00000, 0x00000000c1e00000|100%| O|  |TAMS 0x00000000c1e00000| PB 0x00000000c1e00000| Untracked 
| 542|0x00000000c1e00000, 0x00000000c1f00000, 0x00000000c1f00000|100%| O|  |TAMS 0x00000000c1f00000| PB 0x00000000c1f00000| Untracked 
| 543|0x00000000c1f00000, 0x00000000c2000000, 0x00000000c2000000|100%| O|  |TAMS 0x00000000c2000000| PB 0x00000000c2000000| Untracked 
| 544|0x00000000c2000000, 0x00000000c2100000, 0x00000000c2100000|100%| O|  |TAMS 0x00000000c2100000| PB 0x00000000c2100000| Untracked 
| 545|0x00000000c2100000, 0x00000000c2200000, 0x00000000c2200000|100%| O|  |TAMS 0x00000000c2200000| PB 0x00000000c2200000| Untracked 
| 546|0x00000000c2200000, 0x00000000c2300000, 0x00000000c2300000|100%| O|  |TAMS 0x00000000c2300000| PB 0x00000000c2300000| Updating 
| 547|0x00000000c2300000, 0x00000000c2400000, 0x00000000c2400000|100%| O|  |TAMS 0x00000000c2400000| PB 0x00000000c2400000| Untracked 
| 548|0x00000000c2400000, 0x00000000c2500000, 0x00000000c2500000|100%| O|  |TAMS 0x00000000c2500000| PB 0x00000000c2500000| Updating 
| 549|0x00000000c2500000, 0x00000000c2600000, 0x00000000c2600000|100%| O|  |TAMS 0x00000000c2600000| PB 0x00000000c2600000| Untracked 
| 550|0x00000000c2600000, 0x00000000c2700000, 0x00000000c2700000|100%| O|  |TAMS 0x00000000c2700000| PB 0x00000000c2700000| Updating 
| 551|0x00000000c2700000, 0x00000000c2800000, 0x00000000c2800000|100%| O|  |TAMS 0x00000000c2800000| PB 0x00000000c2800000| Updating 
| 552|0x00000000c2800000, 0x00000000c2900000, 0x00000000c2900000|100%| O|  |TAMS 0x00000000c2900000| PB 0x00000000c2900000| Updating 
| 553|0x00000000c2900000, 0x00000000c2a00000, 0x00000000c2a00000|100%| O|  |TAMS 0x00000000c2a00000| PB 0x00000000c2a00000| Updating 
| 554|0x00000000c2a00000, 0x00000000c2b00000, 0x00000000c2b00000|100%| O|  |TAMS 0x00000000c2b00000| PB 0x00000000c2b00000| Updating 
| 555|0x00000000c2b00000, 0x00000000c2c00000, 0x00000000c2c00000|100%| O|  |TAMS 0x00000000c2c00000| PB 0x00000000c2c00000| Updating 
| 556|0x00000000c2c00000, 0x00000000c2d00000, 0x00000000c2d00000|100%| O|  |TAMS 0x00000000c2d00000| PB 0x00000000c2d00000| Updating 
| 557|0x00000000c2d00000, 0x00000000c2e00000, 0x00000000c2e00000|100%| O|  |TAMS 0x00000000c2e00000| PB 0x00000000c2e00000| Untracked 
| 558|0x00000000c2e00000, 0x00000000c2f00000, 0x00000000c2f00000|100%| O|  |TAMS 0x00000000c2f00000| PB 0x00000000c2f00000| Updating 
| 559|0x00000000c2f00000, 0x00000000c3000000, 0x00000000c3000000|100%| O|  |TAMS 0x00000000c3000000| PB 0x00000000c3000000| Untracked 
| 560|0x00000000c3000000, 0x00000000c3100000, 0x00000000c3100000|100%| O|  |TAMS 0x00000000c3100000| PB 0x00000000c3100000| Untracked 
| 561|0x00000000c3100000, 0x00000000c3200000, 0x00000000c3200000|100%| O|  |TAMS 0x00000000c3200000| PB 0x00000000c3200000| Untracked 
| 562|0x00000000c3200000, 0x00000000c3300000, 0x00000000c3300000|100%| O|  |TAMS 0x00000000c3300000| PB 0x00000000c3300000| Untracked 
| 563|0x00000000c3300000, 0x00000000c3400000, 0x00000000c3400000|100%| O|  |TAMS 0x00000000c3400000| PB 0x00000000c3400000| Untracked 
| 564|0x00000000c3400000, 0x00000000c3500000, 0x00000000c3500000|100%| O|  |TAMS 0x00000000c3500000| PB 0x00000000c3500000| Untracked 
| 565|0x00000000c3500000, 0x00000000c3600000, 0x00000000c3600000|100%| O|  |TAMS 0x00000000c3600000| PB 0x00000000c3600000| Untracked 
| 566|0x00000000c3600000, 0x00000000c3700000, 0x00000000c3700000|100%| O|  |TAMS 0x00000000c3700000| PB 0x00000000c3700000| Untracked 
| 567|0x00000000c3700000, 0x00000000c3800000, 0x00000000c3800000|100%| O|  |TAMS 0x00000000c3800000| PB 0x00000000c3800000| Untracked 
| 568|0x00000000c3800000, 0x00000000c3900000, 0x00000000c3900000|100%| O|  |TAMS 0x00000000c3900000| PB 0x00000000c3900000| Untracked 
| 569|0x00000000c3900000, 0x00000000c3a00000, 0x00000000c3a00000|100%| O|  |TAMS 0x00000000c3a00000| PB 0x00000000c3a00000| Untracked 
| 570|0x00000000c3a00000, 0x00000000c3b00000, 0x00000000c3b00000|100%| O|  |TAMS 0x00000000c3b00000| PB 0x00000000c3b00000| Untracked 
| 571|0x00000000c3b00000, 0x00000000c3c00000, 0x00000000c3c00000|100%| O|  |TAMS 0x00000000c3c00000| PB 0x00000000c3c00000| Untracked 
| 572|0x00000000c3c00000, 0x00000000c3d00000, 0x00000000c3d00000|100%| O|  |TAMS 0x00000000c3d00000| PB 0x00000000c3d00000| Untracked 
| 573|0x00000000c3d00000, 0x00000000c3e00000, 0x00000000c3e00000|100%| O|  |TAMS 0x00000000c3e00000| PB 0x00000000c3e00000| Untracked 
| 574|0x00000000c3e00000, 0x00000000c3f00000, 0x00000000c3f00000|100%| O|  |TAMS 0x00000000c3f00000| PB 0x00000000c3f00000| Updating 
| 575|0x00000000c3f00000, 0x00000000c4000000, 0x00000000c4000000|100%| O|  |TAMS 0x00000000c4000000| PB 0x00000000c4000000| Untracked 
| 576|0x00000000c4000000, 0x00000000c4100000, 0x00000000c4100000|100%| O|  |TAMS 0x00000000c4100000| PB 0x00000000c4100000| Untracked 
| 577|0x00000000c4100000, 0x00000000c4200000, 0x00000000c4200000|100%| O|  |TAMS 0x00000000c4200000| PB 0x00000000c4200000| Untracked 
| 578|0x00000000c4200000, 0x00000000c4300000, 0x00000000c4300000|100%| O|  |TAMS 0x00000000c4300000| PB 0x00000000c4300000| Untracked 
| 579|0x00000000c4300000, 0x00000000c4400000, 0x00000000c4400000|100%| O|  |TAMS 0x00000000c4400000| PB 0x00000000c4400000| Updating 
| 580|0x00000000c4400000, 0x00000000c4500000, 0x00000000c4500000|100%| O|  |TAMS 0x00000000c4500000| PB 0x00000000c4500000| Untracked 
| 581|0x00000000c4500000, 0x00000000c4600000, 0x00000000c4600000|100%| O|  |TAMS 0x00000000c4600000| PB 0x00000000c4600000| Untracked 
| 582|0x00000000c4600000, 0x00000000c4600000, 0x00000000c4700000|  0%| F|  |TAMS 0x00000000c4600000| PB 0x00000000c4600000| Untracked 
| 583|0x00000000c4700000, 0x00000000c4800000, 0x00000000c4800000|100%| O|  |TAMS 0x00000000c4700000| PB 0x00000000c4700000| Untracked 
| 584|0x00000000c4800000, 0x00000000c4900000, 0x00000000c4900000|100%| O|  |TAMS 0x00000000c4800000| PB 0x00000000c4800000| Untracked 
| 585|0x00000000c4900000, 0x00000000c4a00000, 0x00000000c4a00000|100%|HS|  |TAMS 0x00000000c4a00000| PB 0x00000000c4900000| Complete 
| 586|0x00000000c4a00000, 0x00000000c4b00000, 0x00000000c4b00000|100%| O|  |TAMS 0x00000000c4b00000| PB 0x00000000c4b00000| Untracked 
| 587|0x00000000c4b00000, 0x00000000c4c00000, 0x00000000c4c00000|100%| O|  |TAMS 0x00000000c4c00000| PB 0x00000000c4c00000| Untracked 
| 588|0x00000000c4c00000, 0x00000000c4d00000, 0x00000000c4d00000|100%| O|  |TAMS 0x00000000c4d00000| PB 0x00000000c4d00000| Untracked 
| 589|0x00000000c4d00000, 0x00000000c4e00000, 0x00000000c4e00000|100%| O|  |TAMS 0x00000000c4e00000| PB 0x00000000c4e00000| Untracked 
| 590|0x00000000c4e00000, 0x00000000c4f00000, 0x00000000c4f00000|100%| O|  |TAMS 0x00000000c4f00000| PB 0x00000000c4f00000| Untracked 
| 591|0x00000000c4f00000, 0x00000000c5000000, 0x00000000c5000000|100%| O|  |TAMS 0x00000000c5000000| PB 0x00000000c5000000| Untracked 
| 592|0x00000000c5000000, 0x00000000c5100000, 0x00000000c5100000|100%| O|  |TAMS 0x00000000c5100000| PB 0x00000000c5100000| Untracked 
| 593|0x00000000c5100000, 0x00000000c5200000, 0x00000000c5200000|100%| O|  |TAMS 0x00000000c5200000| PB 0x00000000c5200000| Untracked 
| 594|0x00000000c5200000, 0x00000000c5300000, 0x00000000c5300000|100%| O|  |TAMS 0x00000000c5300000| PB 0x00000000c5300000| Untracked 
| 595|0x00000000c5300000, 0x00000000c5400000, 0x00000000c5400000|100%| O|  |TAMS 0x00000000c5400000| PB 0x00000000c5400000| Untracked 
| 596|0x00000000c5400000, 0x00000000c5500000, 0x00000000c5500000|100%| O|  |TAMS 0x00000000c5500000| PB 0x00000000c5500000| Untracked 
| 597|0x00000000c5500000, 0x00000000c5600000, 0x00000000c5600000|100%| O|  |TAMS 0x00000000c5600000| PB 0x00000000c5600000| Updating 
| 598|0x00000000c5600000, 0x00000000c5700000, 0x00000000c5700000|100%| O|  |TAMS 0x00000000c5700000| PB 0x00000000c5700000| Untracked 
| 599|0x00000000c5700000, 0x00000000c5800000, 0x00000000c5800000|100%| O|  |TAMS 0x00000000c5800000| PB 0x00000000c5800000| Untracked 
| 600|0x00000000c5800000, 0x00000000c5900000, 0x00000000c5900000|100%| O|  |TAMS 0x00000000c5900000| PB 0x00000000c5900000| Updating 
| 601|0x00000000c5900000, 0x00000000c5a00000, 0x00000000c5a00000|100%| O|  |TAMS 0x00000000c5a00000| PB 0x00000000c5a00000| Untracked 
| 602|0x00000000c5a00000, 0x00000000c5b00000, 0x00000000c5b00000|100%| O|  |TAMS 0x00000000c5b00000| PB 0x00000000c5b00000| Untracked 
| 603|0x00000000c5b00000, 0x00000000c5c00000, 0x00000000c5c00000|100%| O|  |TAMS 0x00000000c5c00000| PB 0x00000000c5c00000| Untracked 
| 604|0x00000000c5c00000, 0x00000000c5d00000, 0x00000000c5d00000|100%| O|  |TAMS 0x00000000c5d00000| PB 0x00000000c5d00000| Untracked 
| 605|0x00000000c5d00000, 0x00000000c5e00000, 0x00000000c5e00000|100%| O|  |TAMS 0x00000000c5e00000| PB 0x00000000c5e00000| Untracked 
| 606|0x00000000c5e00000, 0x00000000c5f00000, 0x00000000c5f00000|100%| O|  |TAMS 0x00000000c5f00000| PB 0x00000000c5f00000| Updating 
| 607|0x00000000c5f00000, 0x00000000c6000000, 0x00000000c6000000|100%| O|  |TAMS 0x00000000c6000000| PB 0x00000000c6000000| Untracked 
| 608|0x00000000c6000000, 0x00000000c6100000, 0x00000000c6100000|100%| O|  |TAMS 0x00000000c6100000| PB 0x00000000c6100000| Updating 
| 609|0x00000000c6100000, 0x00000000c6200000, 0x00000000c6200000|100%| O|  |TAMS 0x00000000c6200000| PB 0x00000000c6200000| Untracked 
| 610|0x00000000c6200000, 0x00000000c6300000, 0x00000000c6300000|100%| O|  |TAMS 0x00000000c6300000| PB 0x00000000c6300000| Untracked 
| 611|0x00000000c6300000, 0x00000000c6400000, 0x00000000c6400000|100%| O|  |TAMS 0x00000000c6400000| PB 0x00000000c6400000| Untracked 
| 612|0x00000000c6400000, 0x00000000c6500000, 0x00000000c6500000|100%| O|  |TAMS 0x00000000c6500000| PB 0x00000000c6500000| Untracked 
| 613|0x00000000c6500000, 0x00000000c6600000, 0x00000000c6600000|100%| O|  |TAMS 0x00000000c6600000| PB 0x00000000c6600000| Updating 
| 614|0x00000000c6600000, 0x00000000c6700000, 0x00000000c6700000|100%| O|  |TAMS 0x00000000c6700000| PB 0x00000000c6700000| Untracked 
| 615|0x00000000c6700000, 0x00000000c6800000, 0x00000000c6800000|100%| O|  |TAMS 0x00000000c6800000| PB 0x00000000c6800000| Updating 
| 616|0x00000000c6800000, 0x00000000c6900000, 0x00000000c6900000|100%| O|  |TAMS 0x00000000c6900000| PB 0x00000000c6900000| Updating 
| 617|0x00000000c6900000, 0x00000000c6a00000, 0x00000000c6a00000|100%| O|  |TAMS 0x00000000c6a00000| PB 0x00000000c6a00000| Updating 
| 618|0x00000000c6a00000, 0x00000000c6b00000, 0x00000000c6b00000|100%| O|  |TAMS 0x00000000c6b00000| PB 0x00000000c6b00000| Updating 
| 619|0x00000000c6b00000, 0x00000000c6c00000, 0x00000000c6c00000|100%| O|  |TAMS 0x00000000c6c00000| PB 0x00000000c6c00000| Updating 
| 620|0x00000000c6c00000, 0x00000000c6d00000, 0x00000000c6d00000|100%| O|  |TAMS 0x00000000c6d00000| PB 0x00000000c6d00000| Updating 
| 621|0x00000000c6d00000, 0x00000000c6e00000, 0x00000000c6e00000|100%| O|  |TAMS 0x00000000c6e00000| PB 0x00000000c6e00000| Updating 
| 622|0x00000000c6e00000, 0x00000000c6f00000, 0x00000000c6f00000|100%| O|  |TAMS 0x00000000c6f00000| PB 0x00000000c6f00000| Updating 
| 623|0x00000000c6f00000, 0x00000000c7000000, 0x00000000c7000000|100%| O|  |TAMS 0x00000000c7000000| PB 0x00000000c7000000| Updating 
| 624|0x00000000c7000000, 0x00000000c7100000, 0x00000000c7100000|100%| O|  |TAMS 0x00000000c7080000| PB 0x00000000c7080000| Updating 
| 625|0x00000000c7100000, 0x00000000c7200000, 0x00000000c7200000|100%|HS|  |TAMS 0x00000000c7100000| PB 0x00000000c7100000| Complete 
| 626|0x00000000c7200000, 0x00000000c7200000, 0x00000000c7300000|  0%| F|  |TAMS 0x00000000c7200000| PB 0x00000000c7200000| Untracked 
| 627|0x00000000c7300000, 0x00000000c7400000, 0x00000000c7400000|100%|HS|  |TAMS 0x00000000c7300000| PB 0x00000000c7300000| Complete 
| 628|0x00000000c7400000, 0x00000000c7500000, 0x00000000c7500000|100%|HS|  |TAMS 0x00000000c7400000| PB 0x00000000c7400000| Complete 
| 629|0x00000000c7500000, 0x00000000c7600000, 0x00000000c7600000|100%| O|  |TAMS 0x00000000c7600000| PB 0x00000000c7600000| Untracked 
| 630|0x00000000c7600000, 0x00000000c7700000, 0x00000000c7700000|100%| O|  |TAMS 0x00000000c7700000| PB 0x00000000c7700000| Untracked 
| 631|0x00000000c7700000, 0x00000000c7800000, 0x00000000c7800000|100%| O|  |TAMS 0x00000000c7800000| PB 0x00000000c7800000| Untracked 
| 632|0x00000000c7800000, 0x00000000c7900000, 0x00000000c7900000|100%| O|  |TAMS 0x00000000c7900000| PB 0x00000000c7900000| Untracked 
| 633|0x00000000c7900000, 0x00000000c7a00000, 0x00000000c7a00000|100%| O|  |TAMS 0x00000000c7a00000| PB 0x00000000c7a00000| Updating 
| 634|0x00000000c7a00000, 0x00000000c7b00000, 0x00000000c7b00000|100%| O|  |TAMS 0x00000000c7b00000| PB 0x00000000c7b00000| Untracked 
| 635|0x00000000c7b00000, 0x00000000c7c00000, 0x00000000c7c00000|100%| O|  |TAMS 0x00000000c7c00000| PB 0x00000000c7c00000| Updating 
| 636|0x00000000c7c00000, 0x00000000c7d00000, 0x00000000c7d00000|100%| O|  |TAMS 0x00000000c7d00000| PB 0x00000000c7d00000| Updating 
| 637|0x00000000c7d00000, 0x00000000c7e00000, 0x00000000c7e00000|100%| O|  |TAMS 0x00000000c7e00000| PB 0x00000000c7e00000| Updating 
| 638|0x00000000c7e00000, 0x00000000c7e00000, 0x00000000c7f00000|  0%| F|  |TAMS 0x00000000c7e00000| PB 0x00000000c7e00000| Untracked 
| 639|0x00000000c7f00000, 0x00000000c7f00000, 0x00000000c8000000|  0%| F|  |TAMS 0x00000000c7f00000| PB 0x00000000c7f00000| Untracked 
| 640|0x00000000c8000000, 0x00000000c8000000, 0x00000000c8100000|  0%| F|  |TAMS 0x00000000c8000000| PB 0x00000000c8000000| Untracked 
| 641|0x00000000c8100000, 0x00000000c8100000, 0x00000000c8200000|  0%| F|  |TAMS 0x00000000c8100000| PB 0x00000000c8100000| Untracked 
| 642|0x00000000c8200000, 0x00000000c8200000, 0x00000000c8300000|  0%| F|  |TAMS 0x00000000c8200000| PB 0x00000000c8200000| Untracked 
| 643|0x00000000c8300000, 0x00000000c8300000, 0x00000000c8400000|  0%| F|  |TAMS 0x00000000c8300000| PB 0x00000000c8300000| Untracked 
| 644|0x00000000c8400000, 0x00000000c8400000, 0x00000000c8500000|  0%| F|  |TAMS 0x00000000c8400000| PB 0x00000000c8400000| Untracked 
| 645|0x00000000c8500000, 0x00000000c8500000, 0x00000000c8600000|  0%| F|  |TAMS 0x00000000c8500000| PB 0x00000000c8500000| Untracked 
| 646|0x00000000c8600000, 0x00000000c8600000, 0x00000000c8700000|  0%| F|  |TAMS 0x00000000c8600000| PB 0x00000000c8600000| Untracked 
| 647|0x00000000c8700000, 0x00000000c8700000, 0x00000000c8800000|  0%| F|  |TAMS 0x00000000c8700000| PB 0x00000000c8700000| Untracked 
| 648|0x00000000c8800000, 0x00000000c8800000, 0x00000000c8900000|  0%| F|  |TAMS 0x00000000c8800000| PB 0x00000000c8800000| Untracked 
| 649|0x00000000c8900000, 0x00000000c8900000, 0x00000000c8a00000|  0%| F|  |TAMS 0x00000000c8900000| PB 0x00000000c8900000| Untracked 
| 650|0x00000000c8a00000, 0x00000000c8a00000, 0x00000000c8b00000|  0%| F|  |TAMS 0x00000000c8a00000| PB 0x00000000c8a00000| Untracked 
| 651|0x00000000c8b00000, 0x00000000c8b00000, 0x00000000c8c00000|  0%| F|  |TAMS 0x00000000c8b00000| PB 0x00000000c8b00000| Untracked 
| 652|0x00000000c8c00000, 0x00000000c8c00000, 0x00000000c8d00000|  0%| F|  |TAMS 0x00000000c8c00000| PB 0x00000000c8c00000| Untracked 
| 653|0x00000000c8d00000, 0x00000000c8d00000, 0x00000000c8e00000|  0%| F|  |TAMS 0x00000000c8d00000| PB 0x00000000c8d00000| Untracked 
| 654|0x00000000c8e00000, 0x00000000c8e00000, 0x00000000c8f00000|  0%| F|  |TAMS 0x00000000c8e00000| PB 0x00000000c8e00000| Untracked 
| 655|0x00000000c8f00000, 0x00000000c8f00000, 0x00000000c9000000|  0%| F|  |TAMS 0x00000000c8f00000| PB 0x00000000c8f00000| Untracked 
| 656|0x00000000c9000000, 0x00000000c9000000, 0x00000000c9100000|  0%| F|  |TAMS 0x00000000c9000000| PB 0x00000000c9000000| Untracked 
| 657|0x00000000c9100000, 0x00000000c9100000, 0x00000000c9200000|  0%| F|  |TAMS 0x00000000c9100000| PB 0x00000000c9100000| Untracked 
| 658|0x00000000c9200000, 0x00000000c9200000, 0x00000000c9300000|  0%| F|  |TAMS 0x00000000c9200000| PB 0x00000000c9200000| Untracked 
| 659|0x00000000c9300000, 0x00000000c9300000, 0x00000000c9400000|  0%| F|  |TAMS 0x00000000c9300000| PB 0x00000000c9300000| Untracked 
| 660|0x00000000c9400000, 0x00000000c9400000, 0x00000000c9500000|  0%| F|  |TAMS 0x00000000c9400000| PB 0x00000000c9400000| Untracked 
| 661|0x00000000c9500000, 0x00000000c9500000, 0x00000000c9600000|  0%| F|  |TAMS 0x00000000c9500000| PB 0x00000000c9500000| Untracked 
| 662|0x00000000c9600000, 0x00000000c9600000, 0x00000000c9700000|  0%| F|  |TAMS 0x00000000c9600000| PB 0x00000000c9600000| Untracked 
| 663|0x00000000c9700000, 0x00000000c9700000, 0x00000000c9800000|  0%| F|  |TAMS 0x00000000c9700000| PB 0x00000000c9700000| Untracked 
| 664|0x00000000c9800000, 0x00000000c9800000, 0x00000000c9900000|  0%| F|  |TAMS 0x00000000c9800000| PB 0x00000000c9800000| Untracked 
| 665|0x00000000c9900000, 0x00000000c9900000, 0x00000000c9a00000|  0%| F|  |TAMS 0x00000000c9900000| PB 0x00000000c9900000| Untracked 
| 666|0x00000000c9a00000, 0x00000000c9a00000, 0x00000000c9b00000|  0%| F|  |TAMS 0x00000000c9a00000| PB 0x00000000c9a00000| Untracked 
| 667|0x00000000c9b00000, 0x00000000c9b00000, 0x00000000c9c00000|  0%| F|  |TAMS 0x00000000c9b00000| PB 0x00000000c9b00000| Untracked 
| 668|0x00000000c9c00000, 0x00000000c9c00000, 0x00000000c9d00000|  0%| F|  |TAMS 0x00000000c9c00000| PB 0x00000000c9c00000| Untracked 
| 669|0x00000000c9d00000, 0x00000000c9d00000, 0x00000000c9e00000|  0%| F|  |TAMS 0x00000000c9d00000| PB 0x00000000c9d00000| Untracked 
| 670|0x00000000c9e00000, 0x00000000c9e00000, 0x00000000c9f00000|  0%| F|  |TAMS 0x00000000c9e00000| PB 0x00000000c9e00000| Untracked 
| 671|0x00000000c9f00000, 0x00000000c9f00000, 0x00000000ca000000|  0%| F|  |TAMS 0x00000000c9f00000| PB 0x00000000c9f00000| Untracked 
| 672|0x00000000ca000000, 0x00000000ca000000, 0x00000000ca100000|  0%| F|  |TAMS 0x00000000ca000000| PB 0x00000000ca000000| Untracked 
| 673|0x00000000ca100000, 0x00000000ca100000, 0x00000000ca200000|  0%| F|  |TAMS 0x00000000ca100000| PB 0x00000000ca100000| Untracked 
| 674|0x00000000ca200000, 0x00000000ca200000, 0x00000000ca300000|  0%| F|  |TAMS 0x00000000ca200000| PB 0x00000000ca200000| Untracked 
| 675|0x00000000ca300000, 0x00000000ca300000, 0x00000000ca400000|  0%| F|  |TAMS 0x00000000ca300000| PB 0x00000000ca300000| Untracked 
| 676|0x00000000ca400000, 0x00000000ca400000, 0x00000000ca500000|  0%| F|  |TAMS 0x00000000ca400000| PB 0x00000000ca400000| Untracked 
| 677|0x00000000ca500000, 0x00000000ca500000, 0x00000000ca600000|  0%| F|  |TAMS 0x00000000ca500000| PB 0x00000000ca500000| Untracked 
| 678|0x00000000ca600000, 0x00000000ca600000, 0x00000000ca700000|  0%| F|  |TAMS 0x00000000ca600000| PB 0x00000000ca600000| Untracked 
| 679|0x00000000ca700000, 0x00000000ca700000, 0x00000000ca800000|  0%| F|  |TAMS 0x00000000ca700000| PB 0x00000000ca700000| Untracked 
| 680|0x00000000ca800000, 0x00000000ca800000, 0x00000000ca900000|  0%| F|  |TAMS 0x00000000ca800000| PB 0x00000000ca800000| Untracked 
| 681|0x00000000ca900000, 0x00000000ca900000, 0x00000000caa00000|  0%| F|  |TAMS 0x00000000ca900000| PB 0x00000000ca900000| Untracked 
| 682|0x00000000caa00000, 0x00000000caa00000, 0x00000000cab00000|  0%| F|  |TAMS 0x00000000caa00000| PB 0x00000000caa00000| Untracked 
| 683|0x00000000cab00000, 0x00000000cab00000, 0x00000000cac00000|  0%| F|  |TAMS 0x00000000cab00000| PB 0x00000000cab00000| Untracked 
| 684|0x00000000cac00000, 0x00000000cac00000, 0x00000000cad00000|  0%| F|  |TAMS 0x00000000cac00000| PB 0x00000000cac00000| Untracked 
| 685|0x00000000cad00000, 0x00000000cad00000, 0x00000000cae00000|  0%| F|  |TAMS 0x00000000cad00000| PB 0x00000000cad00000| Untracked 
| 686|0x00000000cae00000, 0x00000000cae00000, 0x00000000caf00000|  0%| F|  |TAMS 0x00000000cae00000| PB 0x00000000cae00000| Untracked 
| 687|0x00000000caf00000, 0x00000000caf00000, 0x00000000cb000000|  0%| F|  |TAMS 0x00000000caf00000| PB 0x00000000caf00000| Untracked 
| 688|0x00000000cb000000, 0x00000000cb000000, 0x00000000cb100000|  0%| F|  |TAMS 0x00000000cb000000| PB 0x00000000cb000000| Untracked 
| 689|0x00000000cb100000, 0x00000000cb100000, 0x00000000cb200000|  0%| F|  |TAMS 0x00000000cb100000| PB 0x00000000cb100000| Untracked 
| 690|0x00000000cb200000, 0x00000000cb200000, 0x00000000cb300000|  0%| F|  |TAMS 0x00000000cb200000| PB 0x00000000cb200000| Untracked 
| 691|0x00000000cb300000, 0x00000000cb300000, 0x00000000cb400000|  0%| F|  |TAMS 0x00000000cb300000| PB 0x00000000cb300000| Untracked 
| 692|0x00000000cb400000, 0x00000000cb400000, 0x00000000cb500000|  0%| F|  |TAMS 0x00000000cb400000| PB 0x00000000cb400000| Untracked 
| 693|0x00000000cb500000, 0x00000000cb500000, 0x00000000cb600000|  0%| F|  |TAMS 0x00000000cb500000| PB 0x00000000cb500000| Untracked 
| 694|0x00000000cb600000, 0x00000000cb600000, 0x00000000cb700000|  0%| F|  |TAMS 0x00000000cb600000| PB 0x00000000cb600000| Untracked 
| 695|0x00000000cb700000, 0x00000000cb700000, 0x00000000cb800000|  0%| F|  |TAMS 0x00000000cb700000| PB 0x00000000cb700000| Untracked 
| 696|0x00000000cb800000, 0x00000000cb800000, 0x00000000cb900000|  0%| F|  |TAMS 0x00000000cb800000| PB 0x00000000cb800000| Untracked 
| 697|0x00000000cb900000, 0x00000000cb900000, 0x00000000cba00000|  0%| F|  |TAMS 0x00000000cb900000| PB 0x00000000cb900000| Untracked 
| 698|0x00000000cba00000, 0x00000000cba00000, 0x00000000cbb00000|  0%| F|  |TAMS 0x00000000cba00000| PB 0x00000000cba00000| Untracked 
| 699|0x00000000cbb00000, 0x00000000cbb00000, 0x00000000cbc00000|  0%| F|  |TAMS 0x00000000cbb00000| PB 0x00000000cbb00000| Untracked 
| 700|0x00000000cbc00000, 0x00000000cbc00000, 0x00000000cbd00000|  0%| F|  |TAMS 0x00000000cbc00000| PB 0x00000000cbc00000| Untracked 
| 701|0x00000000cbd00000, 0x00000000cbd00000, 0x00000000cbe00000|  0%| F|  |TAMS 0x00000000cbd00000| PB 0x00000000cbd00000| Untracked 
| 702|0x00000000cbe00000, 0x00000000cbe00000, 0x00000000cbf00000|  0%| F|  |TAMS 0x00000000cbe00000| PB 0x00000000cbe00000| Untracked 
| 703|0x00000000cbf00000, 0x00000000cbf00000, 0x00000000cc000000|  0%| F|  |TAMS 0x00000000cbf00000| PB 0x00000000cbf00000| Untracked 
| 704|0x00000000cc000000, 0x00000000cc000000, 0x00000000cc100000|  0%| F|  |TAMS 0x00000000cc000000| PB 0x00000000cc000000| Untracked 
| 705|0x00000000cc100000, 0x00000000cc100000, 0x00000000cc200000|  0%| F|  |TAMS 0x00000000cc100000| PB 0x00000000cc100000| Untracked 
| 706|0x00000000cc200000, 0x00000000cc200000, 0x00000000cc300000|  0%| F|  |TAMS 0x00000000cc200000| PB 0x00000000cc200000| Untracked 
| 707|0x00000000cc300000, 0x00000000cc300000, 0x00000000cc400000|  0%| F|  |TAMS 0x00000000cc300000| PB 0x00000000cc300000| Untracked 
| 708|0x00000000cc400000, 0x00000000cc400000, 0x00000000cc500000|  0%| F|  |TAMS 0x00000000cc400000| PB 0x00000000cc400000| Untracked 
| 709|0x00000000cc500000, 0x00000000cc500000, 0x00000000cc600000|  0%| F|  |TAMS 0x00000000cc500000| PB 0x00000000cc500000| Untracked 
| 710|0x00000000cc600000, 0x00000000cc600000, 0x00000000cc700000|  0%| F|  |TAMS 0x00000000cc600000| PB 0x00000000cc600000| Untracked 
| 711|0x00000000cc700000, 0x00000000cc700000, 0x00000000cc800000|  0%| F|  |TAMS 0x00000000cc700000| PB 0x00000000cc700000| Untracked 
| 712|0x00000000cc800000, 0x00000000cc800000, 0x00000000cc900000|  0%| F|  |TAMS 0x00000000cc800000| PB 0x00000000cc800000| Untracked 
| 713|0x00000000cc900000, 0x00000000cc900000, 0x00000000cca00000|  0%| F|  |TAMS 0x00000000cc900000| PB 0x00000000cc900000| Untracked 
| 714|0x00000000cca00000, 0x00000000cca00000, 0x00000000ccb00000|  0%| F|  |TAMS 0x00000000cca00000| PB 0x00000000cca00000| Untracked 
| 715|0x00000000ccb00000, 0x00000000ccb00000, 0x00000000ccc00000|  0%| F|  |TAMS 0x00000000ccb00000| PB 0x00000000ccb00000| Untracked 
| 716|0x00000000ccc00000, 0x00000000ccc00000, 0x00000000ccd00000|  0%| F|  |TAMS 0x00000000ccc00000| PB 0x00000000ccc00000| Untracked 
| 717|0x00000000ccd00000, 0x00000000ccd00000, 0x00000000cce00000|  0%| F|  |TAMS 0x00000000ccd00000| PB 0x00000000ccd00000| Untracked 
| 718|0x00000000cce00000, 0x00000000cce00000, 0x00000000ccf00000|  0%| F|  |TAMS 0x00000000cce00000| PB 0x00000000cce00000| Untracked 
| 719|0x00000000ccf00000, 0x00000000ccf00000, 0x00000000cd000000|  0%| F|  |TAMS 0x00000000ccf00000| PB 0x00000000ccf00000| Untracked 
| 720|0x00000000cd000000, 0x00000000cd000000, 0x00000000cd100000|  0%| F|  |TAMS 0x00000000cd000000| PB 0x00000000cd000000| Untracked 
| 721|0x00000000cd100000, 0x00000000cd100000, 0x00000000cd200000|  0%| F|  |TAMS 0x00000000cd100000| PB 0x00000000cd100000| Untracked 
| 722|0x00000000cd200000, 0x00000000cd200000, 0x00000000cd300000|  0%| F|  |TAMS 0x00000000cd200000| PB 0x00000000cd200000| Untracked 
| 723|0x00000000cd300000, 0x00000000cd300000, 0x00000000cd400000|  0%| F|  |TAMS 0x00000000cd300000| PB 0x00000000cd300000| Untracked 
| 724|0x00000000cd400000, 0x00000000cd400000, 0x00000000cd500000|  0%| F|  |TAMS 0x00000000cd400000| PB 0x00000000cd400000| Untracked 
| 725|0x00000000cd500000, 0x00000000cd500000, 0x00000000cd600000|  0%| F|  |TAMS 0x00000000cd500000| PB 0x00000000cd500000| Untracked 
| 726|0x00000000cd600000, 0x00000000cd600000, 0x00000000cd700000|  0%| F|  |TAMS 0x00000000cd600000| PB 0x00000000cd600000| Untracked 
| 727|0x00000000cd700000, 0x00000000cd700000, 0x00000000cd800000|  0%| F|  |TAMS 0x00000000cd700000| PB 0x00000000cd700000| Untracked 
| 728|0x00000000cd800000, 0x00000000cd800000, 0x00000000cd900000|  0%| F|  |TAMS 0x00000000cd800000| PB 0x00000000cd800000| Untracked 
| 729|0x00000000cd900000, 0x00000000cd900000, 0x00000000cda00000|  0%| F|  |TAMS 0x00000000cd900000| PB 0x00000000cd900000| Untracked 
| 730|0x00000000cda00000, 0x00000000cdb00000, 0x00000000cdb00000|100%| S|CS|TAMS 0x00000000cda00000| PB 0x00000000cda00000| Complete 
| 731|0x00000000cdb00000, 0x00000000cdc00000, 0x00000000cdc00000|100%| S|CS|TAMS 0x00000000cdb00000| PB 0x00000000cdb00000| Complete 
| 732|0x00000000cdc00000, 0x00000000cdd00000, 0x00000000cdd00000|100%| S|CS|TAMS 0x00000000cdc00000| PB 0x00000000cdc00000| Complete 
| 733|0x00000000cdd00000, 0x00000000cde00000, 0x00000000cde00000|100%| S|CS|TAMS 0x00000000cdd00000| PB 0x00000000cdd00000| Complete 
| 734|0x00000000cde00000, 0x00000000cdf00000, 0x00000000cdf00000|100%| S|CS|TAMS 0x00000000cde00000| PB 0x00000000cde00000| Complete 
| 735|0x00000000cdf00000, 0x00000000ce000000, 0x00000000ce000000|100%| S|CS|TAMS 0x00000000cdf00000| PB 0x00000000cdf00000| Complete 
| 736|0x00000000ce000000, 0x00000000ce100000, 0x00000000ce100000|100%| S|CS|TAMS 0x00000000ce000000| PB 0x00000000ce000000| Complete 
| 737|0x00000000ce100000, 0x00000000ce200000, 0x00000000ce200000|100%| S|CS|TAMS 0x00000000ce100000| PB 0x00000000ce100000| Complete 
| 738|0x00000000ce200000, 0x00000000ce300000, 0x00000000ce300000|100%| S|CS|TAMS 0x00000000ce200000| PB 0x00000000ce200000| Complete 
| 739|0x00000000ce300000, 0x00000000ce400000, 0x00000000ce400000|100%| S|CS|TAMS 0x00000000ce300000| PB 0x00000000ce300000| Complete 
| 740|0x00000000ce400000, 0x00000000ce500000, 0x00000000ce500000|100%| S|CS|TAMS 0x00000000ce400000| PB 0x00000000ce400000| Complete 
| 741|0x00000000ce500000, 0x00000000ce600000, 0x00000000ce600000|100%| S|CS|TAMS 0x00000000ce500000| PB 0x00000000ce500000| Complete 
| 742|0x00000000ce600000, 0x00000000ce600000, 0x00000000ce700000|  0%| F|  |TAMS 0x00000000ce600000| PB 0x00000000ce600000| Untracked 
| 743|0x00000000ce700000, 0x00000000ce700000, 0x00000000ce800000|  0%| F|  |TAMS 0x00000000ce700000| PB 0x00000000ce700000| Untracked 
| 744|0x00000000ce800000, 0x00000000ce800000, 0x00000000ce900000|  0%| F|  |TAMS 0x00000000ce800000| PB 0x00000000ce800000| Untracked 
| 745|0x00000000ce900000, 0x00000000ce900000, 0x00000000cea00000|  0%| F|  |TAMS 0x00000000ce900000| PB 0x00000000ce900000| Untracked 
| 746|0x00000000cea00000, 0x00000000cea00000, 0x00000000ceb00000|  0%| F|  |TAMS 0x00000000cea00000| PB 0x00000000cea00000| Untracked 
| 747|0x00000000ceb00000, 0x00000000ceb00000, 0x00000000cec00000|  0%| F|  |TAMS 0x00000000ceb00000| PB 0x00000000ceb00000| Untracked 
| 748|0x00000000cec00000, 0x00000000cec00000, 0x00000000ced00000|  0%| F|  |TAMS 0x00000000cec00000| PB 0x00000000cec00000| Untracked 
| 749|0x00000000ced00000, 0x00000000ced00000, 0x00000000cee00000|  0%| F|  |TAMS 0x00000000ced00000| PB 0x00000000ced00000| Untracked 
| 750|0x00000000cee00000, 0x00000000cee00000, 0x00000000cef00000|  0%| F|  |TAMS 0x00000000cee00000| PB 0x00000000cee00000| Untracked 
| 751|0x00000000cef00000, 0x00000000cef00000, 0x00000000cf000000|  0%| F|  |TAMS 0x00000000cef00000| PB 0x00000000cef00000| Untracked 
| 752|0x00000000cf000000, 0x00000000cf000000, 0x00000000cf100000|  0%| F|  |TAMS 0x00000000cf000000| PB 0x00000000cf000000| Untracked 
| 753|0x00000000cf100000, 0x00000000cf100000, 0x00000000cf200000|  0%| F|  |TAMS 0x00000000cf100000| PB 0x00000000cf100000| Untracked 
| 754|0x00000000cf200000, 0x00000000cf200000, 0x00000000cf300000|  0%| F|  |TAMS 0x00000000cf200000| PB 0x00000000cf200000| Untracked 
| 755|0x00000000cf300000, 0x00000000cf300000, 0x00000000cf400000|  0%| F|  |TAMS 0x00000000cf300000| PB 0x00000000cf300000| Untracked 
| 756|0x00000000cf400000, 0x00000000cf400000, 0x00000000cf500000|  0%| F|  |TAMS 0x00000000cf400000| PB 0x00000000cf400000| Untracked 
| 757|0x00000000cf500000, 0x00000000cf500000, 0x00000000cf600000|  0%| F|  |TAMS 0x00000000cf500000| PB 0x00000000cf500000| Untracked 
| 758|0x00000000cf600000, 0x00000000cf600000, 0x00000000cf700000|  0%| F|  |TAMS 0x00000000cf600000| PB 0x00000000cf600000| Untracked 
| 759|0x00000000cf700000, 0x00000000cf700000, 0x00000000cf800000|  0%| F|  |TAMS 0x00000000cf700000| PB 0x00000000cf700000| Untracked 
| 760|0x00000000cf800000, 0x00000000cf800000, 0x00000000cf900000|  0%| F|  |TAMS 0x00000000cf800000| PB 0x00000000cf800000| Untracked 
| 761|0x00000000cf900000, 0x00000000cf900000, 0x00000000cfa00000|  0%| F|  |TAMS 0x00000000cf900000| PB 0x00000000cf900000| Untracked 
| 762|0x00000000cfa00000, 0x00000000cfa00000, 0x00000000cfb00000|  0%| F|  |TAMS 0x00000000cfa00000| PB 0x00000000cfa00000| Untracked 
| 763|0x00000000cfb00000, 0x00000000cfb00000, 0x00000000cfc00000|  0%| F|  |TAMS 0x00000000cfb00000| PB 0x00000000cfb00000| Untracked 
| 764|0x00000000cfc00000, 0x00000000cfc00000, 0x00000000cfd00000|  0%| F|  |TAMS 0x00000000cfc00000| PB 0x00000000cfc00000| Untracked 
| 765|0x00000000cfd00000, 0x00000000cfd00000, 0x00000000cfe00000|  0%| F|  |TAMS 0x00000000cfd00000| PB 0x00000000cfd00000| Untracked 
| 766|0x00000000cfe00000, 0x00000000cfe00000, 0x00000000cff00000|  0%| F|  |TAMS 0x00000000cfe00000| PB 0x00000000cfe00000| Untracked 
| 767|0x00000000cff00000, 0x00000000cff00000, 0x00000000d0000000|  0%| F|  |TAMS 0x00000000cff00000| PB 0x00000000cff00000| Untracked 
| 768|0x00000000d0000000, 0x00000000d0000000, 0x00000000d0100000|  0%| F|  |TAMS 0x00000000d0000000| PB 0x00000000d0000000| Untracked 
| 769|0x00000000d0100000, 0x00000000d0100000, 0x00000000d0200000|  0%| F|  |TAMS 0x00000000d0100000| PB 0x00000000d0100000| Untracked 
| 770|0x00000000d0200000, 0x00000000d0200000, 0x00000000d0300000|  0%| F|  |TAMS 0x00000000d0200000| PB 0x00000000d0200000| Untracked 
| 771|0x00000000d0300000, 0x00000000d0300000, 0x00000000d0400000|  0%| F|  |TAMS 0x00000000d0300000| PB 0x00000000d0300000| Untracked 
| 772|0x00000000d0400000, 0x00000000d0400000, 0x00000000d0500000|  0%| F|  |TAMS 0x00000000d0400000| PB 0x00000000d0400000| Untracked 
| 773|0x00000000d0500000, 0x00000000d0500000, 0x00000000d0600000|  0%| F|  |TAMS 0x00000000d0500000| PB 0x00000000d0500000| Untracked 
| 774|0x00000000d0600000, 0x00000000d0600000, 0x00000000d0700000|  0%| F|  |TAMS 0x00000000d0600000| PB 0x00000000d0600000| Untracked 
| 775|0x00000000d0700000, 0x00000000d0700000, 0x00000000d0800000|  0%| F|  |TAMS 0x00000000d0700000| PB 0x00000000d0700000| Untracked 
| 776|0x00000000d0800000, 0x00000000d0800000, 0x00000000d0900000|  0%| F|  |TAMS 0x00000000d0800000| PB 0x00000000d0800000| Untracked 
| 777|0x00000000d0900000, 0x00000000d0900000, 0x00000000d0a00000|  0%| F|  |TAMS 0x00000000d0900000| PB 0x00000000d0900000| Untracked 
| 778|0x00000000d0a00000, 0x00000000d0a00000, 0x00000000d0b00000|  0%| F|  |TAMS 0x00000000d0a00000| PB 0x00000000d0a00000| Untracked 
| 779|0x00000000d0b00000, 0x00000000d0b00000, 0x00000000d0c00000|  0%| F|  |TAMS 0x00000000d0b00000| PB 0x00000000d0b00000| Untracked 
| 780|0x00000000d0c00000, 0x00000000d0c00000, 0x00000000d0d00000|  0%| F|  |TAMS 0x00000000d0c00000| PB 0x00000000d0c00000| Untracked 
| 781|0x00000000d0d00000, 0x00000000d0d00000, 0x00000000d0e00000|  0%| F|  |TAMS 0x00000000d0d00000| PB 0x00000000d0d00000| Untracked 
| 782|0x00000000d0e00000, 0x00000000d0e00000, 0x00000000d0f00000|  0%| F|  |TAMS 0x00000000d0e00000| PB 0x00000000d0e00000| Untracked 
| 783|0x00000000d0f00000, 0x00000000d0f00000, 0x00000000d1000000|  0%| F|  |TAMS 0x00000000d0f00000| PB 0x00000000d0f00000| Untracked 
| 784|0x00000000d1000000, 0x00000000d1000000, 0x00000000d1100000|  0%| F|  |TAMS 0x00000000d1000000| PB 0x00000000d1000000| Untracked 
| 785|0x00000000d1100000, 0x00000000d1100000, 0x00000000d1200000|  0%| F|  |TAMS 0x00000000d1100000| PB 0x00000000d1100000| Untracked 
| 786|0x00000000d1200000, 0x00000000d1200000, 0x00000000d1300000|  0%| F|  |TAMS 0x00000000d1200000| PB 0x00000000d1200000| Untracked 
| 787|0x00000000d1300000, 0x00000000d1300000, 0x00000000d1400000|  0%| F|  |TAMS 0x00000000d1300000| PB 0x00000000d1300000| Untracked 
| 788|0x00000000d1400000, 0x00000000d1400000, 0x00000000d1500000|  0%| F|  |TAMS 0x00000000d1400000| PB 0x00000000d1400000| Untracked 
| 789|0x00000000d1500000, 0x00000000d1500000, 0x00000000d1600000|  0%| F|  |TAMS 0x00000000d1500000| PB 0x00000000d1500000| Untracked 
| 790|0x00000000d1600000, 0x00000000d1600000, 0x00000000d1700000|  0%| F|  |TAMS 0x00000000d1600000| PB 0x00000000d1600000| Untracked 
| 791|0x00000000d1700000, 0x00000000d1700000, 0x00000000d1800000|  0%| F|  |TAMS 0x00000000d1700000| PB 0x00000000d1700000| Untracked 
| 792|0x00000000d1800000, 0x00000000d1800000, 0x00000000d1900000|  0%| F|  |TAMS 0x00000000d1800000| PB 0x00000000d1800000| Untracked 
| 793|0x00000000d1900000, 0x00000000d1900000, 0x00000000d1a00000|  0%| F|  |TAMS 0x00000000d1900000| PB 0x00000000d1900000| Untracked 
| 794|0x00000000d1a00000, 0x00000000d1a00000, 0x00000000d1b00000|  0%| F|  |TAMS 0x00000000d1a00000| PB 0x00000000d1a00000| Untracked 
| 795|0x00000000d1b00000, 0x00000000d1b00000, 0x00000000d1c00000|  0%| F|  |TAMS 0x00000000d1b00000| PB 0x00000000d1b00000| Untracked 
| 796|0x00000000d1c00000, 0x00000000d1c00000, 0x00000000d1d00000|  0%| F|  |TAMS 0x00000000d1c00000| PB 0x00000000d1c00000| Untracked 
| 797|0x00000000d1d00000, 0x00000000d1d00000, 0x00000000d1e00000|  0%| F|  |TAMS 0x00000000d1d00000| PB 0x00000000d1d00000| Untracked 
| 798|0x00000000d1e00000, 0x00000000d1e00000, 0x00000000d1f00000|  0%| F|  |TAMS 0x00000000d1e00000| PB 0x00000000d1e00000| Untracked 
| 799|0x00000000d1f00000, 0x00000000d1f00000, 0x00000000d2000000|  0%| F|  |TAMS 0x00000000d1f00000| PB 0x00000000d1f00000| Untracked 
| 800|0x00000000d2000000, 0x00000000d2000000, 0x00000000d2100000|  0%| F|  |TAMS 0x00000000d2000000| PB 0x00000000d2000000| Untracked 
| 801|0x00000000d2100000, 0x00000000d2100000, 0x00000000d2200000|  0%| F|  |TAMS 0x00000000d2100000| PB 0x00000000d2100000| Untracked 
| 802|0x00000000d2200000, 0x00000000d2200000, 0x00000000d2300000|  0%| F|  |TAMS 0x00000000d2200000| PB 0x00000000d2200000| Untracked 
| 803|0x00000000d2300000, 0x00000000d2300000, 0x00000000d2400000|  0%| F|  |TAMS 0x00000000d2300000| PB 0x00000000d2300000| Untracked 
| 804|0x00000000d2400000, 0x00000000d2400000, 0x00000000d2500000|  0%| F|  |TAMS 0x00000000d2400000| PB 0x00000000d2400000| Untracked 
| 805|0x00000000d2500000, 0x00000000d2500000, 0x00000000d2600000|  0%| F|  |TAMS 0x00000000d2500000| PB 0x00000000d2500000| Untracked 
| 806|0x00000000d2600000, 0x00000000d2600000, 0x00000000d2700000|  0%| F|  |TAMS 0x00000000d2600000| PB 0x00000000d2600000| Untracked 
| 807|0x00000000d2700000, 0x00000000d2700000, 0x00000000d2800000|  0%| F|  |TAMS 0x00000000d2700000| PB 0x00000000d2700000| Untracked 
| 808|0x00000000d2800000, 0x00000000d2800000, 0x00000000d2900000|  0%| F|  |TAMS 0x00000000d2800000| PB 0x00000000d2800000| Untracked 
| 809|0x00000000d2900000, 0x00000000d2900000, 0x00000000d2a00000|  0%| F|  |TAMS 0x00000000d2900000| PB 0x00000000d2900000| Untracked 
| 810|0x00000000d2a00000, 0x00000000d2a00000, 0x00000000d2b00000|  0%| F|  |TAMS 0x00000000d2a00000| PB 0x00000000d2a00000| Untracked 
| 811|0x00000000d2b00000, 0x00000000d2b00000, 0x00000000d2c00000|  0%| F|  |TAMS 0x00000000d2b00000| PB 0x00000000d2b00000| Untracked 
| 812|0x00000000d2c00000, 0x00000000d2c00000, 0x00000000d2d00000|  0%| F|  |TAMS 0x00000000d2c00000| PB 0x00000000d2c00000| Untracked 
| 813|0x00000000d2d00000, 0x00000000d2d00000, 0x00000000d2e00000|  0%| F|  |TAMS 0x00000000d2d00000| PB 0x00000000d2d00000| Untracked 
| 814|0x00000000d2e00000, 0x00000000d2e00000, 0x00000000d2f00000|  0%| F|  |TAMS 0x00000000d2e00000| PB 0x00000000d2e00000| Untracked 
| 815|0x00000000d2f00000, 0x00000000d2f00000, 0x00000000d3000000|  0%| F|  |TAMS 0x00000000d2f00000| PB 0x00000000d2f00000| Untracked 
| 816|0x00000000d3000000, 0x00000000d3000000, 0x00000000d3100000|  0%| F|  |TAMS 0x00000000d3000000| PB 0x00000000d3000000| Untracked 
| 817|0x00000000d3100000, 0x00000000d3100000, 0x00000000d3200000|  0%| F|  |TAMS 0x00000000d3100000| PB 0x00000000d3100000| Untracked 
| 818|0x00000000d3200000, 0x00000000d3200000, 0x00000000d3300000|  0%| F|  |TAMS 0x00000000d3200000| PB 0x00000000d3200000| Untracked 
| 819|0x00000000d3300000, 0x00000000d3300000, 0x00000000d3400000|  0%| F|  |TAMS 0x00000000d3300000| PB 0x00000000d3300000| Untracked 
| 820|0x00000000d3400000, 0x00000000d3400000, 0x00000000d3500000|  0%| F|  |TAMS 0x00000000d3400000| PB 0x00000000d3400000| Untracked 
| 821|0x00000000d3500000, 0x00000000d3500000, 0x00000000d3600000|  0%| F|  |TAMS 0x00000000d3500000| PB 0x00000000d3500000| Untracked 
| 822|0x00000000d3600000, 0x00000000d3600000, 0x00000000d3700000|  0%| F|  |TAMS 0x00000000d3600000| PB 0x00000000d3600000| Untracked 
| 823|0x00000000d3700000, 0x00000000d3700000, 0x00000000d3800000|  0%| F|  |TAMS 0x00000000d3700000| PB 0x00000000d3700000| Untracked 
| 824|0x00000000d3800000, 0x00000000d3800000, 0x00000000d3900000|  0%| F|  |TAMS 0x00000000d3800000| PB 0x00000000d3800000| Untracked 
| 825|0x00000000d3900000, 0x00000000d3900000, 0x00000000d3a00000|  0%| F|  |TAMS 0x00000000d3900000| PB 0x00000000d3900000| Untracked 
| 826|0x00000000d3a00000, 0x00000000d3a00000, 0x00000000d3b00000|  0%| F|  |TAMS 0x00000000d3a00000| PB 0x00000000d3a00000| Untracked 
| 827|0x00000000d3b00000, 0x00000000d3b00000, 0x00000000d3c00000|  0%| F|  |TAMS 0x00000000d3b00000| PB 0x00000000d3b00000| Untracked 
| 828|0x00000000d3c00000, 0x00000000d3c00000, 0x00000000d3d00000|  0%| F|  |TAMS 0x00000000d3c00000| PB 0x00000000d3c00000| Untracked 
| 829|0x00000000d3d00000, 0x00000000d3d00000, 0x00000000d3e00000|  0%| F|  |TAMS 0x00000000d3d00000| PB 0x00000000d3d00000| Untracked 
| 830|0x00000000d3e00000, 0x00000000d3e00000, 0x00000000d3f00000|  0%| F|  |TAMS 0x00000000d3e00000| PB 0x00000000d3e00000| Untracked 
| 831|0x00000000d3f00000, 0x00000000d3f00000, 0x00000000d4000000|  0%| F|  |TAMS 0x00000000d3f00000| PB 0x00000000d3f00000| Untracked 
| 832|0x00000000d4000000, 0x00000000d4000000, 0x00000000d4100000|  0%| F|  |TAMS 0x00000000d4000000| PB 0x00000000d4000000| Untracked 
| 833|0x00000000d4100000, 0x00000000d4100000, 0x00000000d4200000|  0%| F|  |TAMS 0x00000000d4100000| PB 0x00000000d4100000| Untracked 
| 834|0x00000000d4200000, 0x00000000d4200000, 0x00000000d4300000|  0%| F|  |TAMS 0x00000000d4200000| PB 0x00000000d4200000| Untracked 
| 835|0x00000000d4300000, 0x00000000d4300000, 0x00000000d4400000|  0%| F|  |TAMS 0x00000000d4300000| PB 0x00000000d4300000| Untracked 
| 836|0x00000000d4400000, 0x00000000d4400000, 0x00000000d4500000|  0%| F|  |TAMS 0x00000000d4400000| PB 0x00000000d4400000| Untracked 
| 837|0x00000000d4500000, 0x00000000d4500000, 0x00000000d4600000|  0%| F|  |TAMS 0x00000000d4500000| PB 0x00000000d4500000| Untracked 
| 838|0x00000000d4600000, 0x00000000d4600000, 0x00000000d4700000|  0%| F|  |TAMS 0x00000000d4600000| PB 0x00000000d4600000| Untracked 
| 839|0x00000000d4700000, 0x00000000d4700000, 0x00000000d4800000|  0%| F|  |TAMS 0x00000000d4700000| PB 0x00000000d4700000| Untracked 
| 840|0x00000000d4800000, 0x00000000d4800000, 0x00000000d4900000|  0%| F|  |TAMS 0x00000000d4800000| PB 0x00000000d4800000| Untracked 
| 841|0x00000000d4900000, 0x00000000d4900000, 0x00000000d4a00000|  0%| F|  |TAMS 0x00000000d4900000| PB 0x00000000d4900000| Untracked 
| 842|0x00000000d4a00000, 0x00000000d4a00000, 0x00000000d4b00000|  0%| F|  |TAMS 0x00000000d4a00000| PB 0x00000000d4a00000| Untracked 
| 843|0x00000000d4b00000, 0x00000000d4b00000, 0x00000000d4c00000|  0%| F|  |TAMS 0x00000000d4b00000| PB 0x00000000d4b00000| Untracked 
| 844|0x00000000d4c00000, 0x00000000d4c00000, 0x00000000d4d00000|  0%| F|  |TAMS 0x00000000d4c00000| PB 0x00000000d4c00000| Untracked 
| 845|0x00000000d4d00000, 0x00000000d4d00000, 0x00000000d4e00000|  0%| F|  |TAMS 0x00000000d4d00000| PB 0x00000000d4d00000| Untracked 
| 846|0x00000000d4e00000, 0x00000000d4e00000, 0x00000000d4f00000|  0%| F|  |TAMS 0x00000000d4e00000| PB 0x00000000d4e00000| Untracked 
| 847|0x00000000d4f00000, 0x00000000d4f00000, 0x00000000d5000000|  0%| F|  |TAMS 0x00000000d4f00000| PB 0x00000000d4f00000| Untracked 
| 848|0x00000000d5000000, 0x00000000d5000000, 0x00000000d5100000|  0%| F|  |TAMS 0x00000000d5000000| PB 0x00000000d5000000| Untracked 
| 849|0x00000000d5100000, 0x00000000d5100000, 0x00000000d5200000|  0%| F|  |TAMS 0x00000000d5100000| PB 0x00000000d5100000| Untracked 
| 850|0x00000000d5200000, 0x00000000d5200000, 0x00000000d5300000|  0%| F|  |TAMS 0x00000000d5200000| PB 0x00000000d5200000| Untracked 
| 851|0x00000000d5300000, 0x00000000d5300000, 0x00000000d5400000|  0%| F|  |TAMS 0x00000000d5300000| PB 0x00000000d5300000| Untracked 
| 852|0x00000000d5400000, 0x00000000d5400000, 0x00000000d5500000|  0%| F|  |TAMS 0x00000000d5400000| PB 0x00000000d5400000| Untracked 
| 853|0x00000000d5500000, 0x00000000d5500000, 0x00000000d5600000|  0%| F|  |TAMS 0x00000000d5500000| PB 0x00000000d5500000| Untracked 
| 854|0x00000000d5600000, 0x00000000d5600000, 0x00000000d5700000|  0%| F|  |TAMS 0x00000000d5600000| PB 0x00000000d5600000| Untracked 
| 855|0x00000000d5700000, 0x00000000d5700000, 0x00000000d5800000|  0%| F|  |TAMS 0x00000000d5700000| PB 0x00000000d5700000| Untracked 
| 856|0x00000000d5800000, 0x00000000d5800000, 0x00000000d5900000|  0%| F|  |TAMS 0x00000000d5800000| PB 0x00000000d5800000| Untracked 
| 857|0x00000000d5900000, 0x00000000d5900000, 0x00000000d5a00000|  0%| F|  |TAMS 0x00000000d5900000| PB 0x00000000d5900000| Untracked 
| 858|0x00000000d5a00000, 0x00000000d5a00000, 0x00000000d5b00000|  0%| F|  |TAMS 0x00000000d5a00000| PB 0x00000000d5a00000| Untracked 
| 859|0x00000000d5b00000, 0x00000000d5b00000, 0x00000000d5c00000|  0%| F|  |TAMS 0x00000000d5b00000| PB 0x00000000d5b00000| Untracked 
| 860|0x00000000d5c00000, 0x00000000d5c00000, 0x00000000d5d00000|  0%| F|  |TAMS 0x00000000d5c00000| PB 0x00000000d5c00000| Untracked 
| 861|0x00000000d5d00000, 0x00000000d5d00000, 0x00000000d5e00000|  0%| F|  |TAMS 0x00000000d5d00000| PB 0x00000000d5d00000| Untracked 
| 862|0x00000000d5e00000, 0x00000000d5e00000, 0x00000000d5f00000|  0%| F|  |TAMS 0x00000000d5e00000| PB 0x00000000d5e00000| Untracked 
| 863|0x00000000d5f00000, 0x00000000d5f00000, 0x00000000d6000000|  0%| F|  |TAMS 0x00000000d5f00000| PB 0x00000000d5f00000| Untracked 
| 864|0x00000000d6000000, 0x00000000d6000000, 0x00000000d6100000|  0%| F|  |TAMS 0x00000000d6000000| PB 0x00000000d6000000| Untracked 
| 865|0x00000000d6100000, 0x00000000d6100000, 0x00000000d6200000|  0%| F|  |TAMS 0x00000000d6100000| PB 0x00000000d6100000| Untracked 
| 866|0x00000000d6200000, 0x00000000d6200000, 0x00000000d6300000|  0%| F|  |TAMS 0x00000000d6200000| PB 0x00000000d6200000| Untracked 
| 867|0x00000000d6300000, 0x00000000d6300000, 0x00000000d6400000|  0%| F|  |TAMS 0x00000000d6300000| PB 0x00000000d6300000| Untracked 
| 868|0x00000000d6400000, 0x00000000d6400000, 0x00000000d6500000|  0%| F|  |TAMS 0x00000000d6400000| PB 0x00000000d6400000| Untracked 
| 869|0x00000000d6500000, 0x00000000d6500000, 0x00000000d6600000|  0%| F|  |TAMS 0x00000000d6500000| PB 0x00000000d6500000| Untracked 
| 870|0x00000000d6600000, 0x00000000d6600000, 0x00000000d6700000|  0%| F|  |TAMS 0x00000000d6600000| PB 0x00000000d6600000| Untracked 
| 871|0x00000000d6700000, 0x00000000d6700000, 0x00000000d6800000|  0%| F|  |TAMS 0x00000000d6700000| PB 0x00000000d6700000| Untracked 
| 872|0x00000000d6800000, 0x00000000d6800000, 0x00000000d6900000|  0%| F|  |TAMS 0x00000000d6800000| PB 0x00000000d6800000| Untracked 
| 873|0x00000000d6900000, 0x00000000d6900000, 0x00000000d6a00000|  0%| F|  |TAMS 0x00000000d6900000| PB 0x00000000d6900000| Untracked 
| 874|0x00000000d6a00000, 0x00000000d6a00000, 0x00000000d6b00000|  0%| F|  |TAMS 0x00000000d6a00000| PB 0x00000000d6a00000| Untracked 
| 875|0x00000000d6b00000, 0x00000000d6b00000, 0x00000000d6c00000|  0%| F|  |TAMS 0x00000000d6b00000| PB 0x00000000d6b00000| Untracked 
| 876|0x00000000d6c00000, 0x00000000d6c00000, 0x00000000d6d00000|  0%| F|  |TAMS 0x00000000d6c00000| PB 0x00000000d6c00000| Untracked 
| 877|0x00000000d6d00000, 0x00000000d6d00000, 0x00000000d6e00000|  0%| F|  |TAMS 0x00000000d6d00000| PB 0x00000000d6d00000| Untracked 
| 878|0x00000000d6e00000, 0x00000000d6e00000, 0x00000000d6f00000|  0%| F|  |TAMS 0x00000000d6e00000| PB 0x00000000d6e00000| Untracked 
| 879|0x00000000d6f00000, 0x00000000d6f00000, 0x00000000d7000000|  0%| F|  |TAMS 0x00000000d6f00000| PB 0x00000000d6f00000| Untracked 
| 880|0x00000000d7000000, 0x00000000d7000000, 0x00000000d7100000|  0%| F|  |TAMS 0x00000000d7000000| PB 0x00000000d7000000| Untracked 
| 881|0x00000000d7100000, 0x00000000d7100000, 0x00000000d7200000|  0%| F|  |TAMS 0x00000000d7100000| PB 0x00000000d7100000| Untracked 
| 882|0x00000000d7200000, 0x00000000d7200000, 0x00000000d7300000|  0%| F|  |TAMS 0x00000000d7200000| PB 0x00000000d7200000| Untracked 
| 883|0x00000000d7300000, 0x00000000d7300000, 0x00000000d7400000|  0%| F|  |TAMS 0x00000000d7300000| PB 0x00000000d7300000| Untracked 
| 884|0x00000000d7400000, 0x00000000d7500000, 0x00000000d7500000|100%| E|  |TAMS 0x00000000d7400000| PB 0x00000000d7400000| Complete 
| 885|0x00000000d7500000, 0x00000000d7600000, 0x00000000d7600000|100%| E|CS|TAMS 0x00000000d7500000| PB 0x00000000d7500000| Complete 
| 886|0x00000000d7600000, 0x00000000d7700000, 0x00000000d7700000|100%| E|CS|TAMS 0x00000000d7600000| PB 0x00000000d7600000| Complete 
| 887|0x00000000d7700000, 0x00000000d7800000, 0x00000000d7800000|100%| E|CS|TAMS 0x00000000d7700000| PB 0x00000000d7700000| Complete 
| 888|0x00000000d7800000, 0x00000000d7900000, 0x00000000d7900000|100%| E|CS|TAMS 0x00000000d7800000| PB 0x00000000d7800000| Complete 
| 889|0x00000000d7900000, 0x00000000d7a00000, 0x00000000d7a00000|100%| E|CS|TAMS 0x00000000d7900000| PB 0x00000000d7900000| Complete 
| 890|0x00000000d7a00000, 0x00000000d7b00000, 0x00000000d7b00000|100%| E|CS|TAMS 0x00000000d7a00000| PB 0x00000000d7a00000| Complete 
| 891|0x00000000d7b00000, 0x00000000d7c00000, 0x00000000d7c00000|100%| E|CS|TAMS 0x00000000d7b00000| PB 0x00000000d7b00000| Complete 
| 892|0x00000000d7c00000, 0x00000000d7d00000, 0x00000000d7d00000|100%| E|CS|TAMS 0x00000000d7c00000| PB 0x00000000d7c00000| Complete 
| 893|0x00000000d7d00000, 0x00000000d7e00000, 0x00000000d7e00000|100%| E|CS|TAMS 0x00000000d7d00000| PB 0x00000000d7d00000| Complete 
| 894|0x00000000d7e00000, 0x00000000d7f00000, 0x00000000d7f00000|100%| E|CS|TAMS 0x00000000d7e00000| PB 0x00000000d7e00000| Complete 
| 895|0x00000000d7f00000, 0x00000000d8000000, 0x00000000d8000000|100%| E|CS|TAMS 0x00000000d7f00000| PB 0x00000000d7f00000| Complete 
| 896|0x00000000d8000000, 0x00000000d8100000, 0x00000000d8100000|100%| E|CS|TAMS 0x00000000d8000000| PB 0x00000000d8000000| Complete 
| 897|0x00000000d8100000, 0x00000000d8200000, 0x00000000d8200000|100%| E|CS|TAMS 0x00000000d8100000| PB 0x00000000d8100000| Complete 
| 898|0x00000000d8200000, 0x00000000d8300000, 0x00000000d8300000|100%| E|CS|TAMS 0x00000000d8200000| PB 0x00000000d8200000| Complete 
| 899|0x00000000d8300000, 0x00000000d8400000, 0x00000000d8400000|100%| E|CS|TAMS 0x00000000d8300000| PB 0x00000000d8300000| Complete 
| 900|0x00000000d8400000, 0x00000000d8500000, 0x00000000d8500000|100%| E|CS|TAMS 0x00000000d8400000| PB 0x00000000d8400000| Complete 
| 901|0x00000000d8500000, 0x00000000d8600000, 0x00000000d8600000|100%| E|CS|TAMS 0x00000000d8500000| PB 0x00000000d8500000| Complete 
| 902|0x00000000d8600000, 0x00000000d8700000, 0x00000000d8700000|100%| E|CS|TAMS 0x00000000d8600000| PB 0x00000000d8600000| Complete 
| 903|0x00000000d8700000, 0x00000000d8800000, 0x00000000d8800000|100%| E|CS|TAMS 0x00000000d8700000| PB 0x00000000d8700000| Complete 
| 904|0x00000000d8800000, 0x00000000d8900000, 0x00000000d8900000|100%| E|CS|TAMS 0x00000000d8800000| PB 0x00000000d8800000| Complete 
| 905|0x00000000d8900000, 0x00000000d8a00000, 0x00000000d8a00000|100%| E|CS|TAMS 0x00000000d8900000| PB 0x00000000d8900000| Complete 
| 906|0x00000000d8a00000, 0x00000000d8b00000, 0x00000000d8b00000|100%| E|CS|TAMS 0x00000000d8a00000| PB 0x00000000d8a00000| Complete 
| 907|0x00000000d8b00000, 0x00000000d8c00000, 0x00000000d8c00000|100%| E|CS|TAMS 0x00000000d8b00000| PB 0x00000000d8b00000| Complete 
| 908|0x00000000d8c00000, 0x00000000d8d00000, 0x00000000d8d00000|100%| E|CS|TAMS 0x00000000d8c00000| PB 0x00000000d8c00000| Complete 
| 909|0x00000000d8d00000, 0x00000000d8e00000, 0x00000000d8e00000|100%| E|CS|TAMS 0x00000000d8d00000| PB 0x00000000d8d00000| Complete 
| 910|0x00000000d8e00000, 0x00000000d8f00000, 0x00000000d8f00000|100%| E|CS|TAMS 0x00000000d8e00000| PB 0x00000000d8e00000| Complete 
|1442|0x00000000fa200000, 0x00000000fa300000, 0x00000000fa300000|100%| O|  |TAMS 0x00000000fa300000| PB 0x00000000fa300000| Updating 
|1443|0x00000000fa300000, 0x00000000fa400000, 0x00000000fa400000|100%| O|  |TAMS 0x00000000fa400000| PB 0x00000000fa400000| Updating 
|1444|0x00000000fa400000, 0x00000000fa500000, 0x00000000fa500000|100%| O|  |TAMS 0x00000000fa500000| PB 0x00000000fa500000| Updating 
|1445|0x00000000fa500000, 0x00000000fa600000, 0x00000000fa600000|100%| O|  |TAMS 0x00000000fa600000| PB 0x00000000fa600000| Untracked 
|1446|0x00000000fa600000, 0x00000000fa700000, 0x00000000fa700000|100%| O|  |TAMS 0x00000000fa700000| PB 0x00000000fa700000| Untracked 
|1447|0x00000000fa700000, 0x00000000fa800000, 0x00000000fa800000|100%| O|  |TAMS 0x00000000fa800000| PB 0x00000000fa800000| Untracked 
|1448|0x00000000fa800000, 0x00000000fa900000, 0x00000000fa900000|100%| O|  |TAMS 0x00000000fa900000| PB 0x00000000fa900000| Untracked 
|1449|0x00000000fa900000, 0x00000000faa00000, 0x00000000faa00000|100%| O|  |TAMS 0x00000000faa00000| PB 0x00000000faa00000| Untracked 
|1450|0x00000000faa00000, 0x00000000fab00000, 0x00000000fab00000|100%| O|  |TAMS 0x00000000fab00000| PB 0x00000000fab00000| Untracked 
|1451|0x00000000fab00000, 0x00000000fac00000, 0x00000000fac00000|100%| O|  |TAMS 0x00000000fac00000| PB 0x00000000fac00000| Untracked 
|1452|0x00000000fac00000, 0x00000000fad00000, 0x00000000fad00000|100%| O|  |TAMS 0x00000000fad00000| PB 0x00000000fad00000| Untracked 
|1453|0x00000000fad00000, 0x00000000fae00000, 0x00000000fae00000|100%| O|  |TAMS 0x00000000fae00000| PB 0x00000000fae00000| Untracked 
|1454|0x00000000fae00000, 0x00000000faf00000, 0x00000000faf00000|100%| O|  |TAMS 0x00000000faf00000| PB 0x00000000faf00000| Untracked 
|1455|0x00000000faf00000, 0x00000000fb000000, 0x00000000fb000000|100%| O|  |TAMS 0x00000000fb000000| PB 0x00000000fb000000| Untracked 
|1456|0x00000000fb000000, 0x00000000fb100000, 0x00000000fb100000|100%| O|  |TAMS 0x00000000fb100000| PB 0x00000000fb100000| Untracked 
|1457|0x00000000fb100000, 0x00000000fb200000, 0x00000000fb200000|100%| O|  |TAMS 0x00000000fb200000| PB 0x00000000fb200000| Untracked 
|1458|0x00000000fb200000, 0x00000000fb300000, 0x00000000fb300000|100%| O|  |TAMS 0x00000000fb300000| PB 0x00000000fb300000| Untracked 
|1459|0x00000000fb300000, 0x00000000fb400000, 0x00000000fb400000|100%| O|  |TAMS 0x00000000fb400000| PB 0x00000000fb400000| Untracked 
|1460|0x00000000fb400000, 0x00000000fb500000, 0x00000000fb500000|100%| O|  |TAMS 0x00000000fb500000| PB 0x00000000fb500000| Untracked 
|1461|0x00000000fb500000, 0x00000000fb600000, 0x00000000fb600000|100%| O|  |TAMS 0x00000000fb600000| PB 0x00000000fb600000| Untracked 
|1462|0x00000000fb600000, 0x00000000fb700000, 0x00000000fb700000|100%| O|  |TAMS 0x00000000fb700000| PB 0x00000000fb700000| Untracked 
|1463|0x00000000fb700000, 0x00000000fb800000, 0x00000000fb800000|100%| O|  |TAMS 0x00000000fb800000| PB 0x00000000fb800000| Untracked 
|1464|0x00000000fb800000, 0x00000000fb900000, 0x00000000fb900000|100%| O|  |TAMS 0x00000000fb900000| PB 0x00000000fb900000| Untracked 
|1465|0x00000000fb900000, 0x00000000fba00000, 0x00000000fba00000|100%| O|  |TAMS 0x00000000fba00000| PB 0x00000000fba00000| Untracked 
|1466|0x00000000fba00000, 0x00000000fbb00000, 0x00000000fbb00000|100%| O|  |TAMS 0x00000000fbb00000| PB 0x00000000fbb00000| Untracked 
|1467|0x00000000fbb00000, 0x00000000fbc00000, 0x00000000fbc00000|100%| O|  |TAMS 0x00000000fbc00000| PB 0x00000000fbc00000| Untracked 
|1468|0x00000000fbc00000, 0x00000000fbd00000, 0x00000000fbd00000|100%| O|  |TAMS 0x00000000fbd00000| PB 0x00000000fbd00000| Untracked 
|1469|0x00000000fbd00000, 0x00000000fbe00000, 0x00000000fbe00000|100%| O|  |TAMS 0x00000000fbe00000| PB 0x00000000fbe00000| Untracked 
|1470|0x00000000fbe00000, 0x00000000fbf00000, 0x00000000fbf00000|100%| O|  |TAMS 0x00000000fbf00000| PB 0x00000000fbf00000| Untracked 
|1471|0x00000000fbf00000, 0x00000000fc000000, 0x00000000fc000000|100%| O|  |TAMS 0x00000000fc000000| PB 0x00000000fc000000| Untracked 
|1472|0x00000000fc000000, 0x00000000fc100000, 0x00000000fc100000|100%| O|  |TAMS 0x00000000fc100000| PB 0x00000000fc100000| Untracked 
|1473|0x00000000fc100000, 0x00000000fc200000, 0x00000000fc200000|100%| O|  |TAMS 0x00000000fc200000| PB 0x00000000fc200000| Updating 
|1474|0x00000000fc200000, 0x00000000fc300000, 0x00000000fc300000|100%| O|  |TAMS 0x00000000fc300000| PB 0x00000000fc300000| Updating 
|1475|0x00000000fc300000, 0x00000000fc400000, 0x00000000fc400000|100%| O|  |TAMS 0x00000000fc400000| PB 0x00000000fc400000| Updating 
|1476|0x00000000fc400000, 0x00000000fc500000, 0x00000000fc500000|100%| O|  |TAMS 0x00000000fc500000| PB 0x00000000fc500000| Untracked 
|1477|0x00000000fc500000, 0x00000000fc600000, 0x00000000fc600000|100%| O|  |TAMS 0x00000000fc600000| PB 0x00000000fc600000| Untracked 
|1478|0x00000000fc600000, 0x00000000fc700000, 0x00000000fc700000|100%| O|  |TAMS 0x00000000fc700000| PB 0x00000000fc700000| Updating 
|1479|0x00000000fc700000, 0x00000000fc800000, 0x00000000fc800000|100%| O|  |TAMS 0x00000000fc800000| PB 0x00000000fc800000| Untracked 
|1480|0x00000000fc800000, 0x00000000fc900000, 0x00000000fc900000|100%| O|  |TAMS 0x00000000fc900000| PB 0x00000000fc900000| Untracked 
|1481|0x00000000fc900000, 0x00000000fca00000, 0x00000000fca00000|100%| O|  |TAMS 0x00000000fca00000| PB 0x00000000fca00000| Untracked 
|1482|0x00000000fca00000, 0x00000000fcb00000, 0x00000000fcb00000|100%| O|  |TAMS 0x00000000fcb00000| PB 0x00000000fcb00000| Untracked 
|1483|0x00000000fcb00000, 0x00000000fcc00000, 0x00000000fcc00000|100%| O|  |TAMS 0x00000000fcc00000| PB 0x00000000fcc00000| Updating 
|1484|0x00000000fcc00000, 0x00000000fcd00000, 0x00000000fcd00000|100%| O|  |TAMS 0x00000000fcd00000| PB 0x00000000fcd00000| Updating 
|1485|0x00000000fcd00000, 0x00000000fce00000, 0x00000000fce00000|100%| O|  |TAMS 0x00000000fce00000| PB 0x00000000fce00000| Updating 
|1486|0x00000000fce00000, 0x00000000fcf00000, 0x00000000fcf00000|100%| O|  |TAMS 0x00000000fcf00000| PB 0x00000000fcf00000| Updating 
|1487|0x00000000fcf00000, 0x00000000fd000000, 0x00000000fd000000|100%| O|  |TAMS 0x00000000fd000000| PB 0x00000000fd000000| Updating 
|1488|0x00000000fd000000, 0x00000000fd100000, 0x00000000fd100000|100%| O|  |TAMS 0x00000000fd100000| PB 0x00000000fd100000| Untracked 
|1489|0x00000000fd100000, 0x00000000fd200000, 0x00000000fd200000|100%| O|  |TAMS 0x00000000fd200000| PB 0x00000000fd200000| Updating 
|1490|0x00000000fd200000, 0x00000000fd300000, 0x00000000fd300000|100%| O|  |TAMS 0x00000000fd300000| PB 0x00000000fd300000| Updating 
|1491|0x00000000fd300000, 0x00000000fd400000, 0x00000000fd400000|100%| O|  |TAMS 0x00000000fd400000| PB 0x00000000fd400000| Updating 
|1492|0x00000000fd400000, 0x00000000fd500000, 0x00000000fd500000|100%| O|  |TAMS 0x00000000fd500000| PB 0x00000000fd500000| Untracked 
|1493|0x00000000fd500000, 0x00000000fd600000, 0x00000000fd600000|100%| E|CS|TAMS 0x00000000fd500000| PB 0x00000000fd500000| Complete 
|1494|0x00000000fd600000, 0x00000000fd700000, 0x00000000fd700000|100%| E|CS|TAMS 0x00000000fd600000| PB 0x00000000fd600000| Complete 
|1495|0x00000000fd700000, 0x00000000fd800000, 0x00000000fd800000|100%| E|CS|TAMS 0x00000000fd700000| PB 0x00000000fd700000| Complete 
|1496|0x00000000fd800000, 0x00000000fd900000, 0x00000000fd900000|100%| E|CS|TAMS 0x00000000fd800000| PB 0x00000000fd800000| Complete 
|1497|0x00000000fd900000, 0x00000000fda00000, 0x00000000fda00000|100%| E|CS|TAMS 0x00000000fd900000| PB 0x00000000fd900000| Complete 
|1498|0x00000000fda00000, 0x00000000fdb00000, 0x00000000fdb00000|100%| E|CS|TAMS 0x00000000fda00000| PB 0x00000000fda00000| Complete 
|1499|0x00000000fdb00000, 0x00000000fdc00000, 0x00000000fdc00000|100%| E|CS|TAMS 0x00000000fdb00000| PB 0x00000000fdb00000| Complete 
|1500|0x00000000fdc00000, 0x00000000fdd00000, 0x00000000fdd00000|100%| E|CS|TAMS 0x00000000fdc00000| PB 0x00000000fdc00000| Complete 
|1501|0x00000000fdd00000, 0x00000000fde00000, 0x00000000fde00000|100%| E|CS|TAMS 0x00000000fdd00000| PB 0x00000000fdd00000| Complete 
|1502|0x00000000fde00000, 0x00000000fdf00000, 0x00000000fdf00000|100%| E|CS|TAMS 0x00000000fde00000| PB 0x00000000fde00000| Complete 
|1503|0x00000000fdf00000, 0x00000000fe000000, 0x00000000fe000000|100%| E|CS|TAMS 0x00000000fdf00000| PB 0x00000000fdf00000| Complete 
|1504|0x00000000fe000000, 0x00000000fe100000, 0x00000000fe100000|100%| E|CS|TAMS 0x00000000fe000000| PB 0x00000000fe000000| Complete 
|1505|0x00000000fe100000, 0x00000000fe200000, 0x00000000fe200000|100%| E|CS|TAMS 0x00000000fe100000| PB 0x00000000fe100000| Complete 
|1506|0x00000000fe200000, 0x00000000fe300000, 0x00000000fe300000|100%| E|CS|TAMS 0x00000000fe200000| PB 0x00000000fe200000| Complete 
|1507|0x00000000fe300000, 0x00000000fe400000, 0x00000000fe400000|100%| E|CS|TAMS 0x00000000fe300000| PB 0x00000000fe300000| Complete 
|1508|0x00000000fe400000, 0x00000000fe500000, 0x00000000fe500000|100%| E|CS|TAMS 0x00000000fe400000| PB 0x00000000fe400000| Complete 
|1509|0x00000000fe500000, 0x00000000fe600000, 0x00000000fe600000|100%| E|CS|TAMS 0x00000000fe500000| PB 0x00000000fe500000| Complete 
|1510|0x00000000fe600000, 0x00000000fe700000, 0x00000000fe700000|100%| E|CS|TAMS 0x00000000fe600000| PB 0x00000000fe600000| Complete 
|1511|0x00000000fe700000, 0x00000000fe800000, 0x00000000fe800000|100%| E|CS|TAMS 0x00000000fe700000| PB 0x00000000fe700000| Complete 
|1512|0x00000000fe800000, 0x00000000fe900000, 0x00000000fe900000|100%| E|CS|TAMS 0x00000000fe800000| PB 0x00000000fe800000| Complete 
|1513|0x00000000fe900000, 0x00000000fea00000, 0x00000000fea00000|100%| E|CS|TAMS 0x00000000fe900000| PB 0x00000000fe900000| Complete 
|1514|0x00000000fea00000, 0x00000000feb00000, 0x00000000feb00000|100%| E|CS|TAMS 0x00000000fea00000| PB 0x00000000fea00000| Complete 
|1515|0x00000000feb00000, 0x00000000fec00000, 0x00000000fec00000|100%| E|CS|TAMS 0x00000000feb00000| PB 0x00000000feb00000| Complete 
|1516|0x00000000fec00000, 0x00000000fed00000, 0x00000000fed00000|100%| E|CS|TAMS 0x00000000fec00000| PB 0x00000000fec00000| Complete 
|1517|0x00000000fed00000, 0x00000000fee00000, 0x00000000fee00000|100%| E|CS|TAMS 0x00000000fed00000| PB 0x00000000fed00000| Complete 
|1518|0x00000000fee00000, 0x00000000fef00000, 0x00000000fef00000|100%| E|CS|TAMS 0x00000000fee00000| PB 0x00000000fee00000| Complete 
|1519|0x00000000fef00000, 0x00000000ff000000, 0x00000000ff000000|100%| E|CS|TAMS 0x00000000fef00000| PB 0x00000000fef00000| Complete 
|1520|0x00000000ff000000, 0x00000000ff100000, 0x00000000ff100000|100%| E|CS|TAMS 0x00000000ff000000| PB 0x00000000ff000000| Complete 
|1521|0x00000000ff100000, 0x00000000ff200000, 0x00000000ff200000|100%| E|CS|TAMS 0x00000000ff100000| PB 0x00000000ff100000| Complete 
|1522|0x00000000ff200000, 0x00000000ff300000, 0x00000000ff300000|100%| E|CS|TAMS 0x00000000ff200000| PB 0x00000000ff200000| Complete 
|1523|0x00000000ff300000, 0x00000000ff400000, 0x00000000ff400000|100%| E|CS|TAMS 0x00000000ff300000| PB 0x00000000ff300000| Complete 
|1524|0x00000000ff400000, 0x00000000ff500000, 0x00000000ff500000|100%| E|CS|TAMS 0x00000000ff400000| PB 0x00000000ff400000| Complete 
|1525|0x00000000ff500000, 0x00000000ff600000, 0x00000000ff600000|100%| E|CS|TAMS 0x00000000ff500000| PB 0x00000000ff500000| Complete 
|1526|0x00000000ff600000, 0x00000000ff700000, 0x00000000ff700000|100%| E|CS|TAMS 0x00000000ff600000| PB 0x00000000ff600000| Complete 
|1527|0x00000000ff700000, 0x00000000ff800000, 0x00000000ff800000|100%| E|CS|TAMS 0x00000000ff700000| PB 0x00000000ff700000| Complete 
|1528|0x00000000ff800000, 0x00000000ff900000, 0x00000000ff900000|100%| E|CS|TAMS 0x00000000ff800000| PB 0x00000000ff800000| Complete 
|1529|0x00000000ff900000, 0x00000000ffa00000, 0x00000000ffa00000|100%| E|CS|TAMS 0x00000000ff900000| PB 0x00000000ff900000| Complete 
|1530|0x00000000ffa00000, 0x00000000ffb00000, 0x00000000ffb00000|100%| E|CS|TAMS 0x00000000ffa00000| PB 0x00000000ffa00000| Complete 
|1531|0x00000000ffb00000, 0x00000000ffc00000, 0x00000000ffc00000|100%| E|CS|TAMS 0x00000000ffb00000| PB 0x00000000ffb00000| Complete 
|1532|0x00000000ffc00000, 0x00000000ffd00000, 0x00000000ffd00000|100%| E|CS|TAMS 0x00000000ffc00000| PB 0x00000000ffc00000| Complete 
|1533|0x00000000ffd00000, 0x00000000ffe00000, 0x00000000ffe00000|100%| O|  |TAMS 0x00000000ffe00000| PB 0x00000000ffe00000| Updating 
|1534|0x00000000ffe00000, 0x00000000fff00000, 0x00000000fff00000|100%| E|CS|TAMS 0x00000000ffe00000| PB 0x00000000ffe00000| Complete 
|1535|0x00000000fff00000, 0x0000000100000000, 0x0000000100000000|100%| E|CS|TAMS 0x00000000fff00000| PB 0x00000000fff00000| Complete 

Card table byte_map: [0x00000294b8a60000,0x00000294b8d60000] _byte_map_base: 0x00000294b8560000

Marking Bits: (CMBitMap*) 0x00000294a664acb0
 Bits: [0x00000294b8d60000, 0x00000294ba560000)

Polling page: 0x00000294a45b0000

Metaspace:

Usage:
  Non-class:    185.64 MB used.
      Class:     27.40 MB used.
       Both:    213.04 MB used.

Virtual space:
  Non-class space:      192.00 MB reserved,     187.38 MB ( 98%) committed,  3 nodes.
      Class space:      416.00 MB reserved,      29.06 MB (  7%) committed,  1 nodes.
             Both:      608.00 MB reserved,     216.44 MB ( 36%) committed. 

Chunk freelists:
   Non-Class:  4.63 MB
       Class:  2.98 MB
        Both:  7.61 MB

MaxMetaspaceSize: 512.00 MB
CompressedClassSpaceSize: 416.00 MB
Initial GC threshold: 21.00 MB
Current GC threshold: 359.94 MB
CDS: off
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 6.
num_arena_births: 5950.
num_arena_deaths: 40.
num_vsnodes_births: 4.
num_vsnodes_deaths: 0.
num_space_committed: 3479.
num_space_uncommitted: 12.
num_chunks_returned_to_freelist: 160.
num_chunks_taken_from_freelist: 15789.
num_chunk_merges: 41.
num_chunk_splits: 10248.
num_chunks_enlarged: 6633.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=20653Kb max_used=28276Kb free=99347Kb
 bounds [0x00000294b1230000, 0x00000294b2f40000, 0x00000294b8760000]
CodeHeap 'profiled nmethods': size=120000Kb used=16814Kb max_used=61232Kb free=103186Kb
 bounds [0x00000294a9760000, 0x00000294ad330000, 0x00000294b0c90000]
CodeHeap 'non-nmethods': size=5760Kb used=3207Kb max_used=3299Kb free=2552Kb
 bounds [0x00000294b0c90000, 0x00000294b0fe0000, 0x00000294b1230000]
 total_blobs=13424 nmethods=12195 adapters=1130
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 479.118 Thread 0x00000294c1079250 nmethod 57518 0x00000294b1e77590 code [0x00000294b1e77740, 0x00000294b1e77998]
Event: 479.129 Thread 0x00000294c1099cf0 57519       3       com.android.tools.r8.internal.Cx::<init> (51 bytes)
Event: 479.130 Thread 0x00000294c1099cf0 nmethod 57519 0x00000294aa4fc390 code [0x00000294aa4fc540, 0x00000294aa4fc800]
Event: 479.130 Thread 0x00000294c1099cf0 57520       3       com.android.tools.r8.internal.Cx::a (559 bytes)
Event: 479.132 Thread 0x00000294c1079250 57521       4       com.android.tools.r8.graph.F0::u (11 bytes)
Event: 479.132 Thread 0x00000294c1099cf0 nmethod 57520 0x00000294a9d3c310 code [0x00000294a9d3c840, 0x00000294a9d3f1e8]
Event: 479.133 Thread 0x00000294c1099cf0 57522       3       com.android.tools.r8.graph.F5::getHolder (34 bytes)
Event: 479.133 Thread 0x00000294c1099cf0 nmethod 57522 0x00000294aa4fbf10 code [0x00000294aa4fc0c0, 0x00000294aa4fc240]
Event: 479.133 Thread 0x00000294c1079250 nmethod 57521 0x00000294b1b2f810 code [0x00000294b1b2f9a0, 0x00000294b1b2fa70]
Event: 479.136 Thread 0x00000294c1079250 57523       4       com.android.tools.r8.internal.J20::get (104 bytes)
Event: 479.142 Thread 0x00000294c1099cf0 57526       3       com.android.tools.r8.graph.F5::getHolder (5 bytes)
Event: 479.143 Thread 0x00000294c1099cf0 nmethod 57526 0x00000294aa4fba10 code [0x00000294aa4fbbc0, 0x00000294aa4fbda0]
Event: 479.149 Thread 0x00000294c1079250 nmethod 57523 0x00000294b2197d10 code [0x00000294b2197f40, 0x00000294b21988f0]
Event: 479.149 Thread 0x00000294c1079250 57525       4       com.android.tools.r8.graph.C0::a (55 bytes)
Event: 479.158 Thread 0x00000294c1079250 nmethod 57525 0x00000294b22f5b10 code [0x00000294b22f5d60, 0x00000294b22f6430]
Event: 479.158 Thread 0x00000294c1079250 57524       4       com.android.tools.r8.graph.t3::a (15 bytes)
Event: 479.165 Thread 0x00000294c1079250 nmethod 57524 0x00000294b2badf10 code [0x00000294b2bae0e0, 0x00000294b2bae5d0]
Event: 479.176 Thread 0x00000294c1079250 57527       4       com.android.tools.r8.dex.n0::a (60 bytes)
Event: 479.182 Thread 0x00000294c1079250 nmethod 57527 0x00000294b1994d10 code [0x00000294b1994ee0, 0x00000294b19953a0]
Event: 479.183 Thread 0x00000294c1079250 57528       4       com.android.tools.r8.graph.I2::a (285 bytes)

GC Heap History (20 events):
Event: 462.426 GC heap before
{Heap before GC invocations=169 (full 0):
 garbage-first heap   total 929792K, used 866420K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 449 young (459776K), 15 survivors (15360K)
 Metaspace       used 215458K, committed 218880K, reserved 622592K
  class space    used 27731K, committed 29376K, reserved 425984K
}
Event: 462.445 GC heap after
{Heap after GC invocations=170 (full 0):
 garbage-first heap   total 929792K, used 413028K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 17 young (17408K), 17 survivors (17408K)
 Metaspace       used 215458K, committed 218880K, reserved 622592K
  class space    used 27731K, committed 29376K, reserved 425984K
}
Event: 463.107 GC heap before
{Heap before GC invocations=170 (full 0):
 garbage-first heap   total 929792K, used 864612K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 458 young (468992K), 17 survivors (17408K)
 Metaspace       used 215464K, committed 218880K, reserved 622592K
  class space    used 27731K, committed 29376K, reserved 425984K
}
Event: 463.128 GC heap after
{Heap after GC invocations=171 (full 0):
 garbage-first heap   total 929792K, used 414767K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 18 young (18432K), 18 survivors (18432K)
 Metaspace       used 215464K, committed 218880K, reserved 622592K
  class space    used 27731K, committed 29376K, reserved 425984K
}
Event: 464.077 GC heap before
{Heap before GC invocations=171 (full 0):
 garbage-first heap   total 929792K, used 863279K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 456 young (466944K), 18 survivors (18432K)
 Metaspace       used 215471K, committed 218880K, reserved 622592K
  class space    used 27731K, committed 29376K, reserved 425984K
}
Event: 464.098 GC heap after
{Heap after GC invocations=172 (full 0):
 garbage-first heap   total 929792K, used 420243K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 22 young (22528K), 22 survivors (22528K)
 Metaspace       used 215471K, committed 218880K, reserved 622592K
  class space    used 27731K, committed 29376K, reserved 425984K
}
Event: 468.811 GC heap before
{Heap before GC invocations=173 (full 0):
 garbage-first heap   total 929792K, used 928147K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 406 young (415744K), 22 survivors (22528K)
 Metaspace       used 217141K, committed 220544K, reserved 622592K
  class space    used 27955K, committed 29568K, reserved 425984K
}
Event: 468.852 GC heap after
{Heap after GC invocations=174 (full 0):
 garbage-first heap   total 977920K, used 506904K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 46 young (47104K), 46 survivors (47104K)
 Metaspace       used 217141K, committed 220544K, reserved 622592K
  class space    used 27955K, committed 29568K, reserved 425984K
}
Event: 472.044 GC heap before
{Heap before GC invocations=174 (full 0):
 garbage-first heap   total 977920K, used 887832K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 410 young (419840K), 46 survivors (47104K)
 Metaspace       used 217245K, committed 220608K, reserved 622592K
  class space    used 27961K, committed 29568K, reserved 425984K
}
Event: 472.150 GC heap after
{Heap after GC invocations=175 (full 0):
 garbage-first heap   total 997376K, used 560640K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 52 young (53248K), 52 survivors (53248K)
 Metaspace       used 217245K, committed 220608K, reserved 622592K
  class space    used 27961K, committed 29568K, reserved 425984K
}
Event: 473.705 GC heap before
{Heap before GC invocations=175 (full 0):
 garbage-first heap   total 997376K, used 891392K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 376 young (385024K), 52 survivors (53248K)
 Metaspace       used 217741K, committed 221120K, reserved 622592K
  class space    used 28011K, committed 29632K, reserved 425984K
}
Event: 473.831 GC heap after
{Heap after GC invocations=176 (full 0):
 garbage-first heap   total 1029120K, used 643584K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 47 young (48128K), 47 survivors (48128K)
 Metaspace       used 217741K, committed 221120K, reserved 622592K
  class space    used 28011K, committed 29632K, reserved 425984K
}
Event: 476.538 GC heap before
{Heap before GC invocations=177 (full 0):
 garbage-first heap   total 1029120K, used 875008K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 302 young (309248K), 47 survivors (48128K)
 Metaspace       used 217957K, committed 221376K, reserved 622592K
  class space    used 28041K, committed 29696K, reserved 425984K
}
Event: 476.647 GC heap after
{Heap after GC invocations=178 (full 0):
 garbage-first heap   total 1029120K, used 698368K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 38 young (38912K), 38 survivors (38912K)
 Metaspace       used 217957K, committed 221376K, reserved 622592K
  class space    used 28041K, committed 29696K, reserved 425984K
}
Event: 477.923 GC heap before
{Heap before GC invocations=178 (full 0):
 garbage-first heap   total 1029120K, used 904192K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 236 young (241664K), 38 survivors (38912K)
 Metaspace       used 218122K, committed 221568K, reserved 622592K
  class space    used 28054K, committed 29760K, reserved 425984K
}
Event: 477.992 GC heap after
{Heap after GC invocations=179 (full 0):
 garbage-first heap   total 1029120K, used 703488K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 19 young (19456K), 19 survivors (19456K)
 Metaspace       used 218122K, committed 221568K, reserved 622592K
  class space    used 28054K, committed 29760K, reserved 425984K
}
Event: 478.017 GC heap before
{Heap before GC invocations=179 (full 0):
 garbage-first heap   total 1029120K, used 716800K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 33 young (33792K), 19 survivors (19456K)
 Metaspace       used 218122K, committed 221568K, reserved 622592K
  class space    used 28054K, committed 29760K, reserved 425984K
}
Event: 478.034 GC heap after
{Heap after GC invocations=180 (full 0):
 garbage-first heap   total 1029120K, used 705536K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 2 young (2048K), 2 survivors (2048K)
 Metaspace       used 218122K, committed 221568K, reserved 622592K
  class space    used 28054K, committed 29760K, reserved 425984K
}
Event: 478.935 GC heap before
{Heap before GC invocations=180 (full 0):
 garbage-first heap   total 1029120K, used 922624K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 211 young (216064K), 2 survivors (2048K)
 Metaspace       used 218151K, committed 221632K, reserved 622592K
  class space    used 28058K, committed 29760K, reserved 425984K
}
Event: 478.952 GC heap after
{Heap after GC invocations=181 (full 0):
 garbage-first heap   total 1029120K, used 718848K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 12 young (12288K), 12 survivors (12288K)
 Metaspace       used 218151K, committed 221632K, reserved 622592K
  class space    used 28058K, committed 29760K, reserved 425984K
}

Dll operation events (3 events):
Event: 0.013 Loaded shared library C:\Program Files\Android\Android Studio\jbr\bin\java.dll
Event: 0.018 Loaded shared library C:\Program Files\Android\Android Studio\jbr\bin\zip.dll
Event: 0.714 Loaded shared library C:\Program Files\Android\Android Studio\jbr\bin\verify.dll

Deoptimization events (20 events):
Event: 478.852 Thread 0x00000294d5271e80 DEOPT PACKING pc=0x00000294a976caa5 sp=0x000000e349cfd760
Event: 478.852 Thread 0x00000294d5271e80 DEOPT UNPACKING pc=0x00000294b0ce4e42 sp=0x000000e349cfcc50 mode 0
Event: 478.870 Thread 0x00000294d5271e80 DEOPT PACKING pc=0x00000294a976caa5 sp=0x000000e349cfd760
Event: 478.870 Thread 0x00000294d5271e80 DEOPT UNPACKING pc=0x00000294b0ce4e42 sp=0x000000e349cfcc50 mode 0
Event: 479.132 Thread 0x00000294c23adb70 Uncommon trap: trap_request=0xffffffbe fr.pc=0x00000294b15f07a4 relative=0x00000000000007e4
Event: 479.132 Thread 0x00000294c23adb70 Uncommon trap: reason=profile_predicate action=maybe_recompile pc=0x00000294b15f07a4 method=com.android.tools.r8.internal.J20.get(Ljava/lang/Object;)Ljava/lang/Object; @ 83 c2
Event: 479.132 Thread 0x00000294c23adb70 DEOPT PACKING pc=0x00000294b15f07a4 sp=0x000000e34bcf95d0
Event: 479.132 Thread 0x00000294c23adb70 DEOPT UNPACKING pc=0x00000294b0ce46a2 sp=0x000000e34bcf9568 mode 2
Event: 479.132 Thread 0x00000294c23adb70 Uncommon trap: trap_request=0xffffffbe fr.pc=0x00000294b15f07a4 relative=0x00000000000007e4
Event: 479.132 Thread 0x00000294c23adb70 Uncommon trap: reason=profile_predicate action=maybe_recompile pc=0x00000294b15f07a4 method=com.android.tools.r8.internal.J20.get(Ljava/lang/Object;)Ljava/lang/Object; @ 83 c2
Event: 479.132 Thread 0x00000294c23adb70 DEOPT PACKING pc=0x00000294b15f07a4 sp=0x000000e34bcf9560
Event: 479.132 Thread 0x00000294c23adb70 DEOPT UNPACKING pc=0x00000294b0ce46a2 sp=0x000000e34bcf94f8 mode 2
Event: 479.135 Thread 0x00000294c23adb70 Uncommon trap: trap_request=0xffffffbe fr.pc=0x00000294b15f07a4 relative=0x00000000000007e4
Event: 479.135 Thread 0x00000294c23adb70 Uncommon trap: reason=profile_predicate action=maybe_recompile pc=0x00000294b15f07a4 method=com.android.tools.r8.internal.J20.get(Ljava/lang/Object;)Ljava/lang/Object; @ 83 c2
Event: 479.135 Thread 0x00000294c23adb70 DEOPT PACKING pc=0x00000294b15f07a4 sp=0x000000e34bcf95d0
Event: 479.135 Thread 0x00000294c23adb70 DEOPT UNPACKING pc=0x00000294b0ce46a2 sp=0x000000e34bcf9568 mode 2
Event: 479.135 Thread 0x00000294c23adb70 Uncommon trap: trap_request=0xffffffbe fr.pc=0x00000294b15f07a4 relative=0x00000000000007e4
Event: 479.135 Thread 0x00000294c23adb70 Uncommon trap: reason=profile_predicate action=maybe_recompile pc=0x00000294b15f07a4 method=com.android.tools.r8.internal.J20.get(Ljava/lang/Object;)Ljava/lang/Object; @ 83 c2
Event: 479.135 Thread 0x00000294c23adb70 DEOPT PACKING pc=0x00000294b15f07a4 sp=0x000000e34bcf9560
Event: 479.135 Thread 0x00000294c23adb70 DEOPT UNPACKING pc=0x00000294b0ce46a2 sp=0x000000e34bcf94f8 mode 2

Classes loaded (20 events):
Event: 467.214 Loading class sun/nio/cs/US_ASCII$Decoder
Event: 467.222 Loading class sun/nio/cs/US_ASCII$Decoder done
Event: 468.447 Loading class java/nio/BufferUnderflowException
Event: 468.449 Loading class java/nio/BufferUnderflowException done
Event: 472.191 Loading class java/util/concurrent/ForkJoinTask$AdaptedInterruptibleCallable
Event: 472.197 Loading class java/util/concurrent/ForkJoinTask$InterruptibleTask
Event: 472.205 Loading class java/util/concurrent/ForkJoinTask$InterruptibleTask done
Event: 472.205 Loading class java/util/concurrent/ForkJoinTask$AdaptedInterruptibleCallable done
Event: 472.308 Loading class java/nio/HeapShortBuffer
Event: 472.310 Loading class java/nio/HeapShortBuffer done
Event: 475.889 Loading class sun/nio/cs/US_ASCII$Encoder
Event: 475.891 Loading class sun/nio/cs/US_ASCII$Encoder done
Event: 475.891 Loading class sun/nio/cs/Surrogate$Parser
Event: 475.893 Loading class sun/nio/cs/Surrogate$Parser done
Event: 475.893 Loading class sun/nio/cs/Surrogate
Event: 475.894 Loading class sun/nio/cs/Surrogate done
Event: 475.995 Loading class java/util/TreeMap$AscendingSubMap
Event: 475.998 Loading class java/util/TreeMap$AscendingSubMap done
Event: 475.998 Loading class java/util/TreeMap$NavigableSubMap$SubMapKeyIterator
Event: 475.999 Loading class java/util/TreeMap$NavigableSubMap$SubMapKeyIterator done

Classes unloaded (20 events):
Event: 434.083 Thread 0x00000294bca74d40 Unloading class 0x00000001019f27c0 'jdk/internal/jimage/ImageReader$Node'
Event: 434.083 Thread 0x00000294bca74d40 Unloading class 0x00000001019f25b0 'jdk/internal/jimage/ImageStrings'
Event: 434.083 Thread 0x00000294bca74d40 Unloading class 0x00000001019f22d8 'jdk/internal/jimage/ImageReader$SharedImageReader'
Event: 434.083 Thread 0x00000294bca74d40 Unloading class 0x00000001019f2000 'jdk/internal/jimage/BasicImageReader'
Event: 434.083 Thread 0x00000294bca74d40 Unloading class 0x00000001019f1db0 'jdk/internal/jimage/ImageReader'
Event: 434.083 Thread 0x00000294bca74d40 Unloading class 0x00000001019f1b70 'jdk/internal/jrtfs/SystemImage$2'
Event: 434.083 Thread 0x00000294bca74d40 Unloading class 0x00000001019f1940 'jdk/internal/jrtfs/SystemImage$$Lambda+0x00000001019f1940'
Event: 434.083 Thread 0x00000294bca74d40 Unloading class 0x00000001019f16f8 'jdk/internal/jrtfs/ExplodedImage'
Event: 434.083 Thread 0x00000294bca74d40 Unloading class 0x00000001019f14a8 'jdk/internal/jrtfs/SystemImage$1'
Event: 434.083 Thread 0x00000294bca74d40 Unloading class 0x00000001019f1278 'jdk/internal/jrtfs/SystemImage'
Event: 434.083 Thread 0x00000294bca74d40 Unloading class 0x00000001019f1000 'jdk/internal/jrtfs/JrtFileStore'
Event: 434.083 Thread 0x00000294bca74d40 Unloading class 0x00000001019f0400 'jdk/internal/jrtfs/JrtPath'
Event: 434.083 Thread 0x00000294bca74d40 Unloading class 0x00000001019f0b08 'jdk/internal/jrtfs/JrtFileSystem'
Event: 434.083 Thread 0x00000294bca74d40 Unloading class 0x00000001019f0800 'jdk/internal/jrtfs/JrtFileSystemProvider'
Event: 437.512 Thread 0x00000294bca74d40 Unloading class 0x0000000101c09c00 'java/lang/invoke/LambdaForm$MH+0x0000000101c09c00'
Event: 437.512 Thread 0x00000294bca74d40 Unloading class 0x0000000101c09000 'java/lang/invoke/LambdaForm$DMH+0x0000000101c09000'
Event: 437.512 Thread 0x00000294bca74d40 Unloading class 0x0000000101c08800 'java/lang/invoke/LambdaForm$DMH+0x0000000101c08800'
Event: 437.512 Thread 0x00000294bca74d40 Unloading class 0x0000000101c08000 'java/lang/invoke/LambdaForm$DMH+0x0000000101c08000'
Event: 437.512 Thread 0x00000294bca74d40 Unloading class 0x0000000101b3c000 'java/lang/invoke/LambdaForm$DMH+0x0000000101b3c000'
Event: 437.512 Thread 0x00000294bca74d40 Unloading class 0x0000000101b3c800 'java/lang/invoke/LambdaForm$DMH+0x0000000101b3c800'

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 478.475 Thread 0x00000294cf4f25b0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d55045c0}> (0x00000000d55045c0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 478.477 Thread 0x00000294cf4f25b0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5587248}> (0x00000000d5587248) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 478.478 Thread 0x00000294cf4f25b0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5484bf8}> (0x00000000d5484bf8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 478.480 Thread 0x00000294cf4f25b0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5487748}> (0x00000000d5487748) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 478.481 Thread 0x00000294cf4f25b0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d548a050}> (0x00000000d548a050) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 478.482 Thread 0x00000294cf4f25b0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5301c80}> (0x00000000d5301c80) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 478.516 Thread 0x00000294cf4f25b0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d53052e8}> (0x00000000d53052e8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 478.518 Thread 0x00000294cf4f25b0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d4b83888}> (0x00000000d4b83888) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 478.554 Thread 0x00000294cf4f25b0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d4b861c0}> (0x00000000d4b861c0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 478.571 Thread 0x00000294cf4f25b0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d4203f80}> (0x00000000d4203f80) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 478.578 Thread 0x00000294cf4f25b0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d41038d8}> (0x00000000d41038d8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 478.579 Thread 0x00000294cf4f25b0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d41070b0}> (0x00000000d41070b0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 478.580 Thread 0x00000294cf4f25b0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d410a3c8}> (0x00000000d410a3c8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 478.581 Thread 0x00000294cf4f25b0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d410d4d8}> (0x00000000d410d4d8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 478.582 Thread 0x00000294cf4f25b0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d41101a0}> (0x00000000d41101a0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 478.583 Thread 0x00000294cf4f25b0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d4113280}> (0x00000000d4113280) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 478.584 Thread 0x00000294cf4f25b0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d41965f8}> (0x00000000d41965f8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 478.585 Thread 0x00000294cf4f25b0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d4199968}> (0x00000000d4199968) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 478.586 Thread 0x00000294cf4f25b0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d419c9e0}> (0x00000000d419c9e0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 478.666 Thread 0x00000294c23adb70 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d36787f0}> (0x00000000d36787f0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 474.226 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 475.033 Executing VM operation: G1PauseRemark
Event: 475.227 Executing VM operation: G1PauseRemark done
Event: 475.792 Executing VM operation: G1PauseCleanup
Event: 475.803 Executing VM operation: G1PauseCleanup done
Event: 475.906 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 475.993 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 476.001 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 476.006 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 476.533 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause)
Event: 476.647 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause) done
Event: 477.259 Executing VM operation: ICBufferFull
Event: 477.259 Executing VM operation: ICBufferFull done
Event: 477.923 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause)
Event: 477.992 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause) done
Event: 478.017 Executing VM operation: G1TryInitiateConcMark (G1 Humongous Allocation)
Event: 478.034 Executing VM operation: G1TryInitiateConcMark (G1 Humongous Allocation) done
Event: 478.934 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause)
Event: 478.952 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause) done
Event: 479.192 Executing VM operation: G1PauseRemark

Memory protections (0 events):
No events

Nmethod flushes (20 events):
Event: 479.213 Thread 0x00000294bca74d40 flushing  nmethod 0x00000294ad0ae390
Event: 479.213 Thread 0x00000294bca74d40 flushing  nmethod 0x00000294ad0aea90
Event: 479.213 Thread 0x00000294bca74d40 flushing  nmethod 0x00000294ad0be910
Event: 479.213 Thread 0x00000294bca74d40 flushing  nmethod 0x00000294ad0daf90
Event: 479.213 Thread 0x00000294bca74d40 flushing  nmethod 0x00000294ad17ef10
Event: 479.213 Thread 0x00000294bca74d40 flushing  nmethod 0x00000294ad18c310
Event: 479.213 Thread 0x00000294bca74d40 flushing  nmethod 0x00000294ad18f010
Event: 479.213 Thread 0x00000294bca74d40 flushing  nmethod 0x00000294ad18f410
Event: 479.213 Thread 0x00000294bca74d40 flushing  nmethod 0x00000294ad190210
Event: 479.213 Thread 0x00000294bca74d40 flushing  nmethod 0x00000294ad1f6b10
Event: 479.213 Thread 0x00000294bca74d40 flushing  nmethod 0x00000294ad1f7390
Event: 479.213 Thread 0x00000294bca74d40 flushing  nmethod 0x00000294ad1f7d10
Event: 479.213 Thread 0x00000294bca74d40 flushing  nmethod 0x00000294ad1f8390
Event: 479.213 Thread 0x00000294bca74d40 flushing  nmethod 0x00000294ad27bb10
Event: 479.213 Thread 0x00000294bca74d40 flushing  nmethod 0x00000294ad2b5510
Event: 479.213 Thread 0x00000294bca74d40 flushing  nmethod 0x00000294ad2b5e90
Event: 479.213 Thread 0x00000294bca74d40 flushing  nmethod 0x00000294ad2b6690
Event: 479.213 Thread 0x00000294bca74d40 flushing  nmethod 0x00000294ad2c1190
Event: 479.213 Thread 0x00000294bca74d40 flushing  nmethod 0x00000294ad2c1590
Event: 479.213 Thread 0x00000294bca74d40 flushing  nmethod 0x00000294ad2c2190

Events (20 events):
Event: 431.752 Thread 0x00000294cffa8fa0 Thread exited: 0x00000294cffa8fa0
Event: 437.340 Thread 0x00000294d52645f0 Thread exited: 0x00000294d52645f0
Event: 449.251 Thread 0x00000294d52666c0 Thread exited: 0x00000294d52666c0
Event: 455.944 Thread 0x00000294cfa85460 Thread exited: 0x00000294cfa85460
Event: 457.490 Thread 0x00000294c1099cf0 Thread added: 0x00000294c38f70d0
Event: 457.775 Thread 0x00000294c38f70d0 Thread exited: 0x00000294c38f70d0
Event: 457.937 Thread 0x00000294c1079250 Thread added: 0x00000294d550cf90
Event: 459.623 Thread 0x00000294d550cf90 Thread exited: 0x00000294d550cf90
Event: 464.517 Thread 0x00000294c1099cf0 Thread added: 0x00000294d550bb20
Event: 464.714 Thread 0x00000294cf4f25b0 Thread added: 0x00000294d526d650
Event: 465.643 Thread 0x00000294d550bb20 Thread exited: 0x00000294d550bb20
Event: 467.042 Thread 0x00000294c60c5680 Thread exited: 0x00000294c60c5680
Event: 467.574 Thread 0x00000294cecaeed0 Thread added: 0x00000294d5271e80
Event: 470.548 Thread 0x00000294c1099cf0 Thread added: 0x00000294cf90b440
Event: 471.450 Thread 0x00000294c60c2200 Thread exited: 0x00000294c60c2200
Event: 472.206 Thread 0x00000294d5271e80 Thread added: 0x00000294d52673e0
Event: 472.208 Thread 0x00000294d5271e80 Thread added: 0x00000294d5266030
Event: 472.208 Thread 0x00000294d5271e80 Thread added: 0x00000294d52694b0
Event: 472.287 Thread 0x00000294d52673e0 Thread added: 0x00000294d5269b40
Event: 474.234 Thread 0x00000294d52659a0 Thread exited: 0x00000294d52659a0


Dynamic libraries:
0x00007ff7b0e10000 - 0x00007ff7b0e1a000 	C:\Program Files\Android\Android Studio\jbr\bin\java.exe
0x00007ffef0c10000 - 0x00007ffef0e08000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ffef09c0000 - 0x00007ffef0a82000 	C:\Windows\System32\KERNEL32.DLL
0x00007ffeee760000 - 0x00007ffeeea56000 	C:\Windows\System32\KERNELBASE.dll
0x00007ffeee320000 - 0x00007ffeee420000 	C:\Windows\System32\ucrtbase.dll
0x00007ffecc820000 - 0x00007ffecc838000 	C:\Program Files\Android\Android Studio\jbr\bin\jli.dll
0x00007ffeef0b0000 - 0x00007ffeef24d000 	C:\Windows\System32\USER32.dll
0x00007ffeee580000 - 0x00007ffeee5a2000 	C:\Windows\System32\win32u.dll
0x00007ffef0930000 - 0x00007ffef095b000 	C:\Windows\System32\GDI32.dll
0x00007ffeee5b0000 - 0x00007ffeee6c9000 	C:\Windows\System32\gdi32full.dll
0x00007ffeeea90000 - 0x00007ffeeeb2d000 	C:\Windows\System32\msvcp_win.dll
0x00007ffeca2b0000 - 0x00007ffeca2cb000 	C:\Program Files\Android\Android Studio\jbr\bin\VCRUNTIME140.dll
0x00007ffee0760000 - 0x00007ffee09fa000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7\COMCTL32.dll
0x00007ffeef5f0000 - 0x00007ffeef68e000 	C:\Windows\System32\msvcrt.dll
0x00007ffeeedc0000 - 0x00007ffeeedef000 	C:\Windows\System32\IMM32.DLL
0x00007ffede140000 - 0x00007ffede14c000 	C:\Program Files\Android\Android Studio\jbr\bin\vcruntime140_1.dll
0x00007ffec0510000 - 0x00007ffec059d000 	C:\Program Files\Android\Android Studio\jbr\bin\msvcp140.dll
0x00007ffea3830000 - 0x00007ffea44ba000 	C:\Program Files\Android\Android Studio\jbr\bin\server\jvm.dll
0x00007ffeef530000 - 0x00007ffeef5e1000 	C:\Windows\System32\ADVAPI32.dll
0x00007ffeef010000 - 0x00007ffeef0af000 	C:\Windows\System32\sechost.dll
0x00007ffeeeee0000 - 0x00007ffeef003000 	C:\Windows\System32\RPCRT4.dll
0x00007ffeeea60000 - 0x00007ffeeea87000 	C:\Windows\System32\bcrypt.dll
0x00007ffeef4c0000 - 0x00007ffeef52b000 	C:\Windows\System32\WS2_32.dll
0x00007ffeee100000 - 0x00007ffeee14b000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ffee2be0000 - 0x00007ffee2c07000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ffee5700000 - 0x00007ffee570a000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ffeee0e0000 - 0x00007ffeee0f2000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ffeec100000 - 0x00007ffeec112000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ffedd730000 - 0x00007ffedd73a000 	C:\Program Files\Android\Android Studio\jbr\bin\jimage.dll
0x00007ffedfc10000 - 0x00007ffedfe11000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ffec4fb0000 - 0x00007ffec4fe4000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ffeee6d0000 - 0x00007ffeee752000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ffec20c0000 - 0x00007ffec20ce000 	C:\Program Files\Android\Android Studio\jbr\bin\instrument.dll
0x00007ffeca290000 - 0x00007ffeca2b0000 	C:\Program Files\Android\Android Studio\jbr\bin\java.dll
0x00007ffeca210000 - 0x00007ffeca228000 	C:\Program Files\Android\Android Studio\jbr\bin\zip.dll
0x00007ffef00d0000 - 0x00007ffef083e000 	C:\Windows\System32\SHELL32.dll
0x00007ffeec300000 - 0x00007ffeecaa4000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ffeefd70000 - 0x00007ffef00c3000 	C:\Windows\System32\combase.dll
0x00007ffeedb20000 - 0x00007ffeedb4b000 	C:\Windows\SYSTEM32\Wldp.dll
0x00007ffeeedf0000 - 0x00007ffeeeebd000 	C:\Windows\System32\OLEAUT32.dll
0x00007ffeeec30000 - 0x00007ffeeecdd000 	C:\Windows\System32\SHCORE.dll
0x00007ffeeece0000 - 0x00007ffeeed3b000 	C:\Windows\System32\shlwapi.dll
0x00007ffeee1d0000 - 0x00007ffeee1f4000 	C:\Windows\SYSTEM32\profapi.dll
0x00007ffedbb90000 - 0x00007ffedbba0000 	C:\Program Files\Android\Android Studio\jbr\bin\net.dll
0x00007ffee77d0000 - 0x00007ffee78da000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007ffeed900000 - 0x00007ffeed96a000 	C:\Windows\system32\mswsock.dll
0x00007ffec9a30000 - 0x00007ffec9a46000 	C:\Program Files\Android\Android Studio\jbr\bin\nio.dll
0x00007ffedb850000 - 0x00007ffedb860000 	C:\Program Files\Android\Android Studio\jbr\bin\verify.dll
0x00007ffebf790000 - 0x00007ffebf7b7000 	C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64\native-platform.dll
0x00007ffeb6720000 - 0x00007ffeb6798000 	C:\Users\<USER>\.gradle\native\0.2.7\x86_64-windows-gnu\gradle-fileevents.dll
0x00007ffed0d90000 - 0x00007ffed0d99000 	C:\Program Files\Android\Android Studio\jbr\bin\management.dll
0x00007ffecd120000 - 0x00007ffecd12b000 	C:\Program Files\Android\Android Studio\jbr\bin\management_ext.dll
0x00007ffef0920000 - 0x00007ffef0928000 	C:\Windows\System32\PSAPI.DLL
0x00007ffeed600000 - 0x00007ffeed63b000 	C:\Windows\SYSTEM32\IPHLPAPI.DLL
0x00007ffeef8e0000 - 0x00007ffeef8e8000 	C:\Windows\System32\NSI.dll
0x00007ffecd050000 - 0x00007ffecd059000 	C:\Program Files\Android\Android Studio\jbr\bin\extnet.dll
0x00007ffeedeb0000 - 0x00007ffeedec8000 	C:\Windows\SYSTEM32\CRYPTSP.dll
0x00007ffeed240000 - 0x00007ffeed278000 	C:\Windows\system32\rsaenh.dll
0x00007ffeee150000 - 0x00007ffeee17e000 	C:\Windows\SYSTEM32\USERENV.dll
0x00007ffeeda90000 - 0x00007ffeeda9c000 	C:\Windows\SYSTEM32\CRYPTBASE.dll
0x00007ffec1a90000 - 0x00007ffec1a97000 	C:\Windows\system32\wshunix.dll
0x00007ffeed640000 - 0x00007ffeed70a000 	C:\Windows\SYSTEM32\DNSAPI.dll
0x00007ffee1060000 - 0x00007ffee106a000 	C:\Windows\System32\rasadhlp.dll
0x00007ffee70c0000 - 0x00007ffee7140000 	C:\Windows\System32\fwpuclnt.dll
0x00007ffeed490000 - 0x00007ffeed4c3000 	C:\Windows\SYSTEM32\ntmarta.dll
0x00007ffecca80000 - 0x00007ffecca87000 	C:\Program Files\Android\Android Studio\jbr\bin\rmi.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Android\Android Studio\jbr\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7;C:\Program Files\Android\Android Studio\jbr\bin\server;C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64;C:\Users\<USER>\.gradle\native\0.2.7\x86_64-windows-gnu

VM Arguments:
jvm_args: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -XX:MaxMetaspaceSize=512m -XX:+HeapDumpOnOutOfMemoryError -Xmx1536m -Dfile.encoding=UTF-8 -Duser.country=BR -Duser.language=pt -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.14.1-bin\baw1sv0jfoi8rxs14qo3h49cs\gradle-8.14.1\lib\agents\gradle-instrumentation-agent-8.14.1.jar 
java_command: org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.14.1
java_class_path (initial): C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.14.1-bin\baw1sv0jfoi8rxs14qo3h49cs\gradle-8.14.1\lib\gradle-daemon-main-8.14.1.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 3                                         {product} {ergonomic}
   size_t CompressedClassSpaceSize                 = 436207616                                 {product} {ergonomic}
     uint ConcGCThreads                            = 1                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 4                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 1048576                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
     bool HeapDumpOnOutOfMemoryError               = true                                   {manageable} {command line}
   size_t InitialHeapSize                          = 134217728                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 1610612736                                {product} {command line}
   size_t MaxMetaspaceSize                         = 536870912                                 {product} {command line}
   size_t MaxNewSize                               = 965738496                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 1048576                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5832780                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122912730                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122912730                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 1610612736                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=C:\Program Files\Java\jdk-11.0.10
PATH=F:\PROJETO\React\EscolaSabatina\node_modules\.bin;F:\PROJETO\React\EscolaSabatina\node_modules\.bin;F:\PROJETO\React\node_modules\.bin;F:\PROJETO\node_modules\.bin;F:\node_modules\.bin;C:\Users\<USER>\AppData\Roaming\nvm\v20.10.0\node_modules\npm\node_modules\@npmcli\run-script\lib\node-gyp-bin;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\Microsoft SQL Server\130\Tools\Binn\;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn\;C:\Program Files\dotnet\;C:\Program Files (x86)\Microsoft SQL Server\150\DTS\Binn\;C:\Program Files\Azure Data Studio\bin;C:\Program Files\Git\cmd;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\110\Tools\Binn\;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\130\Tools\Binn\;C:\Program Files (x86)\Microsoft SQL Server\140\Tools\Binn\;C:\Program Files\Microsoft SQL Server\140\Tools\Binn\;C:\Program Files\Microsoft SQL Server\140\DTS\Binn\;C:\Program Files\Microsoft\Web Platform Installer\;C:\Program Files (x86)\dotnet\;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;C:\Program Files\Calibre2\;C:\Program Files\Java\jdk-11\bin;C:\Prog;C:\Program Files\Docker\Docker\resources\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\.local\share\solana\install\active_release\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\;C:\ProgramData\DockerDesktop\version-bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\Users\ADM\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\.dotnet\tools;C:\MinGW\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin
USERNAME=ADM
LANG=en_US.UTF-8
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 58 Stepping 9, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

JNI global refs:
JNI global refs: 29, weak refs: 0

JNI global refs memory usage: 835, weak refs: 833

Process memory usage:
Resident Set Size: 1281680K (15% of 8357708K total physical memory with 390556K free physical memory)

OOME stack traces (most recent first):
Classloader memory used:
Loader org.gradle.internal.classloader.VisitableURLClassLoader$InstrumentingVisitableURLClassLoader: 78590K
Loader org.gradle.internal.classloader.VisitableURLClassLoader                         : 63405K
Loader bootstrap                                                                       : 39458K
Loader org.gradle.initialization.MixInLegacyTypesClassLoader                           : 16083K
Loader jdk.internal.loader.ClassLoaders$AppClassLoader                                 : 10649K
Loader java.net.URLClassLoader                                                         : 5061K
Loader org.gradle.groovy.scripts.internal.DefaultScriptCompilationHandler$ScriptClassLoader: 2168K
Loader jdk.internal.loader.ClassLoaders$PlatformClassLoader                            : 1113K
Loader jdk.internal.reflect.DelegatingClassLoader                                      : 923K
Loader org.gradle.internal.classloader.VisitableURLClassLoader                         : 392K
Loader org.codehaus.groovy.runtime.callsite.CallSiteClassLoader                        : 305K
Loader sun.reflect.misc.MethodUtil                                                     : 2952B

Classes loaded by more than one classloader:
Class Program                                                                         : loaded 12 times (x 68B)
Class Build_gradle$1                                                                  : loaded 10 times (x 74B)
Class Build_gradle                                                                    : loaded 10 times (x 126B)
Class org.gradle.kotlin.dsl.VersionCatalogAccessorsKt                                 : loaded 5 times (x 67B)
Class Build_gradle$4                                                                  : loaded 4 times (x 70B)
Class Build_gradle$6                                                                  : loaded 4 times (x 70B)
Class Build_gradle$2                                                                  : loaded 4 times (x 75B)
Class org.gradle.kotlin.dsl.Accessors96b3ii45gitqpy1kb3tvcvtxvKt                      : loaded 4 times (x 67B)
Class org.gradle.kotlin.dsl.ImplementationConfigurationAccessorsKt                    : loaded 4 times (x 67B)
Class org.gradle.kotlin.dsl.Accessors9osmdt78klrrxidc9srcw3tsqKt                      : loaded 4 times (x 67B)
Class Build_gradle$3                                                                  : loaded 4 times (x 70B)
Class Build_gradle$5                                                                  : loaded 4 times (x 70B)
Class [Lorg.jetbrains.kotlin.build.report.metrics.GradleBuildTime;                    : loaded 3 times (x 65B)
Class org.jetbrains.kotlin.build.report.metrics.BuildTime                             : loaded 3 times (x 66B)
Class org.jetbrains.kotlin.build.report.metrics.GradleBuildTime$Companion             : loaded 3 times (x 67B)
Class org.jetbrains.kotlin.build.report.metrics.BuildMetricsReporter                  : loaded 3 times (x 66B)
Class org.gradle.kotlin.dsl.TestImplementationConfigurationAccessorsKt                : loaded 3 times (x 67B)
Class [Lorg.jetbrains.kotlin.build.report.metrics.BuildTime;                          : loaded 3 times (x 65B)
Class org.jetbrains.kotlin.build.report.metrics.GradleBuildTime                       : loaded 3 times (x 82B)
Class kotlin.sequences.SequencesKt__SequenceBuilderKt                                 : loaded 2 times (x 67B)
Class kotlin.collections.CollectionsKt__ReversedViewsKt                               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinVariantWithCoordinates             : loaded 2 times (x 104B)
Class org.jetbrains.kotlin.gradle.tasks.CInteropProcess                               : loaded 2 times (x 343B)
Class org.jetbrains.kotlin.gradle.internal.KotlinDomApiDependencyManagementKt         : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycleImpl$launch$coroutine$1 : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.NpmDependencyExtensionKt$defaultNpmDependencyDelegate$1: loaded 2 times (x 163B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.MissingNativeStdlibChecker: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.targets.ConfigureNonPackedKlibConsumingSideEffectKt : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.native.KotilnNativeConfigureBinariesSideEffectKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPlatformType$DisambiguationRule        : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPlatformType$Companion                 : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.internal.ConfigurationCacheStartParameterAccessorKt: loaded 2 times (x 67B)
Class com.google.common.cache.LocalCache$ValueIterator                                : loaded 2 times (x 82B)
Class [Lcom.google.common.collect.MapMakerInternalMap$Segment;                        : loaded 2 times (x 65B)
Class com.google.common.io.FileWriteMode                                              : loaded 2 times (x 75B)
Class [Lcom.google.common.base.AbstractIterator$State;                                : loaded 2 times (x 65B)
Class com.google.common.base.CharMatcher$IsEither                                     : loaded 2 times (x 107B)
Class org.gradle.internal.classloader.ClassLoaderHierarchy                            : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.hierarchy.KotlinSourceSetTreeClassifierKt    : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.report.UsesBuildMetricsService                      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory$KotlinCompilationSourceSetsContainerFactory: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.utils.ConfigurationCacheOpaqueValueSource           : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.utils.MutableObservableSet                          : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinHierarchyDsl                              : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.targets.metadata.KotlinMetadataTargetConfiguratorKt$isNativeSourceSet$2: loaded 2 times (x 91B)
Class org.jetbrains.kotlin.gradle.targets.native.KonanPropertiesBuildService$Companion$registerIfAbsent$2$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.report.BuildReportType                              : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.statistics.metrics.StringAnonymizationPolicy$AllowedListAnonymizer: loaded 2 times (x 75B)
Class com.google.common.collect.AbstractMapBasedMultimap$WrappedCollection            : loaded 2 times (x 120B)
Class com.google.common.collect.ImmutableEnumSet                                      : loaded 2 times (x 142B)
Class com.google.common.util.concurrent.ExecutionError                                : loaded 2 times (x 78B)
Class com.google.common.collect.ImmutableList$1                                       : loaded 2 times (x 93B)
Class org.gradle.api.internal.classpath.ManifestUtil                                  : loaded 2 times (x 67B)
Class io.invertase.gradle.common.WithExtensions                                       : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.utils.FutureKt$future$1                             : loaded 2 times (x 91B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$androidLayoutResources$1        : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.report.GradleBuildMetricsReporter                   : loaded 2 times (x 94B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinMetadataCompilation                : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.sources.KotlinDependencyScope                : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig$1   : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationFriendPathsResolver: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$ANDROID_ARM64                     : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinNativeHostSpecificMetadataArtifactKt$KotlinNativeHostSpecificMetadataArtifact$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinLegacyMetadataArtifactKt$KotlinLegacyMetadataArtifact$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationSideEffect$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinUsages$KotlinMetadataDisambiguation: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.FlowActionBuildFusService$Companion$registerIfAbsentImpl$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$IntGradleProperty: loaded 2 times (x 72B)
Class com.google.common.io.ByteArrayDataOutput                                        : loaded 2 times (x 66B)
Class com.google.common.collect.RegularImmutableMap                                   : loaded 2 times (x 117B)
Class com.google.common.collect.ImmutableMap$MapViewOfValuesAsSingletonSets           : loaded 2 times (x 121B)
Class com.google.gson.stream.MalformedJsonException                                   : loaded 2 times (x 78B)
Class io.invertase.gradle.common.WithExtensions$getName                               : loaded 2 times (x 148B)
Class io.invertase.gradle.build.ProjectExtension                                      : loaded 2 times (x 91B)
Class org.jetbrains.kotlin.statistics.AnonymizerUtilsKt$salt$2                        : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.utils.GradleAttributesContainerUtilsKt$setAttributeProvider$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.internal.StdlibDependencyManagementKt$addStdlibDependency$1$1$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.AbstractKotlinCompile$compilerRunner$1        : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.CacheableTasksKt$cacheOnlyIfEnabledForKotlin$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.internal.transforms.BuildToolsApiClasspathEntrySnapshotTransform: loaded 2 times (x 78B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig$2   : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory$PreConfigure: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinProjectSetupActionKt$KotlinProjectSetupCoroutine$1$1: loaded 2 times (x 91B)
Class org.jetbrains.kotlin.gradle.utils.NativeCompilerDownloader$Companion$DEFAULT_KONAN_VERSION$2: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$WATCHOS_ARM32                     : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.targets.js.dsl.KotlinJsTargetDsl                    : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycleImplKt                  : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.DeprecatedKotlinNativeTargetsChecker$runChecks$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycleImpl$WhenMappings       : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinCommonCompilerToolOptionsHelper           : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinTargetComponent                        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradlePluginExtensionPointInternal     : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.statistics.metrics.StringOverridePolicy$OVERRIDE_VERSION_IF_NOT_SET: loaded 2 times (x 81B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinBuildStatsBeanService$Companion$initStatsService$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactories$Companion$getProvider$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$BooleanGradleProperty: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinProjectExtension                          : loaded 2 times (x 118B)
Class [Lorg.objectweb.asm.Symbol;                                                     : loaded 2 times (x 65B)
Class org.objectweb.asm.ClassVisitor                                                  : loaded 2 times (x 84B)
Class com.google.common.cache.LocalCache$Values                                       : loaded 2 times (x 114B)
Class com.google.common.collect.TransformedListIterator                               : loaded 2 times (x 89B)
Class com.google.common.collect.UnmodifiableListIterator                              : loaded 2 times (x 91B)
Class Build_gradle$2$1$1                                                              : loaded 2 times (x 70B)
Class io.invertase.gradle.build.ReactNativeModule$_closure1                           : loaded 2 times (x 137B)
Class build_86psz0zg5ccmkipscnjtptl6f                                                 : loaded 2 times (x 176B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycle$ProjectConfigurationResult$Failure: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.sources.DefaultKotlinSourceSet$MetadataDependencyTransformation: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$Companion          : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain                    : loaded 2 times (x 73B)
Class org.jetbrains.kotlin.gradle.targets.native.toolchain.KotlinNativeBundleBuildService$Parameters: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.targets.native.toolchain.KotlinNativeBundleBuildService: loaded 2 times (x 73B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleBuildServices                    : loaded 2 times (x 76B)
Class [Lorg.jetbrains.kotlin.statistics.ValueAnonymizer;                              : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$MemoizedCallable$value$2: loaded 2 times (x 75B)
Class org.objectweb.asm.MethodTooLargeException                                       : loaded 2 times (x 79B)
Class com.google.common.collect.MapMakerInternalMap$Strength                          : loaded 2 times (x 76B)
Class com.google.common.base.Platform$JdkPatternCompiler                              : loaded 2 times (x 71B)
Class [Lcom.google.common.cache.CacheBuilder$OneWeigher;                              : loaded 2 times (x 65B)
Class com.google.common.base.Suppliers                                                : loaded 2 times (x 67B)
Class org.gradle.internal.classloader.DefaultClassLoaderFactory                       : loaded 2 times (x 78B)
Class kotlin.sequences.SequencesKt___SequencesKt                                      : loaded 2 times (x 67B)
Class kotlin.collections.AbstractList$Companion                                       : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.PropertiesProvider$kotlinCompilerArgumentsLogLevel$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.compilerRunner.CompilerSystemPropertiesService$Parameters_Decorated: loaded 2 times (x 130B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinTargetWithTests                        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.ToolingDiagnosticRenderingOptions: loaded 2 times (x 68B)
Class [Lorg.jetbrains.kotlin.gradle.dsl.KotlinVersion;                                : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.UsesKotlinToolingDiagnosticsParameters: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.JsEnvironmentChecker    : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.NpmDependencyExtensionKt             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.metadata.KotlinMetadataTargetConfiguratorKt$special$$inlined$extrasStoredFuture$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.PropertiesProvider$classLoaderCacheTimeoutInSeconds$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.report.BuildMetricsService                          : loaded 2 times (x 82B)
Class org.jetbrains.kotlin.statistics.metrics.NumberAnonymizationPolicy               : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.statistics.metrics.StringAnonymizationPolicy               : loaded 2 times (x 74B)
Class com.google.common.io.ByteSource$ConcatenatedByteSource                          : loaded 2 times (x 81B)
Class com.google.common.collect.ReverseNaturalOrdering                                : loaded 2 times (x 110B)
Class [Lcom.google.common.collect.AbstractIterator$State;                             : loaded 2 times (x 65B)
Class com.google.common.base.CharMatcher$None                                         : loaded 2 times (x 108B)
Class org.gradle.internal.classloader.ClassLoaderVisitor                              : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.build.report.metrics.BuildPerformanceMetrics               : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationLanguageSettingsConfigurator: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycleKt$await$1              : loaded 2 times (x 84B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.KotlinCompilation;                         : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinTargetComponentWithPublication     : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCreateNativeCompileTasksSideEffectKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinTargetContainerWithJsPresetFunctions      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinProjectSetupActionKt                   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.report.ReportingSettings$Companion                  : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.report.ConfigureReporingKt                          : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.tooling.core.TypeUtils                                     : loaded 2 times (x 67B)
Class com.google.common.collect.Ordering                                              : loaded 2 times (x 110B)
Class [Lcom.google.common.collect.ImmutableMapEntry;                                  : loaded 2 times (x 65B)
Class org.gradle.api.internal.classpath.EffectiveClassPath                            : loaded 2 times (x 86B)
Class kotlin.text.StringsKt__AppendableKt                                             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.IDaemonReuseCounterMXBean         : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.utils.CompatibilityKt                               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.DefaultKotlinCompilationConfigurationsContainer: loaded 2 times (x 81B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$ANDROID_ARM32                     : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.tasks.TasksProviderKt                               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.ToolingDiagnostic                : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.sources.DefaultKotlinSourceSetFactory        : loaded 2 times (x 76B)
Class com.google.common.collect.CollectSpliterators$1WithCharacteristics              : loaded 2 times (x 86B)
Class com.google.common.collect.ImmutableRangeSet                                     : loaded 2 times (x 111B)
Class com.google.common.collect.ImmutableEnumMap                                      : loaded 2 times (x 121B)
Class com.google.common.collect.ListMultimap                                          : loaded 2 times (x 66B)
Class [Lkotlin.reflect.KCallable;                                                     : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.incremental.ClasspathChanges                               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.buildtools.api.jvm.ClassSnapshotGranularity                : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.ExternalKotlinTargetApi                             : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.AndroidPluginWithoutAndroidTargetChecker$runChecks$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.AndroidSourceSetLayoutV1SourceSetsNotFoundChecker$runChecks$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinGradleProjectCheckersRunnerKt$launchKotlinGradleProjectCheckers$1$1: loaded 2 times (x 91B)
Class org.jetbrains.kotlin.konan.target.KonanTarget                                   : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.targets.CreateDefaultTestRunSideEffectKt            : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.utils.ProviderApiUtilsKt$mapOrNull$1                : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.targets.native.toolchain.KotlinNativeBundleBuildService$Companion$registerIfAbsent$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.statistics.metrics.BooleanAnonymizationPolicy              : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.statistics.metrics.BooleanOverridePolicy$OVERRIDE          : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.gradle.plugin.PropertiesProvider$kotlinExperimentalTryNext$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.fus.BuildUidService$Companion                       : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactoriesConfigurator$Companion$getProvider$1: loaded 2 times (x 70B)
Class org.objectweb.asm.AnnotationVisitor                                             : loaded 2 times (x 74B)
Class com.google.common.collect.ImmutableSet$SerializedForm                           : loaded 2 times (x 69B)
Class com.google.common.collect.ComparatorOrdering                                    : loaded 2 times (x 111B)
Class com.google.common.collect.Iterators$EmptyModifiableIterator                     : loaded 2 times (x 82B)
Class com.google.common.collect.Sets$1                                                : loaded 2 times (x 135B)
Class com.google.common.base.Splitter                                                 : loaded 2 times (x 68B)
Class org.gradle.api.internal.DefaultClassPathProvider                                : loaded 2 times (x 72B)
Class kotlin.sequences.Sequence                                                       : loaded 2 times (x 66B)
Class kotlin.text.StringsKt__StringNumberConversionsJVMKt                             : loaded 2 times (x 67B)
Class build_86psz0zg5ccmkipscnjtptl6f$_run_closure1                                   : loaded 2 times (x 135B)
Class org.jetbrains.kotlin.statistics.metrics.OverrideMetricContainer                 : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$1$3$classpathEntrySnapshotFiles$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.KaptGenerateStubsConfig$Companion$wireJavaAndKotlinOutputs$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycleCoroutineContextElement : loaded 2 times (x 80B)
Class org.jetbrains.kotlin.gradle.dsl.PlatformSourceSetConventionsChecker             : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.KotlinSourceSetTreeDependsOnMismatchChecker: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.targets.CreateDefaultCompilationsSideEffectKt       : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.SyncLanguageSettingsWithKotlinExtensionSetupActionKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.statistics.metrics.NumericalMetrics                        : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactories$Companion$getProvider$2$1: loaded 2 times (x 75B)
Class com.google.common.collect.NaturalOrdering                                       : loaded 2 times (x 111B)
Class com.google.common.collect.MapMakerInternalMap$Strength$1                        : loaded 2 times (x 76B)
Class com.google.common.collect.Maps$ViewCachingAbstractMap                           : loaded 2 times (x 121B)
Class com.google.common.collect.Sets$2                                                : loaded 2 times (x 135B)
Class [Lorg.gradle.api.internal.ClassPathProvider;                                    : loaded 2 times (x 65B)
Class org.gradle.api.internal.ClassPathProvider                                       : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.BuildFinishMetrics$reportBuildFinished$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.ExecutedTaskMetrics               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.CheckKotlinGradlePluginConfigurationErrors$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinJvmCompile$DefaultImpls                 : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.CacheableTasksKt                              : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.konan.target.HostManager$targetValues$2                    : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.UnusedSourceSetsChecker$runChecks$1: loaded 2 times (x 84B)
Class [Lorg.jetbrains.kotlin.konan.target.Family;                                     : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.PeerNpmDependencyExtension           : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.dsl.AndroidMainSourceSetConventionsChecker          : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.WasmJsEnvironmentChecker$1: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.targets.native.internal.SetupKotlinNativePlatformDependenciesAndStdlibKt$SetupKotlinNativeStdlibAndPlatformDependenciesImport$1: loaded 2 times (x 91B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.NpmDependencyExtensionKt$AddNpmDependencyExtensionProjectSetupAction$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.FlowActionBuildFusService         : loaded 2 times (x 80B)
Class org.jetbrains.kotlin.build.report.metrics.GradleBuildPerformanceMetric          : loaded 2 times (x 88B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$NullableBooleanGradleProperty: loaded 2 times (x 72B)
Class [Lorg.objectweb.asm.AnnotationVisitor;                                          : loaded 2 times (x 65B)
Class com.google.common.collect.MapMakerInternalMap$Strength$2                        : loaded 2 times (x 76B)
Class com.google.common.collect.Sets$3                                                : loaded 2 times (x 135B)
Class com.google.common.cache.LocalCache$LocalLoadingCache                            : loaded 2 times (x 130B)
Class com.google.common.base.AbstractIterator$State                                   : loaded 2 times (x 75B)
Class org.gradle.internal.service.UnknownServiceException                             : loaded 2 times (x 79B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$2$1$1$1 : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$ScriptFilterSpec                : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginWrapper                          : loaded 2 times (x 82B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.ComposePluginSuggestApplyChecker$runChecks$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$TVOS_ARM64                        : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.DefaultPeerNpmDependencyExtension    : loaded 2 times (x 137B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycleKt                      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.WasmJsEnvironmentChecker$2: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.AbstractKotlinTarget                     : loaded 2 times (x 152B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesLoader$kotlinNativeVersion$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactoriesConfigurator$Inject: loaded 2 times (x 92B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinSingleTargetExtension                     : loaded 2 times (x 120B)
Class org.objectweb.asm.ModuleVisitor                                                 : loaded 2 times (x 77B)
Class com.google.common.collect.MapMakerInternalMap$StrongValueEntry                  : loaded 2 times (x 66B)
Class com.google.common.io.ByteArrayDataInput                                         : loaded 2 times (x 66B)
Class com.google.common.collect.Sets$4                                                : loaded 2 times (x 135B)
Class com.google.common.cache.LocalCache$Strength$1                                   : loaded 2 times (x 77B)
Class build_6s7imafvclc6gm72hnzimmguy                                                 : loaded 2 times (x 181B)
Class io.invertase.gradle.build.ReactNativeProject                                    : loaded 2 times (x 87B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$2$1$1$2 : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinCompilationsKt$addSourcesToKotlinCompileTask$configureAction$2: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationTaskNamesContainer: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory$KotlinCompilerOptionsFactory: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildServiceKt$propertyWithDeprecatedName$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.NpmDependencyExtension               : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.LanguageSettingsBuilder                      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.WasmWasiEnvironmentChecker: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.WasmJsEnvironmentChecker$3: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.targets.ConfigureBuildSideEffectKt                  : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.BuildEventsListenerRegistryHolder$Companion  : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.BuildFusService$Companion$registerIfAbsentImpl$generalConfigurationMetricsProvider$1: loaded 2 times (x 71B)
Class [Lorg.jetbrains.kotlin.statistics.metrics.StringMetrics;                        : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$Inject   : loaded 2 times (x 92B)
Class com.google.common.util.concurrent.SettableFuture                                : loaded 2 times (x 110B)
Class com.google.common.cache.LocalCache$Strength$2                                   : loaded 2 times (x 77B)
Class kotlin.collections.ArraysKt___ArraysJvmKt                                       : loaded 2 times (x 67B)
Class kotlin.collections.CollectionsKt__CollectionsKt                                 : loaded 2 times (x 67B)
Class kotlin.text.StringsKt__RegexExtensionsJVMKt                                     : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.statistics.fileloggers.MetricsContainer$MetricDescriptor   : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinJavaToolchain$JdkSetter                 : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.utils.ConfigurationCacheKt                          : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycle$ProjectConfigurationResult$Success: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.StdlibDependencyManagementKt$configureStdlibVersionAlignment$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.OverriddenKotlinNativeHomeChecker$allKonanMainSubdirectoriesExist$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.dsl.PlatformSourceSetConventionsChecker$CheckedPlatformInfo: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.WasmJsEnvironmentChecker$4: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinUsages$KotlinCinteropDisambiguation: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginInMultipleProjectsHolder         : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.report.BuildReportType$Companion                    : loaded 2 times (x 67B)
Class com.google.common.collect.AllEqualOrdering                                      : loaded 2 times (x 110B)
Class com.google.common.collect.Interners$InternerImpl                                : loaded 2 times (x 71B)
Class com.google.common.collect.Interner                                              : loaded 2 times (x 66B)
Class com.google.common.collect.TransformedIterator                                   : loaded 2 times (x 76B)
Class com.google.common.io.ByteStreams                                                : loaded 2 times (x 67B)
Class com.google.common.cache.LocalCache$Strength$3                                   : loaded 2 times (x 77B)
Class com.google.common.collect.MapDifference                                         : loaded 2 times (x 66B)
Class com.google.common.base.Splitter$1                                               : loaded 2 times (x 73B)
Class kotlin._Assertions                                                              : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompilerPluginData$InputsOutputsState   : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.sources.FragmentConsistencyChecks$optInAnnotationsCheck$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.AbstractKotlinCompilationToRunnableFiles : loaded 2 times (x 192B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinMetadataArtifactKt$KotlinMetadataArtifact$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinUsages$KotlinMetadataCompatibility : loaded 2 times (x 72B)
Class [Lorg.jetbrains.kotlin.gradle.dsl.JvmTarget;                                    : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.utils.ConfigurationsKt$maybeCreateResolvable$1      : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.StatisticsBuildFlowManager$Companion         : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.statistics.metrics.StringMetrics$Companion                 : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$property$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.tooling.core.Extras$Type                                   : loaded 2 times (x 68B)
Class org.objectweb.asm.FieldWriter                                                   : loaded 2 times (x 74B)
Class com.google.common.collect.MapMaker$Dummy                                        : loaded 2 times (x 75B)
Class com.google.common.cache.CacheBuilder$NullListener                               : loaded 2 times (x 78B)
Class org.gradle.internal.classpath.TransformedClassPath                              : loaded 2 times (x 92B)
Class org.gradle.internal.classloader.InstrumentingClassLoader                        : loaded 2 times (x 66B)
Class kotlin.jvm.internal.FunctionReference                                           : loaded 2 times (x 118B)
Class kotlin.jvm.internal.FunctionBase                                                : loaded 2 times (x 66B)
Class kotlin.enums.EnumEntriesList                                                    : loaded 2 times (x 219B)
Class org.jetbrains.kotlin.gradle.internal.StdlibDependencyManagementKt$configureStdlibVersionAlignment$1$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.BuildFinishedListenerService$Companion       : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.BuildFinishedListenerService                 : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.sources.FragmentConsistencyChecks$optInAnnotationsCheck$2: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.targets.NativeForwardImplementationToApiElementsSideEffectKt$special$$inlined$KotlinTargetSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.BuildFusServiceKt                 : loaded 2 times (x 67B)
Class com.google.common.collect.ImmutableListMultimap                                 : loaded 2 times (x 172B)
Class com.google.common.collect.ImmutableRangeSet$AsSet                               : loaded 2 times (x 295B)
Class com.google.common.cache.CacheLoader$FunctionToCacheLoader                       : loaded 2 times (x 71B)
Class com.google.common.collect.Iterators$ArrayItr                                    : loaded 2 times (x 93B)
Class com.google.common.collect.ImmutableCollection$Builder                           : loaded 2 times (x 72B)
Class kotlin.comparisons.ComparisonsKt___ComparisonsJvmKt                             : loaded 2 times (x 67B)
Class kotlin.collections.CollectionsKt__IteratorsJVMKt                                : loaded 2 times (x 67B)
Class build_agr3p375fzty99zrloowe6ju6                                                 : loaded 2 times (x 177B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.BuildFusService$onFinish$2        : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$1$3$2   : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.AbstractKotlinCompileTool$sources$1           : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.TaskConfigAction                : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.OverriddenKotlinNativeHomeChecker$allKonanMainSubdirectoriesExist$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.AbstractKotlinPlugin$Companion               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinSourceSetTree                          : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycle$Stage                  : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCreateResourcesTaskSideEffectKt$KotlinCreateResourcesTaskSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.targets.jvm.KotlinJvmTargetTestFixturesSideEffectKt : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.sources.KotlinSourceSetFactory               : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.targets.native.toolchain.KotlinNativeBundleBuildService$Parameters_Decorated: loaded 2 times (x 139B)
Class org.jetbrains.kotlin.gradle.internal.ClassLoadersCachingBuildService$Companion  : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.IdeaKt$isInIdeaSync$1                      : loaded 2 times (x 70B)
Class com.google.common.collect.EmptyImmutableListMultimap                            : loaded 2 times (x 172B)
Class com.google.common.collect.BaseImmutableMultimap                                 : loaded 2 times (x 119B)
Class com.google.common.base.Joiner                                                   : loaded 2 times (x 75B)
Class com.google.common.base.MoreObjects                                              : loaded 2 times (x 67B)
Class kotlin.sequences.FilteringSequence$iterator$1                                   : loaded 2 times (x 75B)
Class build_agr3p375fzty99zrloowe6ju6$_run_closure1                                   : loaded 2 times (x 135B)
Class [Lorg.jetbrains.kotlin.build.report.metrics.BuildAttribute;                     : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.utils.ProviderApiUtilsKt$mapOrNull$1$1              : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.hierarchy.KotlinSourceSetTreeClassifier$Default: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.InternalKotlinCompilationKt              : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig     : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationImpl$attributes$2: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.OverriddenKotlinNativeHomeChecker$StdlibExistenceCheckerValueSource: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.StatisticsBuildFlowManager$subscribeForBuildResultAndConfigurationTimeMetrics$1$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.utils.IsolatedKotlinClasspathClassCastException     : loaded 2 times (x 78B)
Class [Lorg.objectweb.asm.Type;                                                       : loaded 2 times (x 65B)
Class com.google.common.collect.Platform                                              : loaded 2 times (x 67B)
Class com.google.common.collect.UnmodifiableIterator                                  : loaded 2 times (x 76B)
Class org.gradle.internal.service.ServiceLocator                                      : loaded 2 times (x 66B)
Class kotlin.Pair                                                                     : loaded 2 times (x 68B)
Class [Lkotlin.Function;                                                              : loaded 2 times (x 65B)
Class io.invertase.gradle.common.PackageJson$getForProject                            : loaded 2 times (x 142B)
Class io.invertase.gradle.build.ProjectExtension$getVersion$1                         : loaded 2 times (x 148B)
Class org.jetbrains.kotlin.gradle.utils.GradleAttributesContainerUtilsKt$copyAttributeTo$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$DefaultJavaToolchainSetter$useAsConvention$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilerArgumentsProducer$CreateCompilerArgumentsContext: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.RenderReportedDiagnosticsKt      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.hierarchy.KotlinSourceSetTreeClassifierKt$classify$1: loaded 2 times (x 84B)
Class [Lorg.jetbrains.kotlin.konan.target.KonanTarget;                                : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.WasmSourceSetsNotFoundChecker: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.AndroidSourceSetLayoutV1SourceSetsNotFoundChecker$androidSourceSetRegex$2: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinBuildStatsLoggerService$Companion: loaded 2 times (x 67B)
Class com.google.common.collect.HashBiMap$BiEntry                                     : loaded 2 times (x 79B)
Class com.google.common.collect.ImmutableSortedMap$Builder                            : loaded 2 times (x 85B)
Class com.google.common.base.Ascii                                                    : loaded 2 times (x 67B)
Class com.google.common.collect.SingletonImmutableBiMap                               : loaded 2 times (x 139B)
Class com.google.common.cache.LocalCache$1                                            : loaded 2 times (x 85B)
Class com.google.common.collect.AbstractMapEntry                                      : loaded 2 times (x 77B)
Class kotlin.io.CloseableKt                                                           : loaded 2 times (x 67B)
Class kotlin.collections.CollectionsKt__MutableCollectionsKt                          : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilationWithResources               : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinTargetPreset                           : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.KotlinTargetAlreadyDeclaredChecker: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationProcessorSideEffectKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinUsages$KotlinJavaRuntimeJarsCompatibility: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.utils.FileUtilsKt$localProperties$1                 : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.dsl.HasConfigurableKotlinCompilerOptions            : loaded 2 times (x 66B)
Class com.google.common.io.ByteSource$ByteArrayByteSource                             : loaded 2 times (x 81B)
Class com.google.common.collect.AbstractSetMultimap                                   : loaded 2 times (x 170B)
Class com.google.common.collect.MapMakerInternalMap$AbstractStrongKeyEntry            : loaded 2 times (x 78B)
Class com.google.common.collect.Sets$ImprovedAbstractSet                              : loaded 2 times (x 131B)
Class com.google.common.collect.ImmutableList$Builder                                 : loaded 2 times (x 73B)
Class com.google.common.cache.LocalCache$2                                            : loaded 2 times (x 138B)
Class com.google.common.collect.AbstractMultimap                                      : loaded 2 times (x 119B)
Class kotlin.reflect.KCallable                                                        : loaded 2 times (x 66B)
Class kotlin.io.TerminateException                                                    : loaded 2 times (x 78B)
Class kotlin.io.FilesKt__FilePathComponentsKt                                         : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig$2$1 : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain_Decorated          : loaded 2 times (x 122B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinProjectConfigurationMetrics : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilationInfo$TCS                    : loaded 2 times (x 85B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.HasBinaries                              : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinCommonOptions                             : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCreateSourcesJarTaskSideEffectKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinJvmProjectExtension                       : loaded 2 times (x 143B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradlePluginExtensionPointInternalKt   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.native.toolchain.UsesKotlinNativeBundleBuildService: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilerArgumentsProducer              : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.build.report.metrics.ValueType                             : loaded 2 times (x 75B)
Class com.google.common.collect.ImmutableRangeSet$1                                   : loaded 2 times (x 205B)
Class com.google.common.collect.RegularImmutableAsList                                : loaded 2 times (x 212B)
Class org.gradle.kotlin.dsl.Accessorse8w47d3slt021lb0cbtcdsmobKt                      : loaded 2 times (x 67B)
Class build_1st6b4zcxn0lddyz8z0am5m89$_run_closure2                                   : loaded 2 times (x 136B)
Class org.jetbrains.kotlin.build.report.metrics.BuildTimes$Companion                  : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.tasks.ProducesKlib                         : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$1$classpathConfiguration$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.NpmDependencyExtensionDelegate       : loaded 2 times (x 163B)
Class org.jetbrains.kotlin.gradle.plugin.sources.FragmentConsistencyChecker           : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.targets.native.internal.SetupKotlinNativePlatformDependenciesAndStdlibKt$SetupKotlinNativePlatformDependenciesAndStdlib$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.dsl.DefaultToolchainSupport_Decorated               : loaded 2 times (x 120B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesLoader$jvmArgs$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.report.BuildMetricsService$Companion                : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompileTool                             : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.utils.SingleAction$PerformedActions                 : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactories$Parameters_Decorated: loaded 2 times (x 130B)
Class org.jetbrains.kotlin.gradle.plugin.PropertiesProvider$PropertyNames             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.utils.GradleUtilsKt                                 : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.HasProject                                   : loaded 2 times (x 66B)
Class com.google.common.base.Optional                                                 : loaded 2 times (x 76B)
Class com.google.common.collect.Range                                                 : loaded 2 times (x 83B)
Class com.google.common.util.concurrent.AbstractFuture$Trusted                        : loaded 2 times (x 66B)
Class com.google.common.collect.Iterators                                             : loaded 2 times (x 67B)
Class kotlin.io.FilesKt__FileTreeWalkKt                                               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig$2$3 : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig         : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory$KotlinCompilationTaskNamesContainerFactory: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.internal.KotlinDependenciesManagementKt$CustomizeKotlinDependenciesSetupAction$1$coreLibrariesVersion$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.AndroidPublicationNotConfiguredChecker$runChecks$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.tooling.core.KotlinToolingVersion                          : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.CInteropInputChecker$runChecks$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.NativeVersionChecker    : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.ExperimentalKotlinGradlePluginApi                   : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesLoader          : loaded 2 times (x 82B)
Class org.jetbrains.kotlin.statistics.metrics.NumberAnonymizationPolicy$SAFE          : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.BuildFusService                   : loaded 2 times (x 80B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinAndroidProjectExtension                   : loaded 2 times (x 135B)
Class com.google.common.collect.ExplicitOrdering                                      : loaded 2 times (x 111B)
Class com.google.common.io.ByteSink$AsCharSink                                        : loaded 2 times (x 76B)
Class com.google.common.util.concurrent.UncheckedExecutionException                   : loaded 2 times (x 78B)
Class com.google.common.collect.Lists$StringAsImmutableList                           : loaded 2 times (x 203B)
Class com.google.common.io.LineReader                                                 : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.utils.ReportUtilsKt$addConfigurationMetrics$1       : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig$2$4 : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$scriptSources$1                 : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.build.report.metrics.BuildAttribute$Companion              : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.CInteropInputChecker$runChecks$2: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.KaptGenerateStubsConfig$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.KotlinTestDependencyManagementKt$maybeAddTestDependencyCapability$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.CheckKotlinGradlePluginConfigurationErrorsKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.NpmDependencyExtensionKt$AddNpmDependencyExtensionProjectSetupAction$1$WhenMappings: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinJvmAndroidCompilation              : loaded 2 times (x 194B)
Class org.jetbrains.kotlin.gradle.targets.CreateDefaultCompilationsSideEffectKt$CreateDefaultCompilationsSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.targets.native.internal.AddKotlinPlatformIntegersSupportLibraryKt$AddKotlinPlatformIntegersSupportLibrary$1: loaded 2 times (x 91B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.ApplyUserDefinedAttributesKt             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.tooling.core.TypeUtils$WhenMappings                        : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.utils.FutureImpl                                    : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesLoader$isToolchainEnabled$1: loaded 2 times (x 71B)
Class [Lorg.jetbrains.kotlin.statistics.metrics.NumericalMetrics;                     : loaded 2 times (x 65B)
Class org.gradle.internal.InternalTransformer                                         : loaded 2 times (x 66B)
Class kotlin.collections.EmptyList                                                    : loaded 2 times (x 170B)
Class kotlin.collections.MapsKt__MapsJVMKt                                            : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.CompileUsingKotlinDaemonWithNormalization     : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory$PostConfigure$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.KotlinDomApiDependencyManagementKt$configureKotlinDomApiDefaultDependency$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.DefaultDevNpmDependencyExtension     : loaded 2 times (x 141B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.HasKotlinDependencies;                     : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$Companion$wireJvmTargetToToolchain$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.JsLikeEnvironmentChecker: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPlatformType                           : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildServiceKt        : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleBuildServices$Companion          : loaded 2 times (x 67B)
Class org.objectweb.asm.ClassWriter                                                   : loaded 2 times (x 102B)
Class com.google.common.collect.HashMultimapGwtSerializationDependencies              : loaded 2 times (x 170B)
Class com.google.common.collect.AbstractMapBasedMultimap$KeySet$1                     : loaded 2 times (x 78B)
Class com.google.common.cache.CacheLoader$UnsupportedLoadingOperationException        : loaded 2 times (x 78B)
Class com.google.common.base.CharMatcher$And                                          : loaded 2 times (x 108B)
Class org.gradle.internal.classloader.ClasspathUtil$1                                 : loaded 2 times (x 72B)
Class build_agisdczp7kn8uyp03dp4zx2vi                                                 : loaded 2 times (x 180B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.TransformActionUsingKotlinToolingDiagnostics$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.BuildFinishedListenerService$Companion$registerIfAbsent$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.ToolingDiagnosticRenderingOptions$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinJvmCompilerOptionsHelper                  : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$Companion$wireJvmTargetToToolchain$2: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.ProjectLocalConfigurations$ProjectLocalDisambiguation: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.apple.swiftexport.internal.SwiftExportInitKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$NullableStringGradleProperty: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.targets.native.KonanPropertiesBuildService          : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleBuildServices$detectKotlinPluginLoadedInMultipleProjects$onRegister$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.internal.ConfigurationCacheStartParameterAccessor$Factory: loaded 2 times (x 66B)
Class org.objectweb.asm.FieldVisitor                                                  : loaded 2 times (x 73B)
Class com.google.common.collect.FluentIterable$1                                      : loaded 2 times (x 77B)
Class com.google.common.base.Objects                                                  : loaded 2 times (x 67B)
Class com.google.common.collect.IndexedImmutableSet                                   : loaded 2 times (x 146B)
Class com.google.common.collect.ImmutableMap$1                                        : loaded 2 times (x 77B)
Class com.google.common.collect.BiMap                                                 : loaded 2 times (x 66B)
Class org.gradle.api.internal.classpath.DefaultModuleRegistry$DefaultModule           : loaded 2 times (x 82B)
Class build_agisdczp7kn8uyp03dp4zx2vi$_run_closure1                                   : loaded 2 times (x 135B)
Class org.jetbrains.kotlin.gradle.internal.transforms.BuildToolsApiClasspathEntrySnapshotTransform$Parameters$Inject: loaded 2 times (x 114B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.BuildFinishMetrics$reportGlobalMetrics$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.CheckKotlinGradlePluginConfigurationErrors_Decorated: loaded 2 times (x 361B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationSourceSetInclusion$DefaultAddSourcesToCompileTask$addSources$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.WasmSourceSetsNotFoundChecker$wasmSourceSetRegex$2: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationSideEffect: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradlePluginExtensionPoint             : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.tooling.core.ExtrasUtilsKt                                 : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.buildtools.api.ExperimentalBuildToolsApi                   : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.targets.native.KonanPropertiesBuildService$Companion$registerIfAbsent$2$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.internal.IsInIdeaSyncValueSource$Inject             : loaded 2 times (x 105B)
Class org.jetbrains.kotlin.gradle.utils.ResourceUtilsKt                               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginWrapperKt$kotlinPluginVersionFromResources$1: loaded 2 times (x 74B)
Class com.google.common.collect.NullsFirstOrdering                                    : loaded 2 times (x 111B)
Class com.google.common.base.Absent                                                   : loaded 2 times (x 76B)
Class com.google.common.collect.FluentIterable$2                                      : loaded 2 times (x 77B)
Class com.google.common.primitives.Ints                                               : loaded 2 times (x 67B)
Class com.google.common.collect.AbstractIterator                                      : loaded 2 times (x 78B)
Class kotlin.collections.IndexingIterable                                             : loaded 2 times (x 75B)
Class kotlin.collections.ArraysKt__ArraysKt                                           : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinTargetSoftwareComponentImplKt$KotlinTargetSoftwareComponent$1$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.UsesKotlinJavaToolchain$DefaultImpls          : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.build.report.metrics.BuildAttributeKind$Companion          : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.KaptGenerateStubsConfig$Companion$configureLibraries$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationSourceSetInclusion$DefaultAddSourcesToCompileTask$addSources$2: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.AbstractKotlinCompile                         : loaded 2 times (x 450B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilationOutput                      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.CheckKotlinGradlePluginConfigurationErrorsKt$addDependsOnFromTasksThatShouldFailWhenErrorsReported$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$TVOS_X64                          : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinToolingDiagnosticsSetupActionKt: loaded 2 times (x 67B)
Class kotlin.jvm.internal.CollectionToArray                                           : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleBuildServices$Parameters$Inject  : loaded 2 times (x 102B)
Class org.jetbrains.kotlin.gradle.fus.BuildUidService$Inject                          : loaded 2 times (x 96B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginWrapperKt$kotlinPluginVersionFromResources$1$1: loaded 2 times (x 67B)
Class com.google.common.collect.FluentIterable$3                                      : loaded 2 times (x 77B)
Class com.google.common.math.MathPreconditions                                        : loaded 2 times (x 67B)
Class com.google.common.collect.Multiset                                              : loaded 2 times (x 66B)
Class com.google.common.collect.ArrayListMultimapGwtSerializationDependencies         : loaded 2 times (x 168B)
Class com.google.common.collect.ObjectArrays                                          : loaded 2 times (x 67B)
Class com.google.common.collect.AbstractIndexedListIterator                           : loaded 2 times (x 92B)
Class kotlin.collections.SetsKt__SetsJVMKt                                            : loaded 2 times (x 67B)
Class kotlin.Function                                                                 : loaded 2 times (x 66B)
Class build_evl13vpxjidvxohlw1uueqdij                                                 : loaded 2 times (x 176B)
Class org.jetbrains.kotlin.gradle.plugin.attributes.KlibPackaging$Companion           : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.tooling.core.Extras$Entry                                  : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.sources.InternalKotlinSourceSetKt            : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.Kapt3GradleSubplugin$Kapt3SubpluginContext : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinTargetArtifact                      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinJvmCompilerOptionsDefault_Decorated       : loaded 2 times (x 164B)
Class org.jetbrains.kotlin.gradle.plugin.HasKotlinDependencies                        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.internal.ClassLoadersCachingBuildService$Parameters_Decorated: loaded 2 times (x 130B)
Class org.jetbrains.kotlin.gradle.targets.native.toolchain.KotlinNativeBundleBuildService$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.BuildEventsListenerRegistryHolder_Decorated  : loaded 2 times (x 115B)
Class org.jetbrains.kotlin.gradle.plugin.StatisticsBuildFlowManager$subscribeForBuildResultAndConfigurationTimeMetrics$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.MetricContainer                   : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.old.Pre232IdeaKotlinBuildStatsMXBean: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinBuildStatsServicesRegistry  : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactoriesConfigurator   : loaded 2 times (x 72B)
Class com.google.common.collect.ImmutableMapEntrySet                                  : loaded 2 times (x 147B)
Class org.gradle.api.internal.classpath.Module                                        : loaded 2 times (x 66B)
Class kotlin.internal.ProgressionUtilKt                                               : loaded 2 times (x 67B)
Class kotlin.io.FileAlreadyExistsException                                            : loaded 2 times (x 78B)
Class build_evl13vpxjidvxohlw1uueqdij$_run_closure1                                   : loaded 2 times (x 136B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$getToolsJarFromJvm$1$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$ClasspathSnapshotProperties_Decorated: loaded 2 times (x 133B)
Class org.jetbrains.kotlin.gradle.internal.transforms.BuildToolsApiClasspathEntrySnapshotTransform$Parameters_Decorated: loaded 2 times (x 151B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationSourceSetInclusion$Companion$includedSourceSets$2: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinToolingDiagnosticsSetupActionKt$KotlinToolingDiagnosticsSetupAction$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.NpmDirectoryDependencyWithExternalsExtension: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinProjectSetupAction                     : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleFinishBuildHandler$Companion     : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.IsInIdeaSyncValueSource                    : loaded 2 times (x 74B)
Class com.google.common.cache.Weigher                                                 : loaded 2 times (x 66B)
Class org.gradle.api.internal.classpath.UnknownModuleException                        : loaded 2 times (x 78B)
Class io.invertase.gradle.build.ReactNativeShared$_applyDefaultExcludes_closure2$_closure5: loaded 2 times (x 137B)
Class build_evl13vpxjidvxohlw1uueqdij$_run_closure2                                   : loaded 2 times (x 136B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.InternalKotlinTargetComponentKt          : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.InternalKotlinTarget$DefaultImpls        : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycleImpl$executeCurrentStageAndScheduleNext$3: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationRegisterInSourceSetsConfigurator$configure$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinToolingDiagnosticsSetupActionKt$KotlinToolingDiagnosticsSetupAction$1$2: loaded 2 times (x 91B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.NpmDependencyWithExternalsExtension  : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinUsageContext                       : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinTargetsContainer                       : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.compilerRunner.GradleCliCommonizerKt                       : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.utils.ConfigurationsKt                              : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.StatisticsBuildFlowManager                   : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.BuildFusService$Parameters        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.PluginWrappersKt                             : loaded 2 times (x 67B)
Class com.google.common.base.Stopwatch                                                : loaded 2 times (x 68B)
Class com.google.common.cache.LocalCache$Strength                                     : loaded 2 times (x 77B)
Class com.google.common.cache.Cache                                                   : loaded 2 times (x 66B)
Class com.google.gson.stream.JsonReader$1                                             : loaded 2 times (x 68B)
Class kotlin.collections.IndexingIterator                                             : loaded 2 times (x 75B)
Class org.gradle.internal.classloader.FilteringClassLoader                            : loaded 2 times (x 101B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.InternalKotlinTargetKt                   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinToolingDiagnosticsSetupActionKt$KotlinToolingDiagnosticsSetupAction$1$3: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.utils.NativeCompilerDownloader                      : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.targets.js.dsl.KotlinTargetWithNodeJsDsl            : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.sources.DefaultKotlinSourceSetKt$defaultSourceSetLanguageSettingsChecker$1: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.sources.InternalKotlinSourceSet              : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinJvmPlugin                              : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.SyncLanguageSettingsWithKotlinExtensionSetupActionKt$SyncLanguageSettingsWithKotlinExtensionSetupAction$1: loaded 2 times (x 91B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinGradlePluginPublicDsl                     : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativeProperties                : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.fus.internal.SynchronizedConfigurationMetrics       : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.statistics.BuildSessionLogger                              : loaded 2 times (x 73B)
Class org.jetbrains.kotlin.gradle.plugin.internal.ConfigurationCacheStartParameterAccessor: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.internal.diagnostics.GradleCompatibilityCheck$DefaultCurrentGradleVersionProvider: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinBaseExtension                             : loaded 2 times (x 66B)
Class com.google.common.io.AppendableWriter                                           : loaded 2 times (x 96B)
Class com.google.common.util.concurrent.AbstractFuture$Cancellation                   : loaded 2 times (x 68B)
Class com.google.common.collect.ImmutableMap$Builder                                  : loaded 2 times (x 77B)
Class com.google.common.base.NullnessCasts                                            : loaded 2 times (x 67B)
Class [Lkotlin.reflect.KAnnotatedElement;                                             : loaded 2 times (x 65B)
Class kotlin.io.ByteStreamsKt                                                         : loaded 2 times (x 67B)
Class io.invertase.gradle.build.ReactNativeShared                                     : loaded 2 times (x 89B)
Class org.jetbrains.kotlin.gradle.utils.FutureKt$future$2$1                           : loaded 2 times (x 91B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.statistics.plugins.ObservablePlugins;      : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.internal.BuildIdService$Companion$registerIfAbsent$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationDependencyConfigurationsFactoriesKt$WhenMappings: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$IOS_ARM64                         : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.targets.js.dsl.KotlinWasmTargetDsl                  : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.DisabledCinteropCommonizationInHmppProjectChecker$runChecks$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.plugin.sources.ConsistencyCheck                     : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.sources.DefaultKotlinSourceSetKt$defaultSourceSetLanguageSettingsChecker$2: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinCommonToolOptions                         : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.AbstractKotlinPlugin                         : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$Companion$wireJvmTargetToJvm$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.ComposePluginSuggestApplyChecker: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinMultiplatformExtension                    : loaded 2 times (x 705B)
Class org.jetbrains.kotlin.gradle.plugin.BuildFinishAndConfigurationTimeMetricsFlowAction: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.PropertiesProvider$booleanProvider$1         : loaded 2 times (x 70B)
Class com.google.common.base.Present                                                  : loaded 2 times (x 77B)
Class com.google.common.collect.AbstractMapBasedMultimap$WrappedCollection$WrappedIterator: loaded 2 times (x 80B)
Class com.google.common.cache.RemovalListener                                         : loaded 2 times (x 66B)
Class org.gradle.api.internal.DefaultClassPathRegistry                                : loaded 2 times (x 72B)
Class build_6s7imafvclc6gm72hnzimmguy$_run_closure1                                   : loaded 2 times (x 136B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinTargetSoftwareComponentImplKt$KotlinTargetSoftwareComponent$1$1$configuration$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.DefaultPeerNpmDependencyExtension$delegate$1: loaded 2 times (x 163B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.NpmDependency                        : loaded 2 times (x 93B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.DefaultNpmDependencyExtension        : loaded 2 times (x 148B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.NpmDependency$Scope                  : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.sources.FragmentConsistencyChecks$unstableFeaturesCheck$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.sources.DefaultKotlinSourceSet               : loaded 2 times (x 137B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.AndroidSourceSetLayoutV1SourceSetsNotFoundChecker: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.utils.ConfigurationsKt$sam$org_gradle_api_Action$0  : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactories$Parameters    : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.internal.JavaExecTaskParametersCompatibility$Factory: loaded 2 times (x 66B)
Class com.google.common.collect.ImmutableMultimap$Keys                                : loaded 2 times (x 162B)
Class com.google.common.primitives.IntsMethodsForWeb                                  : loaded 2 times (x 67B)
Class com.google.common.collect.Multimap                                              : loaded 2 times (x 66B)
Class org.gradle.api.internal.ClassPathRegistry                                       : loaded 2 times (x 66B)
Class org.gradle.internal.classpath.DefaultClassPath$ImmutableUniqueList              : loaded 2 times (x 195B)
Class kotlin.text.StringsKt__StringsJVMKt                                             : loaded 2 times (x 67B)
Class build_9cu49dh6h7478zg7wrc3wnbxn                                                 : loaded 2 times (x 177B)
Class org.jetbrains.kotlin.gradle.plugin.internal.state.TaskLoggers                   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilation$DefaultImpls               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.utils.ProviderApiUtilsKt$providerWithLazyConvention$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$Companion$predefinedTargets$2     : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.sources.FragmentConsistencyChecks$unstableFeaturesCheck$2: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycleImpl                    : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinToolingDiagnostics$WasmWasiEnvironmentNotChosenExplicitly: loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinNativeKlibArtifactKt$KotlinNativeKlibArtifact$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinCommonCompilerOptionsDefault              : loaded 2 times (x 92B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinCommonCompilerToolOptionsDefault          : loaded 2 times (x 80B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesLoader$isUseXcodeMessageStyleEnabled$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.internal.ClassLoadersCachingBuildService$Companion$registerIfAbsent$2$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinBuildStatisticsUtilsKt$collectGeneralConfigurationTimeMetrics$statisticOverhead$1$2: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.StatisticsBuildFlowManager_Decorated         : loaded 2 times (x 115B)
Class org.jetbrains.kotlin.gradle.report.BuildReportMode$Companion                    : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactories$VariantImplementationFactory: loaded 2 times (x 66B)
Class com.google.common.collect.NullnessCasts                                         : loaded 2 times (x 67B)
Class com.google.common.collect.MapMakerInternalMap$Segment                           : loaded 2 times (x 138B)
Class com.google.common.base.ExtraObjectsMethodsForWeb                                : loaded 2 times (x 67B)
Class kotlin.collections.SetsKt                                                       : loaded 2 times (x 67B)
Class kotlin.text.StringsKt__IndentKt                                                 : loaded 2 times (x 67B)
Class io.invertase.gradle.common.Utilities$isGradleVersionLT                          : loaded 2 times (x 142B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.ApplyUserDefinedAttributesKt$UserDefinedAttributesSetupAction$1$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.compilerRunner.CompilerSystemPropertiesService             : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationImpl$1  : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.SemVer$Companion                     : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$LINUX_ARM64                       : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$LINUX_X64                         : loaded 2 times (x 74B)
Class [Lorg.jetbrains.kotlin.konan.target.Architecture;                               : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.targets.native.SetupEmbedAndSignAppleFrameworkTaskSideEffectKt$special$$inlined$KotlinTargetSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinUsages$KotlinCinteropCompatibility : loaded 2 times (x 73B)
Class org.jetbrains.kotlin.gradle.dsl.ExplicitApiMode                                 : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.utils.GradleAttributesContainerUtilsKt              : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactories$Companion     : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.internal.CustomPropertiesFileValueSource$Parameters_Decorated: loaded 2 times (x 130B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinTopLevelExtensionConfig                   : loaded 2 times (x 66B)
Class com.google.common.io.ByteSink                                                   : loaded 2 times (x 72B)
Class com.google.common.cache.CacheBuilder                                            : loaded 2 times (x 68B)
Class com.google.common.collect.SingletonImmutableList                                : loaded 2 times (x 203B)
Class org.jetbrains.kotlin.gradle.plugin.PropertiesProvider$booleanPropertyWithDeprecatedValues$1: loaded 2 times (x 74B)
Class org.gradle.internal.classpath.ClassPath                                         : loaded 2 times (x 66B)
Class org.gradle.api.UncheckedIOException                                             : loaded 2 times (x 78B)
Class kotlin.reflect.KFunction                                                        : loaded 2 times (x 66B)
Class kotlin.sequences.SequencesKt__SequencesKt$asSequence$$inlined$Sequence$1        : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.incremental.IncrementalModuleInfoProvider           : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationImpl$2  : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.dsl.PlatformSourceSetConventionsChecker$runChecks$1 : loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationProcessorSideEffectKt$KotlinCompilationProcessorSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinJvmCompilerOptionsDefault                 : loaded 2 times (x 102B)
Class org.jetbrains.kotlin.gradle.internal.UsesClassLoadersCachingBuildService        : loaded 2 times (x 66B)
Class com.google.common.collect.Interners$InternerBuilder                             : loaded 2 times (x 72B)
Class com.google.common.io.Files$FileByteSource                                       : loaded 2 times (x 81B)
Class com.google.common.collect.ImmutableList$ReverseImmutableList                    : loaded 2 times (x 204B)
Class com.google.common.base.JdkPattern                                               : loaded 2 times (x 71B)
Class kotlin.collections.MapsKt__MapsKt                                               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinStdlibConfigurationMetrics$collectMetrics$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.compilerRunner.KotlinCompilerArgumentsLogLevel$Companion   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.DefaultKotlinBasePlugin$addKotlinCompilerConfiguration$3$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.AbstractKotlinCompile$defaultKotlinJavaToolchain$1: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.daemon.common.MultiModuleICSettings                        : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.cli.common.arguments.Freezable                             : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.internal.transforms.BuildToolsApiClasspathEntrySnapshotTransform$Parameters: loaded 2 times (x 83B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilationInfoKt                      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile                                 : loaded 2 times (x 494B)
Class org.jetbrains.kotlin.gradle.plugin.AbstractKotlinPluginWrapper                  : loaded 2 times (x 82B)
Class org.jetbrains.kotlin.gradle.internal.StdlibDependencyManagementKt               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.PlatformSourceSetConventionsChecker$runChecks$2 : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$MINGW_X64                         : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.utils.ObservableSet                                 : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinToolingDiagnosticsCollectorKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.HierarchyAttributeContainer              : loaded 2 times (x 79B)
Class org.jetbrains.kotlin.gradle.tasks.UsesKotlinJavaToolchain                       : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.DefaultKotlinBasePlugin$apply$1              : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.statistics.metrics.StringOverridePolicy$OVERRIDE           : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$Params_Decorated: loaded 2 times (x 130B)
Class org.objectweb.asm.SymbolTable$Entry                                             : loaded 2 times (x 70B)
Class com.google.common.io.CharStreams$NullWriter                                     : loaded 2 times (x 95B)
Class com.google.errorprone.annotations.DoNotMock                                     : loaded 2 times (x 66B)
Class com.google.gson.internal.JsonReaderInternalAccess                               : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.statistics.metrics.IMetricContainer                        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinTargetComponentWithCoordinatesAndPublication: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.incremental.UsesIncrementalModuleInfoBuildService   : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$ANDROID_X86                       : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.dsl.PlatformSourceSetConventionsChecker$runChecks$3 : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.RegisterKotlinPluginExtensionsKt             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.resources.resolve.KotlinTargetResourcesResolutionStrategy: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.utils.CompilerOptionsKt$configureExperimentalTryNext$1$1: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.utils.CompilerOptionsKt                             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.utils.SingleAction                                  : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.plugin.PropertiesProvider$Companion                 : loaded 2 times (x 67B)
Class org.objectweb.asm.Edge                                                          : loaded 2 times (x 68B)
Class com.google.common.base.Suppliers$SupplierOfInstance                             : loaded 2 times (x 75B)
Class com.google.common.collect.Iterators$1                                           : loaded 2 times (x 77B)
Class Build_gradle$2$1                                                                : loaded 2 times (x 70B)
Class kotlin.UninitializedPropertyAccessException                                     : loaded 2 times (x 78B)
Class build_9kn57pj55amg75ewdgalkhlsr                                                 : loaded 2 times (x 184B)
Class build_1st6b4zcxn0lddyz8z0am5m89                                                 : loaded 2 times (x 176B)
Class org.jetbrains.kotlin.gradle.internal.utils.CollectionsKt                        : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$defaultKotlinJavaToolchain$1    : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinJavaToolchain$JavaToolchainSetter       : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.utils.NativeCompilerDownloader$Companion            : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.PlatformSourceSetConventionsChecker$runChecks$4 : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.JsLikeEnvironmentChecker$runChecks$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.UsesKotlinToolingDiagnostics     : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.KonanHomeConflictDeclarationChecker: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinToolingDiagnostics$WasmJsEnvironmentNotChosenExplicitly: loaded 2 times (x 69B)
Class org.jetbrains.kotlin.tooling.core.ExtrasPropertyKt$extrasLazyProperty$1         : loaded 2 times (x 90B)
Class org.jetbrains.kotlin.statistics.metrics.BooleanAnonymizationPolicy$SAFE         : loaded 2 times (x 81B)
Class com.google.common.cache.LocalCache$ComputingValueReference                      : loaded 2 times (x 92B)
Class com.google.common.collect.SingletonImmutableSet                                 : loaded 2 times (x 142B)
Class com.google.common.collect.ImmutableMap$IteratorBasedImmutableMap                : loaded 2 times (x 121B)
Class io.invertase.gradle.build.ReactNativeModule$_applyAndroidVersions_closure2$_closure6: loaded 2 times (x 139B)
Class org.jetbrains.kotlin.gradle.internal.KotlinDependenciesManagementKt$configureDefaultVersionsResolutionStrategy$1$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.DefaultKotlinCompilationSourceSetsContainer$allKotlinSourceSetsImpl$1$1: loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.DefaultKotlinCompilationSourceSetsContainer: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$TVOS_SIMULATOR_ARM64              : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.fus.internal.SynchronizedConfigurationMetrics$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycle$Stage$Companion        : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.DeprecatedKotlinNativeTargetsChecker: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.targets.jvm.KotlinJvmCompilationWireJavaSourcesSideEffectKt$KotlinJvmCompilationWireJavaSourcesSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.targets.native.ConfigureFrameworkExportSideEffectKt$special$$inlined$KotlinTargetSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.tooling.core.AbstractExtras                                : loaded 2 times (x 137B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesLoader$shouldUseEmbeddableCompilerJar$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.fus.BuildUidService$Companion$registerIfAbsent$1    : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.internal.diagnostics.AgpCompatibilityCheck$AndroidGradlePluginVersionProvider: loaded 2 times (x 66B)
Class com.google.common.collect.AbstractMapBasedMultimap$AsMap$AsMapEntries           : loaded 2 times (x 134B)
Class com.google.common.cache.LocalCache$Segment                                      : loaded 2 times (x 150B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinBuildStatsLoggerService$initSessionLogger$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.build.report.metrics.BuildTimes                            : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$ClasspathSnapshotProperties     : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.internal.StdlibDependencyManagementKt$addStdlibDependency$1$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$2$1     : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.TransformActionUsingKotlinToolingDiagnostics: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.dsl.PlatformSourceSetConventionsChecker$runChecks$6 : loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.plugin.sources.DefaultKotlinSourceSet_Decorated     : loaded 2 times (x 188B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinCompilationsKt                     : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.utils.GradleUtilsKt$registerClassLoaderScopedBuildService$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.targets.native.KonanPropertiesBuildService$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.statistics.metrics.StringOverridePolicy                    : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.DefaultKotlinBuildStatsBeanService: loaded 2 times (x 88B)
Class org.jetbrains.kotlin.gradle.plugin.PropertiesProvider$propertyWithDeprecatedValues$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.internal.diagnostics.AgpCompatibilityCheck$AndroidGradlePluginVersionProvider$Default: loaded 2 times (x 70B)
Class [Lorg.objectweb.asm.SymbolTable$Entry;                                          : loaded 2 times (x 65B)
Class [Lcom.google.common.base.Supplier;                                              : loaded 2 times (x 65B)
Class com.google.common.collect.SortedIterable                                        : loaded 2 times (x 66B)
Class com.google.common.cache.CacheLoader                                             : loaded 2 times (x 70B)
Class com.google.common.collect.Iterators$4                                           : loaded 2 times (x 78B)
Class com.google.common.collect.ImmutableBiMap                                        : loaded 2 times (x 139B)
Class com.google.common.collect.AbstractListMultimap                                  : loaded 2 times (x 168B)
Class org.gradle.internal.installation.GradleInstallation$1                           : loaded 2 times (x 71B)
Class org.gradle.api.internal.classpath.ModuleRegistry                                : loaded 2 times (x 66B)
Class io.invertase.gradle.build.ReactNativeModule$_applyReactNativeDependency_closure5: loaded 2 times (x 140B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycle$ProjectConfigurationResult: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.KotlinDependenciesManagementKt$configureDefaultVersionsResolutionStrategy$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.internal.StdlibDependencyManagementKt$configureStdlibDefaultDependency$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.utils.ConfigurationCacheOpaqueValue                 : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.model.builder.KotlinModelBuilder$Companion          : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCreateResourcesTaskSideEffectKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.native.internal.SetupKotlinNativePlatformDependenciesAndStdlibKt: loaded 2 times (x 67B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.mpp.resources.resolve.KotlinTargetResourcesResolutionStrategy;: loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinUsages                             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesLoader$Companion: loaded 2 times (x 67B)
Class [Lorg.jetbrains.kotlin.build.report.metrics.GradleBuildPerformanceMetric;       : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinBuildStatsBeanService$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.BuildFusService$Companion         : loaded 2 times (x 67B)
Class com.google.common.util.concurrent.internal.InternalFutureFailureAccess          : loaded 2 times (x 68B)
Class com.google.common.collect.Iterators$5                                           : loaded 2 times (x 78B)
Class com.google.common.collect.ImmutableSet$JdkBackedSetBuilderImpl                  : loaded 2 times (x 72B)
Class com.google.common.base.Splitter$SplittingIterator                               : loaded 2 times (x 80B)
Class build_6k1vuntnni446s9ebkvsvp2hk$_run_closure1                                   : loaded 2 times (x 135B)
Class org.jetbrains.kotlin.gradle.plugin.HasCompilerOptions                           : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.DefaultKotlinCompilationFriendPathsResolver$FriendArtifactResolver: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$WATCHOS_SIMULATOR_ARM64           : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.targets.js.ir.KotlinJsIrTarget                      : loaded 2 times (x 258B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.DefaultKotlinUsageContext                : loaded 2 times (x 104B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycleImpl$start$3            : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinJvmExtension                              : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinMultiplatformPluginWrapper             : loaded 2 times (x 82B)
Class [Lorg.jetbrains.kotlin.statistics.metrics.BooleanMetrics;                       : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$Companion: loaded 2 times (x 67B)
Class com.google.common.collect.ImmutableSortedAsList                                 : loaded 2 times (x 216B)
Class com.google.common.collect.ImmutableMultimap$Values                              : loaded 2 times (x 122B)
Class com.google.common.cache.LocalCache$LocalManualCache$1                           : loaded 2 times (x 71B)
Class com.google.common.base.CharMatcher$ForPredicate                                 : loaded 2 times (x 108B)
Class kotlin.io.FilesKt__UtilsKt                                                      : loaded 2 times (x 67B)
Class kotlin.collections.CollectionsKt__CollectionsJVMKt                              : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationConfigurationsContainer: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.internal.StdlibDependencyManagementKt$addStdlibDependency$1: loaded 2 times (x 71B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycle$CoroutineStart;      : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.dsl.IosSourceSetConventionChecker                   : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinGradleProjectChecker       : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinMultiplatformSourceSetConventions         : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.sources.KotlinSourceSetFactory$Companion     : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.statistics.metrics.NumberOverridePolicy$SUM                : loaded 2 times (x 81B)
Class com.google.common.collect.ImmutableSortedMap$1EntrySet                          : loaded 2 times (x 149B)
Class com.google.common.collect.ImmutableCollection                                   : loaded 2 times (x 121B)
Class com.google.common.base.CharMatcher$AnyOf                                        : loaded 2 times (x 108B)
Class org.gradle.api.internal.classpath.DefaultModuleRegistry                         : loaded 2 times (x 82B)
Class kotlin.collections.IndexedValue                                                 : loaded 2 times (x 68B)
Class kotlin.sequences.SequencesKt                                                    : loaded 2 times (x 67B)
Class kotlin.collections.CollectionsKt__IteratorsKt                                   : loaded 2 times (x 67B)
Class com.google.common.collect.RegularImmutableMap$KeySet                            : loaded 2 times (x 146B)
Class org.jetbrains.kotlin.gradle.plugin.internal.state.TaskExecutionResults          : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.compilerRunner.KotlinCompilerArgumentsLogLevel             : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.CompositePostConfigure: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.targets.native.internal.CInteropConfigurationsKt    : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.BuildFusService$Parameters$Inject : loaded 2 times (x 108B)
Class org.jetbrains.kotlin.statistics.metrics.StatisticsValuesConsumer                : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.AndroidGradlePluginVersion$Companion         : loaded 2 times (x 67B)
Class com.google.common.util.concurrent.ListenableFuture                              : loaded 2 times (x 66B)
Class com.google.common.cache.ReferenceEntry                                          : loaded 2 times (x 66B)
Class com.google.common.collect.ImmutableSet$CachingAsList                            : loaded 2 times (x 143B)
Class com.google.common.base.Converter                                                : loaded 2 times (x 86B)
Class com.google.common.collect.Lists                                                 : loaded 2 times (x 67B)
Class kotlin.text.StringsKt                                                           : loaded 2 times (x 67B)
Class io.invertase.gradle.build.ReactNativePlugin                                     : loaded 2 times (x 92B)
Class org.jetbrains.kotlin.gradle.plugin.DefaultKotlinBasePlugin$addKotlinCompilerConfiguration$2$1$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompilerExecutionStrategy$Companion     : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.CompilerPluginConfig                         : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.model.builder.KotlinModelBuilder                    : loaded 2 times (x 72B)
Class [Lorg.jetbrains.kotlin.tooling.core.HasMutableExtras;                           : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.GradleDeprecatedPropertyChecker: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinJsKlibArtifactKt$KotlinJsKlibArtifact$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.targets.CreateTargetConfigurationsSideEffectKt      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.ApplyUserDefinedAttributesKt$UserDefinedAttributesSetupAction$1: loaded 2 times (x 91B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinAndroidTarget                      : loaded 2 times (x 161B)
Class org.jetbrains.kotlin.gradle.plugin.DefaultKotlinBasePlugin$addKotlinCompilerConfiguration$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.NonSynchronizedMetricsContainer   : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.gradle.plugin.internal.DefaultCompatibilityConventionRegistrar: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.PropertiesProvider                           : loaded 2 times (x 69B)
Class com.google.common.util.concurrent.AbstractFuture                                : loaded 2 times (x 102B)
Class com.google.common.base.FunctionalEquivalence                                    : loaded 2 times (x 79B)
Class com.google.common.collect.Iterators$9                                           : loaded 2 times (x 77B)
Class com.google.common.collect.AbstractMapBasedMultimap                              : loaded 2 times (x 135B)
Class com.google.common.collect.ImmutableAsList                                       : loaded 2 times (x 205B)
Class kotlin.LazyKt__LazyJVMKt                                                        : loaded 2 times (x 67B)
Class io.invertase.gradle.build.ReactNativeModule                                     : loaded 2 times (x 89B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.MppSourcesJarKt$sourcesJarTask$1         : loaded 2 times (x 91B)
Class [Lorg.jetbrains.kotlin.gradle.dsl.jvm.JvmTargetValidationMode;                  : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$javaVersion$1      : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory$KotlinCompilationOutputFactory: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.konan.target.TargetSupportException                        : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.gradle.internal.IsInIdeaEnvironmentValueSource             : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilationToRunnableFiles             : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.tooling.BuildKotlinToolingMetadataTaskKt            : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.ProjectLocalConfigurations$setupAttributesMatchingStrategy$1$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.targets.native.toolchain.KotlinNativeBundleBuildService$Companion$registerIfAbsent$2$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.statistics.BuildSessionLogger$Companion                    : loaded 2 times (x 67B)
Class org.objectweb.asm.RecordComponentVisitor                                        : loaded 2 times (x 73B)
Class com.google.common.io.CharStreams                                                : loaded 2 times (x 67B)
Class com.google.common.collect.ImmutableMultiset                                     : loaded 2 times (x 159B)
Class com.google.common.cache.CacheLoader$SupplierToCacheLoader                       : loaded 2 times (x 71B)
Class kotlin.io.FilesKt__FileReadWriteKt                                              : loaded 2 times (x 67B)
Class kotlin.text.StringsKt__StringNumberConversionsKt                                : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.MppSourcesJarKt                          : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.SemverKt                             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.StdlibDependencyManagementKt$isStdlibAddedByUser$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinCompilationsKt$sam$java_util_concurrent_Callable$0: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompilerPluginData                      : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.compilerRunner.CompilerSystemPropertiesService$Companion$registerIfAbsent$2$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilationInfo                        : loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.tasks.BaseKotlinCompile                             : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.internal.UsesBuildIdProviderService          : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationSourceSetInclusion$include$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory$KotlinCompilationDependencyConfigurationsFactory: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinProjectExtension$jvmToolchain$1           : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycleCoroutineContextElement$Key: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinToolingDiagnosticsSetupActionKt$KotlinToolingDiagnosticsSetupAction$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.tooling.core.ExtrasPropertyKt                              : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinProjectExtensionKt                        : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.ClassLoadersCachingBuildService$Companion$registerIfAbsent$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleBuildServices$Parameters_Decorated: loaded 2 times (x 130B)
Class org.jetbrains.kotlin.gradle.plugin.DefaultKotlinBasePlugin$addKotlinCompilerConfiguration$3: loaded 2 times (x 71B)
Class [Lorg.jetbrains.kotlin.statistics.metrics.IMetricContainerFactory;              : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.internal.IdeaKt                                     : loaded 2 times (x 67B)
Class com.google.common.collect.UsingToStringOrdering                                 : loaded 2 times (x 110B)
Class com.google.common.collect.Maps$EntryTransformer                                 : loaded 2 times (x 66B)
Class org.gradle.internal.service.ServiceLookupException                              : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.gradle.internal.StdlibDependencyManagementKt$isStdlibAddedByUser$2: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.tasks.GradleCompileTaskProvider_Decorated           : loaded 2 times (x 117B)
Class org.jetbrains.kotlin.gradle.plugin.hierarchy.KotlinSourceSetTreeClassifierKt$sourceSetTreeClassifier$2: loaded 2 times (x 74B)
Class [Lorg.jetbrains.kotlin.tooling.core.KotlinToolingVersion$Maturity;              : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.konan.target.HostManager                                   : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.NpmDirectoryDependencyExtension      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinUsageContext$MavenScope            : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.targets.native.KotilnNativeConfigureBinariesSideEffectKt$special$$inlined$KotlinTargetSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinProjectSetupAction$Companion           : loaded 2 times (x 67B)
Class com.google.common.collect.Lists$ReverseList                                     : loaded 2 times (x 196B)
Class com.google.common.collect.ImmutableSortedSet                                    : loaded 2 times (x 295B)
Class [Lcom.google.common.cache.LocalCache$EntryFactory;                              : loaded 2 times (x 65B)
Class org.gradle.internal.classloader.ClassLoaderFactory                              : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$currentJvmJdkToolsJar$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.SubpluginEnvironment                         : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.internal.StdlibDependencyManagementKt$addStdlibDependency$1$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.sources.AbstractKotlinSourceSet              : loaded 2 times (x 137B)
Class org.jetbrains.kotlin.gradle.tooling.BuildKotlinToolingMetadataTaskKt$RegisterBuildKotlinToolingMetadataTask$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.compilerRunner.GradleCliCommonizerKt$maybeCreateCommonizerClasspathConfiguration$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesLoader$nativeCacheKind$1: loaded 2 times (x 71B)
Class [Lorg.jetbrains.kotlin.build.report.metrics.ValueType;                          : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.internal.diagnostics.AgpCompatibilityCheck          : loaded 2 times (x 67B)
Class com.google.common.collect.ImmutableMap$SerializedForm                           : loaded 2 times (x 69B)
Class com.google.common.cache.LocalCache$LocalManualCache                             : loaded 2 times (x 95B)
Class com.google.common.base.CharMatcher$IsNot                                        : loaded 2 times (x 107B)
Class com.google.common.base.CommonPattern                                            : loaded 2 times (x 70B)
Class org.gradle.internal.service.CachingServiceLocator                               : loaded 2 times (x 78B)
Class kotlin.jvm.internal.CallableReference                                           : loaded 2 times (x 102B)
Class org.jetbrains.kotlin.gradle.dsl.jvm.JvmTargetValidationMode                     : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.BuildFinishedListenerService$Companion$registerIfAbsent$2$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinTargetConfiguratorKt$WhenMappings      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.KotlinTestDependencyManagementKt$configureKotlinTestDependency$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.tooling.core.KotlinToolingVersionKt                        : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.OverriddenKotlinNativeHomeChecker$StdlibExistenceCheckerValueSource$Params_Decorated: loaded 2 times (x 130B)
Class org.jetbrains.kotlin.project.model.LanguageSettings                             : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycleKt$kotlinPluginLifecycle$2: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.targets.native.KonanPropertiesBuildService$Parameters: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.BuildFusService$Companion$registerIfAbsent$1$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.internal.CompatibilityConventionRegistrar$Factory: loaded 2 times (x 66B)
Class com.google.common.collect.AbstractMapBasedMultimap$KeySet                       : loaded 2 times (x 133B)
Class com.google.common.collect.RegularImmutableSortedSet                             : loaded 2 times (x 295B)
Class com.google.common.math.IntMath                                                  : loaded 2 times (x 67B)
Class com.google.common.base.Equivalence$Identity                                     : loaded 2 times (x 78B)
Class io.invertase.gradle.build.ReactNativeShared$_applyPackageVersion_closure3$_closure6: loaded 2 times (x 140B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinMetadataTarget                     : loaded 2 times (x 163B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.CheckKotlinGradlePluginConfigurationErrorsKt$locateOrRegisterCheckKotlinGradlePluginErrorsTask$taskProvider$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.InternalKotlinCompilation$DefaultImpls   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationSourceSetInclusion$DefaultAddSourcesToCompileTask: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.internal.KotlinTestDependencyManagementKt$configureKotlinTestDependency$2: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.dsl.PlatformSourceSetConventionsChecker$runChecks$$inlined$CheckedPlatformInfo$default$1: loaded 2 times (x 74B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycle$Stage;               : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinTargetArtifact$Companion            : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCreateLifecycleTasksSideEffectKt$KotlinCreateLifecycleTasksSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.targets.metadata.KotlinMetadataTargetConfiguratorKt$metadataCompilationsCreated$2: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinGradlePluginDsl                           : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.tooling.core.MutableExtras                                 : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleBuildServices$detectKotlinPluginLoadedInMultipleProjects$onRegister$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.BuildFinishAndConfigurationTimeMetricsFlowAction$Parameters_Decorated: loaded 2 times (x 139B)
Class org.jetbrains.kotlin.gradle.plugin.BuildFinishAndConfigurationTimeMetricsFlowAction$Parameters: loaded 2 times (x 66B)
Class org.objectweb.asm.commons.InstructionAdapter                                    : loaded 2 times (x 184B)
Class [Lcom.google.common.collect.HashBiMap$BiEntry;                                  : loaded 2 times (x 65B)
Class com.google.common.collect.MapMakerInternalMap$InternalEntry                     : loaded 2 times (x 66B)
Class com.google.common.collect.ImmutableMapEntry                                     : loaded 2 times (x 81B)
Class kotlin.enums.EnumEntries                                                        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.build.report.metrics.DoNothingBuildMetricsReporter         : loaded 2 times (x 82B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.CheckKotlinGradlePluginConfigurationErrorsKt$locateOrRegisterCheckKotlinGradlePluginErrorsTask$taskProvider$1$2: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.tasks.CompileUsingKotlinDaemon                      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinWithJavaTarget                     : loaded 2 times (x 162B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCreateNativeCompileTasksSideEffectKt$special$$inlined$KotlinCompilationSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.targets.KotlinTargetSideEffect$Companion            : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinSingleJavaTargetExtension                 : loaded 2 times (x 120B)
Class org.jetbrains.kotlin.gradle.utils.StoredPropertyKt$extrasStoredProperty$1       : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycle$IllegalLifecycleException: loaded 2 times (x 78B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesLoader$userProvidedNativeHome$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleFinishBuildHandler               : loaded 2 times (x 68B)
Class [Lorg.jetbrains.kotlin.build.report.metrics.BuildPerformanceMetric;             : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.statistics.metrics.StringOverridePolicy$CONCAT             : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinTopLevelExtension                         : loaded 2 times (x 66B)
Class com.google.common.collect.ImmutableMultimap                                     : loaded 2 times (x 143B)
Class com.google.common.base.Equivalence                                              : loaded 2 times (x 78B)
Class com.google.common.collect.ImmutableMapEntry$NonTerminalImmutableMapEntry        : loaded 2 times (x 81B)
Class com.google.common.base.Function                                                 : loaded 2 times (x 66B)
Class io.invertase.gradle.build.ReactNativeModule$_applyAndroidVersions_closure2      : loaded 2 times (x 139B)
Class org.jetbrains.kotlin.gradle.plugin.ProjectLocalConfigurationsKt                 : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.internal.KotlinDomApiDependencyManagementKt$addKotlinDomApiDependency$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.KotlinTargetAlreadyDeclaredChecker$runChecks$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinTargetWithBinaries                 : loaded 2 times (x 159B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.KotlinSourceSet;                           : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.JsEnvironmentChecker$1  : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinToolingDiagnostics$JsLikeEnvironmentNotChosenExplicitly: loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinTargetsContainerWithPresets            : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.utils.StoredLazyProperty$getValue$1                 : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.statistics.metrics.IMetricContainerFactory                 : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.statistics.MetricValueValidationFailed                     : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.gradle.plugin.PropertiesProvider$localProperties$2         : loaded 2 times (x 75B)
Class com.google.common.collect.IndexedImmutableSet$1                                 : loaded 2 times (x 208B)
Class com.google.common.cache.CacheBuilder$OneWeigher                                 : loaded 2 times (x 78B)
Class com.google.common.collect.RegularImmutableList                                  : loaded 2 times (x 208B)
Class org.jetbrains.kotlin.statistics.BuildSessionLogger$Companion$listProfileFiles$lambda$3$$inlined$sortedBy$1: loaded 2 times (x 85B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.DefaultKotlinCompilationFriendPathsResolver$resolveFriendPaths$1$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.build.report.metrics.BuildMetricsReporterImpl              : loaded 2 times (x 94B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.KaptGenerateStubsConfig         : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$1$classpathConfiguration$jvmToolchain$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.SemVer                               : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesLoader$konanDataDirProperty$1$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.JsEnvironmentChecker$2  : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.utils.WhenPluginEnabledKt$whenPluginsEnabled$1      : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.AbstractKotlinCompileTool                     : loaded 2 times (x 362B)
Class org.jetbrains.kotlin.gradle.internal.report.BuildScanApi                        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.FlowActionBuildFusService$Companion$registerIfAbsentImpl$1$1: loaded 2 times (x 70B)
Class [Lorg.jetbrains.kotlin.gradle.report.BuildReportType;                           : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinBuildStatsConfiguration     : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$localProperties$2: loaded 2 times (x 75B)
Class com.google.common.collect.ImmutableSortedMap                                    : loaded 2 times (x 213B)
Class org.jetbrains.kotlin.gradle.targets.native.KonanPropertiesBuildService$Parameters_Decorated: loaded 2 times (x 130B)
Class org.gradle.internal.classloader.ClasspathUtil                                   : loaded 2 times (x 67B)
Class kotlin.io.FileSystemException                                                   : loaded 2 times (x 78B)
Class io.invertase.gradle.build.ProjectExtension$getVersionsRoot$2                    : loaded 2 times (x 148B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinCompile                                   : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.DefaultKotlinCompilationFriendPathsResolver$DefaultFriendArtifactResolver: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.InternalGradlePropertiesUsageChecker$runChecks$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.utils.StringUtilsKt$lowerCamelCaseName$1            : loaded 2 times (x 121B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.JsEnvironmentChecker$3  : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinLegacyMetadataArtifactKt            : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.JvmTarget$Companion                             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesLoader$downloadFromMaven$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.internal.DefaultConfigurationCacheStartParameterAccessor_Decorated: loaded 2 times (x 121B)
Class org.jetbrains.kotlin.gradle.plugin.internal.DefaultConfigurationCacheStartParameterAccessor: loaded 2 times (x 73B)
Class org.jetbrains.kotlin.tooling.core.HasExtras                                     : loaded 2 times (x 66B)
Class com.google.common.util.concurrent.Uninterruptibles                              : loaded 2 times (x 67B)
Class kotlin.jvm.internal.DefaultConstructorMarker                                    : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.statistics.metrics.ConcatMetricContainer$Companion         : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilationInfo$TCS$friendPaths$1      : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.fus.internal.SynchronizedConfigurationMetrics$add$1 : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.sources.DefaultLanguageSettingsBuilder$freeCompilerArgsProvider$1: loaded 2 times (x 91B)
Class org.jetbrains.kotlin.konan.target.HostManager$Companion                         : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.DevNpmDependencyExtension            : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.PreHmppDependenciesUsageChecker: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.targets.CreateArtifactsSideEffectKt                 : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.AbstractKotlinMultiplatformPluginWrapper     : loaded 2 times (x 82B)
Class org.jetbrains.kotlin.gradle.utils.GradleConfigurationUtilsKt$addGradlePluginMetadataAttributes$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.internal.DefaultProjectIsolationStartParameterAccessor: loaded 2 times (x 73B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinAndroidExtension                          : loaded 2 times (x 66B)
Class com.google.common.io.CharSource                                                 : loaded 2 times (x 81B)
Class com.google.common.collect.AbstractMapBasedMultimap$WrappedSet                   : loaded 2 times (x 138B)
Class com.google.common.collect.Maps                                                  : loaded 2 times (x 67B)
Class org.gradle.api.specs.Spec                                                       : loaded 2 times (x 66B)
Class io.invertase.gradle.build.ReactNativeShared$_applyPackageVersion_closure3       : loaded 2 times (x 140B)
Class org.jetbrains.kotlin.gradle.fus.internal.SynchronizedConfigurationMetrics$add$2 : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$disableMultiModuleIC$2          : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.GradleCompileTaskProviderKt                   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.SubpluginEnvironment$Companion               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.ApplyUserDefinedAttributesKt$UserDefinedAttributesSetupAction$1$1$3: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationImpl$Params: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.konan.target.UtilsKt                                       : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tooling.BuildKotlinToolingMetadataTask              : loaded 2 times (x 313B)
Class org.jetbrains.kotlin.gradle.plugin.internal.CompatibilityConventionRegistrarKt  : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinJvmJarArtifactKt                    : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.CreateCInteropTasksSideEffectKt$special$$inlined$KotlinCompilationSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.FinalizeConfigurationFusMetricActionKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.resources.resolve.KotlinTargetResourcesResolutionStrategy$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.native.toolchain.KotlinNativeBundleBuildService$Companion$registerIfAbsent$2$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.build.report.metrics.GradleBuildPerformanceMetric$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinBuildStatsBeanService       : loaded 2 times (x 83B)
Class org.jetbrains.kotlin.gradle.plugin.internal.MavenPublicationComponentAccessor$Factory: loaded 2 times (x 66B)
Class build_6t0aaf1vxqnq7aqxdnrvcooc8                                                 : loaded 2 times (x 176B)
Class com.google.common.collect.MapMaker                                              : loaded 2 times (x 68B)
Class [Lcom.google.common.collect.ImmutableEntry;                                     : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.DaemonReuseCounter                : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig$1$1 : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$kotlinOptions$1                 : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativeProperties$Companion      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.utils.StringUtilsKt                                 : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.ConfigureBuildSideEffectKt$ConfigureBuildSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.tooling.core.ExtrasLazyProperty                            : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.utils.CompletableFuture                             : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.build.report.metrics.BuildPerformanceMetric                : loaded 2 times (x 66B)
Class [Lorg.jetbrains.kotlin.statistics.metrics.StringOverridePolicy;                 : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.internal.JavaExecTaskParametersCompatibility : loaded 2 times (x 66B)
Class com.google.common.cache.LocalCache$LoadingValueReference                        : loaded 2 times (x 92B)
Class kotlin.collections.EmptyIterator                                                : loaded 2 times (x 85B)
Class kotlin.SynchronizedLazyImpl                                                     : loaded 2 times (x 72B)
Class kotlin.text.StringsKt__RegexExtensionsKt                                        : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig$getClasspathSnapshotDir$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig$1$2 : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.ConfigurationMetricsBuildFusParameters: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfigKt$explicitApiMode$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.OverriddenKotlinNativeHomeChecker$StdlibExistenceCheckerValueSource$Params$Inject: loaded 2 times (x 102B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.CommonMainOrTestWithDependsOnChecker: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinNativeHostSpecificMetadataArtifactKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCreateLifecycleTasksSideEffectKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinTarget                                 : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.utils.ConfigurationsKt$createResolvable$1           : loaded 2 times (x 74B)
Class [Lorg.jetbrains.kotlin.statistics.metrics.BooleanAnonymizationPolicy;           : loaded 2 times (x 65B)
Class com.google.common.io.LineReader$1                                               : loaded 2 times (x 71B)
Class com.google.common.collect.CollectSpliterators                                   : loaded 2 times (x 67B)
Class com.google.common.collect.AbstractMapBasedMultimap$AsMap                        : loaded 2 times (x 124B)
Class com.google.common.collect.MapMakerInternalMap$StrongKeyDummyValueEntry          : loaded 2 times (x 80B)
Class com.google.common.base.Platform                                                 : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleBuildServices$Inject             : loaded 2 times (x 96B)
Class kotlin.sequences.SequencesKt__SequencesJVMKt                                    : loaded 2 times (x 67B)
Class kotlin.collections.AbstractList                                                 : loaded 2 times (x 193B)
Class kotlin.collections.AbstractCollection                                           : loaded 2 times (x 113B)
Class org.jetbrains.kotlin.gradle.internal.KotlinDependenciesManagementKt$allNonProjectDependencies$1: loaded 2 times (x 70B)
Class [Lorg.jetbrains.kotlin.build.report.metrics.BuildAttributeKind;                 : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.tasks.K2MultiplatformStructure                      : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.compilerRunner.CompilerSystemPropertiesService$Parameters  : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationSourceSetsContainer: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinNativeKlibArtifactKt                : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.ProjectLocalConfigurations                   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinUsages$KotlinUsagesDisambiguation  : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.tooling.core.Extras$Key$Companion                          : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.tooling.core.Extras$Key                                    : loaded 2 times (x 68B)
Class [Lorg.objectweb.asm.AnnotationWriter;                                           : loaded 2 times (x 65B)
Class com.google.common.collect.CompoundOrdering                                      : loaded 2 times (x 111B)
Class com.google.common.collect.AbstractMapBasedMultimap$WrappedList                  : loaded 2 times (x 201B)
Class com.google.common.collect.Maps$8                                                : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.gradle.internal.ClassLoadersCachingBuildService$Companion$registerIfAbsent$2$1$1: loaded 2 times (x 71B)
Class kotlin.collections.ArrayAsCollection                                            : loaded 2 times (x 101B)
Class kotlin.LazyKt__LazyKt                                                           : loaded 2 times (x 67B)
Class kotlin.text.StringsKt__StringBuilderJVMKt                                       : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig$1$4 : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.CheckKotlinGradlePluginConfigurationErrors: loaded 2 times (x 313B)
Class org.jetbrains.kotlin.gradle.plugin.sources.DefaultKotlinSourceSetKt             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinJvmOptions                                : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.HierarchyAttributeContainer$1            : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycle                        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinJsPluginWrapper                        : loaded 2 times (x 82B)
Class org.jetbrains.kotlin.gradle.dsl.ToolchainSupport                                : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.tooling.core.Extras                                        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.InternalKotlinTarget                     : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.statistics.metrics.NumberAnonymizationPolicy$RANDOM_10_PERCENT: loaded 2 times (x 81B)
Class org.jetbrains.kotlin.statistics.metrics.StringMetrics                           : loaded 2 times (x 76B)
Class [Lcom.google.common.collect.Iterators$EmptyModifiableIterator;                  : loaded 2 times (x 65B)
Class com.google.common.collect.Lists$ReverseList$1                                   : loaded 2 times (x 95B)
Class com.google.common.collect.ImmutableRangeSet$Builder                             : loaded 2 times (x 73B)
Class com.google.common.collect.Sets$SetView                                          : loaded 2 times (x 134B)
Class com.google.common.cache.LocalCache$StrongValueReference                         : loaded 2 times (x 86B)
Class org.jetbrains.kotlin.gradle.targets.native.KonanPropertiesBuildService$Companion$registerIfAbsent$1: loaded 2 times (x 71B)
Class kotlin.UNINITIALIZED_VALUE                                                      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.CompilerPluginOptions                         : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinTargetSoftwareComponentImpl        : loaded 2 times (x 86B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.DefaultKotlinUsageContext$PublishOnlyIf  : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig$1$5 : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinCommonCompilerOptionsHelper               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinJvmPlugin$Companion                    : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinToolingDiagnostics         : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinMetadataArtifactKt                  : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.CreateArtifactsSideEffectKt$CreateArtifactsSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$property$1$valueFromGradleAndLocalProperties$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.PropertiesProvider$useClasspathSnapshot$1    : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginWrapperKt$kotlinToolingVersion$2 : loaded 2 times (x 74B)
Class org.objectweb.asm.Type                                                          : loaded 2 times (x 68B)
Class com.google.common.graph.SuccessorsFunction                                      : loaded 2 times (x 66B)
Class [Lcom.google.gson.stream.JsonToken;                                             : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.AndroidPublicationNotConfiguredChecker: loaded 2 times (x 70B)
Class org.gradle.internal.classloader.VisitableURLClassLoader$InstrumentingVisitableURLClassLoader: loaded 2 times (x 119B)
Class kotlin.comparisons.ComparisonsKt__ComparisonsKt                                 : loaded 2 times (x 67B)
Class kotlin.ranges.RangesKt__RangesKt                                                : loaded 2 times (x 67B)
Class kotlin.collections.SetsKt___SetsKt                                              : loaded 2 times (x 67B)
Class kotlin.sequences.SequencesKt__SequencesKt                                       : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.DefaultKotlinUsageContext$1              : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig$1$6 : loaded 2 times (x 71B)
Class [Lorg.jetbrains.kotlin.gradle.tasks.KotlinCompilerExecutionStrategy;            : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.tasks.FailedCompilationException                    : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationImpl$associateWith$2: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.hierarchy.KotlinSourceSetTreeClassifier      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationAssociator: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.konan.target.UtilsKt$evaluatePresetName$1                  : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinNativeTarget                       : loaded 2 times (x 169B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.KotlinSourceSetTreeDependsOnMismatchChecker$runChecks$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.PreHmppDependenciesUsageChecker$runChecks$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.utils.ProjectExtensionsKt                           : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinSourceSetTree$Companion                : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinGradleProjectChecker$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleBuildServices$Parameters         : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.BuildFusService$Companion$registerIfAbsent$1$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.statistics.fileloggers.MetricsContainer$Companion          : loaded 2 times (x 67B)
Class org.objectweb.asm.ByteVector                                                    : loaded 2 times (x 75B)
Class com.google.common.collect.MapMakerInternalMap$StrongKeyDummyValueEntry$Helper   : loaded 2 times (x 75B)
Class com.google.common.io.Files$FileByteSink                                         : loaded 2 times (x 73B)
Class [Lcom.google.common.cache.CacheBuilder$NullListener;                            : loaded 2 times (x 65B)
Class com.google.common.math.IntMath$1                                                : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.utils.Completable$await$1                           : loaded 2 times (x 84B)
Class org.gradle.internal.classloader.VisitableURLClassLoader$Spec                    : loaded 2 times (x 70B)
Class org.gradle.internal.classpath.DefaultClassPath                                  : loaded 2 times (x 86B)
Class kotlin.comparisons.ComparisonsKt                                                : loaded 2 times (x 67B)
Class kotlin.comparisons.ComparisonsKt___ComparisonsKt                                : loaded 2 times (x 67B)
Class kotlin.sequences.FilteringSequence                                              : loaded 2 times (x 71B)
Class com.google.common.base.PairwiseEquivalence                                      : loaded 2 times (x 79B)
Class io.invertase.gradle.build.ProjectExtension$getOption$3                          : loaded 2 times (x 148B)
Class org.jetbrains.kotlin.gradle.plugin.DefaultKotlinBasePlugin$addKotlinCompilerConfiguration$2$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.BuildFinishMetrics                : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinSoftwareComponentKt                : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationSourceSetInclusion$AddSourcesToCompileTask: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.konan.target.Architecture                                  : loaded 2 times (x 75B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.HasProject;                                : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.utils.MutableObservableSetImpl                      : loaded 2 times (x 159B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinTasksProvider                           : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinJsCompilerTypeHolder                   : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.dsl.DefaultToolchainSupport$wireToolchainToTasks$1  : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinCommonCompilerOptions                     : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$MemoizedCallable: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.internal.DefaultMavenPublicationComponentAccessorFactory: loaded 2 times (x 72B)
Class org.objectweb.asm.Symbol                                                        : loaded 2 times (x 69B)
Class com.google.common.collect.ImmutableSortedMap$1EntrySet$1                        : loaded 2 times (x 208B)
Class com.google.common.base.AbstractIterator                                         : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.compilerRunner.CompilerSystemPropertiesService$Companion   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.statistics.metrics.OrMetricContainer                       : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.tasks.AbstractKotlinCompile$friendPathsSet$1        : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.build.report.metrics.BuildAttributes                       : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.cocoapods.KotlinCocoapodsPlugin$Companion    : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationImpl    : loaded 2 times (x 136B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory$PostConfigure;: loaded 2 times (x 65B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$MACOS_X64                         : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.konan.util.Named                                           : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinSourceSet$Companion                    : loaded 2 times (x 67B)
Class [Lorg.jetbrains.kotlin.gradle.report.BuildReportMode;                           : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.statistics.metrics.BooleanOverridePolicy                   : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.gradle.plugin.AbstractKotlinAndroidPluginWrapper           : loaded 2 times (x 82B)
Class com.google.common.io.Java8Compatibility                                         : loaded 2 times (x 67B)
Class com.google.common.io.Closer                                                     : loaded 2 times (x 74B)
Class com.google.common.collect.RegularImmutableMap$Values                            : loaded 2 times (x 203B)
Class com.google.common.collect.ImmutableSet                                          : loaded 2 times (x 141B)
Class com.google.common.base.CharMatcher$BitSetMatcher                                : loaded 2 times (x 108B)
Class Build_gradle$7                                                                  : loaded 2 times (x 70B)
Class org.gradle.internal.classloader.ClassLoaderSpec                                 : loaded 2 times (x 67B)
Class kotlin.jvm.internal.FunctionReferenceImpl                                       : loaded 2 times (x 118B)
Class kotlin.collections.CollectionsKt___CollectionsJvmKt                             : loaded 2 times (x 67B)
Class io.invertase.gradle.build.ProjectExtension$getSharedInstance                    : loaded 2 times (x 142B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.DaemonReuseCounter$Companion      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinStdlibConfigurationMetrics  : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$DefaultJavaToolchainSetter: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.build.report.metrics.BuildAttributeKind                    : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinCompilationsKt$moduleNameForCompilation$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.sources.KotlinDependencyScopeKt              : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.TransformActionUsingKotlinToolingDiagnostics$Parameters: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.compilerRunner.CompilerSystemPropertiesService$Companion$registerIfAbsent$2$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinCompilationsKt$addSourcesToKotlinCompileTask$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.internal.KotlinDependenciesManagementKt$configureDefaultVersionsResolutionStrategy$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.utils.KotlinExtensionUtilsKt$forAllTargets$1        : loaded 2 times (x 91B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.CheckKotlinGradlePluginConfigurationErrorsKt$locateOrRegisterCheckKotlinGradlePluginErrorsTask$taskProvider$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.OverriddenKotlinNativeHomeChecker$StdlibExistenceCheckerValueSource$Params: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.dsl.PlatformSourceSetConventionsChecker$nativeTargetPresets$1$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tooling.BuildKotlinToolingMetadataTaskKt$buildKotlinToolingMetadataTask$1: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.internal.Kapt3GradleSubplugin$Companion             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCreateCompilationArchiveTaskKt$KotlinRegisterCompilationArchiveTasksExtension$1: loaded 2 times (x 70B)
Class [Lorg.jetbrains.kotlin.statistics.metrics.NumberOverridePolicy;                 : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$GradleProperty: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.utils.StoredPropertyKt$projectStoredProperty$1      : loaded 2 times (x 74B)
Class org.objectweb.asm.AnnotationWriter                                              : loaded 2 times (x 75B)
Class com.google.common.io.ByteSource$EmptyByteSource                                 : loaded 2 times (x 81B)
Class com.google.common.cache.CacheStats                                              : loaded 2 times (x 67B)
Class kotlin.TuplesKt                                                                 : loaded 2 times (x 67B)
Class kotlin.Unit                                                                     : loaded 2 times (x 67B)
Class kotlin.sequences.SequencesKt___SequencesJvmKt                                   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.K2MultiplatformStructure$RefinesEdge          : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.SyncLanguageSettingsWithKotlinExtensionSetupActionKt$SyncLanguageSettingsWithKotlinExtensionSetupAction$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.TaskConfigAction$configureTask$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.DuplicateSourceSetChecker$runChecks$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.ToolingDiagnostic$Severity       : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.dsl.JvmTarget                                       : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.internal.ClassLoadersCachingBuildService            : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactories$Inject        : loaded 2 times (x 102B)
Class org.jetbrains.kotlin.gradle.plugin.internal.DefaultJavaExecTaskParametersCompatibility$Factory: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactoriesConfigurator$Companion: loaded 2 times (x 67B)
Class org.objectweb.asm.RecordComponentWriter                                         : loaded 2 times (x 74B)
Class settings_4rq5cjrcdj0mscfkcpdclh2wk$_run_closure1                                : loaded 2 times (x 135B)
Class com.google.common.collect.ImmutableMultisetGwtSerializationDependencies         : loaded 2 times (x 121B)
Class com.google.common.collect.Interners                                             : loaded 2 times (x 67B)
Class com.google.common.io.Closer$Suppressor                                          : loaded 2 times (x 66B)
Class com.google.common.io.LineProcessor                                              : loaded 2 times (x 66B)
Class com.google.common.util.concurrent.AbstractFuture$Listener                       : loaded 2 times (x 68B)
Class com.google.common.base.CharMatcher$Or                                           : loaded 2 times (x 108B)
Class kotlin.ranges.RangesKt                                                          : loaded 2 times (x 67B)
Class io.invertase.gradle.build.ReactNativeShared$_applyDefaultExcludes_closure2      : loaded 2 times (x 137B)
Class [Lorg.jetbrains.kotlin.compilerRunner.KotlinCompilerArgumentsLogLevel;          : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile_Decorated                       : loaded 2 times (x 595B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.SyncLanguageSettingsWithKotlinExtensionSetupActionKt$SyncLanguageSettingsWithKotlinExtensionSetupAction$1$2: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.internal.BuildIdService$Companion            : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.OverriddenKotlinNativeHomeChecker$StdlibExistenceCheckerValueSource$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.js.dsl.KotlinWasmSubTargetContainerDsl      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.WasmWasiEnvironmentChecker$1: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.DisabledCinteropCommonizationInHmppProjectChecker: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.NoKotlinTargetsDeclaredChecker: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.targets.jvm.KotlinJvmTargetTestFixturesSideEffectKt$ConfigureJavaTestFixturesSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.utils.StoredPropertyStorage                         : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinSourceSetContainer                     : loaded 2 times (x 66B)
Class org.objectweb.asm.Attribute                                                     : loaded 2 times (x 73B)
Class com.google.common.base.Supplier                                                 : loaded 2 times (x 66B)
Class org.gradle.internal.classloader.VisitableURLClassLoader                         : loaded 2 times (x 113B)
Class kotlin.collections.ArraysUtilJVM                                                : loaded 2 times (x 67B)
Class kotlin.jvm.functions.Function0                                                  : loaded 2 times (x 66B)
Class build_eupcmxgtu9q3uy0v8rg9z8i7j$_run_closure1                                   : loaded 2 times (x 136B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.plugins.ObservablePlugins         : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.sources.InternalKotlinSourceSetKt$awaitPlatformCompilations$1: loaded 2 times (x 84B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.sources.KotlinDependencyScope;             : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$configureCommonParameters$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.ConfigurationNaming: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.DefaultKotlinCompilationPostConfigureKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$WATCHOS_X64                       : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinGradleProjectCheckersRunnerKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.IdeaKt$isInIdeaEnvironment$1               : loaded 2 times (x 70B)
Class [Lorg.jetbrains.kotlin.tooling.core.HasExtras;                                  : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.IncorrectCompileOnlyDependenciesChecker: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.WasmWasiEnvironmentChecker$2: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinLegacyCompatibilityMetadataArtifactKt$KotlinLegacyCompatibilityMetadataArtifact$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.dsl.DefaultToolchainSupport$wireToolchainToTasks$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.utils.SingleActionPerProject                        : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.plugin.DefaultKotlinBasePlugin                      : loaded 2 times (x 78B)
Class com.google.common.collect.ImmutableList$SerializedForm                          : loaded 2 times (x 69B)
Class Settings_gradle                                                                 : loaded 2 times (x 124B)
Class org.gradle.internal.Cast                                                        : loaded 2 times (x 67B)
Class org.gradle.api.Action                                                           : loaded 2 times (x 66B)
Class kotlin.jvm.functions.Function1                                                  : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.statistics.metrics.StatisticsValuesConsumer$DefaultImpls   : loaded 2 times (x 67B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.mpp.KotlinUsageContext$MavenScope;         : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$configureCommonParameters$2: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationCompilerOptionsFromTargetConfigurator: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.internal.IdeaKt$isInIdeaEnvironment$2               : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.utils.FailuresKt$ideaSyncClasspathModeUtil$1        : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinToolingDiagnosticsCollector$Inject: loaded 2 times (x 92B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinJsKlibArtifactKt                    : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinTargetContainerWithWasmPresetFunctions    : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.GradleUtilsKt                                : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService          : loaded 2 times (x 72B)
Class org.objectweb.asm.Handler                                                       : loaded 2 times (x 68B)
Class com.google.common.collect.HashBiMap                                             : loaded 2 times (x 139B)
Class com.google.common.collect.MapMakerInternalMap                                   : loaded 2 times (x 157B)
Class com.google.common.base.Strings                                                  : loaded 2 times (x 67B)
Class com.google.common.collect.Sets                                                  : loaded 2 times (x 67B)
Class com.google.common.base.Ticker                                                   : loaded 2 times (x 68B)
Class com.google.common.base.CharMatcher$FastMatcher                                  : loaded 2 times (x 107B)
Class kotlin.jvm.functions.Function2                                                  : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinTarget$DefaultImpls                    : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$javaSources$1                   : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.incremental.IncrementalCompilationFeatures                 : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinToolingDiagnosticsCollector: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCreateCompilationArchiveTaskKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.tooling.core.MutableExtrasImpl                             : loaded 2 times (x 161B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinBuildStatsMXBean            : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.utils.FileUtilsKt                                   : loaded 2 times (x 67B)
Class com.google.common.collect.ImmutableSortedSet$Builder                            : loaded 2 times (x 82B)
Class [Lcom.google.common.cache.LocalCache$Segment;                                   : loaded 2 times (x 65B)
Class [Lcom.google.common.cache.LocalCache$Strength;                                  : loaded 2 times (x 65B)
Class com.google.common.cache.CacheBuilder$1                                          : loaded 2 times (x 81B)
Class com.google.common.collect.ImmutableMap                                          : loaded 2 times (x 116B)
Class com.google.common.base.Predicate                                                : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.statistics.AnonymizerUtilsKt                               : loaded 2 times (x 67B)
Class org.gradle.internal.classpath.DefaultClassPath$ImmutableUniqueList$Builder      : loaded 2 times (x 71B)
Class kotlin.jvm.functions.Function3                                                  : loaded 2 times (x 66B)
Class kotlin.collections.MapsKt___MapsJvmKt                                           : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinCompilationFactory$DefaultImpls    : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.InternalGradlePropertiesUsageChecker: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.CreateCInteropTasksSideEffectKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.metadata.KotlinMetadataTargetConfiguratorKt : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.testing.internal.KotlinTestsRegistry$Companion      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinCommonCompilerToolOptions                 : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$Companion$registerIfAbsent$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.internal.DefaultConfigurationCacheStartParameterAccessorVariantFactory: loaded 2 times (x 72B)
Class com.google.common.collect.MapMakerInternalMap$WeakValueReference                : loaded 2 times (x 66B)
Class com.google.common.collect.AbstractRangeSet                                      : loaded 2 times (x 110B)
Class com.google.common.cache.LocalCache                                              : loaded 2 times (x 183B)
Class com.google.common.cache.CacheBuilder$2                                          : loaded 2 times (x 75B)
Class com.google.common.base.CharMatcher$NegatedFastMatcher                           : loaded 2 times (x 109B)
Class com.google.gson.stream.JsonToken                                                : loaded 2 times (x 75B)
Class kotlin.collections.EmptySet                                                     : loaded 2 times (x 118B)
Class kotlin.collections.CollectionsKt___CollectionsKt                                : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.build.report.metrics.BuildPerformanceMetrics$Companion     : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.cli.common.arguments.K2JVMCompilerArguments                : loaded 2 times (x 77B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.DefaultKotlinCompilationSourceSetsContainer$source$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.internal.KotlinTestDependencyManagementKt           : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.NativeVersionChecker$runChecks$nativeVersion$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$ANDROID_X64                       : loaded 2 times (x 74B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.sources.ConsistencyCheck;                  : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.FlowActionBuildFusService$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.report.ReportingSettings                            : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.statistics.metrics.StringAnonymizationPolicy$ComponentVersionAnonymizer: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinBasePluginWrapper                      : loaded 2 times (x 82B)
Class com.google.common.cache.CacheBuilder$3                                          : loaded 2 times (x 68B)
Class [Lcom.google.common.collect.AbstractMapEntry;                                   : loaded 2 times (x 65B)
Class com.google.common.collect.ImmutableEntry                                        : loaded 2 times (x 78B)
Class com.google.common.collect.ArrayListMultimap                                     : loaded 2 times (x 168B)
Class org.gradle.api.GradleException                                                  : loaded 2 times (x 78B)
Class kotlin.reflect.KDeclarationContainer                                            : loaded 2 times (x 66B)
Class kotlin.text.StringsKt___StringsJvmKt                                            : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.NonSynchronizedMetricsContainer$MetricDescriptor: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinToolingDiagnostics$JsEnvironmentNotChosenExplicitly: loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.plugin.ProjectLocalConfigurations$ProjectLocalCompatibility: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinJvmCompilerOptions                        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.internal.DefaultMavenPublicationComponentAccessor: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.internal.DefaultProjectIsolationStartParameterAccessor$Factory: loaded 2 times (x 72B)
Class com.google.common.base.Suppliers$MemoizingSupplier                              : loaded 2 times (x 75B)
Class com.google.common.collect.CollectPreconditions                                  : loaded 2 times (x 67B)
Class kotlin.collections.MapsKt___MapsKt                                              : loaded 2 times (x 67B)
Class kotlin.io.FilesKt                                                               : loaded 2 times (x 67B)
Class kotlin.collections.ArraysKt___ArraysKt                                          : loaded 2 times (x 67B)
Class io.invertase.gradle.common.Utilities                                            : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.buildtools.api.SourcesChanges                              : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycleImpl$launch$1           : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.konan.target.HostManager$Companion$targetAliases$2         : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.utils.Completable                                   : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.DefaultKotlinBasePlugin$addKotlinCompilerConfiguration$2$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinBuildStatsServicesRegistry$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.internal.DefaultCompatibilityConventionRegistrar$Factory: loaded 2 times (x 72B)
Class [Lcom.google.common.collect.MapMakerInternalMap$Strength;                       : loaded 2 times (x 65B)
Class [Lcom.google.common.io.FileWriteMode;                                           : loaded 2 times (x 65B)
Class com.google.common.collect.SortedMapDifference                                   : loaded 2 times (x 66B)
Class kotlin.collections.MapsKt__MapWithDefaultKt                                     : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$DefaultJdkSetter   : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.cli.common.arguments.CommonCompilerArguments               : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.tooling.core.KotlinToolingVersion$Maturity                 : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.IncorrectCompileOnlyDependenciesChecker$runChecks$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinOnlyTarget                         : loaded 2 times (x 155B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.BaseNpmDependencyExtension           : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinVersion$Companion                         : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.InternalKotlinGradlePluginApi                       : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.targets.ConfigureNonPackedKlibConsumingSideEffectKt$ConfigureNonPackedKlibConsumingSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.dsl.DefaultToolchainSupport                         : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.internal.CustomPropertiesFileValueSource     : loaded 2 times (x 78B)
Class com.google.common.collect.Iterables$4                                           : loaded 2 times (x 77B)
Class com.google.common.collect.MapMakerInternalMap$StrongKeyDummyValueSegment        : loaded 2 times (x 138B)
Class com.google.common.cache.LocalCache$WriteThroughEntry                            : loaded 2 times (x 75B)
Class com.google.common.cache.LocalCache$HashIterator                                 : loaded 2 times (x 82B)
Class com.google.common.io.Files                                                      : loaded 2 times (x 67B)
Class com.google.common.cache.LocalCache$EntryFactory                                 : loaded 2 times (x 79B)
Class kotlin.collections.CollectionsKt__MutableCollectionsJVMKt                       : loaded 2 times (x 67B)
Class kotlin.enums.EnumEntriesKt                                                      : loaded 2 times (x 67B)
Class kotlin.text.StringsKt___StringsKt                                               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.TaskOutputsBackup                             : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.cocoapods.KotlinCocoapodsPlugin              : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.internal.CompilerOptionsDslHelpersKt                : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfigKt   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.StdlibDependencyManagementKt$configureStdlibVersionAlignment$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinProjectExtension$awaitSourceSets$1        : loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycle$CoroutineStart         : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.konan.target.Family                                        : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.TasksProviderKt$sam$org_gradle_api_Action$0   : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tooling.BuildKotlinToolingMetadataTask$FromKotlinExtension: loaded 2 times (x 313B)
Class org.jetbrains.kotlin.gradle.utils.FailuresKt                                    : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.native.internal.CInteropConfigurationsKt$special$$inlined$KotlinTargetSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesLoader$konanDataDirProperty$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.BuildFusService$Parameters_Decorated: loaded 2 times (x 139B)
Class org.jetbrains.kotlin.gradle.plugin.PropertiesProvider$keepIncrementalCompilationCachesInMemory$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$Params$Inject: loaded 2 times (x 102B)
Class org.jetbrains.kotlin.tooling.core.HasMutableExtras                              : loaded 2 times (x 66B)
Class com.google.common.collect.NullsLastOrdering                                     : loaded 2 times (x 111B)
Class [Lcom.google.common.collect.MapMaker$Dummy;                                     : loaded 2 times (x 65B)
Class com.google.common.cache.LocalCache$AbstractReferenceEntry                       : loaded 2 times (x 103B)
Class com.google.common.base.CharMatcher$Whitespace                                   : loaded 2 times (x 108B)
Class kotlin.text.StringsKt__StringsKt                                                : loaded 2 times (x 67B)
Class org.gradle.internal.IoActions                                                   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.statistics.BuildSession                                    : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.internal.Kapt3KotlinGradleSubpluginKt               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinJvmCompile                                : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$LINUX_ARM32_HFP                   : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.internal.Kapt3GradleSubplugin                       : loaded 2 times (x 79B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.CInteropInputChecker    : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.targets.native.internal.CInteropConfigurationsKt$special$$inlined$KotlinTargetSideEffect$2: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.FinalizeConfigurationFusMetricActionKt$FinalizeConfigurationFusMetricAction$1: loaded 2 times (x 91B)
Class org.jetbrains.kotlin.gradle.internal.KotlinDependenciesManagementKt$CustomizeKotlinDependenciesSetupAction$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradlePluginExtensionPointInternal$registeredExtensions$2: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.statistics.metrics.BooleanOverridePolicy$OR                : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.gradle.plugin.UsesVariantImplementationFactories           : loaded 2 times (x 66B)
Class org.objectweb.asm.SymbolTable                                                   : loaded 2 times (x 68B)
Class com.google.common.collect.ImmutableMapKeySet                                    : loaded 2 times (x 146B)
Class com.google.common.collect.ImmutableRangeSet$ComplementRanges                    : loaded 2 times (x 203B)
Class com.google.common.cache.CacheLoader$InvalidCacheLoadException                   : loaded 2 times (x 78B)
Class com.google.common.collect.ImmutableSet$RegularSetBuilderImpl                    : loaded 2 times (x 73B)
Class org.gradle.internal.classloader.SystemClassLoaderSpec                           : loaded 2 times (x 67B)
Class kotlin.reflect.KAnnotatedElement                                                : loaded 2 times (x 66B)
Class kotlin.jvm.internal.Intrinsics                                                  : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.CacheableTasksKt$cacheOnlyIfEnabledForKotlin$1$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$1$1     : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory$PostConfigure: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.utils.KotlinExtensionUtilsKt                        : loaded 2 times (x 67B)
Class [Lorg.jetbrains.kotlin.konan.util.Named;                                        : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.NoKotlinTargetsDeclaredChecker$runChecks$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.AbstractKotlinTarget$kotlinComponents$2  : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPlatformType$CompatibilityRule         : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesLoader$actualNativeHomeDirectory$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesKt              : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.BuildEventsListenerRegistryHolder            : loaded 2 times (x 68B)
Class [Lorg.jetbrains.kotlin.statistics.metrics.BooleanOverridePolicy;                : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.internal.CustomPropertiesFileValueSource$Parameters$Inject: loaded 2 times (x 102B)
Class com.google.common.collect.FluentIterable                                        : loaded 2 times (x 77B)
Class com.google.common.cache.LocalCache$StrongEntry                                  : loaded 2 times (x 104B)
Class com.google.common.util.concurrent.AbstractFuture$TrustedFuture                  : loaded 2 times (x 110B)
Class com.google.common.cache.LoadingCache                                            : loaded 2 times (x 66B)
Class kotlin.collections.MapsKt                                                       : loaded 2 times (x 67B)
Class kotlin.collections.CollectionsKt__IterablesKt                                   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.statistics.metrics.ConcatMetricContainer                   : loaded 2 times (x 73B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinTargetSoftwareComponentImplKt$KotlinTargetSoftwareComponent$1: loaded 2 times (x 91B)
Class org.jetbrains.kotlin.gradle.tasks.GradleCompileTaskProvider                     : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$registerBuildToolsApiTransformations$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfigKt$sam$org_gradle_api_Action$0: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.internal.BuildIdService                      : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationDependencyConfigurationsFactoriesKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.DefaultKotlinCompilationFriendPathsResolver: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.WasmSourceSetsNotFoundChecker$runChecks$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$IOS_SIMULATOR_ARM64               : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.targets.jvm.KotlinJvmCompilationWireJavaSourcesSideEffectKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCreateCompilationArchiveTaskKt$KotlinCreateCompilationArchivesTask$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinVersion                                   : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesLoader$actualNativeHomeDirectory$2: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.utils.StoredPropertyKt                              : loaded 2 times (x 67B)
Class org.objectweb.asm.MethodWriter                                                  : loaded 2 times (x 102B)
Class com.google.common.base.Suppliers$NonSerializableMemoizingSupplier               : loaded 2 times (x 75B)
Class com.google.common.io.ByteStreams$LimitedInputStream                             : loaded 2 times (x 88B)
Class [Lcom.google.common.cache.Weigher;                                              : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$Companion                         : loaded 2 times (x 67B)
Class kotlin.KotlinNullPointerException                                               : loaded 2 times (x 79B)
Class org.jetbrains.kotlin.gradle.internal.build.SourcesUtilsKt                       : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.sources.KotlinDependencyScope$Companion      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$1$3     : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$registerBuildToolsApiTransformations$2: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinJvmCompile                              : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationSourceSetsContainerKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycleKt$launchInStage$1      : loaded 2 times (x 91B)
Class org.jetbrains.kotlin.gradle.targets.NativeForwardImplementationToApiElementsSideEffectKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesLoader$actualNativeHomeDirectory$3: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.report.BuildReportMode                              : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.statistics.metrics.StringAnonymizationPolicy$RegexControlled: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.statistics.ValueAnonymizer                                 : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactories$Companion$getProvider$2$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.internal.ProjectIsolationStartParameterAccessor$Factory: loaded 2 times (x 66B)
Class com.google.common.collect.ImmutableMultimap$1                                   : loaded 2 times (x 78B)
Class com.google.common.collect.AbstractMapBasedMultimap$RandomAccessWrappedList      : loaded 2 times (x 201B)
Class Build_gradle$6$1                                                                : loaded 2 times (x 75B)
Class kotlin.ranges.RangesKt___RangesKt                                               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$disableMultiModuleIC$2$1        : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinBuildStatsLoggerService$reportBuildFinished$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$javaExecutable$1   : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.build.report.metrics.BuildAttribute                        : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.internal.transforms.BuildToolsApiClasspathEntrySnapshotTransform$Inject1: loaded 2 times (x 109B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationRegisterInSourceSetsConfigurator: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.DefaultKotlinCompilationTaskNamesContainerFactory: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.targets.native.internal.CommonizerTasksKt           : loaded 2 times (x 67B)
Class [Lorg.jetbrains.kotlin.gradle.targets.js.npm.NpmDependency$Scope;               : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilerPluginSupportPlugin            : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.AbstractKotlinTarget$components$2        : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.targets.CreateNonPackedKlibVariantsSideEffectKt$CreateNonPackedKlibVariantsSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinUsages$setupAttributesMatchingStrategy$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.PropertiesProvider$preciseCompilationResultsBackup$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.internal.diagnostics.AgpCompatibilityCheck$runAgpCompatibilityCheckIfAgpIsApplied$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.utils.AndroidPluginIdsKt                            : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinBasePlugin                             : loaded 2 times (x 66B)
Class com.google.common.collect.AbstractIterator$State                                : loaded 2 times (x 75B)
Class com.google.common.collect.ImmutableMultimap$2                                   : loaded 2 times (x 77B)
Class com.google.common.collect.RegularImmutableBiMap                                 : loaded 2 times (x 144B)
Class com.google.common.cache.LocalCache$ValueReference                               : loaded 2 times (x 66B)
Class com.google.common.base.CharMatcher$Negated                                      : loaded 2 times (x 109B)
Class com.google.common.base.CharMatcher$Is                                           : loaded 2 times (x 107B)
Class kotlin.collections.ArraysKt                                                     : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.internal.KotlinJvmOptionsCompat               : loaded 2 times (x 99B)
Class org.jetbrains.kotlin.cli.common.arguments.CommonToolArguments                   : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.utils.FutureKt                                      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.native.internal.PlatformLibrariesGenerator$GeneratedPlatformLibrariesService: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.logging.GradleLoggingUtilsKt                        : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinAndroidPluginWrapper                   : loaded 2 times (x 82B)
Class com.google.common.hash.PrimitiveSink                                            : loaded 2 times (x 66B)
Class com.google.common.util.concurrent.AbstractFuture$Failure                        : loaded 2 times (x 68B)
Class com.google.common.base.PatternCompiler                                          : loaded 2 times (x 66B)
Class com.google.common.base.Equivalence$Equals                                       : loaded 2 times (x 78B)
Class com.google.common.collect.Hashing                                               : loaded 2 times (x 67B)
Class com.google.common.base.Preconditions                                            : loaded 2 times (x 67B)
Class kotlin.collections.SetsKt__SetsKt                                               : loaded 2 times (x 67B)
Class kotlin.LazyKt                                                                   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.compilerRunner.GradleCompilerRunner$Companion              : loaded 2 times (x 67B)
Class [Lorg.jetbrains.kotlin.gradle.dsl.PlatformSourceSetConventionsChecker$CheckedPlatformInfo;: loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinGradleProjectCheckerContext: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.DuplicateSourceSetChecker: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.ToolingDiagnosticFactory         : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.targets.KotlinTargetSideEffect                      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinTargetContainerWithNativeShortcuts        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.DefaultKotlinBasePlugin$setupAttributeMatchingStrategy$1$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.statistics.metrics.NumericalMetrics$Companion              : loaded 2 times (x 67B)
Class [Lorg.jetbrains.kotlin.statistics.metrics.NumberAnonymizationPolicy;            : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.statistics.metrics.NumberOverridePolicy$OVERRIDE           : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$property$1: loaded 2 times (x 75B)
Class org.objectweb.asm.Label                                                         : loaded 2 times (x 69B)
Class org.objectweb.asm.CurrentFrame                                                  : loaded 2 times (x 69B)
Class org.objectweb.asm.ClassTooLargeException                                        : loaded 2 times (x 79B)
Class com.google.common.collect.ByFunctionOrdering                                    : loaded 2 times (x 111B)
Class com.google.common.collect.ImmutableSetMultimap                                  : loaded 2 times (x 176B)
Class com.google.common.io.CharSink                                                   : loaded 2 times (x 75B)
Class com.google.common.base.Joiner$1                                                 : loaded 2 times (x 76B)
Class com.google.common.collect.PeekingIterator                                       : loaded 2 times (x 66B)
Class com.google.common.base.CharMatcher                                              : loaded 2 times (x 107B)
Class com.google.gson.stream.JsonReader                                               : loaded 2 times (x 92B)
Class kotlin.collections.ArraysKt__ArraysJVMKt                                        : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig$2$2$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.build.report.metrics.BuildAttributes$Companion             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompilationTask                         : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory$ProcessResourcesTaskNameFactory: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$property$2: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.internal.KotlinDependenciesManagementKt             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.DefaultKotlinBasePlugin$setupAttributeMatchingStrategy$1$2: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.targets.native.internal.PlatformLibrariesGenerator$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinBuildStatsLoggerService     : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.internal.CustomPropertiesFileValueSource$Inject: loaded 2 times (x 109B)
Class org.objectweb.asm.Frame                                                         : loaded 2 times (x 69B)
Class org.objectweb.asm.MethodVisitor                                                 : loaded 2 times (x 101B)
Class com.google.common.io.ByteSource$AsCharSource                                    : loaded 2 times (x 82B)
Class com.google.common.collect.MapMakerInternalMap$InternalEntryHelper               : loaded 2 times (x 66B)
Class com.google.common.collect.Maps$IteratorBasedAbstractMap                         : loaded 2 times (x 121B)
Class com.google.common.collect.Table                                                 : loaded 2 times (x 66B)
Class com.google.common.collect.RangeGwtSerializationDependencies                     : loaded 2 times (x 67B)
Class com.google.common.base.Joiner$2                                                 : loaded 2 times (x 75B)
Class com.google.common.cache.LocalCache$EntryFactory$1                               : loaded 2 times (x 79B)
Class [Lcom.google.common.cache.RemovalListener;                                      : loaded 2 times (x 65B)
Class com.google.common.base.CharMatcher$1                                            : loaded 2 times (x 109B)
Class com.google.common.base.CharMatcher$NamedFastMatcher                             : loaded 2 times (x 108B)
Class kotlin.jvm.internal.CallableReference$NoReceiver                                : loaded 2 times (x 67B)
Class kotlin.text.StringsKt__StringBuilderKt                                          : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.InternalKotlinTargetKt$special$$inlined$extrasStoredFuture$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.compilerRunner.UsesCompilerSystemPropertiesService         : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationSourceSetInclusion$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$WATCHOS_DEVICE_ARM64              : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.MissingNativeStdlibChecker$runChecks$1: loaded 2 times (x 84B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.sources.android.AndroidVariantType;        : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.sources.android.AndroidVariantType           : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinTargetSoftwareComponent            : loaded 2 times (x 85B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinVariant                            : loaded 2 times (x 97B)
Class org.jetbrains.kotlin.gradle.targets.native.ConfigureFrameworkExportSideEffectKt : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.utils.WhenPluginEnabledKt                           : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.tooling.core.ExtrasProperty                                : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactories               : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.internal.diagnostics.KotlinCompilerEmbeddableCheck  : loaded 2 times (x 67B)
Class com.google.common.collect.RangeSet                                              : loaded 2 times (x 66B)
Class com.google.common.cache.LocalCache$EntryFactory$2                               : loaded 2 times (x 79B)
Class org.gradle.api.internal.classpath.GlobalCacheRootsProvider                      : loaded 2 times (x 66B)
Class kotlin.Lazy                                                                     : loaded 2 times (x 66B)
Class kotlin.NoWhenBranchMatchedException                                             : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.gradle.internal.StdlibDependencyManagementKt$WhenMappings  : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.tooling.core.Extras$Entry$Companion                        : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.js.dsl.KotlinWasmWasiTargetDsl              : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.DisabledNativeTargetsChecker: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCreateSourcesJarTaskSideEffectKt$KotlinCreateSourcesJarTaskSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.internal.CompilerArgumentAware                      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.utils.ReportUtilsKt                                 : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.utils.StoredLazyProperty                            : loaded 2 times (x 71B)
Class org.objectweb.asm.ModuleWriter                                                  : loaded 2 times (x 78B)
Class com.google.common.collect.CollectCollectors                                     : loaded 2 times (x 67B)
Class com.google.common.cache.LocalCache$EntryFactory$3                               : loaded 2 times (x 79B)
Class com.google.common.base.Ticker$1                                                 : loaded 2 times (x 68B)
Class com.google.common.collect.ImmutableMapEntrySet$RegularEntrySet                  : loaded 2 times (x 147B)
Class com.google.common.collect.Maps$BiMapConverter                                   : loaded 2 times (x 86B)
Class [Lkotlin.jvm.functions.Function1;                                               : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.tasks.K2MultiplatformStructure_Decorated            : loaded 2 times (x 131B)
Class org.jetbrains.kotlin.compilerRunner.GradleCompilerRunner                        : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.dsl.AndroidMainSourceSetConventionsChecker$runChecks$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.plugin.sources.FragmentConsistencyChecks            : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.InternalKotlinTargetComponent            : loaded 2 times (x 89B)
Class org.jetbrains.kotlin.gradle.targets.CreateNonPackedKlibVariantsSideEffectKt     : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.CreateDefaultTestRunSideEffectKt$special$$inlined$KotlinTargetSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.utils.ProviderApiUtilsKt                            : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.native.UsesKonanPropertiesBuildService      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesLoader$forceDisableRunningInProcess$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.utils.PersistentCachesKt                            : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.tasks.TaskWithLocalState                   : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinBuildStatisticsUtilsKt      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.statistics.metrics.BooleanMetrics                          : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$Params   : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.internal.diagnostics.GradleCompatibilityCheck       : loaded 2 times (x 67B)
Class com.google.common.collect.CollectSpliterators$1                                 : loaded 2 times (x 86B)
Class com.google.common.collect.SetMultimap                                           : loaded 2 times (x 66B)
Class com.google.common.cache.LocalCache$EntryFactory$4                               : loaded 2 times (x 79B)
Class com.google.common.collect.ImmutableSet$Builder                                  : loaded 2 times (x 81B)
Class com.google.common.base.CharMatcher$InRange                                      : loaded 2 times (x 107B)
Class kotlin.sequences.ConstrainedOnceSequence                                        : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.sources.KotlinDependencyScopeKt$WhenMappings : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.K2MultiplatformCompilationTask                : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.OverriddenKotlinNativeHomeChecker$StdlibExistenceCheckerValueSource$Inject: loaded 2 times (x 105B)
Class org.jetbrains.kotlin.gradle.utils.ConfigurationsKt$maybeCreateDependencyScope$1 : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.sources.FragmentConsistencyChecks$languageVersionCheck$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.DecoratedKotlinCompilation               : loaded 2 times (x 187B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.ExperimentalTryNextUsageChecker: loaded 2 times (x 70B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.diagnostics.ToolingDiagnostic$Severity;    : loaded 2 times (x 65B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.KotlinPlatformType;                        : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.UsesBuildFusService               : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.old.Pre232IdeaKotlinBuildStatsBeanService: loaded 2 times (x 91B)
Class org.jetbrains.kotlin.statistics.metrics.NumberOverridePolicy$AVERAGE            : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactories$Parameters$Inject: loaded 2 times (x 102B)
Class org.jetbrains.kotlin.gradle.internal.diagnostics.GradleCompatibilityCheck$CurrentGradleVersionProvider: loaded 2 times (x 66B)
Class com.google.common.collect.ReverseOrdering                                       : loaded 2 times (x 111B)
Class com.google.common.io.ByteStreams$1                                              : loaded 2 times (x 81B)
Class com.google.common.cache.LocalCache$EntryFactory$5                               : loaded 2 times (x 79B)
Class kotlin.jvm.internal.markers.KMappedMarker                                       : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.MppSourcesJarKt$sourcesJarTaskNamed$result$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$1       : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.UsesBuildFinishedListenerService             : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.ConfigurationNaming$Default: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationSourceSetInclusion: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.targets.jvm.KotlinJvmTarget                         : loaded 2 times (x 168B)
Class org.jetbrains.kotlin.gradle.targets.js.dsl.KotlinWasmJsTargetDsl                : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.DisabledNativeTargetsChecker$runChecks$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycleImpl$await$2$1          : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.sources.DefaultLanguageSettingsBuilder       : loaded 2 times (x 96B)
Class org.jetbrains.kotlin.gradle.plugin.sources.FragmentConsistencyChecks$languageVersionCheck$2: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.targets.CreateTargetConfigurationsSideEffectKt$CreateTargetConfigurationsSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinTargetContainerWithPresetFunctions        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinPublishingDsl                             : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.utils.GradleConfigurationUtilsKt                    : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.internal.CustomPropertiesFileValueSource$Parameters: loaded 2 times (x 66B)
Class com.google.common.collect.HashMultimap                                          : loaded 2 times (x 170B)
Class com.google.common.cache.LocalCache$EntryFactory$6                               : loaded 2 times (x 79B)
Class kotlin.NotImplementedError                                                      : loaded 2 times (x 78B)
Class org.gradle.internal.installation.CurrentGradleInstallationLocator               : loaded 2 times (x 67B)
Class build_9kn57pj55amg75ewdgalkhlsr$_run_closure1                                   : loaded 2 times (x 136B)
Class org.jetbrains.kotlin.statistics.metrics.SumMetricContainer                      : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$getToolsJarFromJvm$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.build.report.metrics.GcMetrics                             : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.UsesKotlinToolingDiagnosticsKt   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$2       : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.BuildFinishedListenerService$Companion$registerIfAbsent$2$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$IOS_X64                           : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.internal.IsInIdeaEnvironmentValueSource$Inject      : loaded 2 times (x 96B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.InternalKotlinCompilation                : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.utils.WhenEvaluatedKt                               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.native.internal.AddKotlinPlatformIntegersSupportLibraryKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.AbstractKotlinJsPluginWrapper                : loaded 2 times (x 82B)
Class org.jetbrains.kotlin.gradle.plugin.internal.MavenPublicationComponentAccessor   : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.fus.BuildUidService                                 : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginWrapperKt                        : loaded 2 times (x 67B)
Class com.google.common.io.ByteSource$SlicedByteSource                                : loaded 2 times (x 81B)
Class com.google.common.collect.ImmutableMultimap$Builder                             : loaded 2 times (x 79B)
Class com.google.common.collect.Maps$KeySet                                           : loaded 2 times (x 133B)
Class com.google.common.cache.LocalCache$EntryFactory$7                               : loaded 2 times (x 79B)
Class com.google.common.collect.ImmutableSet$EmptySetBuilderImpl                      : loaded 2 times (x 72B)
Class com.google.common.collect.ImmutableList                                         : loaded 2 times (x 202B)
Class org.gradle.internal.service.DefaultServiceLocator                               : loaded 2 times (x 79B)
Class build_agr3p375fzty99zrloowe6ju6$_run_closure1$_closure4                         : loaded 2 times (x 135B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$scriptExtensions$1              : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.K2MultiplatformStructure$Fragment             : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.compilerRunner.CompilerSystemPropertiesService$Companion$registerIfAbsent$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.util.capitalizeDecapitalize.CapitalizeDecapitalizeKt       : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.WasmJsEnvironmentChecker: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.AndroidPluginWithoutAndroidTargetChecker: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.FlowActionBuildFusService$getConfigurationTimeMetrics$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.SynchronizedMetricsContainer      : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.statistics.metrics.NumberOverridePolicy                    : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.statistics.metrics.StringAnonymizationPolicy$AllowedListAnonymizer$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.internal.DefaultProjectIsolationStartParameterAccessor_Decorated: loaded 2 times (x 121B)
Class org.jetbrains.kotlin.gradle.plugin.internal.ProjectIsolationStartParameterAccessorKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.testing.internal.KotlinTestsRegistry                : loaded 2 times (x 68B)
Class com.google.common.cache.LocalCache$SoftValueReference                           : loaded 2 times (x 93B)
Class com.google.common.collect.ImmutableMultimap$EntryCollection                     : loaded 2 times (x 123B)
Class com.google.common.collect.Maps$EntrySet                                         : loaded 2 times (x 132B)
Class com.google.common.cache.LocalCache$EntryFactory$8                               : loaded 2 times (x 79B)
Class com.google.common.collect.ImmutableSet$SetBuilderImpl                           : loaded 2 times (x 72B)
Class org.gradle.internal.installation.CurrentGradleInstallation                      : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.InternalKotlinCompilationKt$awaitAllKotlinSourceSets$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompilerExecutionStrategy               : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.FusMetrics                        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.sources.DefaultKotlinSourceSet$special$$inlined$Iterable$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory$KotlinCompilerOptionsFactory$Options: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilation                            : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinTargetConfiguratorKt                   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.native.internal.PlatformLibrariesGenerator  : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.tooling.core.Extras$Type$Companion                         : loaded 2 times (x 67B)
Class com.google.common.cache.CacheLoader$1                                           : loaded 2 times (x 71B)
Class com.google.common.collect.ImmutableList$SubList                                 : loaded 2 times (x 204B)
Class org.gradle.util.internal.GUtil                                                  : loaded 2 times (x 67B)
Class kotlin.io.NoSuchFileException                                                   : loaded 2 times (x 78B)
Class kotlin.collections.CollectionsKt                                                : loaded 2 times (x 67B)
Class build_9cu49dh6h7478zg7wrc3wnbxn$_run_closure1                                   : loaded 2 times (x 136B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.InternalKotlinTargetKt$isSourcesPublishableFuture$2: loaded 2 times (x 91B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinCompilationFactory                 : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.dsl.IosSourceSetConventionChecker$runChecks$1       : loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.AbstractKotlinCompilation                : loaded 2 times (x 187B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.UnusedSourceSetsChecker : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.utils.Future                                        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinBuildStatsConfiguration$Companion: loaded 2 times (x 67B)
Class settings_4rq5cjrcdj0mscfkcpdclh2wk                                              : loaded 2 times (x 175B)
Class com.google.common.io.ByteSource                                                 : loaded 2 times (x 80B)
Class com.google.common.cache.AbstractCache$StatsCounter                              : loaded 2 times (x 66B)
Class build_9cu49dh6h7478zg7wrc3wnbxn$_run_closure2                                   : loaded 2 times (x 136B)
Class io.invertase.gradle.common.PackageJson                                          : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.gradle.plugin.attributes.KlibPackaging                     : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinTargetSoftwareComponentImplKt      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.js.dsl.KotlinJsSubTargetContainerDsl        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.tooling.core.MutableExtrasImpl$Companion                   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.ClassLoadersCachingBuildService$Parameters : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleBuildServices$Companion$registerIfAbsent$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.statistics.fileloggers.MetricsContainer                    : loaded 2 times (x 73B)
Class org.jetbrains.kotlin.gradle.plugin.internal.ProjectIsolationStartParameterAccessor: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.utils.StoredProperty                                : loaded 2 times (x 66B)
Class com.google.common.io.LineBuffer                                                 : loaded 2 times (x 71B)
Class com.google.common.collect.MapMakerInternalMap$1                                 : loaded 2 times (x 79B)
Class com.google.common.collect.Iterators$MergingIterator                             : loaded 2 times (x 77B)
Class com.google.common.collect.RegularImmutableSet                                   : loaded 2 times (x 144B)
Class org.gradle.internal.installation.GradleInstallation                             : loaded 2 times (x 71B)
Class kotlin.collections.CollectionsKt__IteratorsJVMKt$iterator$1                     : loaded 2 times (x 75B)
Class build_eupcmxgtu9q3uy0v8rg9z8i7j                                                 : loaded 2 times (x 187B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinJvmCompile$DefaultImpls                   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory$create$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinJavaToolchain                           : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.FlowActionBuildFusService$Inject  : loaded 2 times (x 100B)
Class org.jetbrains.kotlin.statistics.metrics.BooleanMetrics$Companion                : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.internal.CompatibilityConventionRegistrar    : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.AndroidGradlePluginVersion                   : loaded 2 times (x 71B)
Class com.google.common.collect.LexicographicalOrdering                               : loaded 2 times (x 111B)
Class com.google.common.collect.Iterables                                             : loaded 2 times (x 67B)
Class com.google.common.base.Splitter$Strategy                                        : loaded 2 times (x 66B)
Class kotlin.jvm.KotlinReflectionNotSupportedError                                    : loaded 2 times (x 78B)
Class build_6k1vuntnni446s9ebkvsvp2hk                                                 : loaded 2 times (x 179B)
Class io.invertase.gradle.build.ProjectExtension$setProject$0                         : loaded 2 times (x 148B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinJvmCompilation                     : loaded 2 times (x 196B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilation$Companion                  : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$WATCHOS_ARM64                     : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$MACOS_ARM64                       : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.OverriddenKotlinNativeHomeChecker: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinJvmJarArtifactKt$KotlinJvmJarArtifact$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinLegacyCompatibilityMetadataArtifactKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.native.SetupEmbedAndSignAppleFrameworkTaskSideEffectKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinProjectSetupActionKt$KotlinProjectSetupCoroutine$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.dsl.ToolchainSupport$Companion                      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinSourceSet                              : loaded 2 times (x 66B)
Class com.google.common.collect.Lists$RandomAccessReverseList                         : loaded 2 times (x 196B)

---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
OS uptime: 14 days 22:00 hours
Hyper-V role detected

CPU: total 4 (initial active 4) (4 cores per cpu, 1 threads per core) family 6 model 58 stepping 9 microcode 0x21, cx8, cmov, fxsr, mmx, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, tsc, tscinvbit, avx, aes, erms, clmul, vzeroupper, clflush, hv, rdtscp, f16c
Processor Information for all 4 processors :
  Max Mhz: 3101, Current Mhz: 3101, Mhz Limit: 3101

Memory: 4k page, system-wide physical 8161M (391M free)
TotalPageFile size 32737M (AvailPageFile size 96M)
current process WorkingSet (physical memory assigned to process): 1254M, peak: 1344M
current process commit charge ("private bytes"): 1662M, peak: 1773M

vm_info: OpenJDK 64-Bit Server VM (21.0.5+-13047016-b750.29) for windows-amd64 JRE (21.0.5+-13047016-b750.29), built on 2025-02-11T21:12:39Z by "builder" with MS VC++ 16.10 / 16.11 (VS2019)

END.
