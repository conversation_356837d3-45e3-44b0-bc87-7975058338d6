#!/bin/bash

# Script para gerar build de produção para Google Play Store
# Este script garante que os anúncios estejam configurados para produção

echo "🚀 Iniciando build de produção para Google Play Store..."

# Verificar se estamos no diretório correto
if [ ! -f "package.json" ]; then
    echo "❌ Erro: Execute este script na raiz do projeto React Native"
    exit 1
fi

# Verificar se o Android SDK está configurado
if [ -z "$ANDROID_HOME" ]; then
    echo "❌ Erro: ANDROID_HOME não está configurado"
    echo "Configure o Android SDK antes de continuar"
    exit 1
fi

echo "📋 Verificando configurações de anúncios..."

# Verificar se os anúncios estão configurados para produção
if grep -q "USE_PRODUCTION: true" src/config/ads.ts; then
    echo "✅ Anúncios configurados para PRODUÇÃO"
else
    echo "❌ Erro: Anúncios não estão configurados para produção"
    echo "Verifique src/config/ads.ts e defina USE_PRODUCTION: true"
    exit 1
fi

echo "🧹 Limpando cache e dependências..."

# Limpar cache do React Native
npx react-native start --reset-cache &
METRO_PID=$!
sleep 3
kill $METRO_PID

# Limpar cache do npm
npm cache clean --force

# Limpar build anterior do Android
cd android
./gradlew clean
cd ..

echo "📦 Instalando dependências..."
npm install

echo "🔧 Gerando bundle de produção..."

# Gerar bundle JavaScript para produção
npx react-native bundle \
    --platform android \
    --dev false \
    --entry-file index.js \
    --bundle-output android/app/src/main/assets/index.android.bundle \
    --assets-dest android/app/src/main/res

echo "🏗️ Compilando APK de produção..."

# Compilar APK de release
cd android
./gradlew assembleRelease
cd ..

echo "📱 Gerando AAB para Google Play Store..."

# Gerar Android App Bundle (AAB) para Google Play
cd android
./gradlew bundleRelease
cd ..

echo "✅ Build de produção concluído!"
echo ""
echo "📁 Arquivos gerados:"
echo "   APK: android/app/build/outputs/apk/release/app-release.apk"
echo "   AAB: android/app/build/outputs/bundle/release/app-release.aab"
echo ""
echo "📋 Próximos passos:"
echo "1. Teste o APK em dispositivos reais"
echo "2. Verifique se os anúncios estão funcionando"
echo "3. Faça upload do AAB para o Google Play Console"
echo ""
echo "⚠️  IMPORTANTE:"
echo "   - Os anúncios estão configurados para PRODUÇÃO"
echo "   - Teste em dispositivos reais antes de publicar"
echo "   - Verifique se todos os anúncios carregam corretamente"
