import React, { useState, useEffect, useCallback } from 'react';
import { View, Text, TouchableOpacity, SafeAreaView, ScrollView, Modal, FlatList, Platform, useWindowDimensions, ActivityIndicator, StyleSheet } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { colors } from '../../theme/colors';
import { fonts } from '../../theme/fonts';
import RenderHTML from 'react-native-render-html';
import Slider from '@react-native-community/slider';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useNavigation, useRoute, useFocusEffect } from '@react-navigation/native';
import { useAuth } from '../../context/AuthContext';
import { listarLicaoDia } from '../../services/licao';
import { LicaoDia } from '../../models/LicaoDia';
import { useLesson } from '../../context/LessonContext';
import { getTrimestreAtual } from '../../services/trimestre';
import { getLicaoDaSemana } from '../../services/licao';
import { Trimestre } from '../../models/Trimestre';
import { Licao } from '../../models/Licao';

const DAYS = [
  'Domingo, 7 de Julho',
  'Segunda-feira, 8 de Julho',
  'Terça-feira, 9 de Julho',
  'Quarta-feira, 10 de Julho',
  'Quinta-feira, 11 de Julho',
  'Sexta-feira, 12 de Julho',
  'Sábado, 13 de Julho',
];

// Função mock para buscar o HTML da lição de cada dia
const fetchLessonContent = (dayIndex: number, lessonGuid?: string, isDarkTheme?: boolean): string => {
  const htmlSamples = [
    `<h2>Domingo</h2><p>Bem-vindo à lição de domingo!</p><p><i>João 3:16</i> - Porque Deus amou o mundo...</p>`,
    `<h2>Segunda-feira</h2><p>Estudo para segunda-feira.</p><p><i>Salmo 23:1</i> - O Senhor é o meu pastor...</p>`,
    `<h2>Terça-feira</h2><p>Conteúdo da terça-feira.</p><p><i>Mateus 5:9</i> - Bem-aventurados os pacificadores...</p>`,
    `<h2>Quarta-feira</h2><p>Reflexão de quarta-feira.</p><p><i>Romanos 8:28</i> - Todas as coisas cooperam para o bem...</p>`,
    `<h2>Quinta-feira</h2><p>Estudo de quinta-feira.</p><p><i>Filipenses 4:13</i> - Tudo posso naquele que me fortalece.</p>`,
    `<h2>Sexta-feira</h2><p>Preparação para o sábado.</p><p><i>Gênesis 2:2</i> - E havendo Deus terminado...</p>`,
    `<h2>Sábado</h2><p>Dia especial de adoração.</p><p><i>Êxodo 20:8</i> - Lembra-te do dia de sábado...</p>`,
  ];

  let content = htmlSamples[dayIndex] || '<p>Conteúdo não disponível.</p>';

  // Se há um GUID da lição, adicionar informações específicas
  if (lessonGuid) {
    const bgColor = isDarkTheme ? '#2D2D2D' : '#f0f0f0';
    const textColor = isDarkTheme ? '#FFFFFF' : '#000000';

    content += `<div style="margin-top: 20px; padding: 10px; background-color: ${bgColor}; color: ${textColor}; border-radius: 5px;">
      <h3 style="color: ${textColor};">📖 Lição Específica</h3>
      <p style="color: ${textColor};"><strong>GUID:</strong> ${lessonGuid}</p>
      <p style="color: ${textColor};"><em>Esta lição foi carregada através do botão "Continuar Lendo"</em></p>
    </div>`;
  }

  return content;
};

const LessonScreen = () => {
  const { colors, theme } = useTheme();
  const navigation = useNavigation();
  const route = useRoute();
  const insets = useSafeAreaInsets();
  const { currentLessonGuid, setCurrentLesson } = useLesson();
  const { token } = useAuth();

  // Calcular o dia atual da semana (0 = Domingo, 6 = Sábado)
  const getCurrentDayIndex = () => {
    const today = new Date().getDay(); // 0 = Domingo, 1 = Segunda, etc.
    console.log('📅 Dia atual da semana:', today, 'Nome:', DAYS[today]);
    return today; // Retorna o índice do dia atual
  };

  const [currentDayIndex, setCurrentDayIndex] = useState(getCurrentDayIndex());
  const [lessonData, setLessonData] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [trimestre, setTrimestre] = useState<Trimestre | null>(null);
  const [licaoSemana, setLicaoSemana] = useState<Licao | null>(null);

  // Debug do tema e parâmetros
  console.log('📖 LessonScreen - Tema atual:', theme);
  console.log('📖 LessonScreen - Cores:', {
    background: colors.background,
    text: colors.text,
    card: colors.card,
    primary: colors.primary
  });
  console.log('📖 LessonScreen - Parâmetros da rota:', route.params);

  // Hook para anúncios intersticiais
  const { showInterstitialAd } = useInterstitialAd({
    showOnNavigate: false, // Controle manual
  });

  // useEffect único para gerenciar todo o fluxo de carregamento
  useEffect(() => {
    const loadLessonContent = async () => {
      console.log('📖 LessonScreen useEffect executado - currentDayIndex:', currentDayIndex);
      console.log('📖 Parâmetros da rota completos:', JSON.stringify(route.params, null, 2));

      setLoading(true);

      // Verificar se há parâmetros da rota (guid da lição)
      const params = route.params as any;
      let lessonGuid = params?.guid || params?.params?.guid || currentLessonGuid;

      console.log('📖 Estado inicial:', {
        'params?.guid': params?.guid,
        'params?.params?.guid': params?.params?.guid,
        'currentLessonGuid': currentLessonGuid,
        'lessonGuid inicial': lessonGuid,
        'token': !!token
      });

      // Se não há GUID e há token, buscar lição atual
      if (!lessonGuid && token) {
        try {
          console.log('🔍 Buscando trimestre e lição atual...');

          // Buscar trimestre
          const trimestreAtual = await getTrimestreAtual(token);
          if (trimestreAtual) {
            console.log('📖 Trimestre encontrado:', trimestreAtual.guid);
            setTrimestre(trimestreAtual);

            // Buscar lição da semana
            const licaoAtual = await getLicaoDaSemana(token, trimestreAtual.guid);
            if (licaoAtual) {
              console.log('📖 Lição encontrada:', licaoAtual.title, 'GUID:', licaoAtual.guid);
              setLicaoSemana(licaoAtual);
              setCurrentLesson(licaoAtual);
              lessonGuid = licaoAtual.guid;
            }
          }
        } catch (error) {
          console.error('❌ Erro ao buscar lição:', error);
        }
      }

      // Determinar fonte do GUID
      const fonte = params?.guid ? 'direto' :
                   params?.params?.guid ? 'aninhado' :
                   currentLessonGuid ? 'contexto' :
                   lessonGuid ? 'busca local' : 'nenhum';

      console.log('📖 GUID final:', lessonGuid, 'Fonte:', fonte);

      // Carregar conteúdo
      try {
        const content = fetchLessonContent(currentDayIndex, lessonGuid, theme === 'dark');
        console.log('📖 Conteúdo carregado, tamanho:', content.length);

        if (!content || content.trim().length === 0) {
          console.log('⚠️ Conteúdo vazio, carregando conteúdo padrão');
          const defaultContent = `
            <h2>${DAYS[currentDayIndex]}</h2>
            <p>Bem-vindo ao estudo da lição de hoje!</p>
            <p>Este é o conteúdo da lição para <strong>${DAYS[currentDayIndex]}</strong>.</p>
            <h3>Versículo do Dia</h3>
            <p><em>"Lâmpada para os meus pés é tua palavra, e luz para o meu caminho." - Salmos 119:105</em></p>
            <h3>Reflexão</h3>
            <p>A Palavra de Deus nos guia em cada passo da nossa jornada espiritual. Que possamos sempre buscar Sua orientação em nossas vidas.</p>
          `;
          setLessonData(defaultContent);
        } else {
          setLessonData(content);
        }

        setLoading(false);
        console.log('✅ Lição carregada com sucesso');
      } catch (error) {
        console.error('❌ Erro ao carregar conteúdo:', error);
        setLessonData(`
          <h2>Erro ao Carregar Lição</h2>
          <p>Não foi possível carregar o conteúdo da lição.</p>
          <p>Por favor, tente novamente mais tarde.</p>
        `);
        setLoading(false);
      }
    };

    loadLessonContent();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentDayIndex, route.params, theme, currentLessonGuid, token]);

  // Garantir que a tela sempre carregue quando ganha foco
  useFocusEffect(
    useCallback(() => {
      console.log('📖 LessonScreen ganhou foco');

      // Se não há dados carregados, forçar carregamento
      if (!lessonData || lessonData.trim().length === 0) {
        console.log('📖 Forçando carregamento por falta de dados');
        setLoading(true);

        setTimeout(() => {
          const params = route.params as any;
          const lessonGuid = params?.guid || params?.params?.guid;
          const content = fetchLessonContent(currentDayIndex, lessonGuid, theme === 'dark');
          setLessonData(content);
          setLoading(false);
        }, 100);
      }
    }, [lessonData, currentDayIndex, route.params, theme])
  );

  const goToPreviousDay = useCallback(async () => {
    console.log('⬅️ Tentando ir para o dia anterior. Índice atual:', currentDayIndex);
    if (currentDayIndex > 0) {
      // Mostrar anúncio ocasionalmente ao navegar entre dias
      if (Math.random() < 0.3) { // 30% de chance
        await showInterstitialAd();
      }
      const newIndex = currentDayIndex - 1;
      console.log('⬅️ Mudando para o dia:', newIndex, 'Nome:', DAYS[newIndex]);
      setCurrentDayIndex((prev) => prev - 1);
    } else {
      console.log('⬅️ Já está no primeiro dia da semana');
    }
  }, [currentDayIndex, showInterstitialAd]);

  const goToNextDay = useCallback(async () => {
    console.log('➡️ Tentando ir para o próximo dia. Índice atual:', currentDayIndex);
    if (currentDayIndex < DAYS.length - 1) {
      // Mostrar anúncio ocasionalmente ao navegar entre dias
      if (Math.random() < 0.3) { // 30% de chance
        await showInterstitialAd();
      }
      const newIndex = currentDayIndex + 1;
      console.log('➡️ Mudando para o dia:', newIndex, 'Nome:', DAYS[newIndex]);
      setCurrentDayIndex((prev) => prev + 1);
    } else {
      console.log('➡️ Já está no último dia da semana');
    }
  }, [currentDayIndex, showInterstitialAd]);

  // Placeholder para título da lição
  const lessonTitle = 'Lição 1 - A Graça Transformadora';

  // Estilos dinâmicos baseados no tema
  const styles = {
    safeArea: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      backgroundColor: colors.card,
      paddingHorizontal: 16,
      paddingTop: 8,
      paddingBottom: 12,
      flexDirection: 'row' as const,
      alignItems: 'center' as const,
      justifyContent: 'space-between' as const,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
      elevation: 2,
      zIndex: 2,
    },
    headerLeft: {
      flexDirection: 'row' as const,
      alignItems: 'center' as const,
      flex: 1,
    },
    lessonTitle: {
      fontFamily: fonts.bold,
      fontSize: 16,
      color: colors.text,
      marginBottom: 2,
    },
    dayNavContainer: {
      flexDirection: 'row' as const,
      alignItems: 'center' as const,
    },
    dayText: {
      fontFamily: fonts.regular,
      fontSize: 13,
      color: colors.textSecondary,
    },
    arrowButton: {
      padding: 8,
      marginHorizontal: 8,
    },
    summaryButton: {
      padding: 8,
    },
    contentContainer: {
      flex: 1,
      backgroundColor: colors.background,
    },
    fabContainer: {
      position: 'absolute' as const,
      right: 20,
      alignItems: 'center' as const,
    },
    fabButton: {
      backgroundColor: colors.primary,
      borderRadius: 28,
      width: 56,
      height: 56,
      alignItems: 'center' as const,
      justifyContent: 'center' as const,
      elevation: 8,
      shadowColor: colors.shadow,
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.3,
      shadowRadius: 8,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center' as const,
      alignItems: 'center' as const,
      backgroundColor: colors.background,
      paddingHorizontal: 20,
    },
    loadingText: {
      marginTop: 16,
      fontSize: 16,
      color: colors.textSecondary,
      fontFamily: fonts.regular,
      textAlign: 'center' as const,
    },
  };

  return (
    <SafeAreaView style={[styles.safeArea, { paddingTop: insets.top }]}>
      <StatusBar
        barStyle={colors.text === '#FFFFFF' ? 'light-content' : 'dark-content'}
        backgroundColor={colors.background}
      />
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <TouchableOpacity onPress={goToPreviousDay} disabled={currentDayIndex === 0} style={styles.arrowButton}>
            <Icon name="chevron-left" size={28} color={currentDayIndex === 0 ? colors.border : colors.primary} />
          </TouchableOpacity>
          <View>
            <Text style={styles.lessonTitle}>{lessonTitle}</Text>
            <View style={styles.dayNavContainer}>
              <Text style={styles.dayText}>{DAYS[currentDayIndex]}</Text>
            </View>
          </View>
          <TouchableOpacity onPress={goToNextDay} disabled={currentDayIndex === DAYS.length - 1} style={styles.arrowButton}>
            <Icon name="chevron-right" size={28} color={currentDayIndex === DAYS.length - 1 ? colors.border : colors.primary} />
          </TouchableOpacity>
        </View>
        <TouchableOpacity style={styles.summaryButton} onPress={() => {/* Futuro: abrir sumário */}}>
          <Icon name="menu-book" size={26} color={colors.primary} />
        </TouchableOpacity>
      </View>

      {/* Banner de Anúncio */}
      <AdBanner
        size={BannerAdSize.BANNER}
        style={{ marginVertical: 8 }}
      />

      {/* Conteúdo da Lição */}
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingText}>
            Carregando lição...{'\n'}
            {DAYS[currentDayIndex]}
          </Text>
        </View>
      ) : (
        <ScrollView style={styles.contentContainer} contentContainerStyle={{ paddingBottom: 80 }}>
          <LessonContentRenderer
            html={lessonData}
            onLinkPress={(_evt, href) => console.log('Link clicado:', href)}
          />
        </ScrollView>
      )}

      {/* Barra de ferramentas flutuante (Ajustes de leitura) */}
      <View style={[styles.fabContainer, { bottom: insets.bottom + 24 }]}> 
        <TouchableOpacity style={styles.fabButton} onPress={() => {/* Futuro: abrir modal de ajustes */}}>
          <Icon name="format-size" size={24} color={colors.background} />
        </TouchableOpacity>
      </View>

      {/* Comentário: Aqui será implementada a lógica de destaques/anotações ao selecionar texto */}
    </SafeAreaView>
  );
};

export default LessonScreen; 