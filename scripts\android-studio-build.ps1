# Script para gerar build compatível com Android Studio
# Este script prepara o projeto para ser aberto no Android Studio

Write-Host "🏗️ Preparando build para Android Studio..." -ForegroundColor Green

# Verificar se estamos no diretório correto
if (-not (Test-Path "package.json")) {
    Write-Host "❌ Erro: Execute este script na raiz do projeto React Native" -ForegroundColor Red
    exit 1
}

# Verificar Android SDK
if (-not $env:ANDROID_HOME) {
    Write-Host "❌ Erro: ANDROID_HOME não está configurado" -ForegroundColor Red
    Write-Host "Configure o Android SDK antes de continuar" -ForegroundColor Yellow
    exit 1
}

Write-Host "📦 Instalando dependências..." -ForegroundColor Cyan
npm install

Write-Host "🧹 Limpando cache..." -ForegroundColor Cyan
npx react-native start --reset-cache &
$metroPid = $LASTEXITCODE
Start-Sleep -Seconds 3
if ($metroPid) { Stop-Process -Id $metroPid -ErrorAction SilentlyContinue }

Write-Host "🔧 Gerando bundle JavaScript..." -ForegroundColor Cyan

# Criar diretório de assets se não existir
$assetsDir = "android/app/src/main/assets"
if (-not (Test-Path $assetsDir)) {
    New-Item -ItemType Directory -Path $assetsDir -Force
}

# Gerar bundle para release
npx react-native bundle `
    --platform android `
    --dev false `
    --entry-file index.js `
    --bundle-output android/app/src/main/assets/index.android.bundle `
    --assets-dest android/app/src/main/res

Write-Host "🏗️ Preparando build Android..." -ForegroundColor Cyan

# Navegar para pasta android
Set-Location android

# Limpar build anterior
if (Test-Path "gradlew.bat") {
    Write-Host "🧹 Limpando build anterior..." -ForegroundColor Yellow
    .\gradlew.bat clean
} else {
    Write-Host "🧹 Limpando build anterior..." -ForegroundColor Yellow
    .\gradlew clean
}

Write-Host "📱 Gerando APK de Release..." -ForegroundColor Cyan
if (Test-Path "gradlew.bat") {
    .\gradlew.bat assembleRelease
} else {
    .\gradlew assembleRelease
}

Write-Host "📦 Gerando AAB para Google Play..." -ForegroundColor Cyan
if (Test-Path "gradlew.bat") {
    .\gradlew.bat bundleRelease
} else {
    .\gradlew bundleRelease
}

# Voltar para raiz
Set-Location ..

Write-Host "✅ Build concluído com sucesso!" -ForegroundColor Green
Write-Host ""
Write-Host "📁 Arquivos gerados:" -ForegroundColor Cyan
Write-Host "   📱 APK: android/app/build/outputs/apk/release/app-release.apk" -ForegroundColor White
Write-Host "   📦 AAB: android/app/build/outputs/bundle/release/app-release.aab" -ForegroundColor White
Write-Host ""
Write-Host "🏗️ Para abrir no Android Studio:" -ForegroundColor Cyan
Write-Host "   1. Abrir Android Studio" -ForegroundColor White
Write-Host "   2. Open an Existing Project" -ForegroundColor White
Write-Host "   3. Selecionar pasta: $PWD\android" -ForegroundColor White
Write-Host "   4. Build > Build Bundle(s) / APK(s)" -ForegroundColor White
Write-Host ""
Write-Host "📱 Para instalar APK:" -ForegroundColor Cyan
Write-Host "   adb install android/app/build/outputs/apk/release/app-release.apk" -ForegroundColor White
Write-Host ""
Write-Host "⚠️  Lembrete:" -ForegroundColor Yellow
Write-Host "   - Anúncios configurados para PRODUÇÃO" -ForegroundColor White
Write-Host "   - Teste em dispositivo real antes de publicar" -ForegroundColor White
