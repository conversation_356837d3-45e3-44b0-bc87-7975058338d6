import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, SafeAreaView, Modal, FlatList, Platform, useWindowDimensions, ActivityIndicator } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { colors } from '../theme/colors';
import { fonts } from '../theme/fonts';
import RenderHTML from 'react-native-render-html';
import Slider from '@react-native-community/slider';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useRoute, useNavigation } from '@react-navigation/native';
import { useAuth } from '../context/AuthContext';
import { useLesson } from '../context/LessonContext';
import { listarLicaoDia, getLicaoDaSemana } from '../services/licao';
import { getTrimestreAtual } from '../services/trimestre';
import { LicaoDia } from '../models/LicaoDia';
import { Trimestre } from '../models/Trimestre';
import { Licao } from '../models/Licao';

const FONT_SIZE_KEY = 'lesson_font_size';

const LessonScreen = () => {
  const route = useRoute();
  const navigation = useNavigation();
  const { guid } = (route.params as { guid?: string }) || {};
  const { token } = useAuth();
  const { currentLessonGuid, setCurrentLesson } = useLesson();
  const [licaoDias, setLicaoDias] = useState<LicaoDia[]>([]);
  const [loading, setLoading] = useState(true);
  const [currentDay, setCurrentDay] = useState(0);
  const [showSummary, setShowSummary] = useState(false);
  const [showReadingModal, setShowReadingModal] = useState(false);
  const [fontSize, setFontSize] = useState(16);
  const [theme, setTheme] = useState<'light' | 'dark'>('light');
  const { width } = useWindowDimensions();
  const [trimestre, setTrimestre] = useState<Trimestre | null>(null);
  const [licaoSemana, setLicaoSemana] = useState<Licao | null>(null);

  useEffect(() => {
    const loadLessonContent = async () => {
      if (!token) {
        setLoading(false);
        return;
      }

      // Verificar se há GUID nos parâmetros (direto ou aninhado) ou no contexto
      const params = route.params as any;
      let lessonGuid = guid || params?.params?.guid || currentLessonGuid;

      console.log('📖 LessonScreen - GUID inicial:', lessonGuid);

      // Se não há GUID, buscar lição atual da semana
      if (!lessonGuid) {
        try {
          console.log('🔍 Buscando lição atual da semana...');

          const trimestreAtual = await getTrimestreAtual(token);
          if (trimestreAtual) {
            setTrimestre(trimestreAtual);
            const licaoAtual = await getLicaoDaSemana(token, trimestreAtual.guid);
            if (licaoAtual) {
              setLicaoSemana(licaoAtual);
              setCurrentLesson(licaoAtual);
              lessonGuid = licaoAtual.guid;
              console.log('📖 Lição encontrada:', licaoAtual.title, 'GUID:', licaoAtual.guid);
            }
          }
        } catch (error) {
          console.error('❌ Erro ao buscar lição:', error);
        }
      }

      // Carregar dias da lição
      if (lessonGuid) {
        try {
          const dias = await listarLicaoDia(token, lessonGuid);
          setLicaoDias(dias);
          setCurrentDay(0);
          console.log('✅ Dias da lição carregados:', dias.length);
        } catch (error) {
          console.error('❌ Erro ao carregar dias:', error);
        }
      }

      setLoading(false);
    };

    loadLessonContent();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [token, guid, currentLessonGuid]);

  // Carregar fontSize salvo
  useEffect(() => {
    AsyncStorage.getItem(FONT_SIZE_KEY).then(val => {
      if (val) setFontSize(Number(val));
    });
  }, []);

  // Salvar fontSize sempre que mudar
  useEffect(() => {
    AsyncStorage.setItem(FONT_SIZE_KEY, String(fontSize));
  }, [fontSize]);

  // Funções para ajuste fino
  const increaseFont = () => setFontSize(f => Math.min(f + 1, 24));
  const decreaseFont = () => setFontSize(f => Math.max(f - 1, 14));

  // Função utilitária para obter o nome do dia da semana
  const getWeekDay = (dateStr: string) => {
    const dias = ['Domingo', 'Segunda-feira', 'Terça-feira', 'Quarta-feira', 'Quinta-feira', 'Sexta-feira', 'Sábado'];
    const d = new Date(dateStr);
    return dias[d.getDay()];
  };

  // Intercepta links de versículos
  const handleLinkPress = (evt: any, href: any) => {
    if (href.startsWith('bible://')) {
      console.log('📖 Versículo clicado:', href.replace('bible://', ''));
      return;
    }
  };

  // Estilos customizados para tags HTML
  const tagsStyles = {
    p: {
      fontFamily: fonts.regular,
      fontSize: fontSize,
      color: theme === 'light' ? colors.text : '#fff',
      lineHeight: fontSize + 10,
      marginBottom: 12,
    },
    h2: {
      fontFamily: fonts.bold,
      fontSize: fontSize + 2,
      color: colors.primary,
      marginTop: 18,
      marginBottom: 8,
    },
    i: {
      fontStyle: 'italic' as 'italic',
      color: colors.accent,
    },
    strong: {
      fontFamily: fonts.bold,
      color: colors.accent,
    },
    a: {
      color: colors.accent,
      textDecorationLine: 'underline' as 'underline',
    },
  };

  if (loading) {
    return <ActivityIndicator style={{ flex: 1, marginTop: 40 }} size="large" />;
  }

  if (!licaoDias.length) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <Text style={{ color: 'red', fontSize: 16 }}>Nenhum conteúdo encontrado para esta lição.</Text>
      </View>
    );
  }

  const current = licaoDias[currentDay];

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: theme === 'light' ? colors.background : '#222' }}>
      {/* Header Fixo */}
      <View style={styles.headerShadow}>
        <View style={styles.header}>
          <TouchableOpacity style={styles.headerBtn} onPress={() => navigation.goBack()}>
            <Icon name="arrow-left" size={26} color={colors.primary} />
          </TouchableOpacity>
          <Text style={styles.headerTitle} numberOfLines={1} ellipsizeMode="tail">
            {getWeekDay(current.data)}
          </Text>
          <TouchableOpacity style={styles.headerBtn} onPress={() => setShowSummary(true)}>
            <Icon name="menu" size={26} color={colors.primary} />
          </TouchableOpacity>
        </View>
        {/* Navegação por Dia */}
        <View style={styles.dayNav}>
          <TouchableOpacity onPress={() => setCurrentDay((prev) => Math.max(0, prev - 1))} disabled={currentDay === 0} style={{ opacity: currentDay === 0 ? 0.3 : 1 }}>
            <Icon name="chevron-left" size={28} color={colors.textSecondary} />
          </TouchableOpacity>
          <Text style={styles.dayNavText} numberOfLines={1} ellipsizeMode="tail">
            {current.titulo} - {new Date(current.data).toLocaleDateString()}
          </Text>
          <TouchableOpacity onPress={() => setCurrentDay((prev) => Math.min(licaoDias.length - 1, prev + 1))} disabled={currentDay === licaoDias.length - 1} style={{ opacity: currentDay === licaoDias.length - 1 ? 0.3 : 1 }}>
            <Icon name="chevron-right" size={28} color={colors.textSecondary} />
          </TouchableOpacity>
        </View>
      </View>
      {/* Modal de Sumário/Índice */}
      <Modal visible={showSummary} animationType="slide" transparent onRequestClose={() => setShowSummary(false)}>
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Dias da Lição</Text>
            <FlatList
              data={licaoDias}
              keyExtractor={item => item.guid}
              renderItem={({ item, index }) => (
                <TouchableOpacity
                  style={[styles.modalItem, index === currentDay && styles.modalItemActive]}
                  onPress={() => { setCurrentDay(index); setShowSummary(false); }}
                >
                  <Text style={[styles.modalItemText, index === currentDay && styles.modalItemTextActive]}>{`${item.titulo} - ${new Date(item.data).toLocaleDateString()}`}</Text>
                </TouchableOpacity>
              )}
            />
            <TouchableOpacity style={styles.modalCloseBtn} onPress={() => setShowSummary(false)}>
              <Text style={styles.modalCloseText}>Fechar</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
      {/* Modal de Ajustes de Leitura */}
      <Modal visible={showReadingModal} animationType="slide" transparent onRequestClose={() => setShowReadingModal(false)}>
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.sheetTitle}>Ajustes de Leitura</Text>
            <Text style={styles.sheetLabel}>Tamanho da Fonte</Text>
            <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 8 }}>
              <TouchableOpacity onPress={decreaseFont} style={{ padding: 8 }}>
                <Icon name="minus-circle-outline" size={24} color={colors.primary} />
              </TouchableOpacity>
              <Slider
                style={{ flex: 1, height: 40 }}
                minimumValue={14}
                maximumValue={24}
                step={1}
                value={fontSize}
                onValueChange={setFontSize}
                minimumTrackTintColor={colors.primary}
                maximumTrackTintColor={colors.border}
                thumbTintColor={colors.primary}
              />
              <TouchableOpacity onPress={increaseFont} style={{ padding: 8 }}>
                <Icon name="plus-circle-outline" size={24} color={colors.primary} />
              </TouchableOpacity>
            </View>
            <Text style={styles.sheetValue}>{fontSize}px</Text>
            <Text style={styles.sheetLabel}>Tema</Text>
            <View style={{ flexDirection: 'row', marginTop: 8 }}>
              <TouchableOpacity
                style={[styles.themeBtn, theme === 'light' && styles.themeBtnActive]}
                onPress={() => setTheme('light')}
              >
                <Icon name="white-balance-sunny" size={22} color={theme === 'light' ? '#fff' : colors.primary} />
                <Text style={[styles.themeBtnText, theme === 'light' && { color: '#fff' }]}>Claro</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.themeBtn, theme === 'dark' && styles.themeBtnActive]}
                onPress={() => setTheme('dark')}
              >
                <Icon name="weather-night" size={22} color={theme === 'dark' ? '#fff' : colors.primary} />
                <Text style={[styles.themeBtnText, theme === 'dark' && { color: '#fff' }]}>Escuro</Text>
              </TouchableOpacity>
            </View>
            <TouchableOpacity style={styles.modalCloseBtn} onPress={() => setShowReadingModal(false)}>
              <Text style={styles.modalCloseText}>Fechar</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
      {/* Conteúdo da Lição */}
      <ScrollView contentContainerStyle={styles.contentContainer} showsVerticalScrollIndicator={false}>
        <RenderHTML
          contentWidth={width - 40}
          source={{ html: current.content }}
          tagsStyles={tagsStyles}
          renderersProps={{ a: { onPress: handleLinkPress } }}
        />
      </ScrollView>
      {/* Barra de ferramentas fixa */}
      <View style={styles.toolbar}>
        <TouchableOpacity style={styles.toolbarBtn} onPress={() => setShowReadingModal(true)}>
          <Icon name="format-size" size={22} color={colors.primary} />
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  headerShadow: {
    backgroundColor: colors.card,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.10,
    shadowRadius: 8,
    elevation: 4,
    zIndex: 10,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingTop: Platform.OS === 'android' ? 12 : 16,
    paddingBottom: 8,
    backgroundColor: colors.card,
  },
  headerBtn: {
    padding: 4,
  },
  headerTitle: {
    flex: 1,
    fontFamily: fonts.bold,
    fontSize: 18,
    color: colors.primary,
    textAlign: 'center',
    marginHorizontal: 8,
  },
  dayNav: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingBottom: 8,
    backgroundColor: colors.card,
  },
  dayNavText: {
    fontFamily: fonts.regular,
    fontSize: 15,
    color: colors.textSecondary,
    marginHorizontal: 12,
    maxWidth: 220,
    textAlign: 'center',
  },
  contentContainer: {
    padding: 20,
    paddingBottom: 40,
  },
  lessonText: {
    fontFamily: fonts.regular,
    fontSize: 16,
    color: colors.text,
    lineHeight: 26,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.25)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '85%',
    backgroundColor: colors.card,
    borderRadius: 16,
    padding: 20,
    alignItems: 'center',
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 8,
  },
  modalTitle: {
    fontFamily: fonts.bold,
    fontSize: 18,
    color: colors.primary,
    marginBottom: 12,
  },
  modalItem: {
    paddingVertical: 10,
    paddingHorizontal: 8,
    borderRadius: 8,
    marginBottom: 4,
    width: '100%',
  },
  modalItemActive: {
    backgroundColor: colors.accentBlue + '22',
  },
  modalItemText: {
    fontFamily: fonts.regular,
    fontSize: 15,
    color: colors.text,
    textAlign: 'center',
  },
  modalItemTextActive: {
    color: colors.primary,
    fontFamily: fonts.bold,
  },
  modalCloseBtn: {
    marginTop: 16,
    paddingVertical: 8,
    paddingHorizontal: 24,
    borderRadius: 8,
    backgroundColor: colors.primary,
  },
  modalCloseText: {
    color: '#fff',
    fontFamily: fonts.bold,
    fontSize: 15,
  },
  toolbar: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: colors.card,
    paddingVertical: 8,
    paddingHorizontal: 16,
    flexDirection: 'row',
    justifyContent: 'space-around',
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 3,
    zIndex: 9,
  },
  toolbarBtn: {
    padding: 8,
  },
  sheetTitle: {
    fontFamily: fonts.bold,
    fontSize: 20,
    color: colors.primary,
    marginBottom: 15,
    textAlign: 'center',
  },
  sheetLabel: {
    fontFamily: fonts.regular,
    fontSize: 16,
    color: colors.textSecondary,
    marginBottom: 8,
  },
  sheetValue: {
    fontFamily: fonts.bold,
    fontSize: 18,
    color: colors.primary,
    textAlign: 'center',
    marginTop: 5,
  },
  themeBtn: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 15,
    borderRadius: 20,
    marginVertical: 5,
  },
  themeBtnActive: {
    backgroundColor: colors.primary,
  },
  themeBtnText: {
    fontFamily: fonts.regular,
    fontSize: 14,
    marginLeft: 8,
    color: colors.primary,
  },
});

export default LessonScreen; 