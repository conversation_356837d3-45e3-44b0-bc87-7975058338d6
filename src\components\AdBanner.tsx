import React, { useState } from 'react';
import { View, StyleSheet, Platform } from 'react-native';
import { BannerAd, BannerAdSize } from 'react-native-google-mobile-ads';
import { getAdUnitId } from '../config/ads';

interface AdBannerProps {
  size?: BannerAdSize;
  style?: any;
}

const AdBanner: React.FC<AdBannerProps> = ({ 
  size = BannerAdSize.BANNER, 
  style 
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);

  // Use configuração centralizada de anúncios
  const adUnitId = getAdUnitId('banner');

  const handleAdLoaded = () => {
    setIsLoaded(true);
    setHasError(false);
    console.log('🎯 Banner Ad carregado com sucesso');
  };

  const handleAdFailedToLoad = (error: any) => {
    setHasError(true);
    setIsLoaded(false);
    console.error('❌ Erro ao carregar Banner Ad:', error);
  };

  // Se houve erro, não renderizar nada
  if (hasError) {
    return null;
  }

  return (
    <View style={[styles.container, style]}>
      <BannerAd
        unitId={adUnitId}
        size={size}
        requestOptions={{
          requestNonPersonalizedAdsOnly: false,
        }}
        onAdLoaded={handleAdLoaded}
        onAdFailedToLoad={handleAdFailedToLoad}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#f5f5f5',
    paddingVertical: 4,
  },
});

export default AdBanner;
