# 🔥 Configuração do Firebase Messaging

## 📋 **O que foi implementado:**

### ✅ **Dependências instaladas:**
- `@react-native-firebase/app`
- `@react-native-firebase/messaging`

### ✅ **AndroidManifest.xml configurado:**
- Google Ads Application ID
- Firebase Messaging Service
- Ícone e cor padrão das notificações
- Canal de notificação padrão

### ✅ **Serviço Firebase criado:**
- `FirebaseMessagingService.ts` com todas as funcionalidades
- Auto-inicialização habilitada
- Listeners de mensagens configurados
- Gerenciamento de tokens FCM

### ✅ **App.tsx atualizado:**
- Inicialização automática do Firebase
- Inscrição em tópicos padrão

## 🚀 **Para finalizar a configuração:**

### **1. Criar projeto no Firebase Console:**
1. Acesse: https://console.firebase.google.com/
2. Clique em "Adicionar projeto"
3. Nome: "Escola Sabatina" (ou o que preferir)
4. Habilite Google Analytics (opcional)

### **2. Adicionar app Android:**
1. No projeto Firebase, clique em "Adicionar app" → Android
2. **Nome do pacote**: `com.escolasabatina` (ou o que está no seu projeto)
3. **Apelido do app**: "Escola Sabatina Android"
4. **Certificado SHA-1**: (opcional para desenvolvimento)

### **3. Baixar google-services.json:**
1. Baixe o arquivo `google-services.json`
2. Coloque em: `android/app/google-services.json`

### **4. Habilitar Cloud Messaging:**
1. No Firebase Console, vá em "Cloud Messaging"
2. Anote o "Server Key" (para enviar notificações)

## 📱 **Funcionalidades implementadas:**

### **🔔 Notificações Push:**
- Recebimento em foreground, background e app fechado
- Auto-inicialização habilitada
- Ícone e cores personalizadas

### **📊 Tópicos:**
- `escola_sabatina_geral` - Notificações gerais
- `licoes_semanais` - Lições da semana

### **🎯 Navegação:**
- Abertura de telas específicas via notificação
- Dados customizados nas mensagens

### **📱 Token Management:**
- Obtenção automática do token FCM
- Refresh automático do token
- Pronto para envio ao servidor

## 🧪 **Para testar:**

### **1. Compile o projeto:**
```bash
npx react-native run-android
```

### **2. Verifique os logs:**
Procure por:
```
🔥 Inicializando Firebase Messaging...
📱 FCM Token: [seu-token]
✅ Firebase Messaging inicializado com sucesso!
```

### **3. Teste notificação:**
No Firebase Console → Cloud Messaging → "Enviar primeira mensagem"

## 📤 **Enviar notificações:**

### **Via Firebase Console:**
1. Cloud Messaging → "Nova campanha"
2. Escolha o app
3. Configure título e mensagem
4. Envie

### **Via API (Server Key):**
```bash
curl -X POST https://fcm.googleapis.com/fcm/send \
  -H "Authorization: key=SEU_SERVER_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "to": "TOKEN_DO_DISPOSITIVO",
    "notification": {
      "title": "Escola Sabatina",
      "body": "Nova lição disponível!"
    },
    "data": {
      "screen": "lesson",
      "lessonId": "123"
    }
  }'
```

## 🎯 **Próximos passos:**
1. Criar projeto no Firebase
2. Baixar google-services.json
3. Testar notificações
4. Implementar envio do servidor
