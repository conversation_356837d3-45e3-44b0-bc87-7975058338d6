import { useEffect, useState } from 'react';
import messaging from '@react-native-firebase/messaging';
import { Alert } from 'react-native';

interface UseFirebaseMessagingReturn {
  fcmToken: string | null;
  hasPermission: boolean;
  isLoading: boolean;
  subscribeToTopic: (topic: string) => Promise<void>;
  unsubscribeFromTopic: (topic: string) => Promise<void>;
}

export const useFirebaseMessaging = (): UseFirebaseMessagingReturn => {
  const [fcmToken, setFcmToken] = useState<string | null>(null);
  const [hasPermission, setHasPermission] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    initializeMessaging();
  }, []);

  const initializeMessaging = async () => {
    try {
      setIsLoading(true);

      // Verificar e solicitar permissão
      const permission = await requestPermission();
      setHasPermission(permission);

      if (permission) {
        // Obter token FCM
        const token = await getFCMToken();
        setFcmToken(token);

        // Configurar listeners
        setupMessageListeners();
      }
    } catch (error) {
      console.error('❌ Erro ao inicializar messaging:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const requestPermission = async (): Promise<boolean> => {
    try {
      const authStatus = await messaging().requestPermission();
      const enabled =
        authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
        authStatus === messaging.AuthorizationStatus.PROVISIONAL;

      if (enabled) {
        console.log('✅ Permissão para notificações concedida');
        return true;
      } else {
        console.log('❌ Permissão para notificações negada');
        return false;
      }
    } catch (error) {
      console.error('❌ Erro ao solicitar permissão:', error);
      return false;
    }
  };

  const getFCMToken = async (): Promise<string | null> => {
    try {
      const token = await messaging().getToken();
      console.log('📱 Token FCM:', token);
      return token;
    } catch (error) {
      console.error('❌ Erro ao obter token FCM:', error);
      return null;
    }
  };

  const setupMessageListeners = () => {
    // Mensagem em foreground
    const unsubscribeForeground = messaging().onMessage(async remoteMessage => {
      console.log('📨 Mensagem em foreground:', remoteMessage);
      
      if (remoteMessage.notification) {
        Alert.alert(
          remoteMessage.notification.title || 'Escola Sabatina',
          remoteMessage.notification.body || 'Nova mensagem'
        );
      }
    });

    // App aberto por notificação (background)
    const unsubscribeBackground = messaging().onNotificationOpenedApp(remoteMessage => {
      console.log('📨 App aberto por notificação:', remoteMessage);
    });

    // Token refresh
    const unsubscribeTokenRefresh = messaging().onTokenRefresh(token => {
      console.log('🔄 Token atualizado:', token);
      setFcmToken(token);
    });

    // Cleanup function
    return () => {
      unsubscribeForeground();
      unsubscribeBackground();
      unsubscribeTokenRefresh();
    };
  };

  const subscribeToTopic = async (topic: string): Promise<void> => {
    try {
      await messaging().subscribeToTopic(topic);
      console.log(`✅ Inscrito no tópico: ${topic}`);
    } catch (error) {
      console.error(`❌ Erro ao se inscrever no tópico ${topic}:`, error);
      throw error;
    }
  };

  const unsubscribeFromTopic = async (topic: string): Promise<void> => {
    try {
      await messaging().unsubscribeFromTopic(topic);
      console.log(`✅ Desinscrito do tópico: ${topic}`);
    } catch (error) {
      console.error(`❌ Erro ao se desinscrever do tópico ${topic}:`, error);
      throw error;
    }
  };

  return {
    fcmToken,
    hasPermission,
    isLoading,
    subscribeToTopic,
    unsubscribeFromTopic,
  };
};
