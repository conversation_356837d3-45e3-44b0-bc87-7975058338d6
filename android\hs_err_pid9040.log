#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 65536 bytes. Error detail: Failed to commit metaspace.
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (virtualSpaceNode.cpp:113), pid=9040, tid=12916
#
# JRE version: OpenJDK Runtime Environment (21.0.5) (build 21.0.5+-13047016-b750.29)
# Java VM: OpenJDK 64-Bit Server VM (21.0.5+-13047016-b750.29, mixed mode, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: org.jetbrains.kotlin.cli.jvm.K2JVMCompiler @F:\PROJETO\React\EscolaSabatina\node_modules\react-native-screens\android\build\20250805_6879488832816659327.compiler.options

Host: Intel(R) Xeon(R) CPU E3-1220 V2 @ 3.10GHz, 4 cores, 7G,  Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
Time: Tue Aug  5 15:34:46 2025 Hora oficial do Brasil elapsed time: 0.551801 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x000001b39d9c3230):  JavaThread "main"             [_thread_in_vm, id=12916, stack(0x00000042fca00000,0x00000042fcb00000) (1024K)]

Stack: [0x00000042fca00000,0x00000042fcb00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6cfb29]
V  [jvm.dll+0x85df93]
V  [jvm.dll+0x8604ee]
V  [jvm.dll+0x860bd3]
V  [jvm.dll+0x27e6b6]
V  [jvm.dll+0x858e6e]
V  [jvm.dll+0x673d95]
V  [jvm.dll+0x1e3111]
V  [jvm.dll+0x1e2ede]
V  [jvm.dll+0x6766e8]
V  [jvm.dll+0x676522]
V  [jvm.dll+0x67478e]
V  [jvm.dll+0x3beaaf]
V  [jvm.dll+0x20cdbb]
V  [jvm.dll+0x5b1d8f]
V  [jvm.dll+0x7d5f50]
V  [jvm.dll+0x7d607e]
V  [jvm.dll+0x46ebdd]
V  [jvm.dll+0x474528]
C  [java.dll+0x17ec]

Java frames: (J=compiled Java code, j=interpreted, Vv=VM code)
j  java.lang.ClassLoader.defineClass1(Ljava/lang/ClassLoader;Ljava/lang/String;[BIILjava/security/ProtectionDomain;Ljava/lang/String;)Ljava/lang/Class;+0 java.base@21.0.5
j  java.lang.ClassLoader.defineClass(Ljava/lang/String;[BIILjava/security/ProtectionDomain;)Ljava/lang/Class;+27 java.base@21.0.5
j  java.security.SecureClassLoader.defineClass(Ljava/lang/String;[BIILjava/security/CodeSource;)Ljava/lang/Class;+12 java.base@21.0.5
j  jdk.internal.loader.BuiltinClassLoader.defineClass(Ljava/lang/String;Ljdk/internal/loader/Resource;)Ljava/lang/Class;+117 java.base@21.0.5
j  jdk.internal.loader.BuiltinClassLoader.findClassOnClassPathOrNull(Ljava/lang/String;)Ljava/lang/Class;+37 java.base@21.0.5
j  jdk.internal.loader.BuiltinClassLoader.loadClassOrNull(Ljava/lang/String;Z)Ljava/lang/Class;+111 java.base@21.0.5
j  jdk.internal.loader.BuiltinClassLoader.loadClass(Ljava/lang/String;Z)Ljava/lang/Class;+3 java.base@21.0.5
j  jdk.internal.loader.ClassLoaders$AppClassLoader.loadClass(Ljava/lang/String;Z)Ljava/lang/Class;+36 java.base@21.0.5
j  java.lang.ClassLoader.loadClass(Ljava/lang/String;)Ljava/lang/Class;+3 java.base@21.0.5
v  ~StubRoutines::call_stub 0x000001b3aa25100d
j  java.lang.ClassLoader.defineClass1(Ljava/lang/ClassLoader;Ljava/lang/String;[BIILjava/security/ProtectionDomain;Ljava/lang/String;)Ljava/lang/Class;+0 java.base@21.0.5
j  java.lang.ClassLoader.defineClass(Ljava/lang/String;[BIILjava/security/ProtectionDomain;)Ljava/lang/Class;+27 java.base@21.0.5
j  java.security.SecureClassLoader.defineClass(Ljava/lang/String;[BIILjava/security/CodeSource;)Ljava/lang/Class;+12 java.base@21.0.5
j  jdk.internal.loader.BuiltinClassLoader.defineClass(Ljava/lang/String;Ljdk/internal/loader/Resource;)Ljava/lang/Class;+117 java.base@21.0.5
j  jdk.internal.loader.BuiltinClassLoader.findClassOnClassPathOrNull(Ljava/lang/String;)Ljava/lang/Class;+37 java.base@21.0.5
j  jdk.internal.loader.BuiltinClassLoader.loadClassOrNull(Ljava/lang/String;Z)Ljava/lang/Class;+111 java.base@21.0.5
j  jdk.internal.loader.BuiltinClassLoader.loadClass(Ljava/lang/String;Z)Ljava/lang/Class;+3 java.base@21.0.5
j  jdk.internal.loader.ClassLoaders$AppClassLoader.loadClass(Ljava/lang/String;Z)Ljava/lang/Class;+36 java.base@21.0.5
j  java.lang.ClassLoader.loadClass(Ljava/lang/String;)Ljava/lang/Class;+3 java.base@21.0.5
v  ~StubRoutines::call_stub 0x000001b3aa25100d
j  java.lang.ClassLoader.defineClass1(Ljava/lang/ClassLoader;Ljava/lang/String;[BIILjava/security/ProtectionDomain;Ljava/lang/String;)Ljava/lang/Class;+0 java.base@21.0.5
j  java.lang.ClassLoader.defineClass(Ljava/lang/String;[BIILjava/security/ProtectionDomain;)Ljava/lang/Class;+27 java.base@21.0.5
j  java.security.SecureClassLoader.defineClass(Ljava/lang/String;[BIILjava/security/CodeSource;)Ljava/lang/Class;+12 java.base@21.0.5
j  jdk.internal.loader.BuiltinClassLoader.defineClass(Ljava/lang/String;Ljdk/internal/loader/Resource;)Ljava/lang/Class;+117 java.base@21.0.5
j  jdk.internal.loader.BuiltinClassLoader.findClassOnClassPathOrNull(Ljava/lang/String;)Ljava/lang/Class;+37 java.base@21.0.5
j  jdk.internal.loader.BuiltinClassLoader.loadClassOrNull(Ljava/lang/String;Z)Ljava/lang/Class;+111 java.base@21.0.5
j  jdk.internal.loader.BuiltinClassLoader.loadClass(Ljava/lang/String;Z)Ljava/lang/Class;+3 java.base@21.0.5
j  jdk.internal.loader.ClassLoaders$AppClassLoader.loadClass(Ljava/lang/String;Z)Ljava/lang/Class;+36 java.base@21.0.5
j  java.lang.ClassLoader.loadClass(Ljava/lang/String;)Ljava/lang/Class;+3 java.base@21.0.5
v  ~StubRoutines::call_stub 0x000001b3aa25100d
j  org.jetbrains.kotlin.cli.common.CompilerSystemProperties.getValue()Ljava/lang/String;+4
j  org.jetbrains.kotlin.cli.common.CLICompiler$Companion.doMain(Lorg/jetbrains/kotlin/cli/common/CLICompiler;[Ljava/lang/String;)V+31
j  org.jetbrains.kotlin.cli.jvm.K2JVMCompiler$Companion.main([Ljava/lang/String;)V+20
j  org.jetbrains.kotlin.cli.jvm.K2JVMCompiler.main([Ljava/lang/String;)V+4
v  ~StubRoutines::call_stub 0x000001b3aa25100d

---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000001b3b6d618a0, length=12, elements={
0x000001b39d9c3230, 0x000001b3b6bc6660, 0x000001b3b6bc7370, 0x000001b3b6baaa30,
0x000001b3b6ba7400, 0x000001b3b6ba7f70, 0x000001b3b6ba89c0, 0x000001b3b6ba9740,
0x000001b3b6baf8a0, 0x000001b3b6d63dd0, 0x000001b3b6d66850, 0x000001b3b6db8e10
}

Java Threads: ( => current thread )
=>0x000001b39d9c3230 JavaThread "main"                              [_thread_in_vm, id=12916, stack(0x00000042fca00000,0x00000042fcb00000) (1024K)]
  0x000001b3b6bc6660 JavaThread "Reference Handler"          daemon [_thread_blocked, id=5008, stack(0x00000042fd300000,0x00000042fd400000) (1024K)]
  0x000001b3b6bc7370 JavaThread "Finalizer"                  daemon [_thread_blocked, id=12684, stack(0x00000042fd400000,0x00000042fd500000) (1024K)]
  0x000001b3b6baaa30 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=1372, stack(0x00000042fd500000,0x00000042fd600000) (1024K)]
  0x000001b3b6ba7400 JavaThread "Attach Listener"            daemon [_thread_blocked, id=6316, stack(0x00000042fd600000,0x00000042fd700000) (1024K)]
  0x000001b3b6ba7f70 JavaThread "Service Thread"             daemon [_thread_blocked, id=12000, stack(0x00000042fd700000,0x00000042fd800000) (1024K)]
  0x000001b3b6ba89c0 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=2476, stack(0x00000042fd800000,0x00000042fd900000) (1024K)]
  0x000001b3b6ba9740 JavaThread "C2 CompilerThread0"         daemon [_thread_blocked, id=1600, stack(0x00000042fd900000,0x00000042fda00000) (1024K)]
  0x000001b3b6baf8a0 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=756, stack(0x00000042fda00000,0x00000042fdb00000) (1024K)]
  0x000001b3b6d63dd0 JavaThread "Notification Thread"        daemon [_thread_blocked, id=4460, stack(0x00000042fdb00000,0x00000042fdc00000) (1024K)]
  0x000001b3b6d66850 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=14276, stack(0x00000042fdc00000,0x00000042fdd00000) (1024K)]
  0x000001b3b6db8e10 JavaThread "C2 CompilerThread1"         daemon [_thread_blocked, id=14968, stack(0x00000042fdd00000,0x00000042fde00000) (1024K)]
Total: 12

Other Threads:
  0x000001b3b6a37840 VMThread "VM Thread"                           [id=10316, stack(0x00000042fd200000,0x00000042fd300000) (1024K)]
  0x000001b3b699f1e0 WatcherThread "VM Periodic Task Thread"        [id=292, stack(0x00000042fd100000,0x00000042fd200000) (1024K)]
  0x000001b39fc17a30 WorkerThread "GC Thread#0"                     [id=13752, stack(0x00000042fcc00000,0x00000042fcd00000) (1024K)]
  0x000001b39fc28960 ConcurrentGCThread "G1 Main Marker"            [id=12484, stack(0x00000042fcd00000,0x00000042fce00000) (1024K)]
  0x000001b39fc2a470 WorkerThread "G1 Conc#0"                       [id=3212, stack(0x00000042fce00000,0x00000042fcf00000) (1024K)]
  0x000001b39fc8e980 ConcurrentGCThread "G1 Refine#0"               [id=892, stack(0x00000042fcf00000,0x00000042fd000000) (1024K)]
  0x000001b3b6864610 ConcurrentGCThread "G1 Service"                [id=13648, stack(0x00000042fd000000,0x00000042fd100000) (1024K)]
Total: 7

Threads with active compile tasks:
Total: 0

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007ffe8ca6b160] Metaspace_lock - owner thread: 0x000001b39d9c3230

Heap address: 0x0000000080600000, size: 2042 MB, Compressed Oops mode: 32-bit

CDS archive(s) not mapped
Compressed class space mapped at: 0x0000000100000000-0x0000000140000000, reserved size: 1073741824
Narrow klass base: 0x0000000000000000, Narrow klass shift: 3, Narrow klass range: 0x140000000

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 5 size 8 Array Of Cards #cards 12 size 40 Howl #buckets 4 coarsen threshold 1843 Howl Bitmap #cards 512 size 80 coarsen threshold 460 Card regions per heap region 1 cards per card region 2048
 CPUs: 4 total, 4 available
 Memory: 8161M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Heap Region Size: 1M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 128M
 Heap Max Capacity: 2042M
 Pre-touch: Disabled
 Parallel Workers: 4
 Concurrent Workers: 1
 Concurrent Refinement Workers: 4
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 131072K, used 5120K [0x0000000080600000, 0x0000000100000000)
  region size 1024K, 4 young (4096K), 0 survivors (0K)
 Metaspace       used 5854K, committed 6016K, reserved 1114112K
  class space    used 511K, committed 576K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom
|   0|0x0000000080600000, 0x0000000080700000, 0x0000000080700000|100%|HS|  |TAMS 0x0000000080600000| PB 0x0000000080600000| Complete 
|   1|0x0000000080700000, 0x0000000080800000, 0x0000000080800000|100%|HC|  |TAMS 0x0000000080700000| PB 0x0000000080700000| Complete 
|   2|0x0000000080800000, 0x0000000080900000, 0x0000000080900000|100%|HC|  |TAMS 0x0000000080800000| PB 0x0000000080800000| Complete 
|   3|0x0000000080900000, 0x0000000080900000, 0x0000000080a00000|  0%| F|  |TAMS 0x0000000080900000| PB 0x0000000080900000| Untracked 
|   4|0x0000000080a00000, 0x0000000080a00000, 0x0000000080b00000|  0%| F|  |TAMS 0x0000000080a00000| PB 0x0000000080a00000| Untracked 
|   5|0x0000000080b00000, 0x0000000080b00000, 0x0000000080c00000|  0%| F|  |TAMS 0x0000000080b00000| PB 0x0000000080b00000| Untracked 
|   6|0x0000000080c00000, 0x0000000080c00000, 0x0000000080d00000|  0%| F|  |TAMS 0x0000000080c00000| PB 0x0000000080c00000| Untracked 
|   7|0x0000000080d00000, 0x0000000080d00000, 0x0000000080e00000|  0%| F|  |TAMS 0x0000000080d00000| PB 0x0000000080d00000| Untracked 
|   8|0x0000000080e00000, 0x0000000080e00000, 0x0000000080f00000|  0%| F|  |TAMS 0x0000000080e00000| PB 0x0000000080e00000| Untracked 
|   9|0x0000000080f00000, 0x0000000080f00000, 0x0000000081000000|  0%| F|  |TAMS 0x0000000080f00000| PB 0x0000000080f00000| Untracked 
|  10|0x0000000081000000, 0x0000000081000000, 0x0000000081100000|  0%| F|  |TAMS 0x0000000081000000| PB 0x0000000081000000| Untracked 
|  11|0x0000000081100000, 0x0000000081100000, 0x0000000081200000|  0%| F|  |TAMS 0x0000000081100000| PB 0x0000000081100000| Untracked 
|  12|0x0000000081200000, 0x0000000081200000, 0x0000000081300000|  0%| F|  |TAMS 0x0000000081200000| PB 0x0000000081200000| Untracked 
|  13|0x0000000081300000, 0x0000000081300000, 0x0000000081400000|  0%| F|  |TAMS 0x0000000081300000| PB 0x0000000081300000| Untracked 
|  14|0x0000000081400000, 0x0000000081400000, 0x0000000081500000|  0%| F|  |TAMS 0x0000000081400000| PB 0x0000000081400000| Untracked 
|  15|0x0000000081500000, 0x0000000081500000, 0x0000000081600000|  0%| F|  |TAMS 0x0000000081500000| PB 0x0000000081500000| Untracked 
|  16|0x0000000081600000, 0x0000000081600000, 0x0000000081700000|  0%| F|  |TAMS 0x0000000081600000| PB 0x0000000081600000| Untracked 
|  17|0x0000000081700000, 0x0000000081700000, 0x0000000081800000|  0%| F|  |TAMS 0x0000000081700000| PB 0x0000000081700000| Untracked 
|  18|0x0000000081800000, 0x0000000081800000, 0x0000000081900000|  0%| F|  |TAMS 0x0000000081800000| PB 0x0000000081800000| Untracked 
|  19|0x0000000081900000, 0x0000000081900000, 0x0000000081a00000|  0%| F|  |TAMS 0x0000000081900000| PB 0x0000000081900000| Untracked 
|  20|0x0000000081a00000, 0x0000000081a00000, 0x0000000081b00000|  0%| F|  |TAMS 0x0000000081a00000| PB 0x0000000081a00000| Untracked 
|  21|0x0000000081b00000, 0x0000000081b00000, 0x0000000081c00000|  0%| F|  |TAMS 0x0000000081b00000| PB 0x0000000081b00000| Untracked 
|  22|0x0000000081c00000, 0x0000000081c00000, 0x0000000081d00000|  0%| F|  |TAMS 0x0000000081c00000| PB 0x0000000081c00000| Untracked 
|  23|0x0000000081d00000, 0x0000000081d00000, 0x0000000081e00000|  0%| F|  |TAMS 0x0000000081d00000| PB 0x0000000081d00000| Untracked 
|  24|0x0000000081e00000, 0x0000000081e00000, 0x0000000081f00000|  0%| F|  |TAMS 0x0000000081e00000| PB 0x0000000081e00000| Untracked 
|  25|0x0000000081f00000, 0x0000000081f00000, 0x0000000082000000|  0%| F|  |TAMS 0x0000000081f00000| PB 0x0000000081f00000| Untracked 
|  26|0x0000000082000000, 0x0000000082000000, 0x0000000082100000|  0%| F|  |TAMS 0x0000000082000000| PB 0x0000000082000000| Untracked 
|  27|0x0000000082100000, 0x0000000082100000, 0x0000000082200000|  0%| F|  |TAMS 0x0000000082100000| PB 0x0000000082100000| Untracked 
|  28|0x0000000082200000, 0x0000000082200000, 0x0000000082300000|  0%| F|  |TAMS 0x0000000082200000| PB 0x0000000082200000| Untracked 
|  29|0x0000000082300000, 0x0000000082300000, 0x0000000082400000|  0%| F|  |TAMS 0x0000000082300000| PB 0x0000000082300000| Untracked 
|  30|0x0000000082400000, 0x0000000082400000, 0x0000000082500000|  0%| F|  |TAMS 0x0000000082400000| PB 0x0000000082400000| Untracked 
|  31|0x0000000082500000, 0x0000000082500000, 0x0000000082600000|  0%| F|  |TAMS 0x0000000082500000| PB 0x0000000082500000| Untracked 
|  32|0x0000000082600000, 0x0000000082600000, 0x0000000082700000|  0%| F|  |TAMS 0x0000000082600000| PB 0x0000000082600000| Untracked 
|  33|0x0000000082700000, 0x0000000082700000, 0x0000000082800000|  0%| F|  |TAMS 0x0000000082700000| PB 0x0000000082700000| Untracked 
|  34|0x0000000082800000, 0x0000000082800000, 0x0000000082900000|  0%| F|  |TAMS 0x0000000082800000| PB 0x0000000082800000| Untracked 
|  35|0x0000000082900000, 0x0000000082900000, 0x0000000082a00000|  0%| F|  |TAMS 0x0000000082900000| PB 0x0000000082900000| Untracked 
|  36|0x0000000082a00000, 0x0000000082a00000, 0x0000000082b00000|  0%| F|  |TAMS 0x0000000082a00000| PB 0x0000000082a00000| Untracked 
|  37|0x0000000082b00000, 0x0000000082b00000, 0x0000000082c00000|  0%| F|  |TAMS 0x0000000082b00000| PB 0x0000000082b00000| Untracked 
|  38|0x0000000082c00000, 0x0000000082c00000, 0x0000000082d00000|  0%| F|  |TAMS 0x0000000082c00000| PB 0x0000000082c00000| Untracked 
|  39|0x0000000082d00000, 0x0000000082d00000, 0x0000000082e00000|  0%| F|  |TAMS 0x0000000082d00000| PB 0x0000000082d00000| Untracked 
|  40|0x0000000082e00000, 0x0000000082e00000, 0x0000000082f00000|  0%| F|  |TAMS 0x0000000082e00000| PB 0x0000000082e00000| Untracked 
|  41|0x0000000082f00000, 0x0000000082f00000, 0x0000000083000000|  0%| F|  |TAMS 0x0000000082f00000| PB 0x0000000082f00000| Untracked 
|  42|0x0000000083000000, 0x0000000083000000, 0x0000000083100000|  0%| F|  |TAMS 0x0000000083000000| PB 0x0000000083000000| Untracked 
|  43|0x0000000083100000, 0x0000000083100000, 0x0000000083200000|  0%| F|  |TAMS 0x0000000083100000| PB 0x0000000083100000| Untracked 
|  44|0x0000000083200000, 0x0000000083200000, 0x0000000083300000|  0%| F|  |TAMS 0x0000000083200000| PB 0x0000000083200000| Untracked 
|  45|0x0000000083300000, 0x0000000083300000, 0x0000000083400000|  0%| F|  |TAMS 0x0000000083300000| PB 0x0000000083300000| Untracked 
|  46|0x0000000083400000, 0x0000000083400000, 0x0000000083500000|  0%| F|  |TAMS 0x0000000083400000| PB 0x0000000083400000| Untracked 
|  47|0x0000000083500000, 0x0000000083500000, 0x0000000083600000|  0%| F|  |TAMS 0x0000000083500000| PB 0x0000000083500000| Untracked 
|  48|0x0000000083600000, 0x0000000083600000, 0x0000000083700000|  0%| F|  |TAMS 0x0000000083600000| PB 0x0000000083600000| Untracked 
|  49|0x0000000083700000, 0x0000000083700000, 0x0000000083800000|  0%| F|  |TAMS 0x0000000083700000| PB 0x0000000083700000| Untracked 
|  50|0x0000000083800000, 0x0000000083800000, 0x0000000083900000|  0%| F|  |TAMS 0x0000000083800000| PB 0x0000000083800000| Untracked 
|  51|0x0000000083900000, 0x0000000083900000, 0x0000000083a00000|  0%| F|  |TAMS 0x0000000083900000| PB 0x0000000083900000| Untracked 
|  52|0x0000000083a00000, 0x0000000083a00000, 0x0000000083b00000|  0%| F|  |TAMS 0x0000000083a00000| PB 0x0000000083a00000| Untracked 
|  53|0x0000000083b00000, 0x0000000083b00000, 0x0000000083c00000|  0%| F|  |TAMS 0x0000000083b00000| PB 0x0000000083b00000| Untracked 
|  54|0x0000000083c00000, 0x0000000083c00000, 0x0000000083d00000|  0%| F|  |TAMS 0x0000000083c00000| PB 0x0000000083c00000| Untracked 
|  55|0x0000000083d00000, 0x0000000083d00000, 0x0000000083e00000|  0%| F|  |TAMS 0x0000000083d00000| PB 0x0000000083d00000| Untracked 
|  56|0x0000000083e00000, 0x0000000083e00000, 0x0000000083f00000|  0%| F|  |TAMS 0x0000000083e00000| PB 0x0000000083e00000| Untracked 
|  57|0x0000000083f00000, 0x0000000083f00000, 0x0000000084000000|  0%| F|  |TAMS 0x0000000083f00000| PB 0x0000000083f00000| Untracked 
|  58|0x0000000084000000, 0x0000000084000000, 0x0000000084100000|  0%| F|  |TAMS 0x0000000084000000| PB 0x0000000084000000| Untracked 
|  59|0x0000000084100000, 0x0000000084100000, 0x0000000084200000|  0%| F|  |TAMS 0x0000000084100000| PB 0x0000000084100000| Untracked 
|  60|0x0000000084200000, 0x0000000084200000, 0x0000000084300000|  0%| F|  |TAMS 0x0000000084200000| PB 0x0000000084200000| Untracked 
|  61|0x0000000084300000, 0x0000000084300000, 0x0000000084400000|  0%| F|  |TAMS 0x0000000084300000| PB 0x0000000084300000| Untracked 
|  62|0x0000000084400000, 0x0000000084400000, 0x0000000084500000|  0%| F|  |TAMS 0x0000000084400000| PB 0x0000000084400000| Untracked 
|  63|0x0000000084500000, 0x0000000084500000, 0x0000000084600000|  0%| F|  |TAMS 0x0000000084500000| PB 0x0000000084500000| Untracked 
|  64|0x0000000084600000, 0x0000000084600000, 0x0000000084700000|  0%| F|  |TAMS 0x0000000084600000| PB 0x0000000084600000| Untracked 
|  65|0x0000000084700000, 0x0000000084700000, 0x0000000084800000|  0%| F|  |TAMS 0x0000000084700000| PB 0x0000000084700000| Untracked 
|  66|0x0000000084800000, 0x0000000084800000, 0x0000000084900000|  0%| F|  |TAMS 0x0000000084800000| PB 0x0000000084800000| Untracked 
|  67|0x0000000084900000, 0x0000000084900000, 0x0000000084a00000|  0%| F|  |TAMS 0x0000000084900000| PB 0x0000000084900000| Untracked 
|  68|0x0000000084a00000, 0x0000000084a00000, 0x0000000084b00000|  0%| F|  |TAMS 0x0000000084a00000| PB 0x0000000084a00000| Untracked 
|  69|0x0000000084b00000, 0x0000000084b00000, 0x0000000084c00000|  0%| F|  |TAMS 0x0000000084b00000| PB 0x0000000084b00000| Untracked 
|  70|0x0000000084c00000, 0x0000000084c00000, 0x0000000084d00000|  0%| F|  |TAMS 0x0000000084c00000| PB 0x0000000084c00000| Untracked 
|  71|0x0000000084d00000, 0x0000000084d00000, 0x0000000084e00000|  0%| F|  |TAMS 0x0000000084d00000| PB 0x0000000084d00000| Untracked 
|  72|0x0000000084e00000, 0x0000000084e00000, 0x0000000084f00000|  0%| F|  |TAMS 0x0000000084e00000| PB 0x0000000084e00000| Untracked 
|  73|0x0000000084f00000, 0x0000000084f00000, 0x0000000085000000|  0%| F|  |TAMS 0x0000000084f00000| PB 0x0000000084f00000| Untracked 
|  74|0x0000000085000000, 0x0000000085000000, 0x0000000085100000|  0%| F|  |TAMS 0x0000000085000000| PB 0x0000000085000000| Untracked 
|  75|0x0000000085100000, 0x0000000085100000, 0x0000000085200000|  0%| F|  |TAMS 0x0000000085100000| PB 0x0000000085100000| Untracked 
|  76|0x0000000085200000, 0x0000000085200000, 0x0000000085300000|  0%| F|  |TAMS 0x0000000085200000| PB 0x0000000085200000| Untracked 
|  77|0x0000000085300000, 0x0000000085300000, 0x0000000085400000|  0%| F|  |TAMS 0x0000000085300000| PB 0x0000000085300000| Untracked 
|  78|0x0000000085400000, 0x0000000085400000, 0x0000000085500000|  0%| F|  |TAMS 0x0000000085400000| PB 0x0000000085400000| Untracked 
|  79|0x0000000085500000, 0x0000000085500000, 0x0000000085600000|  0%| F|  |TAMS 0x0000000085500000| PB 0x0000000085500000| Untracked 
|  80|0x0000000085600000, 0x0000000085600000, 0x0000000085700000|  0%| F|  |TAMS 0x0000000085600000| PB 0x0000000085600000| Untracked 
|  81|0x0000000085700000, 0x0000000085700000, 0x0000000085800000|  0%| F|  |TAMS 0x0000000085700000| PB 0x0000000085700000| Untracked 
|  82|0x0000000085800000, 0x0000000085800000, 0x0000000085900000|  0%| F|  |TAMS 0x0000000085800000| PB 0x0000000085800000| Untracked 
|  83|0x0000000085900000, 0x0000000085900000, 0x0000000085a00000|  0%| F|  |TAMS 0x0000000085900000| PB 0x0000000085900000| Untracked 
|  84|0x0000000085a00000, 0x0000000085a00000, 0x0000000085b00000|  0%| F|  |TAMS 0x0000000085a00000| PB 0x0000000085a00000| Untracked 
|  85|0x0000000085b00000, 0x0000000085b00000, 0x0000000085c00000|  0%| F|  |TAMS 0x0000000085b00000| PB 0x0000000085b00000| Untracked 
|  86|0x0000000085c00000, 0x0000000085c00000, 0x0000000085d00000|  0%| F|  |TAMS 0x0000000085c00000| PB 0x0000000085c00000| Untracked 
|  87|0x0000000085d00000, 0x0000000085d00000, 0x0000000085e00000|  0%| F|  |TAMS 0x0000000085d00000| PB 0x0000000085d00000| Untracked 
|  88|0x0000000085e00000, 0x0000000085e00000, 0x0000000085f00000|  0%| F|  |TAMS 0x0000000085e00000| PB 0x0000000085e00000| Untracked 
|  89|0x0000000085f00000, 0x0000000085f00000, 0x0000000086000000|  0%| F|  |TAMS 0x0000000085f00000| PB 0x0000000085f00000| Untracked 
|  90|0x0000000086000000, 0x0000000086000000, 0x0000000086100000|  0%| F|  |TAMS 0x0000000086000000| PB 0x0000000086000000| Untracked 
|  91|0x0000000086100000, 0x0000000086100000, 0x0000000086200000|  0%| F|  |TAMS 0x0000000086100000| PB 0x0000000086100000| Untracked 
|  92|0x0000000086200000, 0x0000000086200000, 0x0000000086300000|  0%| F|  |TAMS 0x0000000086200000| PB 0x0000000086200000| Untracked 
|  93|0x0000000086300000, 0x0000000086300000, 0x0000000086400000|  0%| F|  |TAMS 0x0000000086300000| PB 0x0000000086300000| Untracked 
|  94|0x0000000086400000, 0x0000000086400000, 0x0000000086500000|  0%| F|  |TAMS 0x0000000086400000| PB 0x0000000086400000| Untracked 
|  95|0x0000000086500000, 0x0000000086500000, 0x0000000086600000|  0%| F|  |TAMS 0x0000000086500000| PB 0x0000000086500000| Untracked 
|  96|0x0000000086600000, 0x0000000086600000, 0x0000000086700000|  0%| F|  |TAMS 0x0000000086600000| PB 0x0000000086600000| Untracked 
|  97|0x0000000086700000, 0x0000000086700000, 0x0000000086800000|  0%| F|  |TAMS 0x0000000086700000| PB 0x0000000086700000| Untracked 
|  98|0x0000000086800000, 0x0000000086800000, 0x0000000086900000|  0%| F|  |TAMS 0x0000000086800000| PB 0x0000000086800000| Untracked 
|  99|0x0000000086900000, 0x0000000086900000, 0x0000000086a00000|  0%| F|  |TAMS 0x0000000086900000| PB 0x0000000086900000| Untracked 
| 100|0x0000000086a00000, 0x0000000086a00000, 0x0000000086b00000|  0%| F|  |TAMS 0x0000000086a00000| PB 0x0000000086a00000| Untracked 
| 101|0x0000000086b00000, 0x0000000086b00000, 0x0000000086c00000|  0%| F|  |TAMS 0x0000000086b00000| PB 0x0000000086b00000| Untracked 
| 102|0x0000000086c00000, 0x0000000086c00000, 0x0000000086d00000|  0%| F|  |TAMS 0x0000000086c00000| PB 0x0000000086c00000| Untracked 
| 103|0x0000000086d00000, 0x0000000086d00000, 0x0000000086e00000|  0%| F|  |TAMS 0x0000000086d00000| PB 0x0000000086d00000| Untracked 
| 104|0x0000000086e00000, 0x0000000086e00000, 0x0000000086f00000|  0%| F|  |TAMS 0x0000000086e00000| PB 0x0000000086e00000| Untracked 
| 105|0x0000000086f00000, 0x0000000086f00000, 0x0000000087000000|  0%| F|  |TAMS 0x0000000086f00000| PB 0x0000000086f00000| Untracked 
| 106|0x0000000087000000, 0x0000000087000000, 0x0000000087100000|  0%| F|  |TAMS 0x0000000087000000| PB 0x0000000087000000| Untracked 
| 107|0x0000000087100000, 0x0000000087100000, 0x0000000087200000|  0%| F|  |TAMS 0x0000000087100000| PB 0x0000000087100000| Untracked 
| 108|0x0000000087200000, 0x0000000087200000, 0x0000000087300000|  0%| F|  |TAMS 0x0000000087200000| PB 0x0000000087200000| Untracked 
| 109|0x0000000087300000, 0x0000000087300000, 0x0000000087400000|  0%| F|  |TAMS 0x0000000087300000| PB 0x0000000087300000| Untracked 
| 110|0x0000000087400000, 0x0000000087400000, 0x0000000087500000|  0%| F|  |TAMS 0x0000000087400000| PB 0x0000000087400000| Untracked 
| 111|0x0000000087500000, 0x0000000087500000, 0x0000000087600000|  0%| F|  |TAMS 0x0000000087500000| PB 0x0000000087500000| Untracked 
| 112|0x0000000087600000, 0x0000000087600000, 0x0000000087700000|  0%| F|  |TAMS 0x0000000087600000| PB 0x0000000087600000| Untracked 
| 113|0x0000000087700000, 0x0000000087700000, 0x0000000087800000|  0%| F|  |TAMS 0x0000000087700000| PB 0x0000000087700000| Untracked 
| 114|0x0000000087800000, 0x0000000087800000, 0x0000000087900000|  0%| F|  |TAMS 0x0000000087800000| PB 0x0000000087800000| Untracked 
| 115|0x0000000087900000, 0x0000000087900000, 0x0000000087a00000|  0%| F|  |TAMS 0x0000000087900000| PB 0x0000000087900000| Untracked 
| 116|0x0000000087a00000, 0x0000000087a00000, 0x0000000087b00000|  0%| F|  |TAMS 0x0000000087a00000| PB 0x0000000087a00000| Untracked 
| 117|0x0000000087b00000, 0x0000000087b00000, 0x0000000087c00000|  0%| F|  |TAMS 0x0000000087b00000| PB 0x0000000087b00000| Untracked 
| 118|0x0000000087c00000, 0x0000000087c00000, 0x0000000087d00000|  0%| F|  |TAMS 0x0000000087c00000| PB 0x0000000087c00000| Untracked 
| 119|0x0000000087d00000, 0x0000000087d00000, 0x0000000087e00000|  0%| F|  |TAMS 0x0000000087d00000| PB 0x0000000087d00000| Untracked 
| 120|0x0000000087e00000, 0x0000000087e00000, 0x0000000087f00000|  0%| F|  |TAMS 0x0000000087e00000| PB 0x0000000087e00000| Untracked 
| 121|0x0000000087f00000, 0x0000000087f00000, 0x0000000088000000|  0%| F|  |TAMS 0x0000000087f00000| PB 0x0000000087f00000| Untracked 
| 122|0x0000000088000000, 0x0000000088000000, 0x0000000088100000|  0%| F|  |TAMS 0x0000000088000000| PB 0x0000000088000000| Untracked 
| 123|0x0000000088100000, 0x0000000088100000, 0x0000000088200000|  0%| F|  |TAMS 0x0000000088100000| PB 0x0000000088100000| Untracked 
| 124|0x0000000088200000, 0x00000000882cfab0, 0x0000000088300000| 81%| E|  |TAMS 0x0000000088200000| PB 0x0000000088200000| Complete 
| 125|0x0000000088300000, 0x0000000088400000, 0x0000000088400000|100%| E|CS|TAMS 0x0000000088300000| PB 0x0000000088300000| Complete 
| 126|0x0000000088400000, 0x0000000088500000, 0x0000000088500000|100%| E|  |TAMS 0x0000000088400000| PB 0x0000000088400000| Complete 
| 127|0x0000000088500000, 0x0000000088600000, 0x0000000088600000|100%| E|CS|TAMS 0x0000000088500000| PB 0x0000000088500000| Complete 

Card table byte_map: [0x000001b3b2120000,0x000001b3b2520000] _byte_map_base: 0x000001b3b1d1d000

Marking Bits: (CMBitMap*) 0x000001b39fc18140
 Bits: [0x000001b3b2520000, 0x000001b3b4508000)

Polling page: 0x000001b39daf0000

Metaspace:

Usage:
  Non-class:      5.22 MB used.
      Class:    511.91 KB used.
       Both:      5.72 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,       5.31 MB (  8%) committed,  1 nodes.
      Class space:        1.00 GB reserved,     576.00 KB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,       5.88 MB ( <1%) committed. 

Chunk freelists:
   Non-Class:  10.67 MB
       Class:  15.23 MB
        Both:  25.91 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 21.00 MB
CDS: off
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 4.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 94.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 26.
num_chunk_merges: 0.
num_chunk_splits: 18.
num_chunks_enlarged: 13.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=59Kb max_used=59Kb free=119940Kb
 bounds [0x000001b3aa7f0000, 0x000001b3aaa60000, 0x000001b3b1d20000]
CodeHeap 'profiled nmethods': size=120000Kb used=221Kb max_used=221Kb free=119778Kb
 bounds [0x000001b3a2d20000, 0x000001b3a2f90000, 0x000001b3aa250000]
CodeHeap 'non-nmethods': size=5760Kb used=1194Kb max_used=1220Kb free=4565Kb
 bounds [0x000001b3aa250000, 0x000001b3aa4c0000, 0x000001b3aa7f0000]
 total_blobs=530 nmethods=188 adapters=247
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 0.546 Thread 0x000001b3b6baf8a0 nmethod 175 0x000001b3a2d52610 code [0x000001b3a2d527a0, 0x000001b3a2d528c0]
Event: 0.546 Thread 0x000001b3b6ba9740  176       4       java.lang.String::hashCode (60 bytes)
Event: 0.546 Thread 0x000001b3b6baf8a0  177       3       java.lang.AbstractStringBuilder::<init> (39 bytes)
Event: 0.546 Thread 0x000001b3b6baf8a0 nmethod 177 0x000001b3a2d52990 code [0x000001b3a2d52b40, 0x000001b3a2d52dc0]
Event: 0.546 Thread 0x000001b3b6baf8a0  179       3       java.lang.StringBuilder::toString (9 bytes)
Event: 0.547 Thread 0x000001b3b6baf8a0 nmethod 179 0x000001b3a2d52e90 code [0x000001b3a2d53040, 0x000001b3a2d53248]
Event: 0.547 Thread 0x000001b3b6baf8a0  180       3       java.lang.String::<init> (7 bytes)
Event: 0.547 Thread 0x000001b3b6baf8a0 nmethod 180 0x000001b3a2d53310 code [0x000001b3a2d534c0, 0x000001b3a2d53608]
Event: 0.547 Thread 0x000001b3b6baf8a0  181       1       java.security.CodeSource::getLocationNoFragString (5 bytes)
Event: 0.547 Thread 0x000001b3b6baf8a0 nmethod 181 0x000001b3aa7fd710 code [0x000001b3aa7fd8a0, 0x000001b3aa7fd968]
Event: 0.547 Thread 0x000001b3b6baf8a0  182       3       java.lang.StringLatin1::replace (198 bytes)
Event: 0.548 Thread 0x000001b3b6baf8a0 nmethod 182 0x000001b3a2d53690 code [0x000001b3a2d53940, 0x000001b3a2d546f8]
Event: 0.548 Thread 0x000001b3b6baf8a0  184       3       java.lang.StringLatin1::newString (24 bytes)
Event: 0.548 Thread 0x000001b3b6baf8a0 nmethod 184 0x000001b3a2d54c10 code [0x000001b3a2d54ee0, 0x000001b3a2d55c70]
Event: 0.548 Thread 0x000001b3b6baf8a0  185       3       java.lang.AbstractStringBuilder::newCapacity (59 bytes)
Event: 0.548 Thread 0x000001b3b6db8e10  188       4       jdk.internal.misc.Unsafe::toUnsignedLong (7 bytes)
Event: 0.549 Thread 0x000001b3b6db8e10 nmethod 188 0x000001b3aa7fe490 code [0x000001b3aa7fe600, 0x000001b3aa7fe668]
Event: 0.549 Thread 0x000001b3b6baf8a0 nmethod 185 0x000001b3a2d56190 code [0x000001b3a2d56380, 0x000001b3a2d56758]
Event: 0.549 Thread 0x000001b3b6baf8a0  186       3       jdk.internal.misc.Unsafe::allocateUninitializedArray0 (90 bytes)
Event: 0.549 Thread 0x000001b3b6baf8a0 nmethod 186 0x000001b3a2d56910 code [0x000001b3a2d56b40, 0x000001b3a2d574e0]

GC Heap History (0 events):
No events

Dll operation events (2 events):
Event: 0.010 Loaded shared library C:\Program Files\Android\Android Studio\jbr\bin\java.dll
Event: 0.025 Loaded shared library C:\Program Files\Android\Android Studio\jbr\bin\zip.dll

Deoptimization events (20 events):
Event: 0.260 Thread 0x000001b39d9c3230 DEOPT PACKING pc=0x000001b3a2d3c549 sp=0x00000042fcafd760
Event: 0.260 Thread 0x000001b39d9c3230 DEOPT UNPACKING pc=0x000001b3aa2a4e42 sp=0x00000042fcafcbd0 mode 0
Event: 0.260 Thread 0x000001b39d9c3230 DEOPT PACKING pc=0x000001b3a2d3c549 sp=0x00000042fcafd760
Event: 0.260 Thread 0x000001b39d9c3230 DEOPT UNPACKING pc=0x000001b3aa2a4e42 sp=0x00000042fcafcbd0 mode 0
Event: 0.260 Thread 0x000001b39d9c3230 DEOPT PACKING pc=0x000001b3a2d3c549 sp=0x00000042fcafd760
Event: 0.260 Thread 0x000001b39d9c3230 DEOPT UNPACKING pc=0x000001b3aa2a4e42 sp=0x00000042fcafcbd0 mode 0
Event: 0.260 Thread 0x000001b39d9c3230 DEOPT PACKING pc=0x000001b3a2d3c549 sp=0x00000042fcafd760
Event: 0.260 Thread 0x000001b39d9c3230 DEOPT UNPACKING pc=0x000001b3aa2a4e42 sp=0x00000042fcafcbd0 mode 0
Event: 0.260 Thread 0x000001b39d9c3230 DEOPT PACKING pc=0x000001b3a2d3c549 sp=0x00000042fcafd760
Event: 0.260 Thread 0x000001b39d9c3230 DEOPT UNPACKING pc=0x000001b3aa2a4e42 sp=0x00000042fcafcbd0 mode 0
Event: 0.260 Thread 0x000001b39d9c3230 DEOPT PACKING pc=0x000001b3a2d3c549 sp=0x00000042fcafd760
Event: 0.260 Thread 0x000001b39d9c3230 DEOPT UNPACKING pc=0x000001b3aa2a4e42 sp=0x00000042fcafcbd0 mode 0
Event: 0.260 Thread 0x000001b39d9c3230 DEOPT PACKING pc=0x000001b3a2d3c549 sp=0x00000042fcafd760
Event: 0.260 Thread 0x000001b39d9c3230 DEOPT UNPACKING pc=0x000001b3aa2a4e42 sp=0x00000042fcafcbd0 mode 0
Event: 0.261 Thread 0x000001b39d9c3230 DEOPT PACKING pc=0x000001b3a2d3c549 sp=0x00000042fcafd760
Event: 0.261 Thread 0x000001b39d9c3230 DEOPT UNPACKING pc=0x000001b3aa2a4e42 sp=0x00000042fcafcbd0 mode 0
Event: 0.538 Thread 0x000001b39d9c3230 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000001b3aa7f6e30 relative=0x0000000000000830
Event: 0.538 Thread 0x000001b39d9c3230 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001b3aa7f6e30 method=java.util.zip.ZipFile$Source.checkAndAddEntry(II)I @ 50 c2
Event: 0.538 Thread 0x000001b39d9c3230 DEOPT PACKING pc=0x000001b3aa7f6e30 sp=0x00000042fcafe4f0
Event: 0.538 Thread 0x000001b39d9c3230 DEOPT UNPACKING pc=0x000001b3aa2a46a2 sp=0x00000042fcafe460 mode 2

Classes loaded (20 events):
Event: 0.453 Loading class jdk/internal/jimage/ImageHeader
Event: 0.453 Loading class jdk/internal/jimage/ImageHeader done
Event: 0.453 Loading class java/nio/DirectIntBufferU
Event: 0.454 Loading class java/nio/IntBuffer
Event: 0.479 Loading class java/nio/IntBuffer done
Event: 0.479 Loading class java/nio/DirectIntBufferU done
Event: 0.480 Loading class java/nio/DirectByteBufferR
Event: 0.503 Loading class java/nio/DirectByteBufferR done
Event: 0.503 Loading class java/nio/DirectIntBufferRU
Event: 0.503 Loading class java/nio/DirectIntBufferRU done
Event: 0.503 Loading class jdk/internal/jimage/ImageStringsReader
Event: 0.504 Loading class jdk/internal/jimage/ImageStrings
Event: 0.504 Loading class jdk/internal/jimage/ImageStrings done
Event: 0.504 Loading class jdk/internal/jimage/ImageStringsReader done
Event: 0.504 Loading class jdk/internal/jimage/decompressor/Decompressor
Event: 0.526 Loading class jdk/internal/jimage/decompressor/Decompressor done
Event: 0.526 Loading class jdk/internal/jimage/ImageLocation
Event: 0.527 Loading class jdk/internal/jimage/ImageLocation done
Event: 0.545 Loading class java/lang/IndexOutOfBoundsException
Event: 0.545 Loading class java/lang/IndexOutOfBoundsException done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (0 events):
No events

ZGC Phase Switch (0 events):
No events

VM Operations (2 events):
Event: 0.346 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.346 Executing VM operation: HandshakeAllThreads (Deoptimize) done

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (16 events):
Event: 0.016 Thread 0x000001b39d9c3230 Thread added: 0x000001b39d9c3230
Event: 0.102 Thread 0x000001b39d9c3230 Thread added: 0x000001b3b6bc6660
Event: 0.103 Thread 0x000001b39d9c3230 Thread added: 0x000001b3b6bc7370
Event: 0.106 Thread 0x000001b39d9c3230 Thread added: 0x000001b3b6baaa30
Event: 0.106 Thread 0x000001b39d9c3230 Thread added: 0x000001b3b6ba7400
Event: 0.106 Thread 0x000001b39d9c3230 Thread added: 0x000001b3b6ba7f70
Event: 0.106 Thread 0x000001b39d9c3230 Thread added: 0x000001b3b6ba89c0
Event: 0.106 Thread 0x000001b39d9c3230 Thread added: 0x000001b3b6ba9740
Event: 0.107 Thread 0x000001b39d9c3230 Thread added: 0x000001b3b6baf8a0
Event: 0.176 Thread 0x000001b39d9c3230 Thread added: 0x000001b3b6d63dd0
Event: 0.183 Thread 0x000001b39d9c3230 Thread added: 0x000001b3b6d66850
Event: 0.206 Loaded shared library C:\Program Files\Android\Android Studio\jbr\bin\net.dll
Event: 0.210 Loaded shared library C:\Program Files\Android\Android Studio\jbr\bin\nio.dll
Event: 0.259 Thread 0x000001b3b6ba9740 Thread added: 0x000001b3b6db8e10
Event: 0.343 Loaded shared library C:\Program Files\Android\Android Studio\jbr\bin\zip.dll
Event: 0.453 Loaded shared library C:\Program Files\Android\Android Studio\jbr\bin\jimage.dll


Dynamic libraries:
0x00007ff7942f0000 - 0x00007ff7942fa000 	C:\Program Files\Android\Android Studio\jbr\bin\java.exe
0x00007ffef0c10000 - 0x00007ffef0e08000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ffef09c0000 - 0x00007ffef0a82000 	C:\Windows\System32\KERNEL32.DLL
0x00007ffeee760000 - 0x00007ffeeea56000 	C:\Windows\System32\KERNELBASE.dll
0x00007ffeee320000 - 0x00007ffeee420000 	C:\Windows\System32\ucrtbase.dll
0x00007ffeca210000 - 0x00007ffeca228000 	C:\Program Files\Android\Android Studio\jbr\bin\jli.dll
0x00007ffeef0b0000 - 0x00007ffeef24d000 	C:\Windows\System32\USER32.dll
0x00007ffeee580000 - 0x00007ffeee5a2000 	C:\Windows\System32\win32u.dll
0x00007ffef0930000 - 0x00007ffef095b000 	C:\Windows\System32\GDI32.dll
0x00007ffeee5b0000 - 0x00007ffeee6c9000 	C:\Windows\System32\gdi32full.dll
0x00007ffeeea90000 - 0x00007ffeeeb2d000 	C:\Windows\System32\msvcp_win.dll
0x00007ffecc820000 - 0x00007ffecc83b000 	C:\Program Files\Android\Android Studio\jbr\bin\VCRUNTIME140.dll
0x00007ffee0760000 - 0x00007ffee09fa000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7\COMCTL32.dll
0x00007ffeef5f0000 - 0x00007ffeef68e000 	C:\Windows\System32\msvcrt.dll
0x00007ffeeedc0000 - 0x00007ffeeedef000 	C:\Windows\System32\IMM32.DLL
0x00007ffecd120000 - 0x00007ffecd12c000 	C:\Program Files\Android\Android Studio\jbr\bin\vcruntime140_1.dll
0x00007ffec0510000 - 0x00007ffec059d000 	C:\Program Files\Android\Android Studio\jbr\bin\msvcp140.dll
0x00007ffe8bec0000 - 0x00007ffe8cb4a000 	C:\Program Files\Android\Android Studio\jbr\bin\server\jvm.dll
0x00007ffeef530000 - 0x00007ffeef5e1000 	C:\Windows\System32\ADVAPI32.dll
0x00007ffeef010000 - 0x00007ffeef0af000 	C:\Windows\System32\sechost.dll
0x00007ffeeeee0000 - 0x00007ffeef003000 	C:\Windows\System32\RPCRT4.dll
0x00007ffeeea60000 - 0x00007ffeeea87000 	C:\Windows\System32\bcrypt.dll
0x00007ffeef4c0000 - 0x00007ffeef52b000 	C:\Windows\System32\WS2_32.dll
0x00007ffeee100000 - 0x00007ffeee14b000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ffee2be0000 - 0x00007ffee2c07000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ffee5700000 - 0x00007ffee570a000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ffeee0e0000 - 0x00007ffeee0f2000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ffeec100000 - 0x00007ffeec112000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ffecd050000 - 0x00007ffecd05a000 	C:\Program Files\Android\Android Studio\jbr\bin\jimage.dll
0x00007ffedfc10000 - 0x00007ffedfe11000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ffec4fb0000 - 0x00007ffec4fe4000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ffeee6d0000 - 0x00007ffeee752000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ffec9a70000 - 0x00007ffec9a90000 	C:\Program Files\Android\Android Studio\jbr\bin\java.dll
0x00007ffec99b0000 - 0x00007ffec99c8000 	C:\Program Files\Android\Android Studio\jbr\bin\zip.dll
0x00007ffef00d0000 - 0x00007ffef083e000 	C:\Windows\System32\SHELL32.dll
0x00007ffeec300000 - 0x00007ffeecaa4000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ffeefd70000 - 0x00007ffef00c3000 	C:\Windows\System32\combase.dll
0x00007ffeedb20000 - 0x00007ffeedb4b000 	C:\Windows\SYSTEM32\Wldp.dll
0x00007ffeeedf0000 - 0x00007ffeeeebd000 	C:\Windows\System32\OLEAUT32.dll
0x00007ffeeec30000 - 0x00007ffeeecdd000 	C:\Windows\System32\SHCORE.dll
0x00007ffeeece0000 - 0x00007ffeeed3b000 	C:\Windows\System32\shlwapi.dll
0x00007ffeee1d0000 - 0x00007ffeee1f4000 	C:\Windows\SYSTEM32\profapi.dll
0x00007ffeca830000 - 0x00007ffeca840000 	C:\Program Files\Android\Android Studio\jbr\bin\net.dll
0x00007ffee77d0000 - 0x00007ffee78da000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007ffeed900000 - 0x00007ffeed96a000 	C:\Windows\system32\mswsock.dll
0x00007ffec4720000 - 0x00007ffec4736000 	C:\Program Files\Android\Android Studio\jbr\bin\nio.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Android\Android Studio\jbr\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7;C:\Program Files\Android\Android Studio\jbr\bin\server

VM Arguments:
java_command: org.jetbrains.kotlin.cli.jvm.K2JVMCompiler @F:\PROJETO\React\EscolaSabatina\node_modules\react-native-screens\android\build\20250805_6879488832816659327.compiler.options
java_class_path (initial): C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-compiler-embeddable\2.1.20\4ef56b3316798316bfac7a0ae443391c9e900ea1\kotlin-compiler-embeddable-2.1.20.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib\2.1.20\aa8ca79cd50578314f6d1180c47cbe14c0fee567\kotlin-stdlib-2.1.20.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-script-runtime\2.1.20\f7c623d7f7bdb01f5ccd6b437bc0a937fcd7c57e\kotlin-script-runtime-2.1.20.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-reflect\1.6.10\1cbe9c92c12a94eea200d23c2bbaedaf3daf5132\kotlin-reflect-1.6.10.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-daemon-embeddable\2.1.20\95670fce77befd02a70a0bc3abe8ee4533521334\kotlin-daemon-embeddable-2.1.20.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.intellij.deps\trove4j\1.0.20200330\3afb14d5f9ceb459d724e907a21145e8ff394f02\trove4j-1.0.20200330.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-core-jvm\1.8.0\ac1dc37a30a93150b704022f8d895ee1bd3a36b3\kotlinx-coroutines-core-jvm-1.8.0.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains\annotations\13.0\919f0dfe192fb4e063e7dacadee7f8bb9a2672a9\annotations-13.0.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 3                                         {product} {ergonomic}
     uint ConcGCThreads                            = 1                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 4                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 1048576                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 134217728                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 2141192192                                {product} {ergonomic}
   size_t MaxNewSize                               = 1284505600                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 1048576                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5832780                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122912730                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122912730                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 2141192192                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=C:\Program Files\Java\jdk-11.0.10
PATH=F:\PROJETO\React\EscolaSabatina\node_modules\.bin;F:\PROJETO\React\EscolaSabatina\node_modules\.bin;F:\PROJETO\React\node_modules\.bin;F:\PROJETO\node_modules\.bin;F:\node_modules\.bin;C:\Users\<USER>\AppData\Roaming\nvm\v20.10.0\node_modules\npm\node_modules\@npmcli\run-script\lib\node-gyp-bin;C:\Program Files\Google\Chrome\Application;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\Microsoft SQL Server\130\Tools\Binn\;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn\;C:\Program Files\dotnet\;C:\Program Files (x86)\Microsoft SQL Server\150\DTS\Binn\;C:\Program Files\Azure Data Studio\bin;C:\Program Files\Git\cmd;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\110\Tools\Binn\;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\130\Tools\Binn\;C:\Program Files (x86)\Microsoft SQL Server\140\Tools\Binn\;C:\Program Files\Microsoft SQL Server\140\Tools\Binn\;C:\Program Files\Microsoft SQL Server\140\DTS\Binn\;C:\Program Files\Microsoft\Web Platform Installer\;C:\Program Files (x86)\dotnet\;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;C:\Program Files\Calibre2\;C:\Program Files\Java\jdk-11\bin;C:\Prog;C:\Program Files\Docker\Docker\resources\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\.local\share\solana\install\active_release\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\Microsoft SQL Server\130\Tools\Binn\;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn\;C:\Program Files\dotnet\;C:\Program Files (x86)\Microsoft SQL Server\150\DTS\Binn\;C:\Program Files\Azure Data Studio\bin;C:\Program Files\Git\cmd;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\110\Tools\Binn\;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\130\Tools\Binn\;C:\Program Files (x86)\Microsoft SQL Server\140\Tools\Binn\;C:\Program Files\Microsoft SQL Server\140\Tools\Binn\;C:\Program Files\Microsoft SQL Server\140\DTS\Binn\;C:\Program Files\Docker\Docker\resources\bin;C:\ProgramData\DockerDesktop\version-bin;C:\Program Files\Microsoft\Web Platform Installer\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\Users\ADM\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\.dotnet\tools;C:\MinGW\bin;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin
USERNAME=ADM
LANG=en_US.UTF-8
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 58 Stepping 9, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

JNI global refs:
JNI global refs: 7, weak refs: 0

JNI global refs memory usage: 835, weak refs: 201

Process memory usage:
Resident Set Size: 40096K (0% of 8357708K total physical memory with 348356K free physical memory)

OOME stack traces (most recent first):
Classloader memory used:
Loader bootstrap                                                                       : 5488K
Loader jdk.internal.loader.ClassLoaders$AppClassLoader                                 : 366K

---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
OS uptime: 13 days 23:15 hours
Hyper-V role detected

CPU: total 4 (initial active 4) (4 cores per cpu, 1 threads per core) family 6 model 58 stepping 9 microcode 0x21, cx8, cmov, fxsr, mmx, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, tsc, tscinvbit, avx, aes, erms, clmul, vzeroupper, clflush, hv, rdtscp, f16c
Processor Information for all 4 processors :
  Max Mhz: 3101, Current Mhz: 3101, Mhz Limit: 3101

Memory: 4k page, system-wide physical 8161M (340M free)
TotalPageFile size 32737M (AvailPageFile size 0M)
current process WorkingSet (physical memory assigned to process): 39M, peak: 39M
current process commit charge ("private bytes"): 196M, peak: 197M

vm_info: OpenJDK 64-Bit Server VM (21.0.5+-13047016-b750.29) for windows-amd64 JRE (21.0.5+-13047016-b750.29), built on 2025-02-11T21:12:39Z by "builder" with MS VC++ 16.10 / 16.11 (VS2019)

END.
