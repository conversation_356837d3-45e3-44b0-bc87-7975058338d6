#!/usr/bin/env python3
"""
Script para gerar ícones do app em diferentes taman<PERSON>: pip install Pillow
"""

import os
import sys
from pathlib import Path

try:
    from PIL import Image, ImageDraw
except ImportError:
    print("❌ Dependências não encontradas!")
    print("Execute: pip install Pillow")
    sys.exit(1)

# <PERSON>an<PERSON> para Android
ANDROID_SIZES = {
    'mipmap-mdpi': 48,
    'mipmap-hdpi': 72,
    'mipmap-xhdpi': 96,
    'mipmap-xxhdpi': 144,
    'mipmap-xxxhdpi': 192
}

# Tamanhos para iOS
IOS_SIZES = {
    'AppIcon-20x20@1x': 20,
    'AppIcon-20x20@2x': 40,
    'AppIcon-20x20@3x': 60,
    'AppIcon-29x29@1x': 29,
    'AppIcon-29x29@2x': 58,
    'AppIcon-29x29@3x': 87,
    'AppIcon-40x40@1x': 40,
    'AppIcon-40x40@2x': 80,
    'AppIcon-40x40@3x': 120,
    'AppIcon-60x60@2x': 120,
    'AppIcon-60x60@3x': 180,
    'AppIcon-76x76@1x': 76,
    'AppIcon-76x76@2x': 152,
    'AppIcon-83.5x83.5@2x': 167,
    'AppIcon-1024x1024@1x': 1024
}

def create_book_icon(output_path, size):
    """Cria ícone do livro usando Pillow"""
    try:
        # Criar imagem com fundo verde
        img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)

        # Calcular proporções
        corner_radius = size // 6
        margin = size // 8
        book_width = size - (margin * 2)
        book_height = int(book_width * 0.75)
        book_x = margin
        book_y = (size - book_height) // 2

        # Desenhar fundo verde com cantos arredondados
        # Simular cantos arredondados com múltiplos retângulos
        bg_color = (45, 90, 61)  # #2D5A3D

        # Fundo principal
        draw.rectangle([corner_radius, 0, size - corner_radius, size], fill=bg_color)
        draw.rectangle([0, corner_radius, size, size - corner_radius], fill=bg_color)

        # Cantos arredondados
        draw.ellipse([0, 0, corner_radius * 2, corner_radius * 2], fill=bg_color)
        draw.ellipse([size - corner_radius * 2, 0, size, corner_radius * 2], fill=bg_color)
        draw.ellipse([0, size - corner_radius * 2, corner_radius * 2, size], fill=bg_color)
        draw.ellipse([size - corner_radius * 2, size - corner_radius * 2, size, size], fill=bg_color)

        # Desenhar livro aberto
        book_color = (255, 255, 255)  # Branco

        # Página esquerda
        left_page = [
            book_x, book_y,
            book_x + book_width // 2, book_y + book_height // 8,
            book_x + book_width // 2, book_y + book_height,
            book_x, book_y + book_height
        ]
        draw.polygon(left_page, fill=book_color)

        # Página direita
        right_page = [
            book_x + book_width // 2, book_y + book_height // 8,
            book_x + book_width, book_y,
            book_x + book_width, book_y + book_height,
            book_x + book_width // 2, book_y + book_height
        ]
        draw.polygon(right_page, fill=book_color)

        # Linha central do livro
        center_x = book_x + book_width // 2
        draw.line([center_x, book_y + book_height // 8, center_x, book_y + book_height],
                 fill=bg_color, width=max(1, size // 128))

        # Linhas de texto (opcionais para ícones maiores)
        if size >= 96:
            line_color = (45, 90, 61, 100)  # Verde semi-transparente
            line_width = max(1, size // 256)

            # Linhas na página esquerda
            for i in range(3):
                y = book_y + book_height // 3 + i * book_height // 8
                draw.line([book_x + book_width // 8, y, book_x + book_width // 2 - book_width // 16, y],
                         fill=line_color, width=line_width)

            # Linhas na página direita
            for i in range(3):
                y = book_y + book_height // 3 + i * book_height // 8
                draw.line([book_x + book_width // 2 + book_width // 16, y, book_x + book_width - book_width // 8, y],
                         fill=line_color, width=line_width)

        # Salvar imagem
        img.save(output_path, 'PNG')
        print(f"✅ Gerado: {output_path} ({size}x{size})")
        return True
    except Exception as e:
        print(f"❌ Erro ao gerar {output_path}: {e}")
        return False

def generate_android_icons(project_root):
    """Gera ícones para Android"""
    print("📱 Gerando ícones para Android...")

    android_res = project_root / "android" / "app" / "src" / "main" / "res"

    for folder, size in ANDROID_SIZES.items():
        folder_path = android_res / folder
        folder_path.mkdir(exist_ok=True)

        # Ícone normal
        icon_path = folder_path / "ic_launcher.png"
        create_book_icon(icon_path, size)

        # Ícone redondo (mesmo ícone)
        round_icon_path = folder_path / "ic_launcher_round.png"
        create_book_icon(round_icon_path, size)

def generate_ios_icons(project_root):
    """Gera ícones para iOS"""
    print("🍎 Gerando ícones para iOS...")

    ios_images = project_root / "ios" / "EscolaSabatina" / "Images.xcassets" / "AppIcon.appiconset"
    ios_images.mkdir(parents=True, exist_ok=True)

    for name, size in IOS_SIZES.items():
        icon_path = ios_images / f"{name}.png"
        create_book_icon(icon_path, size)

def create_contents_json(project_root):
    """Cria Contents.json para iOS"""
    ios_images = project_root / "ios" / "EscolaSabatina" / "Images.xcassets" / "AppIcon.appiconset"
    contents_path = ios_images / "Contents.json"
    
    contents = {
        "images": [
            {"size": "20x20", "idiom": "iphone", "filename": "<EMAIL>", "scale": "2x"},
            {"size": "20x20", "idiom": "iphone", "filename": "<EMAIL>", "scale": "3x"},
            {"size": "29x29", "idiom": "iphone", "filename": "<EMAIL>", "scale": "2x"},
            {"size": "29x29", "idiom": "iphone", "filename": "<EMAIL>", "scale": "3x"},
            {"size": "40x40", "idiom": "iphone", "filename": "<EMAIL>", "scale": "2x"},
            {"size": "40x40", "idiom": "iphone", "filename": "<EMAIL>", "scale": "3x"},
            {"size": "60x60", "idiom": "iphone", "filename": "<EMAIL>", "scale": "2x"},
            {"size": "60x60", "idiom": "iphone", "filename": "<EMAIL>", "scale": "3x"},
            {"size": "20x20", "idiom": "ipad", "filename": "<EMAIL>", "scale": "1x"},
            {"size": "20x20", "idiom": "ipad", "filename": "<EMAIL>", "scale": "2x"},
            {"size": "29x29", "idiom": "ipad", "filename": "<EMAIL>", "scale": "1x"},
            {"size": "29x29", "idiom": "ipad", "filename": "<EMAIL>", "scale": "2x"},
            {"size": "40x40", "idiom": "ipad", "filename": "<EMAIL>", "scale": "1x"},
            {"size": "40x40", "idiom": "ipad", "filename": "<EMAIL>", "scale": "2x"},
            {"size": "76x76", "idiom": "ipad", "filename": "<EMAIL>", "scale": "1x"},
            {"size": "76x76", "idiom": "ipad", "filename": "<EMAIL>", "scale": "2x"},
            {"size": "83.5x83.5", "idiom": "ipad", "filename": "<EMAIL>", "scale": "2x"},
            {"size": "1024x1024", "idiom": "ios-marketing", "filename": "<EMAIL>", "scale": "1x"}
        ],
        "info": {"version": 1, "author": "xcode"}
    }
    
    import json
    with open(contents_path, 'w') as f:
        json.dump(contents, f, indent=2)
    
    print(f"✅ Criado: {contents_path}")

def main():
    project_root = Path(__file__).parent.parent

    print("🎨 Gerando ícones do livro para o app...")

    # Gerar ícones para Android
    generate_android_icons(project_root)

    # Gerar ícones para iOS
    generate_ios_icons(project_root)
    create_contents_json(project_root)

    print("\n✅ Ícones gerados com sucesso!")
    print("📱 Android: android/app/src/main/res/mipmap-*/")
    print("🍎 iOS: ios/EscolaSabatina/Images.xcassets/AppIcon.appiconset/")
    print("\n🔄 Para aplicar as mudanças:")
    print("1. Android: npx react-native run-android")
    print("2. iOS: npx react-native run-ios")

if __name__ == "__main__":
    main()
