#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 1544976 bytes. Error detail: Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:168), pid=1368, tid=14964
#
# JRE version: OpenJDK Runtime Environment (21.0.5) (build 21.0.5+-13047016-b750.29)
# Java VM: OpenJDK 64-Bit Server VM (21.0.5+-13047016-b750.29, mixed mode, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -XX:MaxMetaspaceSize=512m -XX:+HeapDumpOnOutOfMemoryError -Xmx1536m -Dfile.encoding=UTF-8 -Duser.country=BR -Duser.language=pt -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.14.1-bin\baw1sv0jfoi8rxs14qo3h49cs\gradle-8.14.1\lib\agents\gradle-instrumentation-agent-8.14.1.jar org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.14.1

Host: Intel(R) Xeon(R) CPU E3-1220 V2 @ 3.10GHz, 4 cores, 7G,  Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
Time: Wed Aug  6 14:09:37 2025 Hora oficial do Brasil elapsed time: 2011.286212 seconds (0d 0h 33m 31s)

---------------  T H R E A D  ---------------

Current thread (0x0000026b778b9910):  JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=14964, stack(0x000000f9e8500000,0x000000f9e8600000) (1024K)]


Current CompileTask:
C2:2011286 34851       4       org.gradle.api.internal.artifacts.ivyservice.modulecache.PersistentModuleMetadataCache$$Lambda/0x000000010084cca0::get (16 bytes)

Stack: [0x000000f9e8500000,0x000000f9e8600000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6cfb29]
V  [jvm.dll+0x85df93]
V  [jvm.dll+0x8604ee]
V  [jvm.dll+0x860bd3]
V  [jvm.dll+0x27e6b6]
V  [jvm.dll+0xbff6d]
V  [jvm.dll+0xc04a3]
V  [jvm.dll+0x3b5fc1]
V  [jvm.dll+0x381f97]
V  [jvm.dll+0x38140a]
V  [jvm.dll+0x247c62]
V  [jvm.dll+0x247231]
V  [jvm.dll+0x1c5ee4]
V  [jvm.dll+0x25697c]
V  [jvm.dll+0x254ec6]
V  [jvm.dll+0x3f0ce6]
V  [jvm.dll+0x806368]
V  [jvm.dll+0x6ce3fd]
C  [ucrtbase.dll+0x21bb2]
C  [KERNEL32.DLL+0x17374]
C  [ntdll.dll+0x4cc91]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x0000026b083cf2f0, length=45, elements={
0x0000026b5f214990, 0x0000026b7761e440, 0x0000026b778b50e0, 0x0000026b778b6ca0,
0x0000026b778b76f0, 0x0000026b778b8140, 0x0000026b778b8b90, 0x0000026b778b9910,
0x0000026b7789f4e0, 0x0000026b7756de50, 0x0000026b77ba06e0, 0x0000026b7d79c540,
0x0000026b7d8cd9a0, 0x0000026b7d8c2c20, 0x0000026b7da5a830, 0x0000026b7da573b0,
0x0000026b7da57a40, 0x0000026b7e662bd0, 0x0000026b7e665330, 0x0000026b7e667a90,
0x0000026b7e663260, 0x0000026b7efa9360, 0x0000026b7f9a3db0, 0x0000026b7e668120,
0x0000026b7f9a7230, 0x0000026b09bfcec0, 0x0000026b0aaabfa0, 0x0000026b0aaa8b20,
0x0000026b0aaaabf0, 0x0000026b07e88c90, 0x0000026b07e87f70, 0x0000026b07e8d4c0,
0x0000026b07e8f590, 0x0000026b07e8c7a0, 0x0000026b07e89320, 0x0000026b07e8b3f0,
0x0000026b07e8e1e0, 0x0000026b0a01ae60, 0x0000026b0a01b4f0, 0x0000026b0a01a140,
0x0000026b07e8a040, 0x0000026b07e8a6d0, 0x0000026b0a01a7d0, 0x0000026b0a019ab0,
0x0000026b0a01bb80
}

Java Threads: ( => current thread )
  0x0000026b5f214990 JavaThread "main"                              [_thread_blocked, id=16048, stack(0x000000f9e7700000,0x000000f9e7800000) (1024K)]
  0x0000026b7761e440 JavaThread "Reference Handler"          daemon [_thread_blocked, id=9644, stack(0x000000f9e7f00000,0x000000f9e8000000) (1024K)]
  0x0000026b778b50e0 JavaThread "Finalizer"                  daemon [_thread_blocked, id=9624, stack(0x000000f9e8000000,0x000000f9e8100000) (1024K)]
  0x0000026b778b6ca0 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=6772, stack(0x000000f9e8100000,0x000000f9e8200000) (1024K)]
  0x0000026b778b76f0 JavaThread "Attach Listener"            daemon [_thread_blocked, id=304, stack(0x000000f9e8200000,0x000000f9e8300000) (1024K)]
  0x0000026b778b8140 JavaThread "Service Thread"             daemon [_thread_blocked, id=536, stack(0x000000f9e8300000,0x000000f9e8400000) (1024K)]
  0x0000026b778b8b90 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=8072, stack(0x000000f9e8400000,0x000000f9e8500000) (1024K)]
=>0x0000026b778b9910 JavaThread "C2 CompilerThread0"         daemon [_thread_in_native, id=14964, stack(0x000000f9e8500000,0x000000f9e8600000) (1024K)]
  0x0000026b7789f4e0 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=13000, stack(0x000000f9e8600000,0x000000f9e8700000) (1024K)]
  0x0000026b7756de50 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=1660, stack(0x000000f9e8700000,0x000000f9e8800000) (1024K)]
  0x0000026b77ba06e0 JavaThread "Notification Thread"        daemon [_thread_blocked, id=15276, stack(0x000000f9e8800000,0x000000f9e8900000) (1024K)]
  0x0000026b7d79c540 JavaThread "Daemon health stats"               [_thread_blocked, id=1128, stack(0x000000f9e8b00000,0x000000f9e8c00000) (1024K)]
  0x0000026b7d8cd9a0 JavaThread "Incoming local TCP Connector on port 55632"        [_thread_in_native, id=14016, stack(0x000000f9e8d00000,0x000000f9e8e00000) (1024K)]
  0x0000026b7d8c2c20 JavaThread "Daemon periodic checks"            [_thread_blocked, id=13560, stack(0x000000f9e8e00000,0x000000f9e8f00000) (1024K)]
  0x0000026b7da5a830 JavaThread "Cache worker for journal cache (C:\Users\<USER>\.gradle\caches\journal-1)"        [_thread_blocked, id=10904, stack(0x000000f9e9600000,0x000000f9e9700000) (1024K)]
  0x0000026b7da573b0 JavaThread "File lock request listener"        [_thread_in_native, id=10380, stack(0x000000f9e9700000,0x000000f9e9800000) (1024K)]
  0x0000026b7da57a40 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\.gradle\caches\8.14.1\fileHashes)"        [_thread_blocked, id=5372, stack(0x000000f9e9800000,0x000000f9e9900000) (1024K)]
  0x0000026b7e662bd0 JavaThread "File watcher server"        daemon [_thread_in_native, id=3556, stack(0x000000f9e9d00000,0x000000f9e9e00000) (1024K)]
  0x0000026b7e665330 JavaThread "File watcher consumer"      daemon [_thread_blocked, id=15932, stack(0x000000f9e9e00000,0x000000f9e9f00000) (1024K)]
  0x0000026b7e667a90 JavaThread "jar transforms"                    [_thread_blocked, id=13604, stack(0x000000f9e9f00000,0x000000f9ea000000) (1024K)]
  0x0000026b7e663260 JavaThread "Cache worker for file content cache (C:\Users\<USER>\.gradle\caches\8.14.1\fileContent)"        [_thread_blocked, id=9260, stack(0x000000f9ea100000,0x000000f9ea200000) (1024K)]
  0x0000026b7efa9360 JavaThread "Memory manager"                    [_thread_blocked, id=1440, stack(0x000000f9eb000000,0x000000f9eb100000) (1024K)]
  0x0000026b7f9a3db0 JavaThread "jar transforms Thread 2"           [_thread_blocked, id=4364, stack(0x000000f9ed600000,0x000000f9ed700000) (1024K)]
  0x0000026b7e668120 JavaThread "jar transforms Thread 3"           [_thread_blocked, id=7096, stack(0x000000f9ed700000,0x000000f9ed800000) (1024K)]
  0x0000026b7f9a7230 JavaThread "jar transforms Thread 4"           [_thread_blocked, id=3640, stack(0x000000f9edc00000,0x000000f9edd00000) (1024K)]
  0x0000026b09bfcec0 JavaThread "Daemon Thread 4"                   [_thread_blocked, id=9860, stack(0x000000f9e7500000,0x000000f9e7600000) (1024K)]
  0x0000026b0aaabfa0 JavaThread "Handler for socket connection from /127.0.0.1:55632 to /127.0.0.1:56756"        [_thread_in_native, id=6736, stack(0x000000f9e7600000,0x000000f9e7700000) (1024K)]
  0x0000026b0aaa8b20 JavaThread "Cancel handler"                    [_thread_blocked, id=9864, stack(0x000000f9e8f00000,0x000000f9e9000000) (1024K)]
  0x0000026b0aaaabf0 JavaThread "Daemon worker Thread 4"            [_thread_in_Java, id=2204, stack(0x000000f9e9000000,0x000000f9e9100000) (1024K)]
  0x0000026b07e88c90 JavaThread "Asynchronous log dispatcher for DefaultDaemonConnection: socket connection from /127.0.0.1:55632 to /127.0.0.1:56756"        [_thread_blocked, id=10148, stack(0x000000f9e9100000,0x000000f9e9200000) (1024K)]
  0x0000026b07e87f70 JavaThread "Stdin handler"                     [_thread_blocked, id=332, stack(0x000000f9e9200000,0x000000f9e9300000) (1024K)]
  0x0000026b07e8d4c0 JavaThread "Daemon client event forwarder"        [_thread_blocked, id=4808, stack(0x000000f9e9300000,0x000000f9e9400000) (1024K)]
  0x0000026b07e8f590 JavaThread "Cache worker for file hash cache (F:\PROJETO\React\EscolaSabatina\android\.gradle\8.14.1\fileHashes)"        [_thread_blocked, id=2220, stack(0x000000f9e9400000,0x000000f9e9500000) (1024K)]
  0x0000026b07e8c7a0 JavaThread "Cache worker for Build Output Cleanup Cache (F:\PROJETO\React\EscolaSabatina\android\.gradle\buildOutputCleanup)"        [_thread_blocked, id=11372, stack(0x000000f9e9500000,0x000000f9e9600000) (1024K)]
  0x0000026b07e89320 JavaThread "Cache worker for checksums cache (F:\PROJETO\React\EscolaSabatina\android\.gradle\8.14.1\checksums)"        [_thread_blocked, id=12028, stack(0x000000f9e9900000,0x000000f9e9a00000) (1024K)]
  0x0000026b07e8b3f0 JavaThread "Cache worker for cache directory md-supplier (C:\Users\<USER>\.gradle\caches\8.14.1\md-supplier)"        [_thread_blocked, id=17180, stack(0x000000f9e9a00000,0x000000f9e9b00000) (1024K)]
  0x0000026b07e8e1e0 JavaThread "Cache worker for cache directory md-rule (C:\Users\<USER>\.gradle\caches\8.14.1\md-rule)"        [_thread_blocked, id=1752, stack(0x000000f9e9b00000,0x000000f9e9c00000) (1024K)]
  0x0000026b0a01ae60 JavaThread "Cache worker for Build Output Cleanup Cache (F:\PROJETO\React\EscolaSabatina\node_modules\@react-native\gradle-plugin\.gradle\buildOutputCleanup)"        [_thread_blocked, id=15128, stack(0x000000f9e9c00000,0x000000f9e9d00000) (1024K)]
  0x0000026b0a01b4f0 JavaThread "Unconstrained build operations"        [_thread_blocked, id=8868, stack(0x000000f9ea000000,0x000000f9ea100000) (1024K)]
  0x0000026b0a01a140 JavaThread "Unconstrained build operations Thread 2"        [_thread_blocked, id=9132, stack(0x000000f9ea200000,0x000000f9ea300000) (1024K)]
  0x0000026b07e8a040 JavaThread "Unconstrained build operations Thread 3"        [_thread_blocked, id=9996, stack(0x000000f9ea300000,0x000000f9ea400000) (1024K)]
  0x0000026b07e8a6d0 JavaThread "Unconstrained build operations Thread 4"        [_thread_blocked, id=15992, stack(0x000000f9ea400000,0x000000f9ea500000) (1024K)]
  0x0000026b0a01a7d0 JavaThread "Unconstrained build operations Thread 5"        [_thread_new, id=1872, stack(0x0000000000000000,0x0000000000000000) (0B)]
  0x0000026b0a019ab0 JavaThread "Unconstrained build operations Thread 6"        [_thread_new, id=856, stack(0x0000000000000000,0x0000000000000000) (0B)]
  0x0000026b0a01bb80 JavaThread "Unconstrained build operations Thread 7"        [_thread_new, id=11560, stack(0x0000000000000000,0x0000000000000000) (0B)]
Total: 45

Other Threads:
  0x0000026b775d5770 VMThread "VM Thread"                           [id=9164, stack(0x000000f9e7e00000,0x000000f9e7f00000) (1024K)]
  0x0000026b7756c300 WatcherThread "VM Periodic Task Thread"        [id=11924, stack(0x000000f9e7d00000,0x000000f9e7e00000) (1024K)]
  0x0000026b5f25c4d0 WorkerThread "GC Thread#0"                     [id=14584, stack(0x000000f9e7800000,0x000000f9e7900000) (1024K)]
  0x0000026b7c0db200 WorkerThread "GC Thread#1"                     [id=12748, stack(0x000000f9e8900000,0x000000f9e8a00000) (1024K)]
  0x0000026b77ba4230 WorkerThread "GC Thread#2"                     [id=9312, stack(0x000000f9e8a00000,0x000000f9e8b00000) (1024K)]
  0x0000026b7caedd20 WorkerThread "GC Thread#3"                     [id=10344, stack(0x000000f9e8c00000,0x000000f9e8d00000) (1024K)]
  0x0000026b5f2693c0 ConcurrentGCThread "G1 Main Marker"            [id=10092, stack(0x000000f9e7900000,0x000000f9e7a00000) (1024K)]
  0x0000026b5f269dd0 WorkerThread "G1 Conc#0"                       [id=11848, stack(0x000000f9e7a00000,0x000000f9e7b00000) (1024K)]
  0x0000026b5f2de8a0 ConcurrentGCThread "G1 Refine#0"               [id=12456, stack(0x000000f9e7b00000,0x000000f9e7c00000) (1024K)]
  0x0000026b774344a0 ConcurrentGCThread "G1 Service"                [id=9600, stack(0x000000f9e7c00000,0x000000f9e7d00000) (1024K)]
Total: 10

Threads with active compile tasks:
C2 CompilerThread0  2011340 34851       4       org.gradle.api.internal.artifacts.ivyservice.modulecache.PersistentModuleMetadataCache$$Lambda/0x000000010084cca0::get (16 bytes)
Total: 1

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x00000000a0000000, size: 1536 MB, Compressed Oops mode: 32-bit

CDS archive(s) not mapped
Compressed class space mapped at: 0x0000000100000000-0x000000011a000000, reserved size: 436207616
Narrow klass base: 0x0000000000000000, Narrow klass shift: 3, Narrow klass range: 0x11a000000

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 5 size 8 Array Of Cards #cards 12 size 40 Howl #buckets 4 coarsen threshold 1843 Howl Bitmap #cards 512 size 80 coarsen threshold 460 Card regions per heap region 1 cards per card region 2048
 CPUs: 4 total, 4 available
 Memory: 8161M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Heap Region Size: 1M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 128M
 Heap Max Capacity: 1536M
 Pre-touch: Disabled
 Parallel Workers: 4
 Concurrent Workers: 1
 Concurrent Refinement Workers: 4
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 759808K, used 612864K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 235 young (240640K), 37 survivors (37888K)
 Metaspace       used 162943K, committed 166464K, reserved 622592K
  class space    used 21328K, committed 23040K, reserved 425984K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom
|   0|0x00000000a0000000, 0x00000000a0100000, 0x00000000a0100000|100%|HS|  |TAMS 0x00000000a0000000| PB 0x00000000a0000000| Complete 
|   1|0x00000000a0100000, 0x00000000a0200000, 0x00000000a0200000|100%|HC|  |TAMS 0x00000000a0100000| PB 0x00000000a0100000| Complete 
|   2|0x00000000a0200000, 0x00000000a0300000, 0x00000000a0300000|100%|HC|  |TAMS 0x00000000a0200000| PB 0x00000000a0200000| Complete 
|   3|0x00000000a0300000, 0x00000000a0400000, 0x00000000a0400000|100%| O|  |TAMS 0x00000000a0300000| PB 0x00000000a0300000| Untracked 
|   4|0x00000000a0400000, 0x00000000a0500000, 0x00000000a0500000|100%| O|  |TAMS 0x00000000a0400000| PB 0x00000000a0400000| Untracked 
|   5|0x00000000a0500000, 0x00000000a0600000, 0x00000000a0600000|100%| O|  |TAMS 0x00000000a0500000| PB 0x00000000a0500000| Untracked 
|   6|0x00000000a0600000, 0x00000000a0700000, 0x00000000a0700000|100%| O|  |TAMS 0x00000000a0600000| PB 0x00000000a0600000| Untracked 
|   7|0x00000000a0700000, 0x00000000a0800000, 0x00000000a0800000|100%|HS|  |TAMS 0x00000000a0700000| PB 0x00000000a0700000| Complete 
|   8|0x00000000a0800000, 0x00000000a0900000, 0x00000000a0900000|100%| O|  |TAMS 0x00000000a0800000| PB 0x00000000a0800000| Untracked 
|   9|0x00000000a0900000, 0x00000000a0a00000, 0x00000000a0a00000|100%| O|  |TAMS 0x00000000a0900000| PB 0x00000000a0900000| Untracked 
|  10|0x00000000a0a00000, 0x00000000a0b00000, 0x00000000a0b00000|100%| O|  |TAMS 0x00000000a0a00000| PB 0x00000000a0a00000| Untracked 
|  11|0x00000000a0b00000, 0x00000000a0c00000, 0x00000000a0c00000|100%| O|  |TAMS 0x00000000a0b00000| PB 0x00000000a0b00000| Untracked 
|  12|0x00000000a0c00000, 0x00000000a0d00000, 0x00000000a0d00000|100%| O|  |TAMS 0x00000000a0c00000| PB 0x00000000a0c00000| Untracked 
|  13|0x00000000a0d00000, 0x00000000a0e00000, 0x00000000a0e00000|100%| O|  |TAMS 0x00000000a0d00000| PB 0x00000000a0d00000| Untracked 
|  14|0x00000000a0e00000, 0x00000000a0f00000, 0x00000000a0f00000|100%| O|  |TAMS 0x00000000a0e00000| PB 0x00000000a0e00000| Untracked 
|  15|0x00000000a0f00000, 0x00000000a1000000, 0x00000000a1000000|100%| O|  |TAMS 0x00000000a0f00000| PB 0x00000000a0f00000| Untracked 
|  16|0x00000000a1000000, 0x00000000a1100000, 0x00000000a1100000|100%| O|  |TAMS 0x00000000a1000000| PB 0x00000000a1000000| Untracked 
|  17|0x00000000a1100000, 0x00000000a1200000, 0x00000000a1200000|100%| O|  |TAMS 0x00000000a1100000| PB 0x00000000a1100000| Untracked 
|  18|0x00000000a1200000, 0x00000000a1300000, 0x00000000a1300000|100%| O|  |TAMS 0x00000000a1200000| PB 0x00000000a1200000| Untracked 
|  19|0x00000000a1300000, 0x00000000a1400000, 0x00000000a1400000|100%| O|  |TAMS 0x00000000a1300000| PB 0x00000000a1300000| Untracked 
|  20|0x00000000a1400000, 0x00000000a1500000, 0x00000000a1500000|100%| O|  |TAMS 0x00000000a1400000| PB 0x00000000a1400000| Untracked 
|  21|0x00000000a1500000, 0x00000000a1600000, 0x00000000a1600000|100%| O|  |TAMS 0x00000000a1500000| PB 0x00000000a1500000| Untracked 
|  22|0x00000000a1600000, 0x00000000a1700000, 0x00000000a1700000|100%| O|  |TAMS 0x00000000a1600000| PB 0x00000000a1600000| Untracked 
|  23|0x00000000a1700000, 0x00000000a1800000, 0x00000000a1800000|100%| O|  |TAMS 0x00000000a1700000| PB 0x00000000a1700000| Untracked 
|  24|0x00000000a1800000, 0x00000000a1900000, 0x00000000a1900000|100%| O|  |TAMS 0x00000000a1800000| PB 0x00000000a1800000| Untracked 
|  25|0x00000000a1900000, 0x00000000a1a00000, 0x00000000a1a00000|100%| O|  |TAMS 0x00000000a1900000| PB 0x00000000a1900000| Untracked 
|  26|0x00000000a1a00000, 0x00000000a1b00000, 0x00000000a1b00000|100%| O|  |TAMS 0x00000000a1a00000| PB 0x00000000a1a00000| Untracked 
|  27|0x00000000a1b00000, 0x00000000a1c00000, 0x00000000a1c00000|100%| O|  |TAMS 0x00000000a1b00000| PB 0x00000000a1b00000| Untracked 
|  28|0x00000000a1c00000, 0x00000000a1d00000, 0x00000000a1d00000|100%| O|  |TAMS 0x00000000a1c00000| PB 0x00000000a1c00000| Untracked 
|  29|0x00000000a1d00000, 0x00000000a1e00000, 0x00000000a1e00000|100%| O|  |TAMS 0x00000000a1d00000| PB 0x00000000a1d00000| Untracked 
|  30|0x00000000a1e00000, 0x00000000a1f00000, 0x00000000a1f00000|100%| O|  |TAMS 0x00000000a1e00000| PB 0x00000000a1e00000| Untracked 
|  31|0x00000000a1f00000, 0x00000000a2000000, 0x00000000a2000000|100%| O|  |TAMS 0x00000000a1f00000| PB 0x00000000a1f00000| Untracked 
|  32|0x00000000a2000000, 0x00000000a2100000, 0x00000000a2100000|100%| O|  |TAMS 0x00000000a2000000| PB 0x00000000a2000000| Untracked 
|  33|0x00000000a2100000, 0x00000000a2200000, 0x00000000a2200000|100%| O|  |TAMS 0x00000000a2100000| PB 0x00000000a2100000| Untracked 
|  34|0x00000000a2200000, 0x00000000a2300000, 0x00000000a2300000|100%| O|  |TAMS 0x00000000a2200000| PB 0x00000000a2200000| Untracked 
|  35|0x00000000a2300000, 0x00000000a2400000, 0x00000000a2400000|100%| O|  |TAMS 0x00000000a2300000| PB 0x00000000a2300000| Untracked 
|  36|0x00000000a2400000, 0x00000000a2500000, 0x00000000a2500000|100%| O|  |TAMS 0x00000000a2400000| PB 0x00000000a2400000| Untracked 
|  37|0x00000000a2500000, 0x00000000a2600000, 0x00000000a2600000|100%| O|  |TAMS 0x00000000a2500000| PB 0x00000000a2500000| Untracked 
|  38|0x00000000a2600000, 0x00000000a2700000, 0x00000000a2700000|100%| O|  |TAMS 0x00000000a2600000| PB 0x00000000a2600000| Untracked 
|  39|0x00000000a2700000, 0x00000000a2800000, 0x00000000a2800000|100%| O|  |TAMS 0x00000000a2700000| PB 0x00000000a2700000| Untracked 
|  40|0x00000000a2800000, 0x00000000a2900000, 0x00000000a2900000|100%| O|  |TAMS 0x00000000a2800000| PB 0x00000000a2800000| Untracked 
|  41|0x00000000a2900000, 0x00000000a2a00000, 0x00000000a2a00000|100%|HS|  |TAMS 0x00000000a2900000| PB 0x00000000a2900000| Complete 
|  42|0x00000000a2a00000, 0x00000000a2b00000, 0x00000000a2b00000|100%| O|  |TAMS 0x00000000a2a00000| PB 0x00000000a2a00000| Untracked 
|  43|0x00000000a2b00000, 0x00000000a2c00000, 0x00000000a2c00000|100%|HS|  |TAMS 0x00000000a2b00000| PB 0x00000000a2b00000| Complete 
|  44|0x00000000a2c00000, 0x00000000a2d00000, 0x00000000a2d00000|100%|HS|  |TAMS 0x00000000a2c00000| PB 0x00000000a2c00000| Complete 
|  45|0x00000000a2d00000, 0x00000000a2e00000, 0x00000000a2e00000|100%| O|  |TAMS 0x00000000a2d00000| PB 0x00000000a2d00000| Untracked 
|  46|0x00000000a2e00000, 0x00000000a2f00000, 0x00000000a2f00000|100%| O|  |TAMS 0x00000000a2e00000| PB 0x00000000a2e00000| Untracked 
|  47|0x00000000a2f00000, 0x00000000a3000000, 0x00000000a3000000|100%|HS|  |TAMS 0x00000000a2f00000| PB 0x00000000a2f00000| Complete 
|  48|0x00000000a3000000, 0x00000000a3100000, 0x00000000a3100000|100%|HC|  |TAMS 0x00000000a3000000| PB 0x00000000a3000000| Complete 
|  49|0x00000000a3100000, 0x00000000a3200000, 0x00000000a3200000|100%|HC|  |TAMS 0x00000000a3100000| PB 0x00000000a3100000| Complete 
|  50|0x00000000a3200000, 0x00000000a3300000, 0x00000000a3300000|100%|HS|  |TAMS 0x00000000a3200000| PB 0x00000000a3200000| Complete 
|  51|0x00000000a3300000, 0x00000000a3400000, 0x00000000a3400000|100%|HC|  |TAMS 0x00000000a3300000| PB 0x00000000a3300000| Complete 
|  52|0x00000000a3400000, 0x00000000a3500000, 0x00000000a3500000|100%|HC|  |TAMS 0x00000000a3400000| PB 0x00000000a3400000| Complete 
|  53|0x00000000a3500000, 0x00000000a3600000, 0x00000000a3600000|100%| O|  |TAMS 0x00000000a3500000| PB 0x00000000a3500000| Untracked 
|  54|0x00000000a3600000, 0x00000000a3700000, 0x00000000a3700000|100%| O|  |TAMS 0x00000000a3600000| PB 0x00000000a3600000| Untracked 
|  55|0x00000000a3700000, 0x00000000a3800000, 0x00000000a3800000|100%|HS|  |TAMS 0x00000000a3700000| PB 0x00000000a3700000| Complete 
|  56|0x00000000a3800000, 0x00000000a3900000, 0x00000000a3900000|100%| O|  |TAMS 0x00000000a3800000| PB 0x00000000a3800000| Untracked 
|  57|0x00000000a3900000, 0x00000000a3a00000, 0x00000000a3a00000|100%| O|  |TAMS 0x00000000a3900000| PB 0x00000000a3900000| Untracked 
|  58|0x00000000a3a00000, 0x00000000a3b00000, 0x00000000a3b00000|100%| O|  |TAMS 0x00000000a3a00000| PB 0x00000000a3a00000| Untracked 
|  59|0x00000000a3b00000, 0x00000000a3c00000, 0x00000000a3c00000|100%| O|  |TAMS 0x00000000a3b00000| PB 0x00000000a3b00000| Untracked 
|  60|0x00000000a3c00000, 0x00000000a3d00000, 0x00000000a3d00000|100%| O|  |TAMS 0x00000000a3c00000| PB 0x00000000a3c00000| Untracked 
|  61|0x00000000a3d00000, 0x00000000a3e00000, 0x00000000a3e00000|100%| O|  |TAMS 0x00000000a3d00000| PB 0x00000000a3d00000| Untracked 
|  62|0x00000000a3e00000, 0x00000000a3f00000, 0x00000000a3f00000|100%| O|  |TAMS 0x00000000a3e00000| PB 0x00000000a3e00000| Untracked 
|  63|0x00000000a3f00000, 0x00000000a4000000, 0x00000000a4000000|100%| O|  |TAMS 0x00000000a3f00000| PB 0x00000000a3f00000| Untracked 
|  64|0x00000000a4000000, 0x00000000a4100000, 0x00000000a4100000|100%| O|  |TAMS 0x00000000a4000000| PB 0x00000000a4000000| Untracked 
|  65|0x00000000a4100000, 0x00000000a4200000, 0x00000000a4200000|100%| O|  |TAMS 0x00000000a4100000| PB 0x00000000a4100000| Untracked 
|  66|0x00000000a4200000, 0x00000000a4300000, 0x00000000a4300000|100%|HS|  |TAMS 0x00000000a4200000| PB 0x00000000a4200000| Complete 
|  67|0x00000000a4300000, 0x00000000a4400000, 0x00000000a4400000|100%|HS|  |TAMS 0x00000000a4300000| PB 0x00000000a4300000| Complete 
|  68|0x00000000a4400000, 0x00000000a4500000, 0x00000000a4500000|100%|HS|  |TAMS 0x00000000a4400000| PB 0x00000000a4400000| Complete 
|  69|0x00000000a4500000, 0x00000000a4600000, 0x00000000a4600000|100%|HS|  |TAMS 0x00000000a4500000| PB 0x00000000a4500000| Complete 
|  70|0x00000000a4600000, 0x00000000a4700000, 0x00000000a4700000|100%| O|  |TAMS 0x00000000a4600000| PB 0x00000000a4600000| Untracked 
|  71|0x00000000a4700000, 0x00000000a4800000, 0x00000000a4800000|100%| O|  |TAMS 0x00000000a4700000| PB 0x00000000a4700000| Untracked 
|  72|0x00000000a4800000, 0x00000000a4900000, 0x00000000a4900000|100%|HS|  |TAMS 0x00000000a4800000| PB 0x00000000a4800000| Complete 
|  73|0x00000000a4900000, 0x00000000a4a00000, 0x00000000a4a00000|100%|HS|  |TAMS 0x00000000a4900000| PB 0x00000000a4900000| Complete 
|  74|0x00000000a4a00000, 0x00000000a4b00000, 0x00000000a4b00000|100%|HS|  |TAMS 0x00000000a4a00000| PB 0x00000000a4a00000| Complete 
|  75|0x00000000a4b00000, 0x00000000a4c00000, 0x00000000a4c00000|100%| O|  |TAMS 0x00000000a4b00000| PB 0x00000000a4b00000| Untracked 
|  76|0x00000000a4c00000, 0x00000000a4d00000, 0x00000000a4d00000|100%| O|  |TAMS 0x00000000a4c00000| PB 0x00000000a4c00000| Untracked 
|  77|0x00000000a4d00000, 0x00000000a4e00000, 0x00000000a4e00000|100%| O|  |TAMS 0x00000000a4d00000| PB 0x00000000a4d00000| Untracked 
|  78|0x00000000a4e00000, 0x00000000a4f00000, 0x00000000a4f00000|100%| O|  |TAMS 0x00000000a4e00000| PB 0x00000000a4e00000| Untracked 
|  79|0x00000000a4f00000, 0x00000000a5000000, 0x00000000a5000000|100%| O|  |TAMS 0x00000000a4f00000| PB 0x00000000a4f00000| Untracked 
|  80|0x00000000a5000000, 0x00000000a5100000, 0x00000000a5100000|100%| O|  |TAMS 0x00000000a5000000| PB 0x00000000a5000000| Untracked 
|  81|0x00000000a5100000, 0x00000000a5200000, 0x00000000a5200000|100%|HS|  |TAMS 0x00000000a5100000| PB 0x00000000a5100000| Complete 
|  82|0x00000000a5200000, 0x00000000a5300000, 0x00000000a5300000|100%|HS|  |TAMS 0x00000000a5200000| PB 0x00000000a5200000| Complete 
|  83|0x00000000a5300000, 0x00000000a5400000, 0x00000000a5400000|100%| O|  |TAMS 0x00000000a5300000| PB 0x00000000a5300000| Untracked 
|  84|0x00000000a5400000, 0x00000000a5500000, 0x00000000a5500000|100%| O|  |TAMS 0x00000000a5400000| PB 0x00000000a5400000| Untracked 
|  85|0x00000000a5500000, 0x00000000a5600000, 0x00000000a5600000|100%|HS|  |TAMS 0x00000000a5500000| PB 0x00000000a5500000| Complete 
|  86|0x00000000a5600000, 0x00000000a5700000, 0x00000000a5700000|100%|HC|  |TAMS 0x00000000a5600000| PB 0x00000000a5600000| Complete 
|  87|0x00000000a5700000, 0x00000000a5800000, 0x00000000a5800000|100%|HC|  |TAMS 0x00000000a5700000| PB 0x00000000a5700000| Complete 
|  88|0x00000000a5800000, 0x00000000a5900000, 0x00000000a5900000|100%| O|  |TAMS 0x00000000a5800000| PB 0x00000000a5800000| Untracked 
|  89|0x00000000a5900000, 0x00000000a5a00000, 0x00000000a5a00000|100%| O|  |TAMS 0x00000000a5900000| PB 0x00000000a5900000| Untracked 
|  90|0x00000000a5a00000, 0x00000000a5b00000, 0x00000000a5b00000|100%| O|  |TAMS 0x00000000a5a00000| PB 0x00000000a5a00000| Untracked 
|  91|0x00000000a5b00000, 0x00000000a5c00000, 0x00000000a5c00000|100%| O|  |TAMS 0x00000000a5b00000| PB 0x00000000a5b00000| Untracked 
|  92|0x00000000a5c00000, 0x00000000a5d00000, 0x00000000a5d00000|100%| O|  |TAMS 0x00000000a5c00000| PB 0x00000000a5c00000| Untracked 
|  93|0x00000000a5d00000, 0x00000000a5e00000, 0x00000000a5e00000|100%| O|  |TAMS 0x00000000a5d00000| PB 0x00000000a5d00000| Untracked 
|  94|0x00000000a5e00000, 0x00000000a5f00000, 0x00000000a5f00000|100%| O|  |TAMS 0x00000000a5e00000| PB 0x00000000a5e00000| Untracked 
|  95|0x00000000a5f00000, 0x00000000a6000000, 0x00000000a6000000|100%|HS|  |TAMS 0x00000000a5f00000| PB 0x00000000a5f00000| Complete 
|  96|0x00000000a6000000, 0x00000000a6100000, 0x00000000a6100000|100%| O|  |TAMS 0x00000000a6000000| PB 0x00000000a6000000| Untracked 
|  97|0x00000000a6100000, 0x00000000a6200000, 0x00000000a6200000|100%| O|  |TAMS 0x00000000a6100000| PB 0x00000000a6100000| Untracked 
|  98|0x00000000a6200000, 0x00000000a6300000, 0x00000000a6300000|100%| O|  |TAMS 0x00000000a6200000| PB 0x00000000a6200000| Untracked 
|  99|0x00000000a6300000, 0x00000000a6400000, 0x00000000a6400000|100%| O|  |TAMS 0x00000000a6300000| PB 0x00000000a6300000| Untracked 
| 100|0x00000000a6400000, 0x00000000a6500000, 0x00000000a6500000|100%| O|  |TAMS 0x00000000a6400000| PB 0x00000000a6400000| Untracked 
| 101|0x00000000a6500000, 0x00000000a6600000, 0x00000000a6600000|100%| O|  |TAMS 0x00000000a6500000| PB 0x00000000a6500000| Untracked 
| 102|0x00000000a6600000, 0x00000000a6700000, 0x00000000a6700000|100%| O|  |TAMS 0x00000000a6600000| PB 0x00000000a6600000| Untracked 
| 103|0x00000000a6700000, 0x00000000a6800000, 0x00000000a6800000|100%|HS|  |TAMS 0x00000000a6700000| PB 0x00000000a6700000| Complete 
| 104|0x00000000a6800000, 0x00000000a6900000, 0x00000000a6900000|100%| O|  |TAMS 0x00000000a6800000| PB 0x00000000a6800000| Untracked 
| 105|0x00000000a6900000, 0x00000000a6a00000, 0x00000000a6a00000|100%| O|  |TAMS 0x00000000a6900000| PB 0x00000000a6900000| Untracked 
| 106|0x00000000a6a00000, 0x00000000a6b00000, 0x00000000a6b00000|100%| O|  |TAMS 0x00000000a6a00000| PB 0x00000000a6a00000| Untracked 
| 107|0x00000000a6b00000, 0x00000000a6c00000, 0x00000000a6c00000|100%| O|  |TAMS 0x00000000a6b00000| PB 0x00000000a6b00000| Untracked 
| 108|0x00000000a6c00000, 0x00000000a6d00000, 0x00000000a6d00000|100%| O|  |TAMS 0x00000000a6c00000| PB 0x00000000a6c00000| Untracked 
| 109|0x00000000a6d00000, 0x00000000a6d00000, 0x00000000a6e00000|  0%| F|  |TAMS 0x00000000a6d00000| PB 0x00000000a6d00000| Untracked 
| 110|0x00000000a6e00000, 0x00000000a6f00000, 0x00000000a6f00000|100%| O|  |TAMS 0x00000000a6e00000| PB 0x00000000a6e00000| Untracked 
| 111|0x00000000a6f00000, 0x00000000a7000000, 0x00000000a7000000|100%| O|  |TAMS 0x00000000a6f00000| PB 0x00000000a6f00000| Untracked 
| 112|0x00000000a7000000, 0x00000000a7100000, 0x00000000a7100000|100%| O|  |TAMS 0x00000000a7000000| PB 0x00000000a7000000| Untracked 
| 113|0x00000000a7100000, 0x00000000a7200000, 0x00000000a7200000|100%| O|  |TAMS 0x00000000a7100000| PB 0x00000000a7100000| Untracked 
| 114|0x00000000a7200000, 0x00000000a7300000, 0x00000000a7300000|100%| O|  |TAMS 0x00000000a7200000| PB 0x00000000a7200000| Untracked 
| 115|0x00000000a7300000, 0x00000000a7400000, 0x00000000a7400000|100%| O|  |TAMS 0x00000000a7300000| PB 0x00000000a7300000| Untracked 
| 116|0x00000000a7400000, 0x00000000a7500000, 0x00000000a7500000|100%| O|  |TAMS 0x00000000a7400000| PB 0x00000000a7400000| Untracked 
| 117|0x00000000a7500000, 0x00000000a7600000, 0x00000000a7600000|100%| O|  |TAMS 0x00000000a7500000| PB 0x00000000a7500000| Untracked 
| 118|0x00000000a7600000, 0x00000000a7700000, 0x00000000a7700000|100%| O|  |TAMS 0x00000000a7600000| PB 0x00000000a7600000| Untracked 
| 119|0x00000000a7700000, 0x00000000a7800000, 0x00000000a7800000|100%| O|  |TAMS 0x00000000a7700000| PB 0x00000000a7700000| Untracked 
| 120|0x00000000a7800000, 0x00000000a7900000, 0x00000000a7900000|100%| O|  |TAMS 0x00000000a7800000| PB 0x00000000a7800000| Untracked 
| 121|0x00000000a7900000, 0x00000000a7a00000, 0x00000000a7a00000|100%| O|  |TAMS 0x00000000a7900000| PB 0x00000000a7900000| Untracked 
| 122|0x00000000a7a00000, 0x00000000a7b00000, 0x00000000a7b00000|100%| O|  |TAMS 0x00000000a7a00000| PB 0x00000000a7a00000| Untracked 
| 123|0x00000000a7b00000, 0x00000000a7c00000, 0x00000000a7c00000|100%| O|  |TAMS 0x00000000a7b00000| PB 0x00000000a7b00000| Untracked 
| 124|0x00000000a7c00000, 0x00000000a7d00000, 0x00000000a7d00000|100%| O|  |TAMS 0x00000000a7c00000| PB 0x00000000a7c00000| Untracked 
| 125|0x00000000a7d00000, 0x00000000a7e00000, 0x00000000a7e00000|100%| O|  |TAMS 0x00000000a7d00000| PB 0x00000000a7d00000| Untracked 
| 126|0x00000000a7e00000, 0x00000000a7f00000, 0x00000000a7f00000|100%| O|  |TAMS 0x00000000a7e00000| PB 0x00000000a7e00000| Untracked 
| 127|0x00000000a7f00000, 0x00000000a8000000, 0x00000000a8000000|100%| O|  |TAMS 0x00000000a7f00000| PB 0x00000000a7f00000| Untracked 
| 128|0x00000000a8000000, 0x00000000a8100000, 0x00000000a8100000|100%| O|  |TAMS 0x00000000a8000000| PB 0x00000000a8000000| Untracked 
| 129|0x00000000a8100000, 0x00000000a8200000, 0x00000000a8200000|100%| O|  |TAMS 0x00000000a8100000| PB 0x00000000a8100000| Untracked 
| 130|0x00000000a8200000, 0x00000000a8300000, 0x00000000a8300000|100%| O|  |TAMS 0x00000000a8200000| PB 0x00000000a8200000| Untracked 
| 131|0x00000000a8300000, 0x00000000a8400000, 0x00000000a8400000|100%| O|  |TAMS 0x00000000a8300000| PB 0x00000000a8300000| Untracked 
| 132|0x00000000a8400000, 0x00000000a8500000, 0x00000000a8500000|100%| O|  |TAMS 0x00000000a8400000| PB 0x00000000a8400000| Untracked 
| 133|0x00000000a8500000, 0x00000000a8600000, 0x00000000a8600000|100%| O|  |TAMS 0x00000000a8500000| PB 0x00000000a8500000| Untracked 
| 134|0x00000000a8600000, 0x00000000a8700000, 0x00000000a8700000|100%| O|  |TAMS 0x00000000a8600000| PB 0x00000000a8600000| Untracked 
| 135|0x00000000a8700000, 0x00000000a8800000, 0x00000000a8800000|100%| O|  |TAMS 0x00000000a8700000| PB 0x00000000a8700000| Untracked 
| 136|0x00000000a8800000, 0x00000000a8900000, 0x00000000a8900000|100%| O|  |TAMS 0x00000000a8800000| PB 0x00000000a8800000| Untracked 
| 137|0x00000000a8900000, 0x00000000a8a00000, 0x00000000a8a00000|100%| O|  |TAMS 0x00000000a8900000| PB 0x00000000a8900000| Untracked 
| 138|0x00000000a8a00000, 0x00000000a8b00000, 0x00000000a8b00000|100%| O|  |TAMS 0x00000000a8a00000| PB 0x00000000a8a00000| Untracked 
| 139|0x00000000a8b00000, 0x00000000a8c00000, 0x00000000a8c00000|100%| O|  |TAMS 0x00000000a8b00000| PB 0x00000000a8b00000| Untracked 
| 140|0x00000000a8c00000, 0x00000000a8d00000, 0x00000000a8d00000|100%| O|  |TAMS 0x00000000a8c00000| PB 0x00000000a8c00000| Untracked 
| 141|0x00000000a8d00000, 0x00000000a8e00000, 0x00000000a8e00000|100%|HS|  |TAMS 0x00000000a8d00000| PB 0x00000000a8d00000| Complete 
| 142|0x00000000a8e00000, 0x00000000a8f00000, 0x00000000a8f00000|100%| O|  |TAMS 0x00000000a8e00000| PB 0x00000000a8e00000| Untracked 
| 143|0x00000000a8f00000, 0x00000000a9000000, 0x00000000a9000000|100%| O|  |TAMS 0x00000000a8f00000| PB 0x00000000a8f00000| Untracked 
| 144|0x00000000a9000000, 0x00000000a9100000, 0x00000000a9100000|100%| O|  |TAMS 0x00000000a9000000| PB 0x00000000a9000000| Untracked 
| 145|0x00000000a9100000, 0x00000000a9200000, 0x00000000a9200000|100%| O|  |TAMS 0x00000000a9100000| PB 0x00000000a9100000| Untracked 
| 146|0x00000000a9200000, 0x00000000a9300000, 0x00000000a9300000|100%| O|  |TAMS 0x00000000a9200000| PB 0x00000000a9200000| Untracked 
| 147|0x00000000a9300000, 0x00000000a9400000, 0x00000000a9400000|100%| O|  |TAMS 0x00000000a9300000| PB 0x00000000a9300000| Untracked 
| 148|0x00000000a9400000, 0x00000000a9500000, 0x00000000a9500000|100%|HS|  |TAMS 0x00000000a9400000| PB 0x00000000a9400000| Complete 
| 149|0x00000000a9500000, 0x00000000a9600000, 0x00000000a9600000|100%| O|  |TAMS 0x00000000a9500000| PB 0x00000000a9500000| Untracked 
| 150|0x00000000a9600000, 0x00000000a9700000, 0x00000000a9700000|100%|HS|  |TAMS 0x00000000a9600000| PB 0x00000000a9600000| Complete 
| 151|0x00000000a9700000, 0x00000000a9800000, 0x00000000a9800000|100%|HC|  |TAMS 0x00000000a9700000| PB 0x00000000a9700000| Complete 
| 152|0x00000000a9800000, 0x00000000a9900000, 0x00000000a9900000|100%| O|  |TAMS 0x00000000a9800000| PB 0x00000000a9800000| Untracked 
| 153|0x00000000a9900000, 0x00000000a9a00000, 0x00000000a9a00000|100%| O|  |TAMS 0x00000000a9900000| PB 0x00000000a9900000| Untracked 
| 154|0x00000000a9a00000, 0x00000000a9b00000, 0x00000000a9b00000|100%| O|  |TAMS 0x00000000a9a00000| PB 0x00000000a9a00000| Untracked 
| 155|0x00000000a9b00000, 0x00000000a9b00000, 0x00000000a9c00000|  0%| F|  |TAMS 0x00000000a9b00000| PB 0x00000000a9b00000| Untracked 
| 156|0x00000000a9c00000, 0x00000000a9d00000, 0x00000000a9d00000|100%| O|  |TAMS 0x00000000a9c00000| PB 0x00000000a9c00000| Untracked 
| 157|0x00000000a9d00000, 0x00000000a9e00000, 0x00000000a9e00000|100%| O|  |TAMS 0x00000000a9d00000| PB 0x00000000a9d00000| Untracked 
| 158|0x00000000a9e00000, 0x00000000a9f00000, 0x00000000a9f00000|100%| O|  |TAMS 0x00000000a9e00000| PB 0x00000000a9e00000| Untracked 
| 159|0x00000000a9f00000, 0x00000000aa000000, 0x00000000aa000000|100%| O|  |TAMS 0x00000000a9f00000| PB 0x00000000a9f00000| Untracked 
| 160|0x00000000aa000000, 0x00000000aa100000, 0x00000000aa100000|100%| O|  |TAMS 0x00000000aa000000| PB 0x00000000aa000000| Untracked 
| 161|0x00000000aa100000, 0x00000000aa200000, 0x00000000aa200000|100%| O|  |TAMS 0x00000000aa100000| PB 0x00000000aa100000| Untracked 
| 162|0x00000000aa200000, 0x00000000aa300000, 0x00000000aa300000|100%| O|  |TAMS 0x00000000aa200000| PB 0x00000000aa200000| Untracked 
| 163|0x00000000aa300000, 0x00000000aa400000, 0x00000000aa400000|100%| O|  |TAMS 0x00000000aa300000| PB 0x00000000aa300000| Untracked 
| 164|0x00000000aa400000, 0x00000000aa500000, 0x00000000aa500000|100%| O|  |TAMS 0x00000000aa400000| PB 0x00000000aa400000| Untracked 
| 165|0x00000000aa500000, 0x00000000aa600000, 0x00000000aa600000|100%| O|  |TAMS 0x00000000aa500000| PB 0x00000000aa500000| Untracked 
| 166|0x00000000aa600000, 0x00000000aa700000, 0x00000000aa700000|100%| O|  |TAMS 0x00000000aa600000| PB 0x00000000aa600000| Untracked 
| 167|0x00000000aa700000, 0x00000000aa700000, 0x00000000aa800000|  0%| F|  |TAMS 0x00000000aa700000| PB 0x00000000aa700000| Untracked 
| 168|0x00000000aa800000, 0x00000000aa900000, 0x00000000aa900000|100%| O|  |TAMS 0x00000000aa800000| PB 0x00000000aa800000| Untracked 
| 169|0x00000000aa900000, 0x00000000aaa00000, 0x00000000aaa00000|100%| O|  |TAMS 0x00000000aa900000| PB 0x00000000aa900000| Untracked 
| 170|0x00000000aaa00000, 0x00000000aaa00000, 0x00000000aab00000|  0%| F|  |TAMS 0x00000000aaa00000| PB 0x00000000aaa00000| Untracked 
| 171|0x00000000aab00000, 0x00000000aac00000, 0x00000000aac00000|100%| O|  |TAMS 0x00000000aab00000| PB 0x00000000aab00000| Untracked 
| 172|0x00000000aac00000, 0x00000000aad00000, 0x00000000aad00000|100%| O|  |TAMS 0x00000000aac00000| PB 0x00000000aac00000| Untracked 
| 173|0x00000000aad00000, 0x00000000aae00000, 0x00000000aae00000|100%| O|  |TAMS 0x00000000aad00000| PB 0x00000000aad00000| Untracked 
| 174|0x00000000aae00000, 0x00000000aaf00000, 0x00000000aaf00000|100%| O|  |TAMS 0x00000000aae00000| PB 0x00000000aae00000| Untracked 
| 175|0x00000000aaf00000, 0x00000000ab000000, 0x00000000ab000000|100%| O|  |TAMS 0x00000000aaf00000| PB 0x00000000aaf00000| Untracked 
| 176|0x00000000ab000000, 0x00000000ab100000, 0x00000000ab100000|100%| O|  |TAMS 0x00000000ab000000| PB 0x00000000ab000000| Untracked 
| 177|0x00000000ab100000, 0x00000000ab200000, 0x00000000ab200000|100%| O|  |TAMS 0x00000000ab100000| PB 0x00000000ab100000| Untracked 
| 178|0x00000000ab200000, 0x00000000ab300000, 0x00000000ab300000|100%| O|  |TAMS 0x00000000ab200000| PB 0x00000000ab200000| Untracked 
| 179|0x00000000ab300000, 0x00000000ab400000, 0x00000000ab400000|100%| O|  |TAMS 0x00000000ab300000| PB 0x00000000ab300000| Untracked 
| 180|0x00000000ab400000, 0x00000000ab500000, 0x00000000ab500000|100%| O|  |TAMS 0x00000000ab400000| PB 0x00000000ab400000| Untracked 
| 181|0x00000000ab500000, 0x00000000ab600000, 0x00000000ab600000|100%| O|  |TAMS 0x00000000ab500000| PB 0x00000000ab500000| Untracked 
| 182|0x00000000ab600000, 0x00000000ab700000, 0x00000000ab700000|100%| O|  |TAMS 0x00000000ab600000| PB 0x00000000ab600000| Untracked 
| 183|0x00000000ab700000, 0x00000000ab800000, 0x00000000ab800000|100%| O|  |TAMS 0x00000000ab700000| PB 0x00000000ab700000| Untracked 
| 184|0x00000000ab800000, 0x00000000ab900000, 0x00000000ab900000|100%| O|  |TAMS 0x00000000ab800000| PB 0x00000000ab800000| Untracked 
| 185|0x00000000ab900000, 0x00000000aba00000, 0x00000000aba00000|100%| O|  |TAMS 0x00000000ab900000| PB 0x00000000ab900000| Untracked 
| 186|0x00000000aba00000, 0x00000000abb00000, 0x00000000abb00000|100%| O|  |TAMS 0x00000000aba00000| PB 0x00000000aba00000| Untracked 
| 187|0x00000000abb00000, 0x00000000abc00000, 0x00000000abc00000|100%| O|  |TAMS 0x00000000abb00000| PB 0x00000000abb00000| Untracked 
| 188|0x00000000abc00000, 0x00000000abd00000, 0x00000000abd00000|100%| O|  |TAMS 0x00000000abc00000| PB 0x00000000abc00000| Untracked 
| 189|0x00000000abd00000, 0x00000000abe00000, 0x00000000abe00000|100%| O|  |TAMS 0x00000000abd00000| PB 0x00000000abd00000| Untracked 
| 190|0x00000000abe00000, 0x00000000abf00000, 0x00000000abf00000|100%| O|  |TAMS 0x00000000abe00000| PB 0x00000000abe00000| Untracked 
| 191|0x00000000abf00000, 0x00000000ac000000, 0x00000000ac000000|100%| O|  |TAMS 0x00000000abf00000| PB 0x00000000abf00000| Untracked 
| 192|0x00000000ac000000, 0x00000000ac100000, 0x00000000ac100000|100%| O|  |TAMS 0x00000000ac000000| PB 0x00000000ac000000| Untracked 
| 193|0x00000000ac100000, 0x00000000ac200000, 0x00000000ac200000|100%| O|  |TAMS 0x00000000ac100000| PB 0x00000000ac100000| Untracked 
| 194|0x00000000ac200000, 0x00000000ac300000, 0x00000000ac300000|100%| O|  |TAMS 0x00000000ac200000| PB 0x00000000ac200000| Untracked 
| 195|0x00000000ac300000, 0x00000000ac400000, 0x00000000ac400000|100%| O|  |TAMS 0x00000000ac300000| PB 0x00000000ac300000| Untracked 
| 196|0x00000000ac400000, 0x00000000ac500000, 0x00000000ac500000|100%| O|  |TAMS 0x00000000ac400000| PB 0x00000000ac400000| Untracked 
| 197|0x00000000ac500000, 0x00000000ac600000, 0x00000000ac600000|100%| O|  |TAMS 0x00000000ac500000| PB 0x00000000ac500000| Untracked 
| 198|0x00000000ac600000, 0x00000000ac700000, 0x00000000ac700000|100%| O|  |TAMS 0x00000000ac600000| PB 0x00000000ac600000| Untracked 
| 199|0x00000000ac700000, 0x00000000ac800000, 0x00000000ac800000|100%| O|  |TAMS 0x00000000ac700000| PB 0x00000000ac700000| Untracked 
| 200|0x00000000ac800000, 0x00000000ac900000, 0x00000000ac900000|100%| O|  |TAMS 0x00000000ac800000| PB 0x00000000ac800000| Untracked 
| 201|0x00000000ac900000, 0x00000000aca00000, 0x00000000aca00000|100%| O|  |TAMS 0x00000000ac900000| PB 0x00000000ac900000| Untracked 
| 202|0x00000000aca00000, 0x00000000acb00000, 0x00000000acb00000|100%| O|  |TAMS 0x00000000aca00000| PB 0x00000000aca00000| Untracked 
| 203|0x00000000acb00000, 0x00000000acc00000, 0x00000000acc00000|100%| O|  |TAMS 0x00000000acb00000| PB 0x00000000acb00000| Untracked 
| 204|0x00000000acc00000, 0x00000000acd00000, 0x00000000acd00000|100%| O|  |TAMS 0x00000000acc00000| PB 0x00000000acc00000| Untracked 
| 205|0x00000000acd00000, 0x00000000ace00000, 0x00000000ace00000|100%| O|  |TAMS 0x00000000acd00000| PB 0x00000000acd00000| Untracked 
| 206|0x00000000ace00000, 0x00000000acf00000, 0x00000000acf00000|100%| O|  |TAMS 0x00000000ace00000| PB 0x00000000ace00000| Untracked 
| 207|0x00000000acf00000, 0x00000000ad000000, 0x00000000ad000000|100%| O|  |TAMS 0x00000000acf00000| PB 0x00000000acf00000| Untracked 
| 208|0x00000000ad000000, 0x00000000ad100000, 0x00000000ad100000|100%| O|  |TAMS 0x00000000ad000000| PB 0x00000000ad000000| Untracked 
| 209|0x00000000ad100000, 0x00000000ad200000, 0x00000000ad200000|100%| O|  |TAMS 0x00000000ad100000| PB 0x00000000ad100000| Untracked 
| 210|0x00000000ad200000, 0x00000000ad300000, 0x00000000ad300000|100%| O|  |TAMS 0x00000000ad200000| PB 0x00000000ad200000| Untracked 
| 211|0x00000000ad300000, 0x00000000ad400000, 0x00000000ad400000|100%| O|  |TAMS 0x00000000ad300000| PB 0x00000000ad300000| Untracked 
| 212|0x00000000ad400000, 0x00000000ad500000, 0x00000000ad500000|100%| O|  |TAMS 0x00000000ad400000| PB 0x00000000ad400000| Untracked 
| 213|0x00000000ad500000, 0x00000000ad600000, 0x00000000ad600000|100%| O|  |TAMS 0x00000000ad500000| PB 0x00000000ad500000| Untracked 
| 214|0x00000000ad600000, 0x00000000ad700000, 0x00000000ad700000|100%| O|  |TAMS 0x00000000ad600000| PB 0x00000000ad600000| Untracked 
| 215|0x00000000ad700000, 0x00000000ad800000, 0x00000000ad800000|100%| O|  |TAMS 0x00000000ad700000| PB 0x00000000ad700000| Untracked 
| 216|0x00000000ad800000, 0x00000000ad900000, 0x00000000ad900000|100%| O|  |TAMS 0x00000000ad800000| PB 0x00000000ad800000| Untracked 
| 217|0x00000000ad900000, 0x00000000ada00000, 0x00000000ada00000|100%| O|  |TAMS 0x00000000ad900000| PB 0x00000000ad900000| Untracked 
| 218|0x00000000ada00000, 0x00000000adb00000, 0x00000000adb00000|100%| O|  |TAMS 0x00000000ada00000| PB 0x00000000ada00000| Untracked 
| 219|0x00000000adb00000, 0x00000000adc00000, 0x00000000adc00000|100%| O|  |TAMS 0x00000000adb00000| PB 0x00000000adb00000| Untracked 
| 220|0x00000000adc00000, 0x00000000add00000, 0x00000000add00000|100%| O|  |TAMS 0x00000000adc00000| PB 0x00000000adc00000| Untracked 
| 221|0x00000000add00000, 0x00000000ade00000, 0x00000000ade00000|100%| O|  |TAMS 0x00000000add00000| PB 0x00000000add00000| Untracked 
| 222|0x00000000ade00000, 0x00000000adf00000, 0x00000000adf00000|100%| O|  |TAMS 0x00000000ade00000| PB 0x00000000ade00000| Untracked 
| 223|0x00000000adf00000, 0x00000000ae000000, 0x00000000ae000000|100%| O|  |TAMS 0x00000000adf00000| PB 0x00000000adf00000| Untracked 
| 224|0x00000000ae000000, 0x00000000ae100000, 0x00000000ae100000|100%| O|  |TAMS 0x00000000ae000000| PB 0x00000000ae000000| Untracked 
| 225|0x00000000ae100000, 0x00000000ae200000, 0x00000000ae200000|100%| O|  |TAMS 0x00000000ae100000| PB 0x00000000ae100000| Untracked 
| 226|0x00000000ae200000, 0x00000000ae300000, 0x00000000ae300000|100%| O|  |TAMS 0x00000000ae200000| PB 0x00000000ae200000| Untracked 
| 227|0x00000000ae300000, 0x00000000ae400000, 0x00000000ae400000|100%| O|  |TAMS 0x00000000ae300000| PB 0x00000000ae300000| Untracked 
| 228|0x00000000ae400000, 0x00000000ae500000, 0x00000000ae500000|100%| O|  |TAMS 0x00000000ae400000| PB 0x00000000ae400000| Untracked 
| 229|0x00000000ae500000, 0x00000000ae600000, 0x00000000ae600000|100%| O|  |TAMS 0x00000000ae500000| PB 0x00000000ae500000| Untracked 
| 230|0x00000000ae600000, 0x00000000ae700000, 0x00000000ae700000|100%| O|  |TAMS 0x00000000ae600000| PB 0x00000000ae600000| Untracked 
| 231|0x00000000ae700000, 0x00000000ae800000, 0x00000000ae800000|100%| O|  |TAMS 0x00000000ae700000| PB 0x00000000ae700000| Untracked 
| 232|0x00000000ae800000, 0x00000000ae800000, 0x00000000ae900000|  0%| F|  |TAMS 0x00000000ae800000| PB 0x00000000ae800000| Untracked 
| 233|0x00000000ae900000, 0x00000000ae900000, 0x00000000aea00000|  0%| F|  |TAMS 0x00000000ae900000| PB 0x00000000ae900000| Untracked 
| 234|0x00000000aea00000, 0x00000000aeb00000, 0x00000000aeb00000|100%| O|  |TAMS 0x00000000aea00000| PB 0x00000000aea00000| Untracked 
| 235|0x00000000aeb00000, 0x00000000aec00000, 0x00000000aec00000|100%| O|  |TAMS 0x00000000aeb00000| PB 0x00000000aeb00000| Untracked 
| 236|0x00000000aec00000, 0x00000000aed00000, 0x00000000aed00000|100%| O|  |TAMS 0x00000000aec00000| PB 0x00000000aec00000| Untracked 
| 237|0x00000000aed00000, 0x00000000aee00000, 0x00000000aee00000|100%| O|  |TAMS 0x00000000aed00000| PB 0x00000000aed00000| Untracked 
| 238|0x00000000aee00000, 0x00000000aef00000, 0x00000000aef00000|100%| O|  |TAMS 0x00000000aee00000| PB 0x00000000aee00000| Untracked 
| 239|0x00000000aef00000, 0x00000000af000000, 0x00000000af000000|100%| O|  |TAMS 0x00000000aef00000| PB 0x00000000aef00000| Untracked 
| 240|0x00000000af000000, 0x00000000af100000, 0x00000000af100000|100%| O|  |TAMS 0x00000000af000000| PB 0x00000000af000000| Untracked 
| 241|0x00000000af100000, 0x00000000af200000, 0x00000000af200000|100%| O|  |TAMS 0x00000000af100000| PB 0x00000000af100000| Untracked 
| 242|0x00000000af200000, 0x00000000af300000, 0x00000000af300000|100%| O|  |TAMS 0x00000000af200000| PB 0x00000000af200000| Untracked 
| 243|0x00000000af300000, 0x00000000af400000, 0x00000000af400000|100%| O|  |TAMS 0x00000000af300000| PB 0x00000000af300000| Untracked 
| 244|0x00000000af400000, 0x00000000af500000, 0x00000000af500000|100%| O|  |TAMS 0x00000000af400000| PB 0x00000000af400000| Untracked 
| 245|0x00000000af500000, 0x00000000af600000, 0x00000000af600000|100%| O|  |TAMS 0x00000000af500000| PB 0x00000000af500000| Untracked 
| 246|0x00000000af600000, 0x00000000af700000, 0x00000000af700000|100%| O|  |TAMS 0x00000000af600000| PB 0x00000000af600000| Untracked 
| 247|0x00000000af700000, 0x00000000af800000, 0x00000000af800000|100%| O|  |TAMS 0x00000000af700000| PB 0x00000000af700000| Untracked 
| 248|0x00000000af800000, 0x00000000af900000, 0x00000000af900000|100%| O|  |TAMS 0x00000000af800000| PB 0x00000000af800000| Untracked 
| 249|0x00000000af900000, 0x00000000afa00000, 0x00000000afa00000|100%| O|  |TAMS 0x00000000af900000| PB 0x00000000af900000| Untracked 
| 250|0x00000000afa00000, 0x00000000afb00000, 0x00000000afb00000|100%| O|  |TAMS 0x00000000afa00000| PB 0x00000000afa00000| Untracked 
| 251|0x00000000afb00000, 0x00000000afc00000, 0x00000000afc00000|100%| O|  |TAMS 0x00000000afb00000| PB 0x00000000afb00000| Untracked 
| 252|0x00000000afc00000, 0x00000000afd00000, 0x00000000afd00000|100%| O|  |TAMS 0x00000000afc00000| PB 0x00000000afc00000| Untracked 
| 253|0x00000000afd00000, 0x00000000afe00000, 0x00000000afe00000|100%| O|  |TAMS 0x00000000afd00000| PB 0x00000000afd00000| Untracked 
| 254|0x00000000afe00000, 0x00000000aff00000, 0x00000000aff00000|100%| O|  |TAMS 0x00000000afe00000| PB 0x00000000afe00000| Untracked 
| 255|0x00000000aff00000, 0x00000000b0000000, 0x00000000b0000000|100%| O|  |TAMS 0x00000000aff00000| PB 0x00000000aff00000| Untracked 
| 256|0x00000000b0000000, 0x00000000b0100000, 0x00000000b0100000|100%| O|  |TAMS 0x00000000b0000000| PB 0x00000000b0000000| Untracked 
| 257|0x00000000b0100000, 0x00000000b0200000, 0x00000000b0200000|100%| O|  |TAMS 0x00000000b0100000| PB 0x00000000b0100000| Untracked 
| 258|0x00000000b0200000, 0x00000000b0300000, 0x00000000b0300000|100%| O|  |TAMS 0x00000000b0200000| PB 0x00000000b0200000| Untracked 
| 259|0x00000000b0300000, 0x00000000b0400000, 0x00000000b0400000|100%| O|  |TAMS 0x00000000b0300000| PB 0x00000000b0300000| Untracked 
| 260|0x00000000b0400000, 0x00000000b0500000, 0x00000000b0500000|100%| O|  |TAMS 0x00000000b0400000| PB 0x00000000b0400000| Untracked 
| 261|0x00000000b0500000, 0x00000000b0600000, 0x00000000b0600000|100%| O|  |TAMS 0x00000000b0500000| PB 0x00000000b0500000| Untracked 
| 262|0x00000000b0600000, 0x00000000b0700000, 0x00000000b0700000|100%| O|  |TAMS 0x00000000b0600000| PB 0x00000000b0600000| Untracked 
| 263|0x00000000b0700000, 0x00000000b0800000, 0x00000000b0800000|100%| O|  |TAMS 0x00000000b0700000| PB 0x00000000b0700000| Untracked 
| 264|0x00000000b0800000, 0x00000000b0900000, 0x00000000b0900000|100%| O|  |TAMS 0x00000000b0800000| PB 0x00000000b0800000| Untracked 
| 265|0x00000000b0900000, 0x00000000b0a00000, 0x00000000b0a00000|100%| O|  |TAMS 0x00000000b0900000| PB 0x00000000b0900000| Untracked 
| 266|0x00000000b0a00000, 0x00000000b0b00000, 0x00000000b0b00000|100%| O|  |TAMS 0x00000000b0a00000| PB 0x00000000b0a00000| Untracked 
| 267|0x00000000b0b00000, 0x00000000b0c00000, 0x00000000b0c00000|100%| O|  |TAMS 0x00000000b0b00000| PB 0x00000000b0b00000| Untracked 
| 268|0x00000000b0c00000, 0x00000000b0d00000, 0x00000000b0d00000|100%| O|  |TAMS 0x00000000b0c00000| PB 0x00000000b0c00000| Untracked 
| 269|0x00000000b0d00000, 0x00000000b0e00000, 0x00000000b0e00000|100%| O|  |TAMS 0x00000000b0d00000| PB 0x00000000b0d00000| Untracked 
| 270|0x00000000b0e00000, 0x00000000b0f00000, 0x00000000b0f00000|100%| O|  |TAMS 0x00000000b0e00000| PB 0x00000000b0e00000| Untracked 
| 271|0x00000000b0f00000, 0x00000000b1000000, 0x00000000b1000000|100%| O|  |TAMS 0x00000000b0f00000| PB 0x00000000b0f00000| Untracked 
| 272|0x00000000b1000000, 0x00000000b1100000, 0x00000000b1100000|100%| O|  |TAMS 0x00000000b1000000| PB 0x00000000b1000000| Untracked 
| 273|0x00000000b1100000, 0x00000000b1200000, 0x00000000b1200000|100%| O|  |TAMS 0x00000000b1100000| PB 0x00000000b1100000| Untracked 
| 274|0x00000000b1200000, 0x00000000b1300000, 0x00000000b1300000|100%| O|  |TAMS 0x00000000b1200000| PB 0x00000000b1200000| Untracked 
| 275|0x00000000b1300000, 0x00000000b1400000, 0x00000000b1400000|100%| O|  |TAMS 0x00000000b1300000| PB 0x00000000b1300000| Untracked 
| 276|0x00000000b1400000, 0x00000000b1500000, 0x00000000b1500000|100%| O|  |TAMS 0x00000000b1400000| PB 0x00000000b1400000| Untracked 
| 277|0x00000000b1500000, 0x00000000b1600000, 0x00000000b1600000|100%| O|  |TAMS 0x00000000b1500000| PB 0x00000000b1500000| Untracked 
| 278|0x00000000b1600000, 0x00000000b1700000, 0x00000000b1700000|100%| O|  |TAMS 0x00000000b1600000| PB 0x00000000b1600000| Untracked 
| 279|0x00000000b1700000, 0x00000000b1800000, 0x00000000b1800000|100%| O|  |TAMS 0x00000000b1700000| PB 0x00000000b1700000| Untracked 
| 280|0x00000000b1800000, 0x00000000b1900000, 0x00000000b1900000|100%| O|  |TAMS 0x00000000b1800000| PB 0x00000000b1800000| Untracked 
| 281|0x00000000b1900000, 0x00000000b1900000, 0x00000000b1a00000|  0%| F|  |TAMS 0x00000000b1900000| PB 0x00000000b1900000| Untracked 
| 282|0x00000000b1a00000, 0x00000000b1b00000, 0x00000000b1b00000|100%| O|  |TAMS 0x00000000b1a00000| PB 0x00000000b1a00000| Untracked 
| 283|0x00000000b1b00000, 0x00000000b1c00000, 0x00000000b1c00000|100%| O|  |TAMS 0x00000000b1b00000| PB 0x00000000b1b00000| Untracked 
| 284|0x00000000b1c00000, 0x00000000b1d00000, 0x00000000b1d00000|100%| O|  |TAMS 0x00000000b1c00000| PB 0x00000000b1c00000| Untracked 
| 285|0x00000000b1d00000, 0x00000000b1e00000, 0x00000000b1e00000|100%| O|  |TAMS 0x00000000b1d00000| PB 0x00000000b1d00000| Untracked 
| 286|0x00000000b1e00000, 0x00000000b1f00000, 0x00000000b1f00000|100%| O|  |TAMS 0x00000000b1e00000| PB 0x00000000b1e00000| Untracked 
| 287|0x00000000b1f00000, 0x00000000b2000000, 0x00000000b2000000|100%| O|  |TAMS 0x00000000b1f00000| PB 0x00000000b1f00000| Untracked 
| 288|0x00000000b2000000, 0x00000000b2100000, 0x00000000b2100000|100%| O|  |TAMS 0x00000000b2000000| PB 0x00000000b2000000| Untracked 
| 289|0x00000000b2100000, 0x00000000b2200000, 0x00000000b2200000|100%| O|  |TAMS 0x00000000b2100000| PB 0x00000000b2100000| Untracked 
| 290|0x00000000b2200000, 0x00000000b2300000, 0x00000000b2300000|100%| O|  |TAMS 0x00000000b2200000| PB 0x00000000b2200000| Untracked 
| 291|0x00000000b2300000, 0x00000000b2400000, 0x00000000b2400000|100%| O|  |TAMS 0x00000000b2300000| PB 0x00000000b2300000| Untracked 
| 292|0x00000000b2400000, 0x00000000b2500000, 0x00000000b2500000|100%| O|  |TAMS 0x00000000b2400000| PB 0x00000000b2400000| Untracked 
| 293|0x00000000b2500000, 0x00000000b2600000, 0x00000000b2600000|100%| O|  |TAMS 0x00000000b2500000| PB 0x00000000b2500000| Untracked 
| 294|0x00000000b2600000, 0x00000000b2700000, 0x00000000b2700000|100%| O|  |TAMS 0x00000000b2600000| PB 0x00000000b2600000| Untracked 
| 295|0x00000000b2700000, 0x00000000b2800000, 0x00000000b2800000|100%| O|  |TAMS 0x00000000b2700000| PB 0x00000000b2700000| Untracked 
| 296|0x00000000b2800000, 0x00000000b2900000, 0x00000000b2900000|100%| O|  |TAMS 0x00000000b2800000| PB 0x00000000b2800000| Untracked 
| 297|0x00000000b2900000, 0x00000000b2a00000, 0x00000000b2a00000|100%| O|  |TAMS 0x00000000b2900000| PB 0x00000000b2900000| Untracked 
| 298|0x00000000b2a00000, 0x00000000b2b00000, 0x00000000b2b00000|100%| O|  |TAMS 0x00000000b2a00000| PB 0x00000000b2a00000| Untracked 
| 299|0x00000000b2b00000, 0x00000000b2c00000, 0x00000000b2c00000|100%| O|  |TAMS 0x00000000b2b00000| PB 0x00000000b2b00000| Untracked 
| 300|0x00000000b2c00000, 0x00000000b2c00000, 0x00000000b2d00000|  0%| F|  |TAMS 0x00000000b2c00000| PB 0x00000000b2c00000| Untracked 
| 301|0x00000000b2d00000, 0x00000000b2e00000, 0x00000000b2e00000|100%| O|  |TAMS 0x00000000b2d00000| PB 0x00000000b2d00000| Untracked 
| 302|0x00000000b2e00000, 0x00000000b2f00000, 0x00000000b2f00000|100%| O|  |TAMS 0x00000000b2e00000| PB 0x00000000b2e00000| Untracked 
| 303|0x00000000b2f00000, 0x00000000b3000000, 0x00000000b3000000|100%| O|  |TAMS 0x00000000b2f00000| PB 0x00000000b2f00000| Untracked 
| 304|0x00000000b3000000, 0x00000000b3100000, 0x00000000b3100000|100%| O|  |TAMS 0x00000000b3000000| PB 0x00000000b3000000| Untracked 
| 305|0x00000000b3100000, 0x00000000b3200000, 0x00000000b3200000|100%| O|  |TAMS 0x00000000b3100000| PB 0x00000000b3100000| Untracked 
| 306|0x00000000b3200000, 0x00000000b3300000, 0x00000000b3300000|100%| O|  |TAMS 0x00000000b3200000| PB 0x00000000b3200000| Untracked 
| 307|0x00000000b3300000, 0x00000000b3300000, 0x00000000b3400000|  0%| F|  |TAMS 0x00000000b3300000| PB 0x00000000b3300000| Untracked 
| 308|0x00000000b3400000, 0x00000000b3500000, 0x00000000b3500000|100%| O|  |TAMS 0x00000000b3400000| PB 0x00000000b3400000| Untracked 
| 309|0x00000000b3500000, 0x00000000b3600000, 0x00000000b3600000|100%| O|  |TAMS 0x00000000b3500000| PB 0x00000000b3500000| Untracked 
| 310|0x00000000b3600000, 0x00000000b3700000, 0x00000000b3700000|100%| O|  |TAMS 0x00000000b3600000| PB 0x00000000b3600000| Untracked 
| 311|0x00000000b3700000, 0x00000000b3800000, 0x00000000b3800000|100%| O|  |TAMS 0x00000000b3700000| PB 0x00000000b3700000| Untracked 
| 312|0x00000000b3800000, 0x00000000b3900000, 0x00000000b3900000|100%| O|  |TAMS 0x00000000b3800000| PB 0x00000000b3800000| Untracked 
| 313|0x00000000b3900000, 0x00000000b3a00000, 0x00000000b3a00000|100%| O|  |TAMS 0x00000000b3900000| PB 0x00000000b3900000| Untracked 
| 314|0x00000000b3a00000, 0x00000000b3b00000, 0x00000000b3b00000|100%| O|  |TAMS 0x00000000b3a00000| PB 0x00000000b3a00000| Untracked 
| 315|0x00000000b3b00000, 0x00000000b3b00000, 0x00000000b3c00000|  0%| F|  |TAMS 0x00000000b3b00000| PB 0x00000000b3b00000| Untracked 
| 316|0x00000000b3c00000, 0x00000000b3c00000, 0x00000000b3d00000|  0%| F|  |TAMS 0x00000000b3c00000| PB 0x00000000b3c00000| Untracked 
| 317|0x00000000b3d00000, 0x00000000b3e00000, 0x00000000b3e00000|100%| O|  |TAMS 0x00000000b3d00000| PB 0x00000000b3d00000| Untracked 
| 318|0x00000000b3e00000, 0x00000000b3f00000, 0x00000000b3f00000|100%| O|  |TAMS 0x00000000b3e00000| PB 0x00000000b3e00000| Untracked 
| 319|0x00000000b3f00000, 0x00000000b4000000, 0x00000000b4000000|100%| O|  |TAMS 0x00000000b3f00000| PB 0x00000000b3f00000| Untracked 
| 320|0x00000000b4000000, 0x00000000b4000000, 0x00000000b4100000|  0%| F|  |TAMS 0x00000000b4000000| PB 0x00000000b4000000| Untracked 
| 321|0x00000000b4100000, 0x00000000b4100000, 0x00000000b4200000|  0%| F|  |TAMS 0x00000000b4100000| PB 0x00000000b4100000| Untracked 
| 322|0x00000000b4200000, 0x00000000b4300000, 0x00000000b4300000|100%| O|  |TAMS 0x00000000b4200000| PB 0x00000000b4200000| Untracked 
| 323|0x00000000b4300000, 0x00000000b4400000, 0x00000000b4400000|100%| O|  |TAMS 0x00000000b4300000| PB 0x00000000b4300000| Untracked 
| 324|0x00000000b4400000, 0x00000000b4500000, 0x00000000b4500000|100%| O|  |TAMS 0x00000000b4400000| PB 0x00000000b4400000| Untracked 
| 325|0x00000000b4500000, 0x00000000b4600000, 0x00000000b4600000|100%| O|  |TAMS 0x00000000b4500000| PB 0x00000000b4500000| Untracked 
| 326|0x00000000b4600000, 0x00000000b4700000, 0x00000000b4700000|100%| O|  |TAMS 0x00000000b4600000| PB 0x00000000b4600000| Untracked 
| 327|0x00000000b4700000, 0x00000000b4800000, 0x00000000b4800000|100%| O|  |TAMS 0x00000000b4700000| PB 0x00000000b4700000| Untracked 
| 328|0x00000000b4800000, 0x00000000b4900000, 0x00000000b4900000|100%| O|  |TAMS 0x00000000b4800000| PB 0x00000000b4800000| Untracked 
| 329|0x00000000b4900000, 0x00000000b4a00000, 0x00000000b4a00000|100%| O|  |TAMS 0x00000000b4900000| PB 0x00000000b4900000| Untracked 
| 330|0x00000000b4a00000, 0x00000000b4b00000, 0x00000000b4b00000|100%| O|  |TAMS 0x00000000b4a00000| PB 0x00000000b4a00000| Untracked 
| 331|0x00000000b4b00000, 0x00000000b4c00000, 0x00000000b4c00000|100%| O|  |TAMS 0x00000000b4b00000| PB 0x00000000b4b00000| Untracked 
| 332|0x00000000b4c00000, 0x00000000b4d00000, 0x00000000b4d00000|100%| O|  |TAMS 0x00000000b4c00000| PB 0x00000000b4c00000| Untracked 
| 333|0x00000000b4d00000, 0x00000000b4e00000, 0x00000000b4e00000|100%| O|  |TAMS 0x00000000b4d00000| PB 0x00000000b4d00000| Untracked 
| 334|0x00000000b4e00000, 0x00000000b4f00000, 0x00000000b4f00000|100%| O|  |TAMS 0x00000000b4e00000| PB 0x00000000b4e00000| Untracked 
| 335|0x00000000b4f00000, 0x00000000b5000000, 0x00000000b5000000|100%| O|  |TAMS 0x00000000b4f00000| PB 0x00000000b4f00000| Untracked 
| 336|0x00000000b5000000, 0x00000000b5100000, 0x00000000b5100000|100%| O|  |TAMS 0x00000000b5000000| PB 0x00000000b5000000| Untracked 
| 337|0x00000000b5100000, 0x00000000b5200000, 0x00000000b5200000|100%| O|  |TAMS 0x00000000b5100000| PB 0x00000000b5100000| Untracked 
| 338|0x00000000b5200000, 0x00000000b5300000, 0x00000000b5300000|100%| O|  |TAMS 0x00000000b5200000| PB 0x00000000b5200000| Untracked 
| 339|0x00000000b5300000, 0x00000000b5400000, 0x00000000b5400000|100%| O|  |TAMS 0x00000000b5300000| PB 0x00000000b5300000| Untracked 
| 340|0x00000000b5400000, 0x00000000b5500000, 0x00000000b5500000|100%| O|  |TAMS 0x00000000b5400000| PB 0x00000000b5400000| Untracked 
| 341|0x00000000b5500000, 0x00000000b5500000, 0x00000000b5600000|  0%| F|  |TAMS 0x00000000b5500000| PB 0x00000000b5500000| Untracked 
| 342|0x00000000b5600000, 0x00000000b5700000, 0x00000000b5700000|100%| O|  |TAMS 0x00000000b5600000| PB 0x00000000b5600000| Untracked 
| 343|0x00000000b5700000, 0x00000000b5800000, 0x00000000b5800000|100%| O|  |TAMS 0x00000000b5700000| PB 0x00000000b5700000| Untracked 
| 344|0x00000000b5800000, 0x00000000b5900000, 0x00000000b5900000|100%| O|  |TAMS 0x00000000b5800000| PB 0x00000000b5800000| Untracked 
| 345|0x00000000b5900000, 0x00000000b5a00000, 0x00000000b5a00000|100%| O|  |TAMS 0x00000000b5900000| PB 0x00000000b5900000| Untracked 
| 346|0x00000000b5a00000, 0x00000000b5b00000, 0x00000000b5b00000|100%| O|  |TAMS 0x00000000b5a00000| PB 0x00000000b5a00000| Untracked 
| 347|0x00000000b5b00000, 0x00000000b5c00000, 0x00000000b5c00000|100%| O|  |TAMS 0x00000000b5b00000| PB 0x00000000b5b00000| Untracked 
| 348|0x00000000b5c00000, 0x00000000b5d00000, 0x00000000b5d00000|100%| O|  |TAMS 0x00000000b5c00000| PB 0x00000000b5c00000| Untracked 
| 349|0x00000000b5d00000, 0x00000000b5e00000, 0x00000000b5e00000|100%| O|  |TAMS 0x00000000b5d00000| PB 0x00000000b5d00000| Untracked 
| 350|0x00000000b5e00000, 0x00000000b5f00000, 0x00000000b5f00000|100%| O|  |TAMS 0x00000000b5e00000| PB 0x00000000b5e00000| Untracked 
| 351|0x00000000b5f00000, 0x00000000b6000000, 0x00000000b6000000|100%| O|  |TAMS 0x00000000b5f00000| PB 0x00000000b5f00000| Untracked 
| 352|0x00000000b6000000, 0x00000000b6100000, 0x00000000b6100000|100%| O|  |TAMS 0x00000000b6000000| PB 0x00000000b6000000| Untracked 
| 353|0x00000000b6100000, 0x00000000b6200000, 0x00000000b6200000|100%| O|  |TAMS 0x00000000b6100000| PB 0x00000000b6100000| Untracked 
| 354|0x00000000b6200000, 0x00000000b6300000, 0x00000000b6300000|100%| O|  |TAMS 0x00000000b6200000| PB 0x00000000b6200000| Untracked 
| 355|0x00000000b6300000, 0x00000000b6400000, 0x00000000b6400000|100%| O|  |TAMS 0x00000000b6300000| PB 0x00000000b6300000| Untracked 
| 356|0x00000000b6400000, 0x00000000b6500000, 0x00000000b6500000|100%| O|  |TAMS 0x00000000b6400000| PB 0x00000000b6400000| Untracked 
| 357|0x00000000b6500000, 0x00000000b6600000, 0x00000000b6600000|100%| O|  |TAMS 0x00000000b6500000| PB 0x00000000b6500000| Untracked 
| 358|0x00000000b6600000, 0x00000000b6700000, 0x00000000b6700000|100%| O|  |TAMS 0x00000000b6600000| PB 0x00000000b6600000| Untracked 
| 359|0x00000000b6700000, 0x00000000b6800000, 0x00000000b6800000|100%| O|  |TAMS 0x00000000b6700000| PB 0x00000000b6700000| Untracked 
| 360|0x00000000b6800000, 0x00000000b6900000, 0x00000000b6900000|100%| O|  |TAMS 0x00000000b6800000| PB 0x00000000b6800000| Untracked 
| 361|0x00000000b6900000, 0x00000000b6a00000, 0x00000000b6a00000|100%| O|  |TAMS 0x00000000b6900000| PB 0x00000000b6900000| Untracked 
| 362|0x00000000b6a00000, 0x00000000b6b00000, 0x00000000b6b00000|100%| O|  |TAMS 0x00000000b6a00000| PB 0x00000000b6a00000| Untracked 
| 363|0x00000000b6b00000, 0x00000000b6c00000, 0x00000000b6c00000|100%| O|  |TAMS 0x00000000b6b00000| PB 0x00000000b6b00000| Untracked 
| 364|0x00000000b6c00000, 0x00000000b6d00000, 0x00000000b6d00000|100%| O|  |TAMS 0x00000000b6c00000| PB 0x00000000b6c00000| Untracked 
| 365|0x00000000b6d00000, 0x00000000b6e00000, 0x00000000b6e00000|100%| O|  |TAMS 0x00000000b6d00000| PB 0x00000000b6d00000| Untracked 
| 366|0x00000000b6e00000, 0x00000000b6f00000, 0x00000000b6f00000|100%| O|  |TAMS 0x00000000b6e00000| PB 0x00000000b6e00000| Untracked 
| 367|0x00000000b6f00000, 0x00000000b7000000, 0x00000000b7000000|100%| O|  |TAMS 0x00000000b6f00000| PB 0x00000000b6f00000| Untracked 
| 368|0x00000000b7000000, 0x00000000b7100000, 0x00000000b7100000|100%| O|  |TAMS 0x00000000b7000000| PB 0x00000000b7000000| Untracked 
| 369|0x00000000b7100000, 0x00000000b7200000, 0x00000000b7200000|100%| O|  |TAMS 0x00000000b7100000| PB 0x00000000b7100000| Untracked 
| 370|0x00000000b7200000, 0x00000000b7300000, 0x00000000b7300000|100%| O|  |TAMS 0x00000000b7200000| PB 0x00000000b7200000| Untracked 
| 371|0x00000000b7300000, 0x00000000b7400000, 0x00000000b7400000|100%| O|  |TAMS 0x00000000b7300000| PB 0x00000000b7300000| Untracked 
| 372|0x00000000b7400000, 0x00000000b7480000, 0x00000000b7500000| 50%| O|  |TAMS 0x00000000b7400000| PB 0x00000000b7400000| Untracked 
| 373|0x00000000b7500000, 0x00000000b7500000, 0x00000000b7600000|  0%| F|  |TAMS 0x00000000b7500000| PB 0x00000000b7500000| Untracked 
| 374|0x00000000b7600000, 0x00000000b7600000, 0x00000000b7700000|  0%| F|  |TAMS 0x00000000b7600000| PB 0x00000000b7600000| Untracked 
| 375|0x00000000b7700000, 0x00000000b7700000, 0x00000000b7800000|  0%| F|  |TAMS 0x00000000b7700000| PB 0x00000000b7700000| Untracked 
| 376|0x00000000b7800000, 0x00000000b7800000, 0x00000000b7900000|  0%| F|  |TAMS 0x00000000b7800000| PB 0x00000000b7800000| Untracked 
| 377|0x00000000b7900000, 0x00000000b7900000, 0x00000000b7a00000|  0%| F|  |TAMS 0x00000000b7900000| PB 0x00000000b7900000| Untracked 
| 378|0x00000000b7a00000, 0x00000000b7a00000, 0x00000000b7b00000|  0%| F|  |TAMS 0x00000000b7a00000| PB 0x00000000b7a00000| Untracked 
| 379|0x00000000b7b00000, 0x00000000b7b00000, 0x00000000b7c00000|  0%| F|  |TAMS 0x00000000b7b00000| PB 0x00000000b7b00000| Untracked 
| 380|0x00000000b7c00000, 0x00000000b7c00000, 0x00000000b7d00000|  0%| F|  |TAMS 0x00000000b7c00000| PB 0x00000000b7c00000| Untracked 
| 381|0x00000000b7d00000, 0x00000000b7d00000, 0x00000000b7e00000|  0%| F|  |TAMS 0x00000000b7d00000| PB 0x00000000b7d00000| Untracked 
| 382|0x00000000b7e00000, 0x00000000b7e00000, 0x00000000b7f00000|  0%| F|  |TAMS 0x00000000b7e00000| PB 0x00000000b7e00000| Untracked 
| 383|0x00000000b7f00000, 0x00000000b7f00000, 0x00000000b8000000|  0%| F|  |TAMS 0x00000000b7f00000| PB 0x00000000b7f00000| Untracked 
| 384|0x00000000b8000000, 0x00000000b8000000, 0x00000000b8100000|  0%| F|  |TAMS 0x00000000b8000000| PB 0x00000000b8000000| Untracked 
| 385|0x00000000b8100000, 0x00000000b8100000, 0x00000000b8200000|  0%| F|  |TAMS 0x00000000b8100000| PB 0x00000000b8100000| Untracked 
| 386|0x00000000b8200000, 0x00000000b8200000, 0x00000000b8300000|  0%| F|  |TAMS 0x00000000b8200000| PB 0x00000000b8200000| Untracked 
| 387|0x00000000b8300000, 0x00000000b8300000, 0x00000000b8400000|  0%| F|  |TAMS 0x00000000b8300000| PB 0x00000000b8300000| Untracked 
| 388|0x00000000b8400000, 0x00000000b8400000, 0x00000000b8500000|  0%| F|  |TAMS 0x00000000b8400000| PB 0x00000000b8400000| Untracked 
| 389|0x00000000b8500000, 0x00000000b8500000, 0x00000000b8600000|  0%| F|  |TAMS 0x00000000b8500000| PB 0x00000000b8500000| Untracked 
| 390|0x00000000b8600000, 0x00000000b8600000, 0x00000000b8700000|  0%| F|  |TAMS 0x00000000b8600000| PB 0x00000000b8600000| Untracked 
| 391|0x00000000b8700000, 0x00000000b8700000, 0x00000000b8800000|  0%| F|  |TAMS 0x00000000b8700000| PB 0x00000000b8700000| Untracked 
| 392|0x00000000b8800000, 0x00000000b8800000, 0x00000000b8900000|  0%| F|  |TAMS 0x00000000b8800000| PB 0x00000000b8800000| Untracked 
| 393|0x00000000b8900000, 0x00000000b8900000, 0x00000000b8a00000|  0%| F|  |TAMS 0x00000000b8900000| PB 0x00000000b8900000| Untracked 
| 394|0x00000000b8a00000, 0x00000000b8a00000, 0x00000000b8b00000|  0%| F|  |TAMS 0x00000000b8a00000| PB 0x00000000b8a00000| Untracked 
| 395|0x00000000b8b00000, 0x00000000b8b00000, 0x00000000b8c00000|  0%| F|  |TAMS 0x00000000b8b00000| PB 0x00000000b8b00000| Untracked 
| 396|0x00000000b8c00000, 0x00000000b8c00000, 0x00000000b8d00000|  0%| F|  |TAMS 0x00000000b8c00000| PB 0x00000000b8c00000| Untracked 
| 397|0x00000000b8d00000, 0x00000000b8d00000, 0x00000000b8e00000|  0%| F|  |TAMS 0x00000000b8d00000| PB 0x00000000b8d00000| Untracked 
| 398|0x00000000b8e00000, 0x00000000b8e00000, 0x00000000b8f00000|  0%| F|  |TAMS 0x00000000b8e00000| PB 0x00000000b8e00000| Untracked 
| 399|0x00000000b8f00000, 0x00000000b8f00000, 0x00000000b9000000|  0%| F|  |TAMS 0x00000000b8f00000| PB 0x00000000b8f00000| Untracked 
| 400|0x00000000b9000000, 0x00000000b9000000, 0x00000000b9100000|  0%| F|  |TAMS 0x00000000b9000000| PB 0x00000000b9000000| Untracked 
| 401|0x00000000b9100000, 0x00000000b9100000, 0x00000000b9200000|  0%| F|  |TAMS 0x00000000b9100000| PB 0x00000000b9100000| Untracked 
| 402|0x00000000b9200000, 0x00000000b9200000, 0x00000000b9300000|  0%| F|  |TAMS 0x00000000b9200000| PB 0x00000000b9200000| Untracked 
| 403|0x00000000b9300000, 0x00000000b9300000, 0x00000000b9400000|  0%| F|  |TAMS 0x00000000b9300000| PB 0x00000000b9300000| Untracked 
| 404|0x00000000b9400000, 0x00000000b9400000, 0x00000000b9500000|  0%| F|  |TAMS 0x00000000b9400000| PB 0x00000000b9400000| Untracked 
| 405|0x00000000b9500000, 0x00000000b9500000, 0x00000000b9600000|  0%| F|  |TAMS 0x00000000b9500000| PB 0x00000000b9500000| Untracked 
| 406|0x00000000b9600000, 0x00000000b9600000, 0x00000000b9700000|  0%| F|  |TAMS 0x00000000b9600000| PB 0x00000000b9600000| Untracked 
| 407|0x00000000b9700000, 0x00000000b9700000, 0x00000000b9800000|  0%| F|  |TAMS 0x00000000b9700000| PB 0x00000000b9700000| Untracked 
| 408|0x00000000b9800000, 0x00000000b9800000, 0x00000000b9900000|  0%| F|  |TAMS 0x00000000b9800000| PB 0x00000000b9800000| Untracked 
| 409|0x00000000b9900000, 0x00000000b9900000, 0x00000000b9a00000|  0%| F|  |TAMS 0x00000000b9900000| PB 0x00000000b9900000| Untracked 
| 410|0x00000000b9a00000, 0x00000000b9a00000, 0x00000000b9b00000|  0%| F|  |TAMS 0x00000000b9a00000| PB 0x00000000b9a00000| Untracked 
| 411|0x00000000b9b00000, 0x00000000b9b00000, 0x00000000b9c00000|  0%| F|  |TAMS 0x00000000b9b00000| PB 0x00000000b9b00000| Untracked 
| 412|0x00000000b9c00000, 0x00000000b9c00000, 0x00000000b9d00000|  0%| F|  |TAMS 0x00000000b9c00000| PB 0x00000000b9c00000| Untracked 
| 413|0x00000000b9d00000, 0x00000000b9d00000, 0x00000000b9e00000|  0%| F|  |TAMS 0x00000000b9d00000| PB 0x00000000b9d00000| Untracked 
| 414|0x00000000b9e00000, 0x00000000b9e00000, 0x00000000b9f00000|  0%| F|  |TAMS 0x00000000b9e00000| PB 0x00000000b9e00000| Untracked 
| 415|0x00000000b9f00000, 0x00000000b9f00000, 0x00000000ba000000|  0%| F|  |TAMS 0x00000000b9f00000| PB 0x00000000b9f00000| Untracked 
| 416|0x00000000ba000000, 0x00000000ba000000, 0x00000000ba100000|  0%| F|  |TAMS 0x00000000ba000000| PB 0x00000000ba000000| Untracked 
| 417|0x00000000ba100000, 0x00000000ba100000, 0x00000000ba200000|  0%| F|  |TAMS 0x00000000ba100000| PB 0x00000000ba100000| Untracked 
| 418|0x00000000ba200000, 0x00000000ba200000, 0x00000000ba300000|  0%| F|  |TAMS 0x00000000ba200000| PB 0x00000000ba200000| Untracked 
| 419|0x00000000ba300000, 0x00000000ba400000, 0x00000000ba400000|100%| S|CS|TAMS 0x00000000ba300000| PB 0x00000000ba300000| Complete 
| 420|0x00000000ba400000, 0x00000000ba500000, 0x00000000ba500000|100%| S|CS|TAMS 0x00000000ba400000| PB 0x00000000ba400000| Complete 
| 421|0x00000000ba500000, 0x00000000ba600000, 0x00000000ba600000|100%| S|CS|TAMS 0x00000000ba500000| PB 0x00000000ba500000| Complete 
| 422|0x00000000ba600000, 0x00000000ba700000, 0x00000000ba700000|100%| S|CS|TAMS 0x00000000ba600000| PB 0x00000000ba600000| Complete 
| 423|0x00000000ba700000, 0x00000000ba800000, 0x00000000ba800000|100%| S|CS|TAMS 0x00000000ba700000| PB 0x00000000ba700000| Complete 
| 424|0x00000000ba800000, 0x00000000ba900000, 0x00000000ba900000|100%| S|CS|TAMS 0x00000000ba800000| PB 0x00000000ba800000| Complete 
| 425|0x00000000ba900000, 0x00000000baa00000, 0x00000000baa00000|100%| S|CS|TAMS 0x00000000ba900000| PB 0x00000000ba900000| Complete 
| 426|0x00000000baa00000, 0x00000000bab00000, 0x00000000bab00000|100%| S|CS|TAMS 0x00000000baa00000| PB 0x00000000baa00000| Complete 
| 427|0x00000000bab00000, 0x00000000bac00000, 0x00000000bac00000|100%| S|CS|TAMS 0x00000000bab00000| PB 0x00000000bab00000| Complete 
| 428|0x00000000bac00000, 0x00000000bad00000, 0x00000000bad00000|100%| S|CS|TAMS 0x00000000bac00000| PB 0x00000000bac00000| Complete 
| 429|0x00000000bad00000, 0x00000000bae00000, 0x00000000bae00000|100%| S|CS|TAMS 0x00000000bad00000| PB 0x00000000bad00000| Complete 
| 430|0x00000000bae00000, 0x00000000baf00000, 0x00000000baf00000|100%| S|CS|TAMS 0x00000000bae00000| PB 0x00000000bae00000| Complete 
| 431|0x00000000baf00000, 0x00000000bb000000, 0x00000000bb000000|100%| S|CS|TAMS 0x00000000baf00000| PB 0x00000000baf00000| Complete 
| 432|0x00000000bb000000, 0x00000000bb100000, 0x00000000bb100000|100%| S|CS|TAMS 0x00000000bb000000| PB 0x00000000bb000000| Complete 
| 433|0x00000000bb100000, 0x00000000bb200000, 0x00000000bb200000|100%| S|CS|TAMS 0x00000000bb100000| PB 0x00000000bb100000| Complete 
| 434|0x00000000bb200000, 0x00000000bb300000, 0x00000000bb300000|100%| S|CS|TAMS 0x00000000bb200000| PB 0x00000000bb200000| Complete 
| 435|0x00000000bb300000, 0x00000000bb400000, 0x00000000bb400000|100%| S|CS|TAMS 0x00000000bb300000| PB 0x00000000bb300000| Complete 
| 436|0x00000000bb400000, 0x00000000bb500000, 0x00000000bb500000|100%| S|CS|TAMS 0x00000000bb400000| PB 0x00000000bb400000| Complete 
| 437|0x00000000bb500000, 0x00000000bb600000, 0x00000000bb600000|100%| S|CS|TAMS 0x00000000bb500000| PB 0x00000000bb500000| Complete 
| 438|0x00000000bb600000, 0x00000000bb700000, 0x00000000bb700000|100%| S|CS|TAMS 0x00000000bb600000| PB 0x00000000bb600000| Complete 
| 439|0x00000000bb700000, 0x00000000bb800000, 0x00000000bb800000|100%| S|CS|TAMS 0x00000000bb700000| PB 0x00000000bb700000| Complete 
| 440|0x00000000bb800000, 0x00000000bb900000, 0x00000000bb900000|100%| S|CS|TAMS 0x00000000bb800000| PB 0x00000000bb800000| Complete 
| 441|0x00000000bb900000, 0x00000000bba00000, 0x00000000bba00000|100%| S|CS|TAMS 0x00000000bb900000| PB 0x00000000bb900000| Complete 
| 442|0x00000000bba00000, 0x00000000bbb00000, 0x00000000bbb00000|100%| S|CS|TAMS 0x00000000bba00000| PB 0x00000000bba00000| Complete 
| 443|0x00000000bbb00000, 0x00000000bbc00000, 0x00000000bbc00000|100%| S|CS|TAMS 0x00000000bbb00000| PB 0x00000000bbb00000| Complete 
| 444|0x00000000bbc00000, 0x00000000bbd00000, 0x00000000bbd00000|100%| S|CS|TAMS 0x00000000bbc00000| PB 0x00000000bbc00000| Complete 
| 445|0x00000000bbd00000, 0x00000000bbe00000, 0x00000000bbe00000|100%| S|CS|TAMS 0x00000000bbd00000| PB 0x00000000bbd00000| Complete 
| 446|0x00000000bbe00000, 0x00000000bbf00000, 0x00000000bbf00000|100%| S|CS|TAMS 0x00000000bbe00000| PB 0x00000000bbe00000| Complete 
| 447|0x00000000bbf00000, 0x00000000bc000000, 0x00000000bc000000|100%| S|CS|TAMS 0x00000000bbf00000| PB 0x00000000bbf00000| Complete 
| 448|0x00000000bc000000, 0x00000000bc100000, 0x00000000bc100000|100%| S|CS|TAMS 0x00000000bc000000| PB 0x00000000bc000000| Complete 
| 449|0x00000000bc100000, 0x00000000bc200000, 0x00000000bc200000|100%| S|CS|TAMS 0x00000000bc100000| PB 0x00000000bc100000| Complete 
| 450|0x00000000bc200000, 0x00000000bc300000, 0x00000000bc300000|100%| S|CS|TAMS 0x00000000bc200000| PB 0x00000000bc200000| Complete 
| 451|0x00000000bc300000, 0x00000000bc400000, 0x00000000bc400000|100%| S|CS|TAMS 0x00000000bc300000| PB 0x00000000bc300000| Complete 
| 452|0x00000000bc400000, 0x00000000bc500000, 0x00000000bc500000|100%| S|CS|TAMS 0x00000000bc400000| PB 0x00000000bc400000| Complete 
| 453|0x00000000bc500000, 0x00000000bc600000, 0x00000000bc600000|100%| S|CS|TAMS 0x00000000bc500000| PB 0x00000000bc500000| Complete 
| 454|0x00000000bc600000, 0x00000000bc700000, 0x00000000bc700000|100%| S|CS|TAMS 0x00000000bc600000| PB 0x00000000bc600000| Complete 
| 455|0x00000000bc700000, 0x00000000bc800000, 0x00000000bc800000|100%| S|CS|TAMS 0x00000000bc700000| PB 0x00000000bc700000| Complete 
| 456|0x00000000bc800000, 0x00000000bc800000, 0x00000000bc900000|  0%| F|  |TAMS 0x00000000bc800000| PB 0x00000000bc800000| Untracked 
| 457|0x00000000bc900000, 0x00000000bc900000, 0x00000000bca00000|  0%| F|  |TAMS 0x00000000bc900000| PB 0x00000000bc900000| Untracked 
| 458|0x00000000bca00000, 0x00000000bca00000, 0x00000000bcb00000|  0%| F|  |TAMS 0x00000000bca00000| PB 0x00000000bca00000| Untracked 
| 459|0x00000000bcb00000, 0x00000000bcb00000, 0x00000000bcc00000|  0%| F|  |TAMS 0x00000000bcb00000| PB 0x00000000bcb00000| Untracked 
| 460|0x00000000bcc00000, 0x00000000bcc00000, 0x00000000bcd00000|  0%| F|  |TAMS 0x00000000bcc00000| PB 0x00000000bcc00000| Untracked 
| 461|0x00000000bcd00000, 0x00000000bcd00000, 0x00000000bce00000|  0%| F|  |TAMS 0x00000000bcd00000| PB 0x00000000bcd00000| Untracked 
| 462|0x00000000bce00000, 0x00000000bce00000, 0x00000000bcf00000|  0%| F|  |TAMS 0x00000000bce00000| PB 0x00000000bce00000| Untracked 
| 463|0x00000000bcf00000, 0x00000000bcf00000, 0x00000000bd000000|  0%| F|  |TAMS 0x00000000bcf00000| PB 0x00000000bcf00000| Untracked 
| 464|0x00000000bd000000, 0x00000000bd000000, 0x00000000bd100000|  0%| F|  |TAMS 0x00000000bd000000| PB 0x00000000bd000000| Untracked 
| 465|0x00000000bd100000, 0x00000000bd100000, 0x00000000bd200000|  0%| F|  |TAMS 0x00000000bd100000| PB 0x00000000bd100000| Untracked 
| 466|0x00000000bd200000, 0x00000000bd200000, 0x00000000bd300000|  0%| F|  |TAMS 0x00000000bd200000| PB 0x00000000bd200000| Untracked 
| 467|0x00000000bd300000, 0x00000000bd300000, 0x00000000bd400000|  0%| F|  |TAMS 0x00000000bd300000| PB 0x00000000bd300000| Untracked 
| 468|0x00000000bd400000, 0x00000000bd400000, 0x00000000bd500000|  0%| F|  |TAMS 0x00000000bd400000| PB 0x00000000bd400000| Untracked 
| 469|0x00000000bd500000, 0x00000000bd500000, 0x00000000bd600000|  0%| F|  |TAMS 0x00000000bd500000| PB 0x00000000bd500000| Untracked 
| 470|0x00000000bd600000, 0x00000000bd600000, 0x00000000bd700000|  0%| F|  |TAMS 0x00000000bd600000| PB 0x00000000bd600000| Untracked 
| 471|0x00000000bd700000, 0x00000000bd700000, 0x00000000bd800000|  0%| F|  |TAMS 0x00000000bd700000| PB 0x00000000bd700000| Untracked 
| 472|0x00000000bd800000, 0x00000000bd800000, 0x00000000bd900000|  0%| F|  |TAMS 0x00000000bd800000| PB 0x00000000bd800000| Untracked 
| 473|0x00000000bd900000, 0x00000000bd900000, 0x00000000bda00000|  0%| F|  |TAMS 0x00000000bd900000| PB 0x00000000bd900000| Untracked 
| 474|0x00000000bda00000, 0x00000000bda00000, 0x00000000bdb00000|  0%| F|  |TAMS 0x00000000bda00000| PB 0x00000000bda00000| Untracked 
| 475|0x00000000bdb00000, 0x00000000bdb00000, 0x00000000bdc00000|  0%| F|  |TAMS 0x00000000bdb00000| PB 0x00000000bdb00000| Untracked 
| 476|0x00000000bdc00000, 0x00000000bdc00000, 0x00000000bdd00000|  0%| F|  |TAMS 0x00000000bdc00000| PB 0x00000000bdc00000| Untracked 
| 477|0x00000000bdd00000, 0x00000000bdd00000, 0x00000000bde00000|  0%| F|  |TAMS 0x00000000bdd00000| PB 0x00000000bdd00000| Untracked 
| 478|0x00000000bde00000, 0x00000000bde00000, 0x00000000bdf00000|  0%| F|  |TAMS 0x00000000bde00000| PB 0x00000000bde00000| Untracked 
| 479|0x00000000bdf00000, 0x00000000bdf00000, 0x00000000be000000|  0%| F|  |TAMS 0x00000000bdf00000| PB 0x00000000bdf00000| Untracked 
| 480|0x00000000be000000, 0x00000000be000000, 0x00000000be100000|  0%| F|  |TAMS 0x00000000be000000| PB 0x00000000be000000| Untracked 
| 481|0x00000000be100000, 0x00000000be100000, 0x00000000be200000|  0%| F|  |TAMS 0x00000000be100000| PB 0x00000000be100000| Untracked 
| 482|0x00000000be200000, 0x00000000be200000, 0x00000000be300000|  0%| F|  |TAMS 0x00000000be200000| PB 0x00000000be200000| Untracked 
| 483|0x00000000be300000, 0x00000000be300000, 0x00000000be400000|  0%| F|  |TAMS 0x00000000be300000| PB 0x00000000be300000| Untracked 
| 484|0x00000000be400000, 0x00000000be400000, 0x00000000be500000|  0%| F|  |TAMS 0x00000000be400000| PB 0x00000000be400000| Untracked 
| 485|0x00000000be500000, 0x00000000be500000, 0x00000000be600000|  0%| F|  |TAMS 0x00000000be500000| PB 0x00000000be500000| Untracked 
| 486|0x00000000be600000, 0x00000000be600000, 0x00000000be700000|  0%| F|  |TAMS 0x00000000be600000| PB 0x00000000be600000| Untracked 
| 487|0x00000000be700000, 0x00000000be700000, 0x00000000be800000|  0%| F|  |TAMS 0x00000000be700000| PB 0x00000000be700000| Untracked 
| 488|0x00000000be800000, 0x00000000be800000, 0x00000000be900000|  0%| F|  |TAMS 0x00000000be800000| PB 0x00000000be800000| Untracked 
| 489|0x00000000be900000, 0x00000000be900000, 0x00000000bea00000|  0%| F|  |TAMS 0x00000000be900000| PB 0x00000000be900000| Untracked 
| 490|0x00000000bea00000, 0x00000000bea00000, 0x00000000beb00000|  0%| F|  |TAMS 0x00000000bea00000| PB 0x00000000bea00000| Untracked 
| 491|0x00000000beb00000, 0x00000000beb00000, 0x00000000bec00000|  0%| F|  |TAMS 0x00000000beb00000| PB 0x00000000beb00000| Untracked 
| 492|0x00000000bec00000, 0x00000000bec00000, 0x00000000bed00000|  0%| F|  |TAMS 0x00000000bec00000| PB 0x00000000bec00000| Untracked 
| 493|0x00000000bed00000, 0x00000000bed00000, 0x00000000bee00000|  0%| F|  |TAMS 0x00000000bed00000| PB 0x00000000bed00000| Untracked 
| 494|0x00000000bee00000, 0x00000000bee00000, 0x00000000bef00000|  0%| F|  |TAMS 0x00000000bee00000| PB 0x00000000bee00000| Untracked 
| 495|0x00000000bef00000, 0x00000000bef00000, 0x00000000bf000000|  0%| F|  |TAMS 0x00000000bef00000| PB 0x00000000bef00000| Untracked 
| 496|0x00000000bf000000, 0x00000000bf000000, 0x00000000bf100000|  0%| F|  |TAMS 0x00000000bf000000| PB 0x00000000bf000000| Untracked 
| 497|0x00000000bf100000, 0x00000000bf100000, 0x00000000bf200000|  0%| F|  |TAMS 0x00000000bf100000| PB 0x00000000bf100000| Untracked 
| 498|0x00000000bf200000, 0x00000000bf200000, 0x00000000bf300000|  0%| F|  |TAMS 0x00000000bf200000| PB 0x00000000bf200000| Untracked 
| 499|0x00000000bf300000, 0x00000000bf300000, 0x00000000bf400000|  0%| F|  |TAMS 0x00000000bf300000| PB 0x00000000bf300000| Untracked 
| 500|0x00000000bf400000, 0x00000000bf400000, 0x00000000bf500000|  0%| F|  |TAMS 0x00000000bf400000| PB 0x00000000bf400000| Untracked 
| 501|0x00000000bf500000, 0x00000000bf500000, 0x00000000bf600000|  0%| F|  |TAMS 0x00000000bf500000| PB 0x00000000bf500000| Untracked 
| 502|0x00000000bf600000, 0x00000000bf600000, 0x00000000bf700000|  0%| F|  |TAMS 0x00000000bf600000| PB 0x00000000bf600000| Untracked 
| 503|0x00000000bf700000, 0x00000000bf700000, 0x00000000bf800000|  0%| F|  |TAMS 0x00000000bf700000| PB 0x00000000bf700000| Untracked 
| 504|0x00000000bf800000, 0x00000000bf800000, 0x00000000bf900000|  0%| F|  |TAMS 0x00000000bf800000| PB 0x00000000bf800000| Untracked 
| 505|0x00000000bf900000, 0x00000000bf900000, 0x00000000bfa00000|  0%| F|  |TAMS 0x00000000bf900000| PB 0x00000000bf900000| Untracked 
| 506|0x00000000bfa00000, 0x00000000bfa00000, 0x00000000bfb00000|  0%| F|  |TAMS 0x00000000bfa00000| PB 0x00000000bfa00000| Untracked 
| 507|0x00000000bfb00000, 0x00000000bfb00000, 0x00000000bfc00000|  0%| F|  |TAMS 0x00000000bfb00000| PB 0x00000000bfb00000| Untracked 
| 508|0x00000000bfc00000, 0x00000000bfc00000, 0x00000000bfd00000|  0%| F|  |TAMS 0x00000000bfc00000| PB 0x00000000bfc00000| Untracked 
| 509|0x00000000bfd00000, 0x00000000bfd00000, 0x00000000bfe00000|  0%| F|  |TAMS 0x00000000bfd00000| PB 0x00000000bfd00000| Untracked 
| 510|0x00000000bfe00000, 0x00000000bfe00000, 0x00000000bff00000|  0%| F|  |TAMS 0x00000000bfe00000| PB 0x00000000bfe00000| Untracked 
| 511|0x00000000bff00000, 0x00000000bff00000, 0x00000000c0000000|  0%| F|  |TAMS 0x00000000bff00000| PB 0x00000000bff00000| Untracked 
| 512|0x00000000c0000000, 0x00000000c0000000, 0x00000000c0100000|  0%| F|  |TAMS 0x00000000c0000000| PB 0x00000000c0000000| Untracked 
| 513|0x00000000c0100000, 0x00000000c0100000, 0x00000000c0200000|  0%| F|  |TAMS 0x00000000c0100000| PB 0x00000000c0100000| Untracked 
| 514|0x00000000c0200000, 0x00000000c0200000, 0x00000000c0300000|  0%| F|  |TAMS 0x00000000c0200000| PB 0x00000000c0200000| Untracked 
| 515|0x00000000c0300000, 0x00000000c0300000, 0x00000000c0400000|  0%| F|  |TAMS 0x00000000c0300000| PB 0x00000000c0300000| Untracked 
| 516|0x00000000c0400000, 0x00000000c0400000, 0x00000000c0500000|  0%| F|  |TAMS 0x00000000c0400000| PB 0x00000000c0400000| Untracked 
| 517|0x00000000c0500000, 0x00000000c0500000, 0x00000000c0600000|  0%| F|  |TAMS 0x00000000c0500000| PB 0x00000000c0500000| Untracked 
| 518|0x00000000c0600000, 0x00000000c0600000, 0x00000000c0700000|  0%| F|  |TAMS 0x00000000c0600000| PB 0x00000000c0600000| Untracked 
| 519|0x00000000c0700000, 0x00000000c0700000, 0x00000000c0800000|  0%| F|  |TAMS 0x00000000c0700000| PB 0x00000000c0700000| Untracked 
| 520|0x00000000c0800000, 0x00000000c0800000, 0x00000000c0900000|  0%| F|  |TAMS 0x00000000c0800000| PB 0x00000000c0800000| Untracked 
| 521|0x00000000c0900000, 0x00000000c0900000, 0x00000000c0a00000|  0%| F|  |TAMS 0x00000000c0900000| PB 0x00000000c0900000| Untracked 
| 522|0x00000000c0a00000, 0x00000000c0a00000, 0x00000000c0b00000|  0%| F|  |TAMS 0x00000000c0a00000| PB 0x00000000c0a00000| Untracked 
| 523|0x00000000c0b00000, 0x00000000c0b00000, 0x00000000c0c00000|  0%| F|  |TAMS 0x00000000c0b00000| PB 0x00000000c0b00000| Untracked 
| 524|0x00000000c0c00000, 0x00000000c0c00000, 0x00000000c0d00000|  0%| F|  |TAMS 0x00000000c0c00000| PB 0x00000000c0c00000| Untracked 
| 525|0x00000000c0d00000, 0x00000000c0d00000, 0x00000000c0e00000|  0%| F|  |TAMS 0x00000000c0d00000| PB 0x00000000c0d00000| Untracked 
| 526|0x00000000c0e00000, 0x00000000c0e00000, 0x00000000c0f00000|  0%| F|  |TAMS 0x00000000c0e00000| PB 0x00000000c0e00000| Untracked 
| 527|0x00000000c0f00000, 0x00000000c0f00000, 0x00000000c1000000|  0%| F|  |TAMS 0x00000000c0f00000| PB 0x00000000c0f00000| Untracked 
| 528|0x00000000c1000000, 0x00000000c1000000, 0x00000000c1100000|  0%| F|  |TAMS 0x00000000c1000000| PB 0x00000000c1000000| Untracked 
| 529|0x00000000c1100000, 0x00000000c1100000, 0x00000000c1200000|  0%| F|  |TAMS 0x00000000c1100000| PB 0x00000000c1100000| Untracked 
| 530|0x00000000c1200000, 0x00000000c1200000, 0x00000000c1300000|  0%| F|  |TAMS 0x00000000c1200000| PB 0x00000000c1200000| Untracked 
| 531|0x00000000c1300000, 0x00000000c1300000, 0x00000000c1400000|  0%| F|  |TAMS 0x00000000c1300000| PB 0x00000000c1300000| Untracked 
| 532|0x00000000c1400000, 0x00000000c1400000, 0x00000000c1500000|  0%| F|  |TAMS 0x00000000c1400000| PB 0x00000000c1400000| Untracked 
| 533|0x00000000c1500000, 0x00000000c1500000, 0x00000000c1600000|  0%| F|  |TAMS 0x00000000c1500000| PB 0x00000000c1500000| Untracked 
| 534|0x00000000c1600000, 0x00000000c1600000, 0x00000000c1700000|  0%| F|  |TAMS 0x00000000c1600000| PB 0x00000000c1600000| Untracked 
| 535|0x00000000c1700000, 0x00000000c1700000, 0x00000000c1800000|  0%| F|  |TAMS 0x00000000c1700000| PB 0x00000000c1700000| Untracked 
| 536|0x00000000c1800000, 0x00000000c1800000, 0x00000000c1900000|  0%| F|  |TAMS 0x00000000c1800000| PB 0x00000000c1800000| Untracked 
| 537|0x00000000c1900000, 0x00000000c196d8f8, 0x00000000c1a00000| 42%| E|  |TAMS 0x00000000c1900000| PB 0x00000000c1900000| Complete 
| 538|0x00000000c1a00000, 0x00000000c1b00000, 0x00000000c1b00000|100%| E|CS|TAMS 0x00000000c1a00000| PB 0x00000000c1a00000| Complete 
| 539|0x00000000c1b00000, 0x00000000c1c00000, 0x00000000c1c00000|100%| E|CS|TAMS 0x00000000c1b00000| PB 0x00000000c1b00000| Complete 
| 540|0x00000000c1c00000, 0x00000000c1d00000, 0x00000000c1d00000|100%| E|CS|TAMS 0x00000000c1c00000| PB 0x00000000c1c00000| Complete 
| 541|0x00000000c1d00000, 0x00000000c1e00000, 0x00000000c1e00000|100%| E|CS|TAMS 0x00000000c1d00000| PB 0x00000000c1d00000| Complete 
| 542|0x00000000c1e00000, 0x00000000c1f00000, 0x00000000c1f00000|100%| E|CS|TAMS 0x00000000c1e00000| PB 0x00000000c1e00000| Complete 
| 543|0x00000000c1f00000, 0x00000000c2000000, 0x00000000c2000000|100%| E|CS|TAMS 0x00000000c1f00000| PB 0x00000000c1f00000| Complete 
| 544|0x00000000c2000000, 0x00000000c2100000, 0x00000000c2100000|100%| E|CS|TAMS 0x00000000c2000000| PB 0x00000000c2000000| Complete 
| 545|0x00000000c2100000, 0x00000000c2200000, 0x00000000c2200000|100%| E|CS|TAMS 0x00000000c2100000| PB 0x00000000c2100000| Complete 
| 546|0x00000000c2200000, 0x00000000c2300000, 0x00000000c2300000|100%| E|CS|TAMS 0x00000000c2200000| PB 0x00000000c2200000| Complete 
| 547|0x00000000c2300000, 0x00000000c2400000, 0x00000000c2400000|100%| E|CS|TAMS 0x00000000c2300000| PB 0x00000000c2300000| Complete 
| 548|0x00000000c2400000, 0x00000000c2500000, 0x00000000c2500000|100%| E|CS|TAMS 0x00000000c2400000| PB 0x00000000c2400000| Complete 
| 549|0x00000000c2500000, 0x00000000c2600000, 0x00000000c2600000|100%| E|CS|TAMS 0x00000000c2500000| PB 0x00000000c2500000| Complete 
| 550|0x00000000c2600000, 0x00000000c2700000, 0x00000000c2700000|100%| E|CS|TAMS 0x00000000c2600000| PB 0x00000000c2600000| Complete 
| 551|0x00000000c2700000, 0x00000000c2800000, 0x00000000c2800000|100%| E|CS|TAMS 0x00000000c2700000| PB 0x00000000c2700000| Complete 
| 552|0x00000000c2800000, 0x00000000c2900000, 0x00000000c2900000|100%| E|CS|TAMS 0x00000000c2800000| PB 0x00000000c2800000| Complete 
| 553|0x00000000c2900000, 0x00000000c2a00000, 0x00000000c2a00000|100%| E|CS|TAMS 0x00000000c2900000| PB 0x00000000c2900000| Complete 
| 554|0x00000000c2a00000, 0x00000000c2b00000, 0x00000000c2b00000|100%| E|CS|TAMS 0x00000000c2a00000| PB 0x00000000c2a00000| Complete 
| 555|0x00000000c2b00000, 0x00000000c2c00000, 0x00000000c2c00000|100%| E|CS|TAMS 0x00000000c2b00000| PB 0x00000000c2b00000| Complete 
| 556|0x00000000c2c00000, 0x00000000c2d00000, 0x00000000c2d00000|100%| E|CS|TAMS 0x00000000c2c00000| PB 0x00000000c2c00000| Complete 
| 557|0x00000000c2d00000, 0x00000000c2e00000, 0x00000000c2e00000|100%| E|CS|TAMS 0x00000000c2d00000| PB 0x00000000c2d00000| Complete 
| 558|0x00000000c2e00000, 0x00000000c2f00000, 0x00000000c2f00000|100%| E|CS|TAMS 0x00000000c2e00000| PB 0x00000000c2e00000| Complete 
| 559|0x00000000c2f00000, 0x00000000c3000000, 0x00000000c3000000|100%| E|CS|TAMS 0x00000000c2f00000| PB 0x00000000c2f00000| Complete 
| 560|0x00000000c3000000, 0x00000000c3100000, 0x00000000c3100000|100%| E|CS|TAMS 0x00000000c3000000| PB 0x00000000c3000000| Complete 
| 561|0x00000000c3100000, 0x00000000c3200000, 0x00000000c3200000|100%| E|CS|TAMS 0x00000000c3100000| PB 0x00000000c3100000| Complete 
| 562|0x00000000c3200000, 0x00000000c3300000, 0x00000000c3300000|100%| E|CS|TAMS 0x00000000c3200000| PB 0x00000000c3200000| Complete 
| 563|0x00000000c3300000, 0x00000000c3400000, 0x00000000c3400000|100%| E|CS|TAMS 0x00000000c3300000| PB 0x00000000c3300000| Complete 
| 564|0x00000000c3400000, 0x00000000c3500000, 0x00000000c3500000|100%| E|CS|TAMS 0x00000000c3400000| PB 0x00000000c3400000| Complete 
| 565|0x00000000c3500000, 0x00000000c3600000, 0x00000000c3600000|100%| E|CS|TAMS 0x00000000c3500000| PB 0x00000000c3500000| Complete 
| 566|0x00000000c3600000, 0x00000000c3700000, 0x00000000c3700000|100%| E|CS|TAMS 0x00000000c3600000| PB 0x00000000c3600000| Complete 
| 567|0x00000000c3700000, 0x00000000c3800000, 0x00000000c3800000|100%| E|CS|TAMS 0x00000000c3700000| PB 0x00000000c3700000| Complete 
| 568|0x00000000c3800000, 0x00000000c3900000, 0x00000000c3900000|100%| E|CS|TAMS 0x00000000c3800000| PB 0x00000000c3800000| Complete 
| 569|0x00000000c3900000, 0x00000000c3a00000, 0x00000000c3a00000|100%| E|CS|TAMS 0x00000000c3900000| PB 0x00000000c3900000| Complete 
| 570|0x00000000c3a00000, 0x00000000c3b00000, 0x00000000c3b00000|100%| E|CS|TAMS 0x00000000c3a00000| PB 0x00000000c3a00000| Complete 
| 571|0x00000000c3b00000, 0x00000000c3c00000, 0x00000000c3c00000|100%| E|CS|TAMS 0x00000000c3b00000| PB 0x00000000c3b00000| Complete 
| 572|0x00000000c3c00000, 0x00000000c3d00000, 0x00000000c3d00000|100%| E|CS|TAMS 0x00000000c3c00000| PB 0x00000000c3c00000| Complete 
| 573|0x00000000c3d00000, 0x00000000c3e00000, 0x00000000c3e00000|100%| E|CS|TAMS 0x00000000c3d00000| PB 0x00000000c3d00000| Complete 
| 574|0x00000000c3e00000, 0x00000000c3f00000, 0x00000000c3f00000|100%| E|CS|TAMS 0x00000000c3e00000| PB 0x00000000c3e00000| Complete 
| 575|0x00000000c3f00000, 0x00000000c4000000, 0x00000000c4000000|100%| E|CS|TAMS 0x00000000c3f00000| PB 0x00000000c3f00000| Complete 
| 576|0x00000000c4000000, 0x00000000c4100000, 0x00000000c4100000|100%| E|CS|TAMS 0x00000000c4000000| PB 0x00000000c4000000| Complete 
| 577|0x00000000c4100000, 0x00000000c4200000, 0x00000000c4200000|100%| E|CS|TAMS 0x00000000c4100000| PB 0x00000000c4100000| Complete 
| 578|0x00000000c4200000, 0x00000000c4300000, 0x00000000c4300000|100%| E|CS|TAMS 0x00000000c4200000| PB 0x00000000c4200000| Complete 
| 579|0x00000000c4300000, 0x00000000c4400000, 0x00000000c4400000|100%| E|CS|TAMS 0x00000000c4300000| PB 0x00000000c4300000| Complete 
| 580|0x00000000c4400000, 0x00000000c4500000, 0x00000000c4500000|100%| E|CS|TAMS 0x00000000c4400000| PB 0x00000000c4400000| Complete 
| 581|0x00000000c4500000, 0x00000000c4600000, 0x00000000c4600000|100%| E|CS|TAMS 0x00000000c4500000| PB 0x00000000c4500000| Complete 
| 582|0x00000000c4600000, 0x00000000c4700000, 0x00000000c4700000|100%| E|CS|TAMS 0x00000000c4600000| PB 0x00000000c4600000| Complete 
| 583|0x00000000c4700000, 0x00000000c4800000, 0x00000000c4800000|100%| E|  |TAMS 0x00000000c4700000| PB 0x00000000c4700000| Complete 
| 584|0x00000000c4800000, 0x00000000c4900000, 0x00000000c4900000|100%| E|CS|TAMS 0x00000000c4800000| PB 0x00000000c4800000| Complete 
| 585|0x00000000c4900000, 0x00000000c4a00000, 0x00000000c4a00000|100%| E|CS|TAMS 0x00000000c4900000| PB 0x00000000c4900000| Complete 
| 586|0x00000000c4a00000, 0x00000000c4b00000, 0x00000000c4b00000|100%| E|CS|TAMS 0x00000000c4a00000| PB 0x00000000c4a00000| Complete 
| 587|0x00000000c4b00000, 0x00000000c4c00000, 0x00000000c4c00000|100%| E|CS|TAMS 0x00000000c4b00000| PB 0x00000000c4b00000| Complete 
| 588|0x00000000c4c00000, 0x00000000c4d00000, 0x00000000c4d00000|100%| E|CS|TAMS 0x00000000c4c00000| PB 0x00000000c4c00000| Complete 
| 589|0x00000000c4d00000, 0x00000000c4e00000, 0x00000000c4e00000|100%| E|CS|TAMS 0x00000000c4d00000| PB 0x00000000c4d00000| Complete 
| 590|0x00000000c4e00000, 0x00000000c4f00000, 0x00000000c4f00000|100%| E|CS|TAMS 0x00000000c4e00000| PB 0x00000000c4e00000| Complete 
| 591|0x00000000c4f00000, 0x00000000c5000000, 0x00000000c5000000|100%| E|CS|TAMS 0x00000000c4f00000| PB 0x00000000c4f00000| Complete 
| 592|0x00000000c5000000, 0x00000000c5100000, 0x00000000c5100000|100%| E|CS|TAMS 0x00000000c5000000| PB 0x00000000c5000000| Complete 
| 593|0x00000000c5100000, 0x00000000c5200000, 0x00000000c5200000|100%| E|CS|TAMS 0x00000000c5100000| PB 0x00000000c5100000| Complete 
| 594|0x00000000c5200000, 0x00000000c5300000, 0x00000000c5300000|100%| E|CS|TAMS 0x00000000c5200000| PB 0x00000000c5200000| Complete 
| 595|0x00000000c5300000, 0x00000000c5400000, 0x00000000c5400000|100%| E|CS|TAMS 0x00000000c5300000| PB 0x00000000c5300000| Complete 
| 596|0x00000000c5400000, 0x00000000c5500000, 0x00000000c5500000|100%| E|CS|TAMS 0x00000000c5400000| PB 0x00000000c5400000| Complete 
| 597|0x00000000c5500000, 0x00000000c5600000, 0x00000000c5600000|100%| E|CS|TAMS 0x00000000c5500000| PB 0x00000000c5500000| Complete 
| 598|0x00000000c5600000, 0x00000000c5700000, 0x00000000c5700000|100%| E|CS|TAMS 0x00000000c5600000| PB 0x00000000c5600000| Complete 
| 599|0x00000000c5700000, 0x00000000c5800000, 0x00000000c5800000|100%| E|CS|TAMS 0x00000000c5700000| PB 0x00000000c5700000| Complete 
| 600|0x00000000c5800000, 0x00000000c5900000, 0x00000000c5900000|100%| E|CS|TAMS 0x00000000c5800000| PB 0x00000000c5800000| Complete 
| 601|0x00000000c5900000, 0x00000000c5a00000, 0x00000000c5a00000|100%| E|CS|TAMS 0x00000000c5900000| PB 0x00000000c5900000| Complete 
| 602|0x00000000c5a00000, 0x00000000c5b00000, 0x00000000c5b00000|100%| E|CS|TAMS 0x00000000c5a00000| PB 0x00000000c5a00000| Complete 
| 603|0x00000000c5b00000, 0x00000000c5c00000, 0x00000000c5c00000|100%| E|CS|TAMS 0x00000000c5b00000| PB 0x00000000c5b00000| Complete 
| 604|0x00000000c5c00000, 0x00000000c5d00000, 0x00000000c5d00000|100%| E|CS|TAMS 0x00000000c5c00000| PB 0x00000000c5c00000| Complete 
| 605|0x00000000c5d00000, 0x00000000c5e00000, 0x00000000c5e00000|100%| E|CS|TAMS 0x00000000c5d00000| PB 0x00000000c5d00000| Complete 
| 606|0x00000000c5e00000, 0x00000000c5f00000, 0x00000000c5f00000|100%| E|CS|TAMS 0x00000000c5e00000| PB 0x00000000c5e00000| Complete 
| 607|0x00000000c5f00000, 0x00000000c6000000, 0x00000000c6000000|100%| E|CS|TAMS 0x00000000c5f00000| PB 0x00000000c5f00000| Complete 
| 608|0x00000000c6000000, 0x00000000c6100000, 0x00000000c6100000|100%| E|CS|TAMS 0x00000000c6000000| PB 0x00000000c6000000| Complete 
| 609|0x00000000c6100000, 0x00000000c6200000, 0x00000000c6200000|100%| E|CS|TAMS 0x00000000c6100000| PB 0x00000000c6100000| Complete 
| 610|0x00000000c6200000, 0x00000000c6300000, 0x00000000c6300000|100%| E|CS|TAMS 0x00000000c6200000| PB 0x00000000c6200000| Complete 
| 611|0x00000000c6300000, 0x00000000c6400000, 0x00000000c6400000|100%| E|CS|TAMS 0x00000000c6300000| PB 0x00000000c6300000| Complete 
| 612|0x00000000c6400000, 0x00000000c6500000, 0x00000000c6500000|100%| E|CS|TAMS 0x00000000c6400000| PB 0x00000000c6400000| Complete 
| 613|0x00000000c6500000, 0x00000000c6600000, 0x00000000c6600000|100%| E|CS|TAMS 0x00000000c6500000| PB 0x00000000c6500000| Complete 
| 614|0x00000000c6600000, 0x00000000c6700000, 0x00000000c6700000|100%| E|CS|TAMS 0x00000000c6600000| PB 0x00000000c6600000| Complete 
| 615|0x00000000c6700000, 0x00000000c6800000, 0x00000000c6800000|100%| E|CS|TAMS 0x00000000c6700000| PB 0x00000000c6700000| Complete 
| 616|0x00000000c6800000, 0x00000000c6900000, 0x00000000c6900000|100%| E|CS|TAMS 0x00000000c6800000| PB 0x00000000c6800000| Complete 
| 617|0x00000000c6900000, 0x00000000c6a00000, 0x00000000c6a00000|100%| E|CS|TAMS 0x00000000c6900000| PB 0x00000000c6900000| Complete 
| 618|0x00000000c6a00000, 0x00000000c6b00000, 0x00000000c6b00000|100%| E|CS|TAMS 0x00000000c6a00000| PB 0x00000000c6a00000| Complete 
| 619|0x00000000c6b00000, 0x00000000c6c00000, 0x00000000c6c00000|100%| E|CS|TAMS 0x00000000c6b00000| PB 0x00000000c6b00000| Complete 
| 620|0x00000000c6c00000, 0x00000000c6d00000, 0x00000000c6d00000|100%| E|CS|TAMS 0x00000000c6c00000| PB 0x00000000c6c00000| Complete 
| 621|0x00000000c6d00000, 0x00000000c6e00000, 0x00000000c6e00000|100%| E|CS|TAMS 0x00000000c6d00000| PB 0x00000000c6d00000| Complete 
| 622|0x00000000c6e00000, 0x00000000c6f00000, 0x00000000c6f00000|100%| E|CS|TAMS 0x00000000c6e00000| PB 0x00000000c6e00000| Complete 
| 623|0x00000000c6f00000, 0x00000000c7000000, 0x00000000c7000000|100%| E|CS|TAMS 0x00000000c6f00000| PB 0x00000000c6f00000| Complete 
| 624|0x00000000c7000000, 0x00000000c7100000, 0x00000000c7100000|100%| E|CS|TAMS 0x00000000c7000000| PB 0x00000000c7000000| Complete 
| 625|0x00000000c7100000, 0x00000000c7200000, 0x00000000c7200000|100%| E|CS|TAMS 0x00000000c7100000| PB 0x00000000c7100000| Complete 
| 626|0x00000000c7200000, 0x00000000c7300000, 0x00000000c7300000|100%| E|CS|TAMS 0x00000000c7200000| PB 0x00000000c7200000| Complete 
| 627|0x00000000c7300000, 0x00000000c7400000, 0x00000000c7400000|100%| E|CS|TAMS 0x00000000c7300000| PB 0x00000000c7300000| Complete 
| 628|0x00000000c7400000, 0x00000000c7500000, 0x00000000c7500000|100%| E|CS|TAMS 0x00000000c7400000| PB 0x00000000c7400000| Complete 
| 629|0x00000000c7500000, 0x00000000c7600000, 0x00000000c7600000|100%| E|CS|TAMS 0x00000000c7500000| PB 0x00000000c7500000| Complete 
| 630|0x00000000c7600000, 0x00000000c7700000, 0x00000000c7700000|100%| E|CS|TAMS 0x00000000c7600000| PB 0x00000000c7600000| Complete 
| 631|0x00000000c7700000, 0x00000000c7800000, 0x00000000c7800000|100%| E|CS|TAMS 0x00000000c7700000| PB 0x00000000c7700000| Complete 
| 632|0x00000000c7800000, 0x00000000c7900000, 0x00000000c7900000|100%| E|CS|TAMS 0x00000000c7800000| PB 0x00000000c7800000| Complete 
| 633|0x00000000c7900000, 0x00000000c7a00000, 0x00000000c7a00000|100%| E|CS|TAMS 0x00000000c7900000| PB 0x00000000c7900000| Complete 
| 634|0x00000000c7a00000, 0x00000000c7b00000, 0x00000000c7b00000|100%| E|CS|TAMS 0x00000000c7a00000| PB 0x00000000c7a00000| Complete 
| 635|0x00000000c7b00000, 0x00000000c7c00000, 0x00000000c7c00000|100%| E|CS|TAMS 0x00000000c7b00000| PB 0x00000000c7b00000| Complete 
| 636|0x00000000c7c00000, 0x00000000c7d00000, 0x00000000c7d00000|100%| E|CS|TAMS 0x00000000c7c00000| PB 0x00000000c7c00000| Complete 
| 637|0x00000000c7d00000, 0x00000000c7e00000, 0x00000000c7e00000|100%| E|CS|TAMS 0x00000000c7d00000| PB 0x00000000c7d00000| Complete 
| 638|0x00000000c7e00000, 0x00000000c7f00000, 0x00000000c7f00000|100%| E|CS|TAMS 0x00000000c7e00000| PB 0x00000000c7e00000| Complete 
| 639|0x00000000c7f00000, 0x00000000c8000000, 0x00000000c8000000|100%| E|CS|TAMS 0x00000000c7f00000| PB 0x00000000c7f00000| Complete 
| 640|0x00000000c8000000, 0x00000000c8100000, 0x00000000c8100000|100%| E|CS|TAMS 0x00000000c8000000| PB 0x00000000c8000000| Complete 
| 641|0x00000000c8100000, 0x00000000c8200000, 0x00000000c8200000|100%| E|CS|TAMS 0x00000000c8100000| PB 0x00000000c8100000| Complete 
| 642|0x00000000c8200000, 0x00000000c8300000, 0x00000000c8300000|100%| E|CS|TAMS 0x00000000c8200000| PB 0x00000000c8200000| Complete 
| 643|0x00000000c8300000, 0x00000000c8400000, 0x00000000c8400000|100%| E|CS|TAMS 0x00000000c8300000| PB 0x00000000c8300000| Complete 
| 644|0x00000000c8400000, 0x00000000c8500000, 0x00000000c8500000|100%| E|CS|TAMS 0x00000000c8400000| PB 0x00000000c8400000| Complete 
| 645|0x00000000c8500000, 0x00000000c8600000, 0x00000000c8600000|100%| E|CS|TAMS 0x00000000c8500000| PB 0x00000000c8500000| Complete 
| 646|0x00000000c8600000, 0x00000000c8700000, 0x00000000c8700000|100%| E|CS|TAMS 0x00000000c8600000| PB 0x00000000c8600000| Complete 
| 647|0x00000000c8700000, 0x00000000c8800000, 0x00000000c8800000|100%| E|CS|TAMS 0x00000000c8700000| PB 0x00000000c8700000| Complete 
| 648|0x00000000c8800000, 0x00000000c8900000, 0x00000000c8900000|100%| E|CS|TAMS 0x00000000c8800000| PB 0x00000000c8800000| Complete 
| 649|0x00000000c8900000, 0x00000000c8a00000, 0x00000000c8a00000|100%| E|CS|TAMS 0x00000000c8900000| PB 0x00000000c8900000| Complete 
| 650|0x00000000c8a00000, 0x00000000c8b00000, 0x00000000c8b00000|100%| E|CS|TAMS 0x00000000c8a00000| PB 0x00000000c8a00000| Complete 
| 651|0x00000000c8b00000, 0x00000000c8c00000, 0x00000000c8c00000|100%| E|CS|TAMS 0x00000000c8b00000| PB 0x00000000c8b00000| Complete 
| 652|0x00000000c8c00000, 0x00000000c8d00000, 0x00000000c8d00000|100%| E|CS|TAMS 0x00000000c8c00000| PB 0x00000000c8c00000| Complete 
| 653|0x00000000c8d00000, 0x00000000c8e00000, 0x00000000c8e00000|100%| E|CS|TAMS 0x00000000c8d00000| PB 0x00000000c8d00000| Complete 
| 654|0x00000000c8e00000, 0x00000000c8f00000, 0x00000000c8f00000|100%| E|CS|TAMS 0x00000000c8e00000| PB 0x00000000c8e00000| Complete 
| 655|0x00000000c8f00000, 0x00000000c9000000, 0x00000000c9000000|100%| E|CS|TAMS 0x00000000c8f00000| PB 0x00000000c8f00000| Complete 
| 656|0x00000000c9000000, 0x00000000c9100000, 0x00000000c9100000|100%| E|CS|TAMS 0x00000000c9000000| PB 0x00000000c9000000| Complete 
| 657|0x00000000c9100000, 0x00000000c9200000, 0x00000000c9200000|100%| E|CS|TAMS 0x00000000c9100000| PB 0x00000000c9100000| Complete 
| 658|0x00000000c9200000, 0x00000000c9300000, 0x00000000c9300000|100%| E|CS|TAMS 0x00000000c9200000| PB 0x00000000c9200000| Complete 
| 659|0x00000000c9300000, 0x00000000c9400000, 0x00000000c9400000|100%| E|CS|TAMS 0x00000000c9300000| PB 0x00000000c9300000| Complete 
| 660|0x00000000c9400000, 0x00000000c9500000, 0x00000000c9500000|100%| E|CS|TAMS 0x00000000c9400000| PB 0x00000000c9400000| Complete 
| 661|0x00000000c9500000, 0x00000000c9600000, 0x00000000c9600000|100%| E|CS|TAMS 0x00000000c9500000| PB 0x00000000c9500000| Complete 
| 662|0x00000000c9600000, 0x00000000c9700000, 0x00000000c9700000|100%| E|CS|TAMS 0x00000000c9600000| PB 0x00000000c9600000| Complete 
| 663|0x00000000c9700000, 0x00000000c9800000, 0x00000000c9800000|100%| E|CS|TAMS 0x00000000c9700000| PB 0x00000000c9700000| Complete 
| 664|0x00000000c9800000, 0x00000000c9900000, 0x00000000c9900000|100%| E|CS|TAMS 0x00000000c9800000| PB 0x00000000c9800000| Complete 
| 665|0x00000000c9900000, 0x00000000c9a00000, 0x00000000c9a00000|100%| E|CS|TAMS 0x00000000c9900000| PB 0x00000000c9900000| Complete 
| 666|0x00000000c9a00000, 0x00000000c9b00000, 0x00000000c9b00000|100%| E|CS|TAMS 0x00000000c9a00000| PB 0x00000000c9a00000| Complete 
| 667|0x00000000c9b00000, 0x00000000c9c00000, 0x00000000c9c00000|100%| E|CS|TAMS 0x00000000c9b00000| PB 0x00000000c9b00000| Complete 
| 668|0x00000000c9c00000, 0x00000000c9d00000, 0x00000000c9d00000|100%| E|CS|TAMS 0x00000000c9c00000| PB 0x00000000c9c00000| Complete 
| 669|0x00000000c9d00000, 0x00000000c9e00000, 0x00000000c9e00000|100%| E|CS|TAMS 0x00000000c9d00000| PB 0x00000000c9d00000| Complete 
| 670|0x00000000c9e00000, 0x00000000c9f00000, 0x00000000c9f00000|100%| E|CS|TAMS 0x00000000c9e00000| PB 0x00000000c9e00000| Complete 
| 671|0x00000000c9f00000, 0x00000000ca000000, 0x00000000ca000000|100%| E|CS|TAMS 0x00000000c9f00000| PB 0x00000000c9f00000| Complete 
| 672|0x00000000ca000000, 0x00000000ca100000, 0x00000000ca100000|100%| E|CS|TAMS 0x00000000ca000000| PB 0x00000000ca000000| Complete 
| 673|0x00000000ca100000, 0x00000000ca200000, 0x00000000ca200000|100%| E|CS|TAMS 0x00000000ca100000| PB 0x00000000ca100000| Complete 
| 674|0x00000000ca200000, 0x00000000ca300000, 0x00000000ca300000|100%| E|CS|TAMS 0x00000000ca200000| PB 0x00000000ca200000| Complete 
| 675|0x00000000ca300000, 0x00000000ca400000, 0x00000000ca400000|100%| E|CS|TAMS 0x00000000ca300000| PB 0x00000000ca300000| Complete 
| 676|0x00000000ca400000, 0x00000000ca500000, 0x00000000ca500000|100%| E|CS|TAMS 0x00000000ca400000| PB 0x00000000ca400000| Complete 
| 677|0x00000000ca500000, 0x00000000ca600000, 0x00000000ca600000|100%| E|CS|TAMS 0x00000000ca500000| PB 0x00000000ca500000| Complete 
| 678|0x00000000ca600000, 0x00000000ca700000, 0x00000000ca700000|100%| E|CS|TAMS 0x00000000ca600000| PB 0x00000000ca600000| Complete 
| 679|0x00000000ca700000, 0x00000000ca800000, 0x00000000ca800000|100%| E|CS|TAMS 0x00000000ca700000| PB 0x00000000ca700000| Complete 
| 680|0x00000000ca800000, 0x00000000ca900000, 0x00000000ca900000|100%| E|CS|TAMS 0x00000000ca800000| PB 0x00000000ca800000| Complete 
| 681|0x00000000ca900000, 0x00000000caa00000, 0x00000000caa00000|100%| E|CS|TAMS 0x00000000ca900000| PB 0x00000000ca900000| Complete 
| 682|0x00000000caa00000, 0x00000000cab00000, 0x00000000cab00000|100%| E|CS|TAMS 0x00000000caa00000| PB 0x00000000caa00000| Complete 
| 683|0x00000000cab00000, 0x00000000cac00000, 0x00000000cac00000|100%| E|CS|TAMS 0x00000000cab00000| PB 0x00000000cab00000| Complete 
| 684|0x00000000cac00000, 0x00000000cad00000, 0x00000000cad00000|100%| E|CS|TAMS 0x00000000cac00000| PB 0x00000000cac00000| Complete 
| 685|0x00000000cad00000, 0x00000000cae00000, 0x00000000cae00000|100%| E|CS|TAMS 0x00000000cad00000| PB 0x00000000cad00000| Complete 
| 686|0x00000000cae00000, 0x00000000caf00000, 0x00000000caf00000|100%| E|CS|TAMS 0x00000000cae00000| PB 0x00000000cae00000| Complete 
| 687|0x00000000caf00000, 0x00000000cb000000, 0x00000000cb000000|100%| E|CS|TAMS 0x00000000caf00000| PB 0x00000000caf00000| Complete 
| 688|0x00000000cb000000, 0x00000000cb100000, 0x00000000cb100000|100%| E|CS|TAMS 0x00000000cb000000| PB 0x00000000cb000000| Complete 
| 689|0x00000000cb100000, 0x00000000cb200000, 0x00000000cb200000|100%| E|CS|TAMS 0x00000000cb100000| PB 0x00000000cb100000| Complete 
| 690|0x00000000cb200000, 0x00000000cb300000, 0x00000000cb300000|100%| E|CS|TAMS 0x00000000cb200000| PB 0x00000000cb200000| Complete 
| 691|0x00000000cb300000, 0x00000000cb400000, 0x00000000cb400000|100%| E|CS|TAMS 0x00000000cb300000| PB 0x00000000cb300000| Complete 
| 692|0x00000000cb400000, 0x00000000cb500000, 0x00000000cb500000|100%| E|CS|TAMS 0x00000000cb400000| PB 0x00000000cb400000| Complete 
| 693|0x00000000cb500000, 0x00000000cb600000, 0x00000000cb600000|100%| E|CS|TAMS 0x00000000cb500000| PB 0x00000000cb500000| Complete 
| 694|0x00000000cb600000, 0x00000000cb700000, 0x00000000cb700000|100%| E|CS|TAMS 0x00000000cb600000| PB 0x00000000cb600000| Complete 
| 695|0x00000000cb700000, 0x00000000cb800000, 0x00000000cb800000|100%| E|CS|TAMS 0x00000000cb700000| PB 0x00000000cb700000| Complete 
| 696|0x00000000cb800000, 0x00000000cb900000, 0x00000000cb900000|100%| E|CS|TAMS 0x00000000cb800000| PB 0x00000000cb800000| Complete 
| 697|0x00000000cb900000, 0x00000000cba00000, 0x00000000cba00000|100%| E|CS|TAMS 0x00000000cb900000| PB 0x00000000cb900000| Complete 
| 698|0x00000000cba00000, 0x00000000cbb00000, 0x00000000cbb00000|100%| E|CS|TAMS 0x00000000cba00000| PB 0x00000000cba00000| Complete 
| 699|0x00000000cbb00000, 0x00000000cbc00000, 0x00000000cbc00000|100%| E|CS|TAMS 0x00000000cbb00000| PB 0x00000000cbb00000| Complete 
| 700|0x00000000cbc00000, 0x00000000cbd00000, 0x00000000cbd00000|100%| E|CS|TAMS 0x00000000cbc00000| PB 0x00000000cbc00000| Complete 
| 701|0x00000000cbd00000, 0x00000000cbe00000, 0x00000000cbe00000|100%| E|CS|TAMS 0x00000000cbd00000| PB 0x00000000cbd00000| Complete 
| 702|0x00000000cbe00000, 0x00000000cbf00000, 0x00000000cbf00000|100%| E|CS|TAMS 0x00000000cbe00000| PB 0x00000000cbe00000| Complete 
| 703|0x00000000cbf00000, 0x00000000cc000000, 0x00000000cc000000|100%| E|CS|TAMS 0x00000000cbf00000| PB 0x00000000cbf00000| Complete 
| 704|0x00000000cc000000, 0x00000000cc100000, 0x00000000cc100000|100%| E|CS|TAMS 0x00000000cc000000| PB 0x00000000cc000000| Complete 
| 705|0x00000000cc100000, 0x00000000cc200000, 0x00000000cc200000|100%| E|CS|TAMS 0x00000000cc100000| PB 0x00000000cc100000| Complete 
| 706|0x00000000cc200000, 0x00000000cc300000, 0x00000000cc300000|100%| E|CS|TAMS 0x00000000cc200000| PB 0x00000000cc200000| Complete 
| 707|0x00000000cc300000, 0x00000000cc400000, 0x00000000cc400000|100%| E|CS|TAMS 0x00000000cc300000| PB 0x00000000cc300000| Complete 
| 708|0x00000000cc400000, 0x00000000cc500000, 0x00000000cc500000|100%| E|CS|TAMS 0x00000000cc400000| PB 0x00000000cc400000| Complete 
| 709|0x00000000cc500000, 0x00000000cc600000, 0x00000000cc600000|100%| E|CS|TAMS 0x00000000cc500000| PB 0x00000000cc500000| Complete 
| 710|0x00000000cc600000, 0x00000000cc700000, 0x00000000cc700000|100%| E|CS|TAMS 0x00000000cc600000| PB 0x00000000cc600000| Complete 
| 711|0x00000000cc700000, 0x00000000cc800000, 0x00000000cc800000|100%| E|CS|TAMS 0x00000000cc700000| PB 0x00000000cc700000| Complete 
| 712|0x00000000cc800000, 0x00000000cc900000, 0x00000000cc900000|100%| E|CS|TAMS 0x00000000cc800000| PB 0x00000000cc800000| Complete 
| 713|0x00000000cc900000, 0x00000000cca00000, 0x00000000cca00000|100%| E|CS|TAMS 0x00000000cc900000| PB 0x00000000cc900000| Complete 
| 714|0x00000000cca00000, 0x00000000ccb00000, 0x00000000ccb00000|100%| E|CS|TAMS 0x00000000cca00000| PB 0x00000000cca00000| Complete 
| 715|0x00000000ccb00000, 0x00000000ccc00000, 0x00000000ccc00000|100%| E|CS|TAMS 0x00000000ccb00000| PB 0x00000000ccb00000| Complete 
| 716|0x00000000ccc00000, 0x00000000ccd00000, 0x00000000ccd00000|100%| E|CS|TAMS 0x00000000ccc00000| PB 0x00000000ccc00000| Complete 
| 717|0x00000000ccd00000, 0x00000000cce00000, 0x00000000cce00000|100%| E|CS|TAMS 0x00000000ccd00000| PB 0x00000000ccd00000| Complete 
| 718|0x00000000cce00000, 0x00000000ccf00000, 0x00000000ccf00000|100%| E|CS|TAMS 0x00000000cce00000| PB 0x00000000cce00000| Complete 
| 719|0x00000000ccf00000, 0x00000000cd000000, 0x00000000cd000000|100%| E|CS|TAMS 0x00000000ccf00000| PB 0x00000000ccf00000| Complete 
| 720|0x00000000cd000000, 0x00000000cd100000, 0x00000000cd100000|100%| E|CS|TAMS 0x00000000cd000000| PB 0x00000000cd000000| Complete 
|1515|0x00000000feb00000, 0x00000000fec00000, 0x00000000fec00000|100%| E|CS|TAMS 0x00000000feb00000| PB 0x00000000feb00000| Complete 
|1516|0x00000000fec00000, 0x00000000fed00000, 0x00000000fed00000|100%| E|CS|TAMS 0x00000000fec00000| PB 0x00000000fec00000| Complete 
|1517|0x00000000fed00000, 0x00000000fee00000, 0x00000000fee00000|100%| E|CS|TAMS 0x00000000fed00000| PB 0x00000000fed00000| Complete 
|1518|0x00000000fee00000, 0x00000000fef00000, 0x00000000fef00000|100%| E|CS|TAMS 0x00000000fee00000| PB 0x00000000fee00000| Complete 
|1519|0x00000000fef00000, 0x00000000ff000000, 0x00000000ff000000|100%| O|  |TAMS 0x00000000fef00000| PB 0x00000000fef00000| Untracked 
|1520|0x00000000ff000000, 0x00000000ff100000, 0x00000000ff100000|100%| O|  |TAMS 0x00000000ff000000| PB 0x00000000ff000000| Untracked 
|1521|0x00000000ff100000, 0x00000000ff200000, 0x00000000ff200000|100%| O|  |TAMS 0x00000000ff100000| PB 0x00000000ff100000| Untracked 
|1522|0x00000000ff200000, 0x00000000ff300000, 0x00000000ff300000|100%| O|  |TAMS 0x00000000ff200000| PB 0x00000000ff200000| Untracked 
|1523|0x00000000ff300000, 0x00000000ff400000, 0x00000000ff400000|100%| O|  |TAMS 0x00000000ff300000| PB 0x00000000ff300000| Untracked 
|1524|0x00000000ff400000, 0x00000000ff500000, 0x00000000ff500000|100%| O|  |TAMS 0x00000000ff400000| PB 0x00000000ff400000| Untracked 
|1525|0x00000000ff500000, 0x00000000ff600000, 0x00000000ff600000|100%| E|CS|TAMS 0x00000000ff500000| PB 0x00000000ff500000| Complete 
|1526|0x00000000ff600000, 0x00000000ff700000, 0x00000000ff700000|100%| E|CS|TAMS 0x00000000ff600000| PB 0x00000000ff600000| Complete 
|1527|0x00000000ff700000, 0x00000000ff800000, 0x00000000ff800000|100%| E|CS|TAMS 0x00000000ff700000| PB 0x00000000ff700000| Complete 
|1528|0x00000000ff800000, 0x00000000ff900000, 0x00000000ff900000|100%| E|CS|TAMS 0x00000000ff800000| PB 0x00000000ff800000| Complete 
|1529|0x00000000ff900000, 0x00000000ffa00000, 0x00000000ffa00000|100%| E|CS|TAMS 0x00000000ff900000| PB 0x00000000ff900000| Complete 
|1530|0x00000000ffa00000, 0x00000000ffb00000, 0x00000000ffb00000|100%| E|CS|TAMS 0x00000000ffa00000| PB 0x00000000ffa00000| Complete 
|1531|0x00000000ffb00000, 0x00000000ffc00000, 0x00000000ffc00000|100%| E|CS|TAMS 0x00000000ffb00000| PB 0x00000000ffb00000| Complete 
|1532|0x00000000ffc00000, 0x00000000ffd00000, 0x00000000ffd00000|100%| O|  |TAMS 0x00000000ffc00000| PB 0x00000000ffc00000| Untracked 
|1533|0x00000000ffd00000, 0x00000000ffe00000, 0x00000000ffe00000|100%| E|CS|TAMS 0x00000000ffd00000| PB 0x00000000ffd00000| Complete 
|1534|0x00000000ffe00000, 0x00000000fff00000, 0x00000000fff00000|100%| E|CS|TAMS 0x00000000ffe00000| PB 0x00000000ffe00000| Complete 
|1535|0x00000000fff00000, 0x0000000100000000, 0x0000000100000000|100%| E|CS|TAMS 0x00000000fff00000| PB 0x00000000fff00000| Complete 

Card table byte_map: [0x0000026b734a0000,0x0000026b737a0000] _byte_map_base: 0x0000026b72fa0000

Marking Bits: (CMBitMap*) 0x0000026b5f25cae0
 Bits: [0x0000026b737a0000, 0x0000026b74fa0000)

Polling page: 0x0000026b5f2e0000

Metaspace:

Usage:
  Non-class:    138.30 MB used.
      Class:     20.83 MB used.
       Both:    159.12 MB used.

Virtual space:
  Non-class space:      192.00 MB reserved,     140.06 MB ( 73%) committed,  3 nodes.
      Class space:      416.00 MB reserved,      22.50 MB (  5%) committed,  1 nodes.
             Both:      608.00 MB reserved,     162.56 MB ( 27%) committed. 

Chunk freelists:
   Non-Class:  3.09 MB
       Class:  9.35 MB
        Both:  12.44 MB

MaxMetaspaceSize: 512.00 MB
CompressedClassSpaceSize: 416.00 MB
Initial GC threshold: 21.00 MB
Current GC threshold: 266.50 MB
CDS: off
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 6.
num_arena_births: 6034.
num_arena_deaths: 0.
num_vsnodes_births: 4.
num_vsnodes_deaths: 0.
num_space_committed: 2598.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 6.
num_chunks_taken_from_freelist: 13835.
num_chunk_merges: 6.
num_chunk_splits: 8792.
num_chunks_enlarged: 5328.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=23316Kb max_used=23316Kb free=96683Kb
 bounds [0x0000026b6b650000, 0x0000026b6cd20000, 0x0000026b72b80000]
CodeHeap 'profiled nmethods': size=120000Kb used=43756Kb max_used=43756Kb free=76243Kb
 bounds [0x0000026b63b80000, 0x0000026b66640000, 0x0000026b6b0b0000]
CodeHeap 'non-nmethods': size=5760Kb used=3049Kb max_used=3109Kb free=2710Kb
 bounds [0x0000026b6b0b0000, 0x0000026b6b3d0000, 0x0000026b6b650000]
 total_blobs=23508 nmethods=22386 adapters=1024
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 2010.441 Thread 0x0000026b7789f4e0 nmethod 34841 0x0000026b6663b210 code [0x0000026b6663b3e0, 0x0000026b6663b7f0]
Event: 2010.441 Thread 0x0000026b7789f4e0 34842       3       org.gradle.api.internal.initialization.DefaultClassLoaderScope::<init> (45 bytes)
Event: 2010.443 Thread 0x0000026b7789f4e0 nmethod 34842 0x0000026b6663b990 code [0x0000026b6663bb80, 0x0000026b6663c198]
Event: 2010.443 Thread 0x0000026b7789f4e0 34843       3       java.lang.invoke.Invokers$Holder::linkToTargetMethod (19 bytes)
Event: 2010.444 Thread 0x0000026b7789f4e0 nmethod 34843 0x0000026b6663c390 code [0x0000026b6663c560, 0x0000026b6663c998]
Event: 2010.540 Thread 0x0000026b7789f4e0 34844       1       org.gradle.internal.cc.impl.services.DefaultBuildModelParameters::isIsolatedProjects (5 bytes)
Event: 2010.540 Thread 0x0000026b7789f4e0 nmethod 34844 0x0000026b6cd11710 code [0x0000026b6cd118a0, 0x0000026b6cd11970]
Event: 2010.742 Thread 0x0000026b778b9910 34845       4       com.google.common.cache.LocalCache$Segment::<init> (163 bytes)
Event: 2010.773 Thread 0x0000026b778b9910 nmethod 34845 0x0000026b6cd11a10 code [0x0000026b6cd11ca0, 0x0000026b6cd13378]
Event: 2010.846 Thread 0x0000026b778b9910 34846       4       com.google.common.cache.LocalCache::createSegment (13 bytes)
Event: 2010.847 Thread 0x0000026b778b9910 nmethod 34846 0x0000026b6cd13a90 code [0x0000026b6cd13c20, 0x0000026b6cd13d80]
Event: 2010.859 Thread 0x0000026b7789f4e0 34847       1       org.gradle.plugin.management.internal.DefaultPluginRequest::isApply (5 bytes)
Event: 2010.859 Thread 0x0000026b7789f4e0 nmethod 34847 0x0000026b6cd13e90 code [0x0000026b6cd14020, 0x0000026b6cd140f0]
Event: 2010.918 Thread 0x0000026b7789f4e0 34848       3       org.gradle.internal.component.model.DefaultMultipleCandidateResult::closestMatch (93 bytes)
Event: 2010.922 Thread 0x0000026b7789f4e0 nmethod 34848 0x0000026b6663ca90 code [0x0000026b6663cd40, 0x0000026b6663d978]
Event: 2010.937 Thread 0x0000026b778b9910 34849       4       org.gradle.internal.component.model.MutableModuleSources::add (34 bytes)
Event: 2010.942 Thread 0x0000026b778b9910 nmethod 34849 0x0000026b6cd14190 code [0x0000026b6cd14360, 0x0000026b6cd14868]
Event: 2010.942 Thread 0x0000026b778b9910 34850       4       org.gradle.internal.component.model.MutableModuleSources::maybeCreateStore (20 bytes)
Event: 2010.944 Thread 0x0000026b778b9910 nmethod 34850 0x0000026b6cd14b10 code [0x0000026b6cd14ca0, 0x0000026b6cd14f80]
Event: 2011.026 Thread 0x0000026b778b9910 34851       4       org.gradle.api.internal.artifacts.ivyservice.modulecache.PersistentModuleMetadataCache$$Lambda/0x000000010084cca0::get (16 bytes)

GC Heap History (20 events):
Event: 383.752 GC heap before
{Heap before GC invocations=121 (full 0):
 garbage-first heap   total 593920K, used 458240K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 127 young (130048K), 8 survivors (8192K)
 Metaspace       used 151399K, committed 154560K, reserved 622592K
  class space    used 19972K, committed 21504K, reserved 425984K
}
Event: 383.773 GC heap after
{Heap after GC invocations=122 (full 0):
 garbage-first heap   total 593920K, used 347089K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 16 young (16384K), 16 survivors (16384K)
 Metaspace       used 151399K, committed 154560K, reserved 622592K
  class space    used 19972K, committed 21504K, reserved 425984K
}
Event: 385.222 GC heap before
{Heap before GC invocations=122 (full 0):
 garbage-first heap   total 593920K, used 423889K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 93 young (95232K), 16 survivors (16384K)
 Metaspace       used 152643K, committed 155712K, reserved 622592K
  class space    used 20098K, committed 21568K, reserved 425984K
}
Event: 385.227 GC heap after
{Heap after GC invocations=123 (full 0):
 garbage-first heap   total 593920K, used 335872K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 3 young (3072K), 3 survivors (3072K)
 Metaspace       used 152643K, committed 155712K, reserved 622592K
  class space    used 20098K, committed 21568K, reserved 425984K
}
Event: 388.256 GC heap before
{Heap before GC invocations=124 (full 0):
 garbage-first heap   total 593920K, used 481280K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 149 young (152576K), 3 survivors (3072K)
 Metaspace       used 153039K, committed 156096K, reserved 622592K
  class space    used 20129K, committed 21632K, reserved 425984K
}
Event: 388.266 GC heap after
{Heap after GC invocations=125 (full 0):
 garbage-first heap   total 593920K, used 337408K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 10 young (10240K), 10 survivors (10240K)
 Metaspace       used 153039K, committed 156096K, reserved 622592K
  class space    used 20129K, committed 21632K, reserved 425984K
}
Event: 393.711 GC heap before
{Heap before GC invocations=125 (full 0):
 garbage-first heap   total 593920K, used 506368K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 176 young (180224K), 10 survivors (10240K)
 Metaspace       used 154273K, committed 157376K, reserved 622592K
  class space    used 20264K, committed 21824K, reserved 425984K
}
Event: 393.734 GC heap after
{Heap after GC invocations=126 (full 0):
 garbage-first heap   total 593920K, used 311181K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 19 young (19456K), 19 survivors (19456K)
 Metaspace       used 154273K, committed 157376K, reserved 622592K
  class space    used 20264K, committed 21824K, reserved 425984K
}
Event: 395.954 GC heap before
{Heap before GC invocations=126 (full 0):
 garbage-first heap   total 593920K, used 501645K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 206 young (210944K), 19 survivors (19456K)
 Metaspace       used 154418K, committed 157504K, reserved 622592K
  class space    used 20272K, committed 21824K, reserved 425984K
}
Event: 395.974 GC heap after
{Heap after GC invocations=127 (full 0):
 garbage-first heap   total 593920K, used 316416K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 17 young (17408K), 17 survivors (17408K)
 Metaspace       used 154418K, committed 157504K, reserved 622592K
  class space    used 20272K, committed 21824K, reserved 425984K
}
Event: 457.952 GC heap before
{Heap before GC invocations=128 (full 0):
 garbage-first heap   total 735232K, used 734208K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 212 young (217088K), 17 survivors (17408K)
 Metaspace       used 159121K, committed 162304K, reserved 622592K
  class space    used 20904K, committed 22464K, reserved 425984K
}
Event: 457.978 GC heap after
{Heap after GC invocations=129 (full 0):
 garbage-first heap   total 759808K, used 317440K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 14 young (14336K), 14 survivors (14336K)
 Metaspace       used 159121K, committed 162304K, reserved 622592K
  class space    used 20904K, committed 22464K, reserved 425984K
}
Event: 940.089 GC heap before
{Heap before GC invocations=129 (full 0):
 garbage-first heap   total 759808K, used 620544K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 310 young (317440K), 14 survivors (14336K)
 Metaspace       used 159823K, committed 163072K, reserved 622592K
  class space    used 20968K, committed 22528K, reserved 425984K
}
Event: 940.139 GC heap after
{Heap after GC invocations=130 (full 0):
 garbage-first heap   total 759808K, used 340480K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 39 young (39936K), 39 survivors (39936K)
 Metaspace       used 159823K, committed 163072K, reserved 622592K
  class space    used 20968K, committed 22528K, reserved 425984K
}
Event: 946.547 GC heap before
{Heap before GC invocations=130 (full 0):
 garbage-first heap   total 759808K, used 539136K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 234 young (239616K), 39 survivors (39936K)
 Metaspace       used 160362K, committed 163648K, reserved 622592K
  class space    used 21046K, committed 22656K, reserved 425984K
}
Event: 946.595 GC heap after
{Heap after GC invocations=131 (full 0):
 garbage-first heap   total 759808K, used 358912K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 19 young (19456K), 19 survivors (19456K)
 Metaspace       used 160362K, committed 163648K, reserved 622592K
  class space    used 21046K, committed 22656K, reserved 425984K
}
Event: 954.898 GC heap before
{Heap before GC invocations=132 (full 0):
 garbage-first heap   total 759808K, used 665088K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 316 young (323584K), 19 survivors (19456K)
 Metaspace       used 161841K, committed 165312K, reserved 622592K
  class space    used 21231K, committed 22912K, reserved 425984K
}
Event: 954.942 GC heap after
{Heap after GC invocations=133 (full 0):
 garbage-first heap   total 759808K, used 385024K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 40 young (40960K), 40 survivors (40960K)
 Metaspace       used 161841K, committed 165312K, reserved 622592K
  class space    used 21231K, committed 22912K, reserved 425984K
}
Event: 961.575 GC heap before
{Heap before GC invocations=133 (full 0):
 garbage-first heap   total 759808K, used 668672K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 316 young (323584K), 40 survivors (40960K)
 Metaspace       used 162681K, committed 166208K, reserved 622592K
  class space    used 21313K, committed 23040K, reserved 425984K
}
Event: 961.644 GC heap after
{Heap after GC invocations=134 (full 0):
 garbage-first heap   total 759808K, used 412160K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 37 young (37888K), 37 survivors (37888K)
 Metaspace       used 162681K, committed 166208K, reserved 622592K
  class space    used 21313K, committed 23040K, reserved 425984K
}

Dll operation events (3 events):
Event: 0.026 Loaded shared library C:\Program Files\Android\Android Studio\jbr\bin\java.dll
Event: 0.042 Loaded shared library C:\Program Files\Android\Android Studio\jbr\bin\zip.dll
Event: 1.323 Loaded shared library C:\Program Files\Android\Android Studio\jbr\bin\verify.dll

Deoptimization events (20 events):
Event: 961.909 Thread 0x0000026b021c2750 Uncommon trap: trap_request=0xffffffd6 fr.pc=0x0000026b6ccb1b6c relative=0x0000000000000ccc
Event: 961.909 Thread 0x0000026b021c2750 Uncommon trap: reason=array_check action=maybe_recompile pc=0x0000026b6ccb1b6c method=org.codehaus.groovy.runtime.callsite.AbstractCallSite.createPogoGetPropertySite(Ljava/lang/Class;)Lorg/codehaus/groovy/runtime/callsite/CallSite; @ 22 c2
Event: 961.909 Thread 0x0000026b021c2750 DEOPT PACKING pc=0x0000026b6ccb1b6c sp=0x000000f9e90f81b0
Event: 961.909 Thread 0x0000026b021c2750 DEOPT UNPACKING pc=0x0000026b6b1046a2 sp=0x000000f9e90f8050 mode 2
Event: 961.910 Thread 0x0000026b021c2750 Uncommon trap: trap_request=0xffffffd6 fr.pc=0x0000026b6ccb1b6c relative=0x0000000000000ccc
Event: 961.910 Thread 0x0000026b021c2750 Uncommon trap: reason=array_check action=maybe_recompile pc=0x0000026b6ccb1b6c method=org.codehaus.groovy.runtime.callsite.AbstractCallSite.createPogoGetPropertySite(Ljava/lang/Class;)Lorg/codehaus/groovy/runtime/callsite/CallSite; @ 22 c2
Event: 961.910 Thread 0x0000026b021c2750 DEOPT PACKING pc=0x0000026b6ccb1b6c sp=0x000000f9e90f81b0
Event: 961.910 Thread 0x0000026b021c2750 DEOPT UNPACKING pc=0x0000026b6b1046a2 sp=0x000000f9e90f8050 mode 2
Event: 961.910 Thread 0x0000026b021c2750 Uncommon trap: trap_request=0xffffffd6 fr.pc=0x0000026b6ccb1b6c relative=0x0000000000000ccc
Event: 961.910 Thread 0x0000026b021c2750 Uncommon trap: reason=array_check action=maybe_recompile pc=0x0000026b6ccb1b6c method=org.codehaus.groovy.runtime.callsite.AbstractCallSite.createPogoGetPropertySite(Ljava/lang/Class;)Lorg/codehaus/groovy/runtime/callsite/CallSite; @ 22 c2
Event: 961.910 Thread 0x0000026b021c2750 DEOPT PACKING pc=0x0000026b6ccb1b6c sp=0x000000f9e90f81b0
Event: 961.910 Thread 0x0000026b021c2750 DEOPT UNPACKING pc=0x0000026b6b1046a2 sp=0x000000f9e90f8050 mode 2
Event: 962.391 Thread 0x0000026b021c2750 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000026b6b68d5c0 relative=0x00000000000001c0
Event: 962.392 Thread 0x0000026b021c2750 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000026b6b68d5c0 method=org.gradle.internal.ImmutableActionSet.add(Lorg/gradle/api/Action;)Lorg/gradle/internal/ImmutableActionSet; @ 62 c2
Event: 962.392 Thread 0x0000026b021c2750 DEOPT PACKING pc=0x0000026b6b68d5c0 sp=0x000000f9e90fa4e0
Event: 962.392 Thread 0x0000026b021c2750 DEOPT UNPACKING pc=0x0000026b6b1046a2 sp=0x000000f9e90fa408 mode 2
Event: 962.604 Thread 0x0000026b021c2750 Uncommon trap: trap_request=0xffffffc6 fr.pc=0x0000026b6c297f4c relative=0x000000000000114c
Event: 962.604 Thread 0x0000026b021c2750 Uncommon trap: reason=bimorphic_or_optimized_type_check action=maybe_recompile pc=0x0000026b6c297f4c method=java.util.HashMap.removeNode(ILjava/lang/Object;Ljava/lang/Object;ZZ)Ljava/util/HashMap$Node; @ 143 c2
Event: 962.604 Thread 0x0000026b021c2750 DEOPT PACKING pc=0x0000026b6c297f4c sp=0x000000f9e90fdb40
Event: 962.604 Thread 0x0000026b021c2750 DEOPT UNPACKING pc=0x0000026b6b1046a2 sp=0x000000f9e90fda00 mode 2

Classes loaded (20 events):
Event: 456.483 Loading class javax/management/NotCompliantMBeanException
Event: 456.483 Loading class javax/management/NotCompliantMBeanException done
Event: 456.483 Loading class javax/management/AttributeNotFoundException
Event: 456.483 Loading class javax/management/AttributeNotFoundException done
Event: 456.483 Loading class javax/management/ReflectionException
Event: 456.483 Loading class javax/management/ReflectionException done
Event: 456.483 Loading class javax/management/InstanceNotFoundException
Event: 456.483 Loading class javax/management/InstanceNotFoundException done
Event: 456.520 Loading class java/nio/channels/CancelledKeyException
Event: 456.520 Loading class java/nio/channels/CancelledKeyException done
Event: 456.538 Loading class java/lang/ProcessBuilder$NullInputStream
Event: 456.539 Loading class java/lang/ProcessBuilder$NullInputStream done
Event: 456.623 Loading class java/nio/channels/AsynchronousCloseException
Event: 456.624 Loading class java/nio/channels/AsynchronousCloseException done
Event: 456.626 Loading class sun/nio/ch/SocketChannelImpl$DefaultOptionsHolder
Event: 456.627 Loading class sun/nio/ch/SocketChannelImpl$DefaultOptionsHolder done
Event: 457.627 Loading class sun/net/ConnectionResetException
Event: 457.627 Loading class sun/net/ConnectionResetException done
Event: 941.424 Loading class java/util/regex/Pattern$BackRef
Event: 941.424 Loading class java/util/regex/Pattern$BackRef done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 962.570 Thread 0x0000026b09bfc830 Exception <a 'sun/nio/fs/WindowsException'{0x00000000c6db4870}> (0x00000000c6db4870) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 962.780 Thread 0x0000026b0a2be8a0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000c5757a78}> (0x00000000c5757a78) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 962.782 Thread 0x0000026b0a2be8a0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000c5781f60}> (0x00000000c5781f60) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 962.782 Thread 0x0000026b0a2be8a0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000c5782a40}> (0x00000000c5782a40) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 962.782 Thread 0x0000026b0a2be8a0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000c5783568}> (0x00000000c5783568) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 962.783 Thread 0x0000026b0a2be8a0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000c5795ba0}> (0x00000000c5795ba0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 962.783 Thread 0x0000026b0a2be8a0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000c579d9c0}> (0x00000000c579d9c0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 962.783 Thread 0x0000026b0a2be8a0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000c57a5428}> (0x00000000c57a5428) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 962.783 Thread 0x0000026b0a2be8a0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000c57a8e20}> (0x00000000c57a8e20) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 2007.143 Thread 0x0000026b0aaaabf0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000c378b910}> (0x00000000c378b910) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 2007.144 Thread 0x0000026b0aaaabf0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000c378ccc8}> (0x00000000c378ccc8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 2007.145 Thread 0x0000026b0aaaabf0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000c378e070}> (0x00000000c378e070) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 2007.146 Thread 0x0000026b0aaaabf0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000c378f660}> (0x00000000c378f660) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 2007.968 Thread 0x0000026b0aaaabf0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000c368b628}> (0x00000000c368b628) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 2007.968 Thread 0x0000026b0aaaabf0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000c368cf40}> (0x00000000c368cf40) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 2007.968 Thread 0x0000026b0aaaabf0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000c368e848}> (0x00000000c368e848) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 2007.969 Thread 0x0000026b0aaaabf0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000c3690150}> (0x00000000c3690150) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 2010.270 Thread 0x0000026b0aaaabf0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000c1d52d58}> (0x00000000c1d52d58) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 2010.276 Thread 0x0000026b0aaaabf0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000c1d54848}> (0x00000000c1d54848) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 2010.365 Thread 0x0000026b0aaaabf0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000c1d56f40}> (0x00000000c1d56f40) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 1335.870 Executing VM operation: Cleanup
Event: 1335.870 Executing VM operation: Cleanup done
Event: 1385.257 Executing VM operation: Cleanup
Event: 1385.257 Executing VM operation: Cleanup done
Event: 1405.454 Executing VM operation: Cleanup
Event: 1405.454 Executing VM operation: Cleanup done
Event: 1512.352 Executing VM operation: Cleanup
Event: 1512.352 Executing VM operation: Cleanup done
Event: 1697.010 Executing VM operation: Cleanup
Event: 1697.010 Executing VM operation: Cleanup done
Event: 2006.539 Executing VM operation: Cleanup
Event: 2006.571 Executing VM operation: Cleanup done
Event: 2007.582 Executing VM operation: Cleanup
Event: 2007.660 Executing VM operation: Cleanup done
Event: 2008.665 Executing VM operation: Cleanup
Event: 2008.792 Executing VM operation: Cleanup done
Event: 2009.801 Executing VM operation: Cleanup
Event: 2009.902 Executing VM operation: Cleanup done
Event: 2010.909 Executing VM operation: Cleanup
Event: 2010.909 Executing VM operation: Cleanup done

Memory protections (0 events):
No events

Nmethod flushes (20 events):
Event: 947.152 Thread 0x0000026b775d5770 flushing  nmethod 0x0000026b65aaa090
Event: 947.152 Thread 0x0000026b775d5770 flushing  nmethod 0x0000026b65aee690
Event: 947.152 Thread 0x0000026b775d5770 flushing  nmethod 0x0000026b65af3910
Event: 947.152 Thread 0x0000026b775d5770 flushing  nmethod 0x0000026b65af3d90
Event: 947.152 Thread 0x0000026b775d5770 flushing  nmethod 0x0000026b65b20090
Event: 947.152 Thread 0x0000026b775d5770 flushing  nmethod 0x0000026b65d41690
Event: 947.152 Thread 0x0000026b775d5770 flushing  nmethod 0x0000026b65e70210
Event: 947.152 Thread 0x0000026b775d5770 flushing  nmethod 0x0000026b6605c890
Event: 947.152 Thread 0x0000026b775d5770 flushing  nmethod 0x0000026b6605d910
Event: 947.152 Thread 0x0000026b775d5770 flushing  nmethod 0x0000026b6605e110
Event: 947.152 Thread 0x0000026b775d5770 flushing  nmethod 0x0000026b6605ff10
Event: 947.152 Thread 0x0000026b775d5770 flushing  nmethod 0x0000026b66061590
Event: 947.152 Thread 0x0000026b775d5770 flushing  nmethod 0x0000026b66061a90
Event: 947.152 Thread 0x0000026b775d5770 flushing  nmethod 0x0000026b66061e90
Event: 947.152 Thread 0x0000026b775d5770 flushing  nmethod 0x0000026b66062690
Event: 947.152 Thread 0x0000026b775d5770 flushing  nmethod 0x0000026b66062a10
Event: 947.152 Thread 0x0000026b775d5770 flushing  nmethod 0x0000026b66097990
Event: 947.152 Thread 0x0000026b775d5770 flushing  nmethod 0x0000026b66098d90
Event: 947.152 Thread 0x0000026b775d5770 flushing  nmethod 0x0000026b66099510
Event: 947.152 Thread 0x0000026b775d5770 flushing  nmethod 0x0000026b66099910

Events (20 events):
Event: 2005.397 Thread 0x0000026b7d8cd9a0 Thread added: 0x0000026b09bfcec0
Event: 2005.492 Thread 0x0000026b09bfcec0 Thread added: 0x0000026b0aaabfa0
Event: 2005.686 Thread 0x0000026b09bfcec0 Thread added: 0x0000026b0aaa8b20
Event: 2005.710 Thread 0x0000026b09bfcec0 Thread added: 0x0000026b0aaaabf0
Event: 2005.921 Thread 0x0000026b0aaaabf0 Thread added: 0x0000026b07e88c90
Event: 2005.948 Thread 0x0000026b0aaaabf0 Thread added: 0x0000026b07e87f70
Event: 2005.950 Thread 0x0000026b0aaaabf0 Thread added: 0x0000026b07e8d4c0
Event: 2006.423 Thread 0x0000026b0aaaabf0 Thread added: 0x0000026b07e8f590
Event: 2006.464 Thread 0x0000026b0aaaabf0 Thread added: 0x0000026b07e8c7a0
Event: 2007.190 Thread 0x0000026b0aaaabf0 Thread added: 0x0000026b07e89320
Event: 2007.541 Thread 0x0000026b0aaaabf0 Thread added: 0x0000026b07e8b3f0
Event: 2007.548 Thread 0x0000026b0aaaabf0 Thread added: 0x0000026b07e8e1e0
Event: 2007.684 Thread 0x0000026b0aaaabf0 Thread added: 0x0000026b0a01ae60
Event: 2009.336 Thread 0x0000026b0aaaabf0 Thread added: 0x0000026b0a01b4f0
Event: 2009.336 Thread 0x0000026b0aaaabf0 Thread added: 0x0000026b0a01a140
Event: 2009.425 Thread 0x0000026b0aaaabf0 Thread added: 0x0000026b07e8a040
Event: 2009.425 Thread 0x0000026b0aaaabf0 Thread added: 0x0000026b07e8a6d0
Event: 2011.213 Thread 0x0000026b0aaaabf0 Thread added: 0x0000026b0a01a7d0
Event: 2011.213 Thread 0x0000026b0aaaabf0 Thread added: 0x0000026b0a019ab0
Event: 2011.215 Thread 0x0000026b0aaaabf0 Thread added: 0x0000026b0a01bb80


Dynamic libraries:
0x00007ff7b0e10000 - 0x00007ff7b0e1a000 	C:\Program Files\Android\Android Studio\jbr\bin\java.exe
0x00007ffef0c10000 - 0x00007ffef0e08000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ffef09c0000 - 0x00007ffef0a82000 	C:\Windows\System32\KERNEL32.DLL
0x00007ffeee760000 - 0x00007ffeeea56000 	C:\Windows\System32\KERNELBASE.dll
0x00007ffeee320000 - 0x00007ffeee420000 	C:\Windows\System32\ucrtbase.dll
0x00007ffecc820000 - 0x00007ffecc838000 	C:\Program Files\Android\Android Studio\jbr\bin\jli.dll
0x00007ffeef0b0000 - 0x00007ffeef24d000 	C:\Windows\System32\USER32.dll
0x00007ffeee580000 - 0x00007ffeee5a2000 	C:\Windows\System32\win32u.dll
0x00007ffef0930000 - 0x00007ffef095b000 	C:\Windows\System32\GDI32.dll
0x00007ffeee5b0000 - 0x00007ffeee6c9000 	C:\Windows\System32\gdi32full.dll
0x00007ffeeea90000 - 0x00007ffeeeb2d000 	C:\Windows\System32\msvcp_win.dll
0x00007ffeca2b0000 - 0x00007ffeca2cb000 	C:\Program Files\Android\Android Studio\jbr\bin\VCRUNTIME140.dll
0x00007ffee0760000 - 0x00007ffee09fa000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7\COMCTL32.dll
0x00007ffeef5f0000 - 0x00007ffeef68e000 	C:\Windows\System32\msvcrt.dll
0x00007ffeeedc0000 - 0x00007ffeeedef000 	C:\Windows\System32\IMM32.DLL
0x00007ffede140000 - 0x00007ffede14c000 	C:\Program Files\Android\Android Studio\jbr\bin\vcruntime140_1.dll
0x00007ffec0510000 - 0x00007ffec059d000 	C:\Program Files\Android\Android Studio\jbr\bin\msvcp140.dll
0x00007ffea3830000 - 0x00007ffea44ba000 	C:\Program Files\Android\Android Studio\jbr\bin\server\jvm.dll
0x00007ffeef530000 - 0x00007ffeef5e1000 	C:\Windows\System32\ADVAPI32.dll
0x00007ffeef010000 - 0x00007ffeef0af000 	C:\Windows\System32\sechost.dll
0x00007ffeeeee0000 - 0x00007ffeef003000 	C:\Windows\System32\RPCRT4.dll
0x00007ffeeea60000 - 0x00007ffeeea87000 	C:\Windows\System32\bcrypt.dll
0x00007ffeef4c0000 - 0x00007ffeef52b000 	C:\Windows\System32\WS2_32.dll
0x00007ffeee100000 - 0x00007ffeee14b000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ffee5700000 - 0x00007ffee570a000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ffee2be0000 - 0x00007ffee2c07000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ffeee0e0000 - 0x00007ffeee0f2000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ffeec100000 - 0x00007ffeec112000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ffedd730000 - 0x00007ffedd73a000 	C:\Program Files\Android\Android Studio\jbr\bin\jimage.dll
0x00007ffedfc10000 - 0x00007ffedfe11000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ffec4fb0000 - 0x00007ffec4fe4000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ffeee6d0000 - 0x00007ffeee752000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ffebf790000 - 0x00007ffebf79e000 	C:\Program Files\Android\Android Studio\jbr\bin\instrument.dll
0x00007ffeca290000 - 0x00007ffeca2b0000 	C:\Program Files\Android\Android Studio\jbr\bin\java.dll
0x00007ffeca210000 - 0x00007ffeca228000 	C:\Program Files\Android\Android Studio\jbr\bin\zip.dll
0x00007ffef00d0000 - 0x00007ffef083e000 	C:\Windows\System32\SHELL32.dll
0x00007ffeec300000 - 0x00007ffeecaa4000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ffeefd70000 - 0x00007ffef00c3000 	C:\Windows\System32\combase.dll
0x00007ffeedb20000 - 0x00007ffeedb4b000 	C:\Windows\SYSTEM32\Wldp.dll
0x00007ffeeedf0000 - 0x00007ffeeeebd000 	C:\Windows\System32\OLEAUT32.dll
0x00007ffeeec30000 - 0x00007ffeeecdd000 	C:\Windows\System32\SHCORE.dll
0x00007ffeeece0000 - 0x00007ffeeed3b000 	C:\Windows\System32\shlwapi.dll
0x00007ffeee1d0000 - 0x00007ffeee1f4000 	C:\Windows\SYSTEM32\profapi.dll
0x00007ffedbb90000 - 0x00007ffedbba0000 	C:\Program Files\Android\Android Studio\jbr\bin\net.dll
0x00007ffee77d0000 - 0x00007ffee78da000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007ffeed900000 - 0x00007ffeed96a000 	C:\Windows\system32\mswsock.dll
0x00007ffec9a30000 - 0x00007ffec9a46000 	C:\Program Files\Android\Android Studio\jbr\bin\nio.dll
0x00007ffedb850000 - 0x00007ffedb860000 	C:\Program Files\Android\Android Studio\jbr\bin\verify.dll
0x00007ffeb9400000 - 0x00007ffeb9427000 	C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64\native-platform.dll
0x00007ffeb65c0000 - 0x00007ffeb6638000 	C:\Users\<USER>\.gradle\native\0.2.7\x86_64-windows-gnu\gradle-fileevents.dll
0x00007ffed0d90000 - 0x00007ffed0d99000 	C:\Program Files\Android\Android Studio\jbr\bin\management.dll
0x00007ffecd120000 - 0x00007ffecd12b000 	C:\Program Files\Android\Android Studio\jbr\bin\management_ext.dll
0x00007ffef0920000 - 0x00007ffef0928000 	C:\Windows\System32\PSAPI.DLL
0x00007ffeed600000 - 0x00007ffeed63b000 	C:\Windows\SYSTEM32\IPHLPAPI.DLL
0x00007ffeef8e0000 - 0x00007ffeef8e8000 	C:\Windows\System32\NSI.dll
0x00007ffecd050000 - 0x00007ffecd059000 	C:\Program Files\Android\Android Studio\jbr\bin\extnet.dll
0x00007ffeedeb0000 - 0x00007ffeedec8000 	C:\Windows\SYSTEM32\CRYPTSP.dll
0x00007ffeed240000 - 0x00007ffeed278000 	C:\Windows\system32\rsaenh.dll
0x00007ffeee150000 - 0x00007ffeee17e000 	C:\Windows\SYSTEM32\USERENV.dll
0x00007ffeeda90000 - 0x00007ffeeda9c000 	C:\Windows\SYSTEM32\CRYPTBASE.dll
0x00007ffe7dea0000 - 0x00007ffe7dea7000 	C:\Windows\system32\wshunix.dll
0x00007ffeed490000 - 0x00007ffeed4c3000 	C:\Windows\SYSTEM32\ntmarta.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Android\Android Studio\jbr\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7;C:\Program Files\Android\Android Studio\jbr\bin\server;C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64;C:\Users\<USER>\.gradle\native\0.2.7\x86_64-windows-gnu

VM Arguments:
jvm_args: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -XX:MaxMetaspaceSize=512m -XX:+HeapDumpOnOutOfMemoryError -Xmx1536m -Dfile.encoding=UTF-8 -Duser.country=BR -Duser.language=pt -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.14.1-bin\baw1sv0jfoi8rxs14qo3h49cs\gradle-8.14.1\lib\agents\gradle-instrumentation-agent-8.14.1.jar 
java_command: org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.14.1
java_class_path (initial): C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.14.1-bin\baw1sv0jfoi8rxs14qo3h49cs\gradle-8.14.1\lib\gradle-daemon-main-8.14.1.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 3                                         {product} {ergonomic}
   size_t CompressedClassSpaceSize                 = 436207616                                 {product} {ergonomic}
     uint ConcGCThreads                            = 1                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 4                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 1048576                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
     bool HeapDumpOnOutOfMemoryError               = true                                   {manageable} {command line}
   size_t InitialHeapSize                          = 134217728                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 1610612736                                {product} {command line}
   size_t MaxMetaspaceSize                         = 536870912                                 {product} {command line}
   size_t MaxNewSize                               = 965738496                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 1048576                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5832780                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122912730                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122912730                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 1610612736                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=C:\Program Files\Java\jdk-11.0.10
PATH=c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\Microsoft SQL Server\130\Tools\Binn\;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn\;C:\Program Files\dotnet\;C:\Program Files (x86)\Microsoft SQL Server\150\DTS\Binn\;C:\Program Files\Azure Data Studio\bin;C:\Program Files\Git\cmd;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\110\Tools\Binn\;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\130\Tools\Binn\;C:\Program Files (x86)\Microsoft SQL Server\140\Tools\Binn\;C:\Program Files\Microsoft SQL Server\140\Tools\Binn\;C:\Program Files\Microsoft SQL Server\140\DTS\Binn\;C:\Program Files\Microsoft\Web Platform Installer\;C:\Program Files (x86)\dotnet\;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;C:\Program Files\Calibre2\;C:\Program Files\Java\jdk-11\bin;C:\Prog;C:\Program Files\Docker\Docker\resources\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\.local\share\solana\install\active_release\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\;C:\ProgramData\DockerDesktop\version-bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\Users\ADM\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\.dotnet\tools;C:\MinGW\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin
USERNAME=ADM
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 58 Stepping 9, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

JNI global refs:
JNI global refs: 27, weak refs: 1

JNI global refs memory usage: 835, weak refs: 833

Process memory usage:
Resident Set Size: 202492K (2% of 8357708K total physical memory with 363344K free physical memory)

OOME stack traces (most recent first):
Classloader memory used:
Loader org.gradle.internal.classloader.VisitableURLClassLoader$InstrumentingVisitableURLClassLoader: 55902K
Loader org.gradle.internal.classloader.VisitableURLClassLoader                         : 52399K
Loader bootstrap                                                                       : 33902K
Loader org.gradle.initialization.MixInLegacyTypesClassLoader                           : 15541K
Loader org.gradle.groovy.scripts.internal.DefaultScriptCompilationHandler$ScriptClassLoader: 3272K
Loader jdk.internal.loader.ClassLoaders$PlatformClassLoader                            : 565K
Loader jdk.internal.reflect.DelegatingClassLoader                                      : 459K
Loader org.gradle.internal.classloader.VisitableURLClassLoader                         : 341K
Loader org.codehaus.groovy.runtime.callsite.CallSiteClassLoader                        : 320K
Loader jdk.internal.loader.ClassLoaders$AppClassLoader                                 : 235K
Loader sun.reflect.misc.MethodUtil                                                     : 2952B

Classes loaded by more than one classloader:
Class Program                                                                         : loaded 12 times (x 68B)
Class Build_gradle                                                                    : loaded 10 times (x 126B)
Class Build_gradle$1                                                                  : loaded 10 times (x 74B)
Class org.gradle.kotlin.dsl.VersionCatalogAccessorsKt                                 : loaded 5 times (x 67B)
Class org.gradle.kotlin.dsl.ImplementationConfigurationAccessorsKt                    : loaded 4 times (x 67B)
Class org.gradle.kotlin.dsl.Accessors9osmdt78klrrxidc9srcw3tsqKt                      : loaded 4 times (x 67B)
Class org.gradle.kotlin.dsl.Accessors96b3ii45gitqpy1kb3tvcvtxvKt                      : loaded 4 times (x 67B)
Class Build_gradle$3                                                                  : loaded 4 times (x 70B)
Class Build_gradle$6                                                                  : loaded 4 times (x 70B)
Class Build_gradle$5                                                                  : loaded 4 times (x 70B)
Class Build_gradle$4                                                                  : loaded 4 times (x 70B)
Class Build_gradle$2                                                                  : loaded 4 times (x 75B)
Class build_eupcmxgtu9q3uy0v8rg9z8i7j$_run_closure1                                   : loaded 3 times (x 136B)
Class build_9kn57pj55amg75ewdgalkhlsr$_run_closure1                                   : loaded 3 times (x 136B)
Class org.gradle.kotlin.dsl.TestImplementationConfigurationAccessorsKt                : loaded 3 times (x 67B)
Class build_eupcmxgtu9q3uy0v8rg9z8i7j                                                 : loaded 3 times (x 187B)
Class build_6k1vuntnni446s9ebkvsvp2hk$_run_closure1                                   : loaded 3 times (x 135B)
Class build_1st6b4zcxn0lddyz8z0am5m89                                                 : loaded 3 times (x 176B)
Class build_9kn57pj55amg75ewdgalkhlsr                                                 : loaded 3 times (x 184B)
Class build_agr3p375fzty99zrloowe6ju6$_run_closure1$_closure4                         : loaded 3 times (x 135B)
Class build_6s7imafvclc6gm72hnzimmguy$_run_closure1                                   : loaded 3 times (x 136B)
Class build_evl13vpxjidvxohlw1uueqdij$_run_closure2                                   : loaded 3 times (x 136B)
Class build_evl13vpxjidvxohlw1uueqdij$_run_closure1                                   : loaded 3 times (x 136B)
Class build_evl13vpxjidvxohlw1uueqdij                                                 : loaded 3 times (x 176B)
Class build_agisdczp7kn8uyp03dp4zx2vi$_run_closure1                                   : loaded 3 times (x 135B)
Class build_agisdczp7kn8uyp03dp4zx2vi                                                 : loaded 3 times (x 180B)
Class build_6k1vuntnni446s9ebkvsvp2hk                                                 : loaded 3 times (x 179B)
Class build_1st6b4zcxn0lddyz8z0am5m89$_run_closure2                                   : loaded 3 times (x 136B)
Class build_agr3p375fzty99zrloowe6ju6$_run_closure1                                   : loaded 3 times (x 135B)
Class build_agr3p375fzty99zrloowe6ju6                                                 : loaded 3 times (x 177B)
Class build_6s7imafvclc6gm72hnzimmguy                                                 : loaded 3 times (x 181B)
Class build_86psz0zg5ccmkipscnjtptl6f$_run_closure1                                   : loaded 3 times (x 135B)
Class build_86psz0zg5ccmkipscnjtptl6f                                                 : loaded 3 times (x 176B)
Class org.gradle.internal.classloader.ClassLoaderHierarchy                            : loaded 2 times (x 66B)
Class build_9kn57pj55amg75ewdgalkhlsr$_shouldUseCommonInterfaceFromRNSVG_closure7     : loaded 2 times (x 135B)
Class build_evl13vpxjidvxohlw1uueqdij$_run_closure2$_closure7                         : loaded 2 times (x 135B)
Class org.jetbrains.kotlin.gradle.plugin.hierarchy.KotlinSourceSetTreeClassifierKt    : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.report.UsesBuildMetricsService                      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory$KotlinCompilationSourceSetsContainerFactory: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.utils.ConfigurationCacheOpaqueValueSource           : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.utils.MutableObservableSet                          : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinHierarchyDsl                              : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.targets.metadata.KotlinMetadataTargetConfiguratorKt$isNativeSourceSet$2: loaded 2 times (x 91B)
Class org.jetbrains.kotlin.gradle.targets.native.KonanPropertiesBuildService$Companion$registerIfAbsent$2$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.report.BuildReportType                              : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.statistics.metrics.StringAnonymizationPolicy$AllowedListAnonymizer: loaded 2 times (x 75B)
Class [Lcom.google.common.cache.RemovalCause;                                         : loaded 2 times (x 65B)
Class com.google.common.collect.AbstractMapBasedMultimap$WrappedCollection            : loaded 2 times (x 120B)
Class com.google.common.collect.ImmutableEnumSet                                      : loaded 2 times (x 142B)
Class com.google.common.util.concurrent.ExecutionError                                : loaded 2 times (x 78B)
Class com.google.common.collect.ImmutableList$1                                       : loaded 2 times (x 93B)
Class org.gradle.api.internal.classpath.ManifestUtil                                  : loaded 2 times (x 67B)
Class io.invertase.gradle.common.WithExtensions                                       : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$androidLayoutResources$1        : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.report.GradleBuildMetricsReporter                   : loaded 2 times (x 94B)
Class org.jetbrains.kotlin.gradle.utils.FutureKt$future$1                             : loaded 2 times (x 91B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinMetadataCompilation                : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.sources.KotlinDependencyScope                : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig$1   : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationFriendPathsResolver: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$ANDROID_ARM64                     : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinNativeHostSpecificMetadataArtifactKt$KotlinNativeHostSpecificMetadataArtifact$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinLegacyMetadataArtifactKt$KotlinLegacyMetadataArtifact$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationSideEffect$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinUsages$KotlinMetadataDisambiguation: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.FlowActionBuildFusService$Companion$registerIfAbsentImpl$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$IntGradleProperty: loaded 2 times (x 72B)
Class com.google.common.io.ByteArrayDataOutput                                        : loaded 2 times (x 66B)
Class com.google.common.collect.RegularImmutableMap                                   : loaded 2 times (x 117B)
Class com.google.common.collect.ImmutableMap$MapViewOfValuesAsSingletonSets           : loaded 2 times (x 121B)
Class build_agisdczp7kn8uyp03dp4zx2vi$safeExtGet                                      : loaded 2 times (x 148B)
Class build_6k1vuntnni446s9ebkvsvp2hk$_run_closure1$_closure4                         : loaded 2 times (x 135B)
Class build_evl13vpxjidvxohlw1uueqdij$_run_closure2$_closure9                         : loaded 2 times (x 135B)
Class io.invertase.gradle.common.WithExtensions$getName                               : loaded 2 times (x 148B)
Class io.invertase.gradle.build.ProjectExtension                                      : loaded 2 times (x 91B)
Class org.jetbrains.kotlin.gradle.internal.StdlibDependencyManagementKt$addStdlibDependency$1$1$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.AbstractKotlinCompile$compilerRunner$1        : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.CacheableTasksKt$cacheOnlyIfEnabledForKotlin$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.utils.GradleAttributesContainerUtilsKt$setAttributeProvider$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.statistics.AnonymizerUtilsKt$salt$2                        : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.internal.transforms.BuildToolsApiClasspathEntrySnapshotTransform: loaded 2 times (x 78B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig$2   : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory$PreConfigure: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinProjectSetupActionKt$KotlinProjectSetupCoroutine$1$1: loaded 2 times (x 91B)
Class org.jetbrains.kotlin.gradle.utils.NativeCompilerDownloader$Companion$DEFAULT_KONAN_VERSION$2: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$WATCHOS_ARM32                     : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.targets.js.dsl.KotlinJsTargetDsl                    : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycleImplKt                  : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.DeprecatedKotlinNativeTargetsChecker$runChecks$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycleImpl$WhenMappings       : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinCommonCompilerToolOptionsHelper           : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinTargetComponent                        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradlePluginExtensionPointInternal     : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.statistics.metrics.StringOverridePolicy$OVERRIDE_VERSION_IF_NOT_SET: loaded 2 times (x 81B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinBuildStatsBeanService$Companion$initStatsService$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactories$Companion$getProvider$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$BooleanGradleProperty: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinProjectExtension                          : loaded 2 times (x 118B)
Class [Lorg.objectweb.asm.Symbol;                                                     : loaded 2 times (x 65B)
Class org.objectweb.asm.ClassVisitor                                                  : loaded 2 times (x 84B)
Class com.google.common.reflect.TypeCapture                                           : loaded 2 times (x 67B)
Class com.google.common.cache.LocalCache$Values                                       : loaded 2 times (x 114B)
Class com.google.common.collect.TransformedListIterator                               : loaded 2 times (x 89B)
Class com.google.common.collect.UnmodifiableListIterator                              : loaded 2 times (x 91B)
Class Build_gradle$2$1$1                                                              : loaded 2 times (x 70B)
Class build_6k1vuntnni446s9ebkvsvp2hk$_run_closure1$_closure5                         : loaded 2 times (x 135B)
Class io.invertase.gradle.build.ReactNativeModule$_closure1                           : loaded 2 times (x 137B)
Class com.google.common.base.CharMatcher$IsEither                                     : loaded 2 times (x 107B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycle$ProjectConfigurationResult$Failure: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.sources.DefaultKotlinSourceSet$MetadataDependencyTransformation: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$Companion          : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain                    : loaded 2 times (x 73B)
Class org.jetbrains.kotlin.gradle.targets.native.toolchain.KotlinNativeBundleBuildService$Parameters: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.targets.native.toolchain.KotlinNativeBundleBuildService: loaded 2 times (x 73B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleBuildServices                    : loaded 2 times (x 76B)
Class [Lorg.jetbrains.kotlin.statistics.ValueAnonymizer;                              : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$MemoizedCallable$value$2: loaded 2 times (x 75B)
Class org.objectweb.asm.MethodTooLargeException                                       : loaded 2 times (x 79B)
Class com.google.common.collect.MapMakerInternalMap$Strength                          : loaded 2 times (x 76B)
Class com.google.common.base.Platform$JdkPatternCompiler                              : loaded 2 times (x 71B)
Class [Lcom.google.common.cache.CacheBuilder$OneWeigher;                              : loaded 2 times (x 65B)
Class com.google.common.base.Suppliers                                                : loaded 2 times (x 67B)
Class org.gradle.internal.classloader.DefaultClassLoaderFactory                       : loaded 2 times (x 78B)
Class build_6s7imafvclc6gm72hnzimmguy$_run_closure3$_closure20                        : loaded 2 times (x 136B)
Class build_6k1vuntnni446s9ebkvsvp2hk$_run_closure1$_closure6                         : loaded 2 times (x 135B)
Class org.jetbrains.kotlin.gradle.plugin.PropertiesProvider$kotlinCompilerArgumentsLogLevel$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.compilerRunner.CompilerSystemPropertiesService$Parameters_Decorated: loaded 2 times (x 130B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinTargetWithTests                        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.ToolingDiagnosticRenderingOptions: loaded 2 times (x 68B)
Class [Lorg.jetbrains.kotlin.gradle.dsl.KotlinVersion;                                : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.UsesKotlinToolingDiagnosticsParameters: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.JsEnvironmentChecker    : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.NpmDependencyExtensionKt             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.metadata.KotlinMetadataTargetConfiguratorKt$special$$inlined$extrasStoredFuture$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.PropertiesProvider$classLoaderCacheTimeoutInSeconds$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.report.BuildMetricsService                          : loaded 2 times (x 82B)
Class org.jetbrains.kotlin.statistics.metrics.NumberAnonymizationPolicy               : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.statistics.metrics.StringAnonymizationPolicy               : loaded 2 times (x 74B)
Class com.google.common.io.ByteSource$ConcatenatedByteSource                          : loaded 2 times (x 81B)
Class com.google.common.collect.ReverseNaturalOrdering                                : loaded 2 times (x 110B)
Class [Lcom.google.common.collect.AbstractIterator$State;                             : loaded 2 times (x 65B)
Class com.google.common.base.CharMatcher$None                                         : loaded 2 times (x 108B)
Class org.gradle.internal.classloader.ClassLoaderVisitor                              : loaded 2 times (x 72B)
Class build_agisdczp7kn8uyp03dp4zx2vi$_run_closure5$_closure20                        : loaded 2 times (x 135B)
Class build_6k1vuntnni446s9ebkvsvp2hk$_run_closure1$_closure7                         : loaded 2 times (x 135B)
Class org.jetbrains.kotlin.build.report.metrics.BuildPerformanceMetrics               : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationLanguageSettingsConfigurator: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycleKt$await$1              : loaded 2 times (x 84B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.KotlinCompilation;                         : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinTargetComponentWithPublication     : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCreateNativeCompileTasksSideEffectKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinTargetContainerWithJsPresetFunctions      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinProjectSetupActionKt                   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.report.ReportingSettings$Companion                  : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.report.ConfigureReporingKt                          : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.tooling.core.TypeUtils                                     : loaded 2 times (x 67B)
Class com.google.common.collect.Ordering                                              : loaded 2 times (x 110B)
Class [Lcom.google.common.collect.ImmutableMapEntry;                                  : loaded 2 times (x 65B)
Class org.gradle.api.internal.classpath.EffectiveClassPath                            : loaded 2 times (x 86B)
Class build_6k1vuntnni446s9ebkvsvp2hk$_run_closure1$_closure8                         : loaded 2 times (x 135B)
Class org.jetbrains.kotlin.gradle.utils.CompatibilityKt                               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.IDaemonReuseCounterMXBean         : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.DefaultKotlinCompilationConfigurationsContainer: loaded 2 times (x 81B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$ANDROID_ARM32                     : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.tasks.TasksProviderKt                               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.ToolingDiagnostic                : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.sources.DefaultKotlinSourceSetFactory        : loaded 2 times (x 76B)
Class com.google.common.collect.ImmutableRangeSet                                     : loaded 2 times (x 111B)
Class com.google.common.collect.ImmutableEnumMap                                      : loaded 2 times (x 121B)
Class com.google.common.collect.ListMultimap                                          : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.incremental.ClasspathChanges                               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.buildtools.api.jvm.ClassSnapshotGranularity                : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.ExternalKotlinTargetApi                             : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.AndroidPluginWithoutAndroidTargetChecker$runChecks$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.AndroidSourceSetLayoutV1SourceSetsNotFoundChecker$runChecks$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinGradleProjectCheckersRunnerKt$launchKotlinGradleProjectCheckers$1$1: loaded 2 times (x 91B)
Class org.jetbrains.kotlin.konan.target.KonanTarget                                   : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.targets.CreateDefaultTestRunSideEffectKt            : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.utils.ProviderApiUtilsKt$mapOrNull$1                : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.targets.native.toolchain.KotlinNativeBundleBuildService$Companion$registerIfAbsent$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.statistics.metrics.BooleanAnonymizationPolicy              : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.statistics.metrics.BooleanOverridePolicy$OVERRIDE          : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.gradle.plugin.PropertiesProvider$kotlinExperimentalTryNext$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.fus.BuildUidService$Companion                       : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactoriesConfigurator$Companion$getProvider$1: loaded 2 times (x 70B)
Class org.objectweb.asm.AnnotationVisitor                                             : loaded 2 times (x 74B)
Class com.google.common.collect.ComparatorOrdering                                    : loaded 2 times (x 111B)
Class com.google.common.collect.Iterators$EmptyModifiableIterator                     : loaded 2 times (x 82B)
Class com.google.common.collect.Sets$1                                                : loaded 2 times (x 135B)
Class com.google.common.base.Splitter                                                 : loaded 2 times (x 68B)
Class org.gradle.api.internal.DefaultClassPathProvider                                : loaded 2 times (x 72B)
Class [Lcom.google.common.base.AbstractIterator$State;                                : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$1$3$classpathEntrySnapshotFiles$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.statistics.metrics.OverrideMetricContainer                 : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.KaptGenerateStubsConfig$Companion$wireJavaAndKotlinOutputs$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycleCoroutineContextElement : loaded 2 times (x 80B)
Class org.jetbrains.kotlin.gradle.dsl.PlatformSourceSetConventionsChecker             : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.KotlinSourceSetTreeDependsOnMismatchChecker: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.targets.CreateDefaultCompilationsSideEffectKt       : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.SyncLanguageSettingsWithKotlinExtensionSetupActionKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.statistics.metrics.NumericalMetrics                        : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactories$Companion$getProvider$2$1: loaded 2 times (x 75B)
Class [Lcom.google.common.base.Function;                                              : loaded 2 times (x 65B)
Class com.google.common.collect.NaturalOrdering                                       : loaded 2 times (x 111B)
Class com.google.common.collect.MapMakerInternalMap$Strength$1                        : loaded 2 times (x 76B)
Class com.google.common.collect.Maps$ViewCachingAbstractMap                           : loaded 2 times (x 121B)
Class com.google.common.collect.Sets$2                                                : loaded 2 times (x 135B)
Class [Lorg.gradle.api.internal.ClassPathProvider;                                    : loaded 2 times (x 65B)
Class org.gradle.api.internal.ClassPathProvider                                       : loaded 2 times (x 66B)
Class build_86psz0zg5ccmkipscnjtptl6f$_run_closure2                                   : loaded 2 times (x 136B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.CheckKotlinGradlePluginConfigurationErrors$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinJvmCompile$DefaultImpls                 : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.CacheableTasksKt                              : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.BuildFinishMetrics$reportBuildFinished$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.ExecutedTaskMetrics               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.konan.target.HostManager$targetValues$2                    : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.UnusedSourceSetsChecker$runChecks$1: loaded 2 times (x 84B)
Class [Lorg.jetbrains.kotlin.konan.target.Family;                                     : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.PeerNpmDependencyExtension           : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.dsl.AndroidMainSourceSetConventionsChecker          : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.WasmJsEnvironmentChecker$1: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.targets.native.internal.SetupKotlinNativePlatformDependenciesAndStdlibKt$SetupKotlinNativeStdlibAndPlatformDependenciesImport$1: loaded 2 times (x 91B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.NpmDependencyExtensionKt$AddNpmDependencyExtensionProjectSetupAction$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.FlowActionBuildFusService         : loaded 2 times (x 80B)
Class org.jetbrains.kotlin.build.report.metrics.GradleBuildPerformanceMetric          : loaded 2 times (x 88B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$NullableBooleanGradleProperty: loaded 2 times (x 72B)
Class [Lorg.objectweb.asm.AnnotationVisitor;                                          : loaded 2 times (x 65B)
Class com.google.common.reflect.Types$ClassOwnership$1                                : loaded 2 times (x 76B)
Class com.google.common.collect.MapMakerInternalMap$Strength$2                        : loaded 2 times (x 76B)
Class com.google.common.collect.Sets$3                                                : loaded 2 times (x 135B)
Class com.google.common.cache.LocalCache$LocalLoadingCache                            : loaded 2 times (x 130B)
Class com.google.common.base.AbstractIterator$State                                   : loaded 2 times (x 75B)
Class org.gradle.internal.service.UnknownServiceException                             : loaded 2 times (x 79B)
Class build_agisdczp7kn8uyp03dp4zx2vi$_run_closure4$_closure10$_closure16             : loaded 2 times (x 135B)
Class build_86psz0zg5ccmkipscnjtptl6f$_run_closure3                                   : loaded 2 times (x 135B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$2$1$1$1 : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$ScriptFilterSpec                : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginWrapper                          : loaded 2 times (x 82B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.ComposePluginSuggestApplyChecker$runChecks$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$TVOS_ARM64                        : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.DefaultPeerNpmDependencyExtension    : loaded 2 times (x 137B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycleKt                      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.WasmJsEnvironmentChecker$2: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.AbstractKotlinTarget                     : loaded 2 times (x 152B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesLoader$kotlinNativeVersion$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactoriesConfigurator$Inject: loaded 2 times (x 92B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinSingleTargetExtension                     : loaded 2 times (x 120B)
Class org.objectweb.asm.ModuleVisitor                                                 : loaded 2 times (x 77B)
Class com.google.common.reflect.Types$ClassOwnership$2                                : loaded 2 times (x 76B)
Class com.google.common.reflect.TypeResolver                                          : loaded 2 times (x 68B)
Class com.google.common.io.ByteArrayDataInput                                         : loaded 2 times (x 66B)
Class com.google.common.collect.MapMakerInternalMap$StrongValueEntry                  : loaded 2 times (x 66B)
Class com.google.common.collect.Sets$4                                                : loaded 2 times (x 135B)
Class com.google.common.cache.LocalCache$Strength$1                                   : loaded 2 times (x 77B)
Class com.google.common.io.FileWriteMode                                              : loaded 2 times (x 75B)
Class de.undercouch.gradle.tasks.download.DownloadSpec                                : loaded 2 times (x 66B)
Class build_9kn57pj55amg75ewdgalkhlsr$_run_closure3$_closure13$_closure19             : loaded 2 times (x 135B)
Class io.invertase.gradle.build.ReactNativeProject                                    : loaded 2 times (x 87B)
Class build_86psz0zg5ccmkipscnjtptl6f$_run_closure4                                   : loaded 2 times (x 136B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$2$1$1$2 : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinCompilationsKt$addSourcesToKotlinCompileTask$configureAction$2: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationTaskNamesContainer: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory$KotlinCompilerOptionsFactory: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildServiceKt$propertyWithDeprecatedName$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.NpmDependencyExtension               : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.LanguageSettingsBuilder                      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.WasmWasiEnvironmentChecker: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.WasmJsEnvironmentChecker$3: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.targets.ConfigureBuildSideEffectKt                  : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.BuildEventsListenerRegistryHolder$Companion  : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.BuildFusService$Companion$registerIfAbsentImpl$generalConfigurationMetricsProvider$1: loaded 2 times (x 71B)
Class [Lorg.jetbrains.kotlin.statistics.metrics.StringMetrics;                        : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$Inject   : loaded 2 times (x 92B)
Class com.google.common.reflect.Types$ClassOwnership$3                                : loaded 2 times (x 67B)
Class com.google.common.collect.Collections2$FilteredCollection                       : loaded 2 times (x 115B)
Class com.google.common.util.concurrent.SettableFuture                                : loaded 2 times (x 110B)
Class com.google.common.cache.LocalCache$Strength$2                                   : loaded 2 times (x 77B)
Class build_eupcmxgtu9q3uy0v8rg9z8i7j$_run_closure4$_closure22                        : loaded 2 times (x 135B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinJavaToolchain$JdkSetter                 : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.utils.ConfigurationCacheKt                          : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.statistics.fileloggers.MetricsContainer$MetricDescriptor   : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycle$ProjectConfigurationResult$Success: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.StdlibDependencyManagementKt$configureStdlibVersionAlignment$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.OverriddenKotlinNativeHomeChecker$allKonanMainSubdirectoriesExist$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.dsl.PlatformSourceSetConventionsChecker$CheckedPlatformInfo: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.WasmJsEnvironmentChecker$4: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinUsages$KotlinCinteropDisambiguation: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginInMultipleProjectsHolder         : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.report.BuildReportType$Companion                    : loaded 2 times (x 67B)
Class com.google.common.io.ByteStreams                                                : loaded 2 times (x 67B)
Class [Lcom.google.common.collect.Maps$EntryFunction;                                 : loaded 2 times (x 65B)
Class com.google.common.collect.AllEqualOrdering                                      : loaded 2 times (x 110B)
Class com.google.common.collect.Interners$InternerImpl                                : loaded 2 times (x 71B)
Class com.google.common.collect.Interner                                              : loaded 2 times (x 66B)
Class com.google.common.collect.TransformedIterator                                   : loaded 2 times (x 76B)
Class com.google.common.cache.LocalCache$Strength$3                                   : loaded 2 times (x 77B)
Class com.google.common.collect.MapDifference                                         : loaded 2 times (x 66B)
Class com.google.common.base.Splitter$1                                               : loaded 2 times (x 73B)
Class build_6s7imafvclc6gm72hnzimmguy$_run_closure2$_closure7$_closure15$_closure16   : loaded 2 times (x 136B)
Class build_eupcmxgtu9q3uy0v8rg9z8i7j$safeAppExtGet                                   : loaded 2 times (x 148B)
Class build_eupcmxgtu9q3uy0v8rg9z8i7j$_run_closure4$_closure23                        : loaded 2 times (x 136B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompilerPluginData$InputsOutputsState   : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.sources.FragmentConsistencyChecks$optInAnnotationsCheck$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.AbstractKotlinCompilationToRunnableFiles : loaded 2 times (x 192B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinMetadataArtifactKt$KotlinMetadataArtifact$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinUsages$KotlinMetadataCompatibility : loaded 2 times (x 72B)
Class [Lorg.jetbrains.kotlin.gradle.dsl.JvmTarget;                                    : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.utils.ConfigurationsKt$maybeCreateResolvable$1      : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.StatisticsBuildFlowManager$Companion         : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.statistics.metrics.StringMetrics$Companion                 : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$property$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.tooling.core.Extras$Type                                   : loaded 2 times (x 68B)
Class org.objectweb.asm.FieldWriter                                                   : loaded 2 times (x 74B)
Class com.google.common.reflect.Invokable                                             : loaded 2 times (x 99B)
Class com.google.common.collect.MapMaker$Dummy                                        : loaded 2 times (x 75B)
Class com.google.common.cache.CacheBuilder$NullListener                               : loaded 2 times (x 78B)
Class org.gradle.internal.classpath.TransformedClassPath                              : loaded 2 times (x 92B)
Class org.gradle.internal.classloader.InstrumentingClassLoader                        : loaded 2 times (x 66B)
Class build_eupcmxgtu9q3uy0v8rg9z8i7j$_run_closure4$_closure24                        : loaded 2 times (x 136B)
Class org.jetbrains.kotlin.gradle.internal.StdlibDependencyManagementKt$configureStdlibVersionAlignment$1$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.BuildFinishedListenerService$Companion       : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.BuildFinishedListenerService                 : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.sources.FragmentConsistencyChecks$optInAnnotationsCheck$2: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.targets.NativeForwardImplementationToApiElementsSideEffectKt$special$$inlined$KotlinTargetSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.BuildFusServiceKt                 : loaded 2 times (x 67B)
Class com.google.common.reflect.Types$ParameterizedTypeImpl                           : loaded 2 times (x 77B)
Class com.google.common.collect.Maps$IteratorBasedAbstractMap$1                       : loaded 2 times (x 134B)
Class com.google.common.collect.ImmutableRangeSet$AsSet                               : loaded 2 times (x 295B)
Class com.google.common.cache.CacheLoader$FunctionToCacheLoader                       : loaded 2 times (x 71B)
Class com.google.common.collect.Iterators$ArrayItr                                    : loaded 2 times (x 93B)
Class com.google.common.collect.ImmutableCollection$Builder                           : loaded 2 times (x 72B)
Class [Lcom.google.common.collect.MapMakerInternalMap$Segment;                        : loaded 2 times (x 65B)
Class build_eupcmxgtu9q3uy0v8rg9z8i7j$_run_closure4$_closure25                        : loaded 2 times (x 135B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$1$3$2   : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.AbstractKotlinCompileTool$sources$1           : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.BuildFusService$onFinish$2        : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.TaskConfigAction                : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.OverriddenKotlinNativeHomeChecker$allKonanMainSubdirectoriesExist$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.AbstractKotlinPlugin$Companion               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinSourceSetTree                          : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycle$Stage                  : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCreateResourcesTaskSideEffectKt$KotlinCreateResourcesTaskSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.targets.jvm.KotlinJvmTargetTestFixturesSideEffectKt : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.sources.KotlinSourceSetFactory               : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.targets.native.toolchain.KotlinNativeBundleBuildService$Parameters_Decorated: loaded 2 times (x 139B)
Class org.jetbrains.kotlin.gradle.internal.ClassLoadersCachingBuildService$Companion  : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.IdeaKt$isInIdeaSync$1                      : loaded 2 times (x 70B)
Class com.google.common.reflect.TypeResolver$TypeVariableKey                          : loaded 2 times (x 68B)
Class com.google.common.collect.BaseImmutableMultimap                                 : loaded 2 times (x 119B)
Class com.google.common.base.Joiner                                                   : loaded 2 times (x 75B)
Class com.google.common.base.MoreObjects                                              : loaded 2 times (x 67B)
Class com.google.common.cache.LocalCache$ValueIterator                                : loaded 2 times (x 82B)
Class build_eupcmxgtu9q3uy0v8rg9z8i7j$_run_closure4$_closure26                        : loaded 2 times (x 136B)
Class build_eupcmxgtu9q3uy0v8rg9z8i7j$_run_closure4$_closure23$_closure34             : loaded 2 times (x 136B)
Class [Lorg.jetbrains.kotlin.build.report.metrics.BuildAttribute;                     : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.utils.ProviderApiUtilsKt$mapOrNull$1$1              : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.hierarchy.KotlinSourceSetTreeClassifier$Default: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.InternalKotlinCompilationKt              : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig     : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationImpl$attributes$2: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.OverriddenKotlinNativeHomeChecker$StdlibExistenceCheckerValueSource: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.StatisticsBuildFlowManager$subscribeForBuildResultAndConfigurationTimeMetrics$1$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.build.report.metrics.GradleBuildTime                       : loaded 2 times (x 82B)
Class org.jetbrains.kotlin.gradle.utils.IsolatedKotlinClasspathClassCastException     : loaded 2 times (x 78B)
Class [Lorg.objectweb.asm.Type;                                                       : loaded 2 times (x 65B)
Class com.google.common.collect.Platform                                              : loaded 2 times (x 67B)
Class com.google.common.collect.UnmodifiableIterator                                  : loaded 2 times (x 76B)
Class org.gradle.internal.service.ServiceLocator                                      : loaded 2 times (x 66B)
Class build_agr3p375fzty99zrloowe6ju6$_run_closure2                                   : loaded 2 times (x 135B)
Class build_eupcmxgtu9q3uy0v8rg9z8i7j$_run_closure4$_closure27                        : loaded 2 times (x 135B)
Class build_eupcmxgtu9q3uy0v8rg9z8i7j$_run_closure4$_closure23$_closure35             : loaded 2 times (x 136B)
Class io.invertase.gradle.common.PackageJson$getForProject                            : loaded 2 times (x 142B)
Class io.invertase.gradle.build.ProjectExtension$getVersion$1                         : loaded 2 times (x 148B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$DefaultJavaToolchainSetter$useAsConvention$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilerArgumentsProducer$CreateCompilerArgumentsContext: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.utils.GradleAttributesContainerUtilsKt$copyAttributeTo$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.RenderReportedDiagnosticsKt      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.hierarchy.KotlinSourceSetTreeClassifierKt$classify$1: loaded 2 times (x 84B)
Class [Lorg.jetbrains.kotlin.konan.target.KonanTarget;                                : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.WasmSourceSetsNotFoundChecker: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.AndroidSourceSetLayoutV1SourceSetsNotFoundChecker$androidSourceSetRegex$2: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinBuildStatsLoggerService$Companion: loaded 2 times (x 67B)
Class com.google.common.base.Ascii                                                    : loaded 2 times (x 67B)
Class com.google.common.collect.SingletonImmutableBiMap                               : loaded 2 times (x 139B)
Class com.google.common.cache.LocalCache$1                                            : loaded 2 times (x 85B)
Class com.google.common.collect.AbstractMapEntry                                      : loaded 2 times (x 77B)
Class build_agr3p375fzty99zrloowe6ju6$_run_closure3                                   : loaded 2 times (x 135B)
Class build_eupcmxgtu9q3uy0v8rg9z8i7j$_run_closure4$_closure28                        : loaded 2 times (x 135B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilationWithResources               : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinTargetPreset                           : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.KotlinTargetAlreadyDeclaredChecker: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationProcessorSideEffectKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinUsages$KotlinJavaRuntimeJarsCompatibility: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.utils.FileUtilsKt$localProperties$1                 : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.dsl.HasConfigurableKotlinCompilerOptions            : loaded 2 times (x 66B)
Class com.google.common.reflect.TypeToken$SimpleTypeToken                             : loaded 2 times (x 69B)
Class com.google.common.io.ByteSource$ByteArrayByteSource                             : loaded 2 times (x 81B)
Class com.google.common.collect.AbstractSetMultimap                                   : loaded 2 times (x 170B)
Class com.google.common.collect.MapMakerInternalMap$AbstractStrongKeyEntry            : loaded 2 times (x 78B)
Class com.google.common.collect.Sets$ImprovedAbstractSet                              : loaded 2 times (x 131B)
Class com.google.common.collect.ImmutableList$Builder                                 : loaded 2 times (x 73B)
Class com.google.common.cache.LocalCache$2                                            : loaded 2 times (x 138B)
Class com.google.common.collect.AbstractMultimap                                      : loaded 2 times (x 119B)
Class build_eupcmxgtu9q3uy0v8rg9z8i7j$_run_closure4$_closure32$_closure42             : loaded 2 times (x 136B)
Class build_eupcmxgtu9q3uy0v8rg9z8i7j$_run_closure4$_closure29                        : loaded 2 times (x 135B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig$2$1 : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain_Decorated          : loaded 2 times (x 122B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinProjectConfigurationMetrics : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilationInfo$TCS                    : loaded 2 times (x 85B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.HasBinaries                              : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinCommonOptions                             : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCreateSourcesJarTaskSideEffectKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinJvmProjectExtension                       : loaded 2 times (x 143B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradlePluginExtensionPointInternalKt   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.native.toolchain.UsesKotlinNativeBundleBuildService: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilerArgumentsProducer              : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.build.report.metrics.ValueType                             : loaded 2 times (x 75B)
Class com.google.common.collect.ImmutableRangeSet$1                                   : loaded 2 times (x 205B)
Class com.google.common.collect.RegularImmutableAsList                                : loaded 2 times (x 212B)
Class org.gradle.kotlin.dsl.Accessorse8w47d3slt021lb0cbtcdsmobKt                      : loaded 2 times (x 67B)
Class com.google.common.collect.SortedSetMultimap                                     : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.build.report.metrics.BuildTimes$Companion                  : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.tasks.ProducesKlib                         : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$1$classpathConfiguration$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.NpmDependencyExtensionDelegate       : loaded 2 times (x 163B)
Class org.jetbrains.kotlin.gradle.plugin.sources.FragmentConsistencyChecker           : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.targets.native.internal.SetupKotlinNativePlatformDependenciesAndStdlibKt$SetupKotlinNativePlatformDependenciesAndStdlib$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.dsl.DefaultToolchainSupport_Decorated               : loaded 2 times (x 120B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesLoader$jvmArgs$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.report.BuildMetricsService$Companion                : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompileTool                             : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.utils.SingleAction$PerformedActions                 : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactories$Parameters_Decorated: loaded 2 times (x 130B)
Class org.jetbrains.kotlin.gradle.plugin.PropertiesProvider$PropertyNames             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.utils.GradleUtilsKt                                 : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.HasProject                                   : loaded 2 times (x 66B)
Class com.google.common.base.Optional                                                 : loaded 2 times (x 76B)
Class com.google.common.collect.Range                                                 : loaded 2 times (x 83B)
Class com.google.common.util.concurrent.AbstractFuture$Trusted                        : loaded 2 times (x 66B)
Class com.google.common.collect.Iterators                                             : loaded 2 times (x 67B)
Class build_1st6b4zcxn0lddyz8z0am5m89$_run_closure3                                   : loaded 2 times (x 136B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig$2$3 : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig         : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory$KotlinCompilationTaskNamesContainerFactory: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.internal.KotlinDependenciesManagementKt$CustomizeKotlinDependenciesSetupAction$1$coreLibrariesVersion$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.AndroidPublicationNotConfiguredChecker$runChecks$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.tooling.core.KotlinToolingVersion                          : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.CInteropInputChecker$runChecks$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.NativeVersionChecker    : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.ExperimentalKotlinGradlePluginApi                   : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesLoader          : loaded 2 times (x 82B)
Class [Lorg.jetbrains.kotlin.build.report.metrics.BuildTime;                          : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.statistics.metrics.NumberAnonymizationPolicy$SAFE          : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.BuildFusService                   : loaded 2 times (x 80B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinAndroidProjectExtension                   : loaded 2 times (x 135B)
Class com.google.common.collect.ExplicitOrdering                                      : loaded 2 times (x 111B)
Class com.google.common.io.ByteSink$AsCharSink                                        : loaded 2 times (x 76B)
Class com.google.common.util.concurrent.UncheckedExecutionException                   : loaded 2 times (x 78B)
Class com.google.common.collect.Lists$StringAsImmutableList                           : loaded 2 times (x 203B)
Class org.jetbrains.kotlin.gradle.plugin.internal.ConfigurationCacheStartParameterAccessorKt: loaded 2 times (x 67B)
Class build_1st6b4zcxn0lddyz8z0am5m89$_run_closure4                                   : loaded 2 times (x 135B)
Class org.jetbrains.kotlin.gradle.utils.ReportUtilsKt$addConfigurationMetrics$1       : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig$2$4 : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$scriptSources$1                 : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.build.report.metrics.BuildAttribute$Companion              : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.CInteropInputChecker$runChecks$2: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.KaptGenerateStubsConfig$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.KotlinTestDependencyManagementKt$maybeAddTestDependencyCapability$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.CheckKotlinGradlePluginConfigurationErrorsKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.NpmDependencyExtensionKt$AddNpmDependencyExtensionProjectSetupAction$1$WhenMappings: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinJvmAndroidCompilation              : loaded 2 times (x 194B)
Class org.jetbrains.kotlin.gradle.targets.CreateDefaultCompilationsSideEffectKt$CreateDefaultCompilationsSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.targets.native.internal.AddKotlinPlatformIntegersSupportLibraryKt$AddKotlinPlatformIntegersSupportLibrary$1: loaded 2 times (x 91B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.ApplyUserDefinedAttributesKt             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.tooling.core.TypeUtils$WhenMappings                        : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.utils.FutureImpl                                    : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesLoader$isToolchainEnabled$1: loaded 2 times (x 71B)
Class [Lorg.jetbrains.kotlin.statistics.metrics.NumericalMetrics;                     : loaded 2 times (x 65B)
Class org.gradle.internal.InternalTransformer                                         : loaded 2 times (x 66B)
Class build_6s7imafvclc6gm72hnzimmguy$_run_closure2$_closure7$_closure14              : loaded 2 times (x 135B)
Class build_1st6b4zcxn0lddyz8z0am5m89$_run_closure5                                   : loaded 2 times (x 136B)
Class org.jetbrains.kotlin.gradle.tasks.CompileUsingKotlinDaemonWithNormalization     : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory$PostConfigure$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.KotlinDomApiDependencyManagementKt$configureKotlinDomApiDefaultDependency$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.DefaultDevNpmDependencyExtension     : loaded 2 times (x 141B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.HasKotlinDependencies;                     : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$Companion$wireJvmTargetToToolchain$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.JsLikeEnvironmentChecker: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPlatformType                           : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildServiceKt        : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleBuildServices$Companion          : loaded 2 times (x 67B)
Class org.objectweb.asm.ClassWriter                                                   : loaded 2 times (x 102B)
Class com.google.common.collect.HashMultimapGwtSerializationDependencies              : loaded 2 times (x 170B)
Class com.google.common.collect.AbstractMapBasedMultimap$KeySet$1                     : loaded 2 times (x 78B)
Class com.google.common.cache.CacheLoader$UnsupportedLoadingOperationException        : loaded 2 times (x 78B)
Class com.google.common.base.CharMatcher$And                                          : loaded 2 times (x 108B)
Class org.gradle.internal.classloader.ClasspathUtil$1                                 : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPlatformType$Companion                 : loaded 2 times (x 67B)
Class build_6s7imafvclc6gm72hnzimmguy$_run_closure4$_closure21                        : loaded 2 times (x 135B)
Class build_6s7imafvclc6gm72hnzimmguy$_run_closure2$_closure7$_closure15              : loaded 2 times (x 136B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.TransformActionUsingKotlinToolingDiagnostics$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.BuildFinishedListenerService$Companion$registerIfAbsent$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.ToolingDiagnosticRenderingOptions$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinJvmCompilerOptionsHelper                  : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$Companion$wireJvmTargetToToolchain$2: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.ProjectLocalConfigurations$ProjectLocalDisambiguation: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.apple.swiftexport.internal.SwiftExportInitKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$NullableStringGradleProperty: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.targets.native.KonanPropertiesBuildService          : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleBuildServices$detectKotlinPluginLoadedInMultipleProjects$onRegister$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.internal.ConfigurationCacheStartParameterAccessor$Factory: loaded 2 times (x 66B)
Class org.objectweb.asm.FieldVisitor                                                  : loaded 2 times (x 73B)
Class com.google.common.collect.FluentIterable$1                                      : loaded 2 times (x 77B)
Class com.google.common.base.Objects                                                  : loaded 2 times (x 67B)
Class com.google.common.collect.IndexedImmutableSet                                   : loaded 2 times (x 146B)
Class com.google.common.collect.ImmutableMap$1                                        : loaded 2 times (x 77B)
Class com.google.common.collect.BiMap                                                 : loaded 2 times (x 66B)
Class org.gradle.api.internal.classpath.DefaultModuleRegistry$DefaultModule           : loaded 2 times (x 82B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPlatformType$DisambiguationRule        : loaded 2 times (x 72B)
Class build_9kn57pj55amg75ewdgalkhlsr$_run_closure4$_closure21                        : loaded 2 times (x 135B)
Class org.jetbrains.kotlin.gradle.internal.transforms.BuildToolsApiClasspathEntrySnapshotTransform$Parameters$Inject: loaded 2 times (x 114B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.CheckKotlinGradlePluginConfigurationErrors_Decorated: loaded 2 times (x 361B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.BuildFinishMetrics$reportGlobalMetrics$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationSourceSetInclusion$DefaultAddSourcesToCompileTask$addSources$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.WasmSourceSetsNotFoundChecker$wasmSourceSetRegex$2: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationSideEffect: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradlePluginExtensionPoint             : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.tooling.core.ExtrasUtilsKt                                 : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.buildtools.api.ExperimentalBuildToolsApi                   : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.targets.native.KonanPropertiesBuildService$Companion$registerIfAbsent$2$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.internal.IsInIdeaSyncValueSource$Inject             : loaded 2 times (x 105B)
Class org.jetbrains.kotlin.gradle.utils.ResourceUtilsKt                               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginWrapperKt$kotlinPluginVersionFromResources$1: loaded 2 times (x 74B)
Class com.google.common.reflect.Reflection                                            : loaded 2 times (x 67B)
Class com.google.common.collect.NullsFirstOrdering                                    : loaded 2 times (x 111B)
Class com.google.common.base.Absent                                                   : loaded 2 times (x 76B)
Class com.google.common.collect.FluentIterable$2                                      : loaded 2 times (x 77B)
Class com.google.common.primitives.Ints                                               : loaded 2 times (x 67B)
Class com.google.common.collect.AbstractIterator                                      : loaded 2 times (x 78B)
Class build_agisdczp7kn8uyp03dp4zx2vi$_run_closure2                                   : loaded 2 times (x 136B)
Class build_9kn57pj55amg75ewdgalkhlsr$_run_closure4$_closure22                        : loaded 2 times (x 135B)
Class org.jetbrains.kotlin.gradle.tasks.UsesKotlinJavaToolchain$DefaultImpls          : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.build.report.metrics.BuildAttributeKind$Companion          : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinTargetSoftwareComponentImplKt$KotlinTargetSoftwareComponent$1$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.KaptGenerateStubsConfig$Companion$configureLibraries$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationSourceSetInclusion$DefaultAddSourcesToCompileTask$addSources$2: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.AbstractKotlinCompile                         : loaded 2 times (x 450B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilationOutput                      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.CheckKotlinGradlePluginConfigurationErrorsKt$addDependsOnFromTasksThatShouldFailWhenErrorsReported$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$TVOS_X64                          : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinToolingDiagnosticsSetupActionKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.native.KonanPropertiesBuildService$Parameters_Decorated: loaded 2 times (x 130B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleBuildServices$Parameters$Inject  : loaded 2 times (x 102B)
Class org.jetbrains.kotlin.gradle.fus.BuildUidService$Inject                          : loaded 2 times (x 96B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginWrapperKt$kotlinPluginVersionFromResources$1$1: loaded 2 times (x 67B)
Class com.google.common.collect.FluentIterable$3                                      : loaded 2 times (x 77B)
Class com.google.common.math.MathPreconditions                                        : loaded 2 times (x 67B)
Class com.google.common.collect.Multiset                                              : loaded 2 times (x 66B)
Class com.google.common.collect.ArrayListMultimapGwtSerializationDependencies         : loaded 2 times (x 168B)
Class com.google.common.collect.ObjectArrays                                          : loaded 2 times (x 67B)
Class com.google.common.collect.AbstractIndexedListIterator                           : loaded 2 times (x 92B)
Class build_agisdczp7kn8uyp03dp4zx2vi$_run_closure3                                   : loaded 2 times (x 135B)
Class build_1st6b4zcxn0lddyz8z0am5m89$_run_closure3$_closure6                         : loaded 2 times (x 136B)
Class org.jetbrains.kotlin.gradle.targets.native.KotilnNativeConfigureBinariesSideEffectKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.attributes.KlibPackaging$Companion           : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.tooling.core.Extras$Entry                                  : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.sources.InternalKotlinSourceSetKt            : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.Kapt3GradleSubplugin$Kapt3SubpluginContext : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinTargetArtifact                      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinJvmCompilerOptionsDefault_Decorated       : loaded 2 times (x 164B)
Class org.jetbrains.kotlin.gradle.plugin.HasKotlinDependencies                        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.internal.ClassLoadersCachingBuildService$Parameters_Decorated: loaded 2 times (x 130B)
Class org.jetbrains.kotlin.gradle.targets.native.toolchain.KotlinNativeBundleBuildService$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.BuildEventsListenerRegistryHolder_Decorated  : loaded 2 times (x 115B)
Class org.jetbrains.kotlin.gradle.plugin.StatisticsBuildFlowManager$subscribeForBuildResultAndConfigurationTimeMetrics$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.MetricContainer                   : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.old.Pre232IdeaKotlinBuildStatsMXBean: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinBuildStatsServicesRegistry  : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactoriesConfigurator   : loaded 2 times (x 72B)
Class com.google.common.collect.ImmutableMapEntrySet                                  : loaded 2 times (x 147B)
Class org.gradle.api.internal.classpath.Module                                        : loaded 2 times (x 66B)
Class build_agisdczp7kn8uyp03dp4zx2vi$_run_closure4                                   : loaded 2 times (x 136B)
Class de.undercouch.gradle.tasks.download.DownloadTaskPlugin                          : loaded 2 times (x 72B)
Class build_1st6b4zcxn0lddyz8z0am5m89$_run_closure3$_closure7                         : loaded 2 times (x 135B)
Class org.jetbrains.kotlin.gradle.targets.ConfigureNonPackedKlibConsumingSideEffectKt : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$getToolsJarFromJvm$1$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$ClasspathSnapshotProperties_Decorated: loaded 2 times (x 133B)
Class org.jetbrains.kotlin.gradle.internal.transforms.BuildToolsApiClasspathEntrySnapshotTransform$Parameters_Decorated: loaded 2 times (x 151B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationSourceSetInclusion$Companion$includedSourceSets$2: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinToolingDiagnosticsSetupActionKt$KotlinToolingDiagnosticsSetupAction$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.NpmDirectoryDependencyWithExternalsExtension: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinProjectSetupAction                     : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleFinishBuildHandler$Companion     : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.IsInIdeaSyncValueSource                    : loaded 2 times (x 74B)
Class com.google.common.cache.Weigher                                                 : loaded 2 times (x 66B)
Class org.gradle.api.internal.classpath.UnknownModuleException                        : loaded 2 times (x 78B)
Class build_agisdczp7kn8uyp03dp4zx2vi$_run_closure5                                   : loaded 2 times (x 135B)
Class build_1st6b4zcxn0lddyz8z0am5m89$_run_closure3$_closure8                         : loaded 2 times (x 135B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.MissingNativeStdlibChecker: loaded 2 times (x 70B)
Class io.invertase.gradle.build.ReactNativeShared$_applyDefaultExcludes_closure2$_closure5: loaded 2 times (x 137B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.InternalKotlinTargetComponentKt          : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.InternalKotlinTarget$DefaultImpls        : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycleImpl$executeCurrentStageAndScheduleNext$3: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationRegisterInSourceSetsConfigurator$configure$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinToolingDiagnosticsSetupActionKt$KotlinToolingDiagnosticsSetupAction$1$2: loaded 2 times (x 91B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.NpmDependencyWithExternalsExtension  : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinUsageContext                       : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinTargetsContainer                       : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.compilerRunner.GradleCliCommonizerKt                       : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.utils.ConfigurationsKt                              : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.StatisticsBuildFlowManager                   : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.BuildFusService$Parameters        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.PluginWrappersKt                             : loaded 2 times (x 67B)
Class com.google.common.base.Stopwatch                                                : loaded 2 times (x 68B)
Class com.google.common.cache.LocalCache$Strength                                     : loaded 2 times (x 77B)
Class com.google.common.cache.Cache                                                   : loaded 2 times (x 66B)
Class build_agisdczp7kn8uyp03dp4zx2vi$_run_closure6                                   : loaded 2 times (x 136B)
Class build_evl13vpxjidvxohlw1uueqdij$_run_closure3                                   : loaded 2 times (x 135B)
Class org.jetbrains.kotlin.build.report.metrics.BuildMetricsReporter                  : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.InternalKotlinTargetKt                   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinToolingDiagnosticsSetupActionKt$KotlinToolingDiagnosticsSetupAction$1$3: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.utils.NativeCompilerDownloader                      : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.targets.js.dsl.KotlinTargetWithNodeJsDsl            : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.sources.DefaultKotlinSourceSetKt$defaultSourceSetLanguageSettingsChecker$1: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.sources.InternalKotlinSourceSet              : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinJvmPlugin                              : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.SyncLanguageSettingsWithKotlinExtensionSetupActionKt$SyncLanguageSettingsWithKotlinExtensionSetupAction$1: loaded 2 times (x 91B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinGradlePluginPublicDsl                     : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativeProperties                : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.fus.internal.SynchronizedConfigurationMetrics       : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.statistics.BuildSessionLogger                              : loaded 2 times (x 73B)
Class org.jetbrains.kotlin.gradle.plugin.internal.ConfigurationCacheStartParameterAccessor: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.internal.diagnostics.GradleCompatibilityCheck$DefaultCurrentGradleVersionProvider: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinBaseExtension                             : loaded 2 times (x 66B)
Class com.google.common.util.concurrent.AbstractFuture$Cancellation                   : loaded 2 times (x 68B)
Class com.google.common.collect.ImmutableMap$Builder                                  : loaded 2 times (x 77B)
Class com.google.common.base.NullnessCasts                                            : loaded 2 times (x 67B)
Class build_evl13vpxjidvxohlw1uueqdij$_run_closure4                                   : loaded 2 times (x 135B)
Class io.invertase.gradle.build.ReactNativeShared                                     : loaded 2 times (x 89B)
Class org.jetbrains.kotlin.gradle.utils.FutureKt$future$2$1                           : loaded 2 times (x 91B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.statistics.plugins.ObservablePlugins;      : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.internal.BuildIdService$Companion$registerIfAbsent$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationDependencyConfigurationsFactoriesKt$WhenMappings: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$IOS_ARM64                         : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.targets.js.dsl.KotlinWasmTargetDsl                  : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.DisabledCinteropCommonizationInHmppProjectChecker$runChecks$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.plugin.sources.ConsistencyCheck                     : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.sources.DefaultKotlinSourceSetKt$defaultSourceSetLanguageSettingsChecker$2: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinCommonToolOptions                         : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.AbstractKotlinPlugin                         : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$Companion$wireJvmTargetToJvm$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.ComposePluginSuggestApplyChecker: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinMultiplatformExtension                    : loaded 2 times (x 705B)
Class org.jetbrains.kotlin.gradle.plugin.BuildFinishAndConfigurationTimeMetricsFlowAction: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.PropertiesProvider$booleanProvider$1         : loaded 2 times (x 70B)
Class com.google.common.collect.Maps$EntryFunction$1                                  : loaded 2 times (x 87B)
Class com.google.common.base.Present                                                  : loaded 2 times (x 77B)
Class com.google.common.collect.AbstractMapBasedMultimap$WrappedCollection$WrappedIterator: loaded 2 times (x 80B)
Class com.google.common.cache.RemovalListener                                         : loaded 2 times (x 66B)
Class org.gradle.api.internal.DefaultClassPathRegistry                                : loaded 2 times (x 72B)
Class build_agr3p375fzty99zrloowe6ju6$_run_closure2$_closure7                         : loaded 2 times (x 135B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.NpmDependencyExtensionKt$defaultNpmDependencyDelegate$1: loaded 2 times (x 163B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinTargetSoftwareComponentImplKt$KotlinTargetSoftwareComponent$1$1$configuration$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.DefaultPeerNpmDependencyExtension$delegate$1: loaded 2 times (x 163B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.NpmDependency                        : loaded 2 times (x 93B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.DefaultNpmDependencyExtension        : loaded 2 times (x 148B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.NpmDependency$Scope                  : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.sources.FragmentConsistencyChecks$unstableFeaturesCheck$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.sources.DefaultKotlinSourceSet               : loaded 2 times (x 137B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.AndroidSourceSetLayoutV1SourceSetsNotFoundChecker: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.utils.ConfigurationsKt$sam$org_gradle_api_Action$0  : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactories$Parameters    : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.internal.JavaExecTaskParametersCompatibility$Factory: loaded 2 times (x 66B)
Class com.google.common.reflect.Invokable$ConstructorInvokable                        : loaded 2 times (x 103B)
Class com.google.common.collect.Maps$EntryFunction$2                                  : loaded 2 times (x 87B)
Class com.google.common.primitives.IntsMethodsForWeb                                  : loaded 2 times (x 67B)
Class com.google.common.collect.Multimap                                              : loaded 2 times (x 66B)
Class org.gradle.api.internal.ClassPathRegistry                                       : loaded 2 times (x 66B)
Class org.gradle.internal.classpath.DefaultClassPath$ImmutableUniqueList              : loaded 2 times (x 195B)
Class build_6s7imafvclc6gm72hnzimmguy$_run_closure2                                   : loaded 2 times (x 136B)
Class org.jetbrains.kotlin.gradle.plugin.internal.state.TaskLoggers                   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilation$DefaultImpls               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.utils.ProviderApiUtilsKt$providerWithLazyConvention$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$Companion$predefinedTargets$2     : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.sources.FragmentConsistencyChecks$unstableFeaturesCheck$2: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycleImpl                    : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinToolingDiagnostics$WasmWasiEnvironmentNotChosenExplicitly: loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinNativeKlibArtifactKt$KotlinNativeKlibArtifact$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinCommonCompilerOptionsDefault              : loaded 2 times (x 92B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinCommonCompilerToolOptionsDefault          : loaded 2 times (x 80B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesLoader$isUseXcodeMessageStyleEnabled$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.internal.ClassLoadersCachingBuildService$Companion$registerIfAbsent$2$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinBuildStatisticsUtilsKt$collectGeneralConfigurationTimeMetrics$statisticOverhead$1$2: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.StatisticsBuildFlowManager_Decorated         : loaded 2 times (x 115B)
Class org.jetbrains.kotlin.gradle.report.BuildReportMode$Companion                    : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactories$VariantImplementationFactory: loaded 2 times (x 66B)
Class com.google.common.reflect.TypeToken                                             : loaded 2 times (x 69B)
Class com.google.common.collect.NullnessCasts                                         : loaded 2 times (x 67B)
Class com.google.common.collect.MapMakerInternalMap$Segment                           : loaded 2 times (x 138B)
Class com.google.common.base.ExtraObjectsMethodsForWeb                                : loaded 2 times (x 67B)
Class build_6s7imafvclc6gm72hnzimmguy$_run_closure3                                   : loaded 2 times (x 136B)
Class build_6s7imafvclc6gm72hnzimmguy$_run_closure2$_closure13$_closure18             : loaded 2 times (x 136B)
Class build_eupcmxgtu9q3uy0v8rg9z8i7j$_run_closure4$_closure26$_closure39$_closure40  : loaded 2 times (x 136B)
Class build_9kn57pj55amg75ewdgalkhlsr$getExternalLibVersion$0                         : loaded 2 times (x 148B)
Class io.invertase.gradle.common.Utilities$isGradleVersionLT                          : loaded 2 times (x 142B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.ApplyUserDefinedAttributesKt$UserDefinedAttributesSetupAction$1$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.compilerRunner.CompilerSystemPropertiesService             : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationImpl$1  : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.SemVer$Companion                     : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$LINUX_ARM64                       : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$LINUX_X64                         : loaded 2 times (x 74B)
Class [Lorg.jetbrains.kotlin.konan.target.Architecture;                               : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.targets.native.SetupEmbedAndSignAppleFrameworkTaskSideEffectKt$special$$inlined$KotlinTargetSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinUsages$KotlinCinteropCompatibility : loaded 2 times (x 73B)
Class org.jetbrains.kotlin.gradle.dsl.ExplicitApiMode                                 : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.utils.GradleAttributesContainerUtilsKt              : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactories$Companion     : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.internal.CustomPropertiesFileValueSource$Parameters_Decorated: loaded 2 times (x 130B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinTopLevelExtensionConfig                   : loaded 2 times (x 66B)
Class com.google.common.cache.RemovalCause                                            : loaded 2 times (x 76B)
Class com.google.common.io.ByteSink                                                   : loaded 2 times (x 72B)
Class com.google.common.cache.CacheBuilder                                            : loaded 2 times (x 68B)
Class com.google.common.collect.SingletonImmutableList                                : loaded 2 times (x 203B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycleImpl$launch$coroutine$1 : loaded 2 times (x 72B)
Class org.gradle.internal.classpath.ClassPath                                         : loaded 2 times (x 66B)
Class org.gradle.api.UncheckedIOException                                             : loaded 2 times (x 78B)
Class build_6s7imafvclc6gm72hnzimmguy$_run_closure4                                   : loaded 2 times (x 135B)
Class build_6s7imafvclc6gm72hnzimmguy$_run_closure2$_closure13$_closure19             : loaded 2 times (x 135B)
Class org.jetbrains.kotlin.gradle.incremental.IncrementalModuleInfoProvider           : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationImpl$2  : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.dsl.PlatformSourceSetConventionsChecker$runChecks$1 : loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationProcessorSideEffectKt$KotlinCompilationProcessorSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinJvmCompilerOptionsDefault                 : loaded 2 times (x 102B)
Class org.jetbrains.kotlin.gradle.internal.UsesClassLoadersCachingBuildService        : loaded 2 times (x 66B)
Class [Lcom.google.common.reflect.Types$JavaVersion;                                  : loaded 2 times (x 65B)
Class com.google.common.collect.Interners$InternerBuilder                             : loaded 2 times (x 72B)
Class com.google.common.io.Files$FileByteSource                                       : loaded 2 times (x 81B)
Class com.google.common.collect.ImmutableList$ReverseImmutableList                    : loaded 2 times (x 204B)
Class com.google.common.base.JdkPattern                                               : loaded 2 times (x 71B)
Class build_6k1vuntnni446s9ebkvsvp2hk$getExtOrDefault                                 : loaded 2 times (x 148B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinStdlibConfigurationMetrics$collectMetrics$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.compilerRunner.KotlinCompilerArgumentsLogLevel$Companion   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.DefaultKotlinBasePlugin$addKotlinCompilerConfiguration$3$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.AbstractKotlinCompile$defaultKotlinJavaToolchain$1: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.daemon.common.MultiModuleICSettings                        : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.cli.common.arguments.Freezable                             : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.internal.transforms.BuildToolsApiClasspathEntrySnapshotTransform$Parameters: loaded 2 times (x 83B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilationInfoKt                      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile                                 : loaded 2 times (x 494B)
Class org.jetbrains.kotlin.gradle.plugin.AbstractKotlinPluginWrapper                  : loaded 2 times (x 82B)
Class org.jetbrains.kotlin.gradle.internal.StdlibDependencyManagementKt               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.PlatformSourceSetConventionsChecker$runChecks$2 : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$MINGW_X64                         : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.utils.ObservableSet                                 : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinToolingDiagnosticsCollectorKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.HierarchyAttributeContainer              : loaded 2 times (x 79B)
Class org.jetbrains.kotlin.gradle.tasks.UsesKotlinJavaToolchain                       : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.DefaultKotlinBasePlugin$apply$1              : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.statistics.metrics.StringOverridePolicy$OVERRIDE           : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$Params_Decorated: loaded 2 times (x 130B)
Class org.objectweb.asm.SymbolTable$Entry                                             : loaded 2 times (x 70B)
Class com.google.errorprone.annotations.DoNotMock                                     : loaded 2 times (x 66B)
Class build_agisdczp7kn8uyp03dp4zx2vi$_run_closure4$_closure7$_closure15              : loaded 2 times (x 135B)
Class build_eupcmxgtu9q3uy0v8rg9z8i7j$_run_closure4$_closure24$_closure36             : loaded 2 times (x 136B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinTargetComponentWithCoordinatesAndPublication: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.statistics.metrics.IMetricContainer                        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.incremental.UsesIncrementalModuleInfoBuildService   : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$ANDROID_X86                       : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.dsl.PlatformSourceSetConventionsChecker$runChecks$3 : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.RegisterKotlinPluginExtensionsKt             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.resources.resolve.KotlinTargetResourcesResolutionStrategy: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.utils.CompilerOptionsKt$configureExperimentalTryNext$1$1: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.utils.CompilerOptionsKt                             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.utils.SingleAction                                  : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.plugin.PropertiesProvider$Companion                 : loaded 2 times (x 67B)
Class org.objectweb.asm.Edge                                                          : loaded 2 times (x 68B)
Class com.google.common.base.Suppliers$SupplierOfInstance                             : loaded 2 times (x 75B)
Class com.google.common.collect.Iterators$1                                           : loaded 2 times (x 77B)
Class Build_gradle$2$1                                                                : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.internal.KotlinDomApiDependencyManagementKt         : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.CInteropProcess                               : loaded 2 times (x 343B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$defaultKotlinJavaToolchain$1    : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinJavaToolchain$JavaToolchainSetter       : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.internal.utils.CollectionsKt                        : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.utils.NativeCompilerDownloader$Companion            : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.PlatformSourceSetConventionsChecker$runChecks$4 : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.JsLikeEnvironmentChecker$runChecks$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.UsesKotlinToolingDiagnostics     : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.KonanHomeConflictDeclarationChecker: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinToolingDiagnostics$WasmJsEnvironmentNotChosenExplicitly: loaded 2 times (x 69B)
Class org.jetbrains.kotlin.tooling.core.ExtrasPropertyKt$extrasLazyProperty$1         : loaded 2 times (x 90B)
Class org.jetbrains.kotlin.statistics.metrics.BooleanAnonymizationPolicy$SAFE         : loaded 2 times (x 81B)
Class com.google.common.reflect.TypeToken$1                                           : loaded 2 times (x 106B)
Class com.google.common.cache.LocalCache$ComputingValueReference                      : loaded 2 times (x 92B)
Class com.google.common.collect.SingletonImmutableSet                                 : loaded 2 times (x 142B)
Class com.google.common.collect.ImmutableMap$IteratorBasedImmutableMap                : loaded 2 times (x 121B)
Class build_eupcmxgtu9q3uy0v8rg9z8i7j$_run_closure4$_closure33$_closure43             : loaded 2 times (x 136B)
Class build_eupcmxgtu9q3uy0v8rg9z8i7j$_run_closure4$_closure26$_closure39$_closure40$_closure41: loaded 2 times (x 136B)
Class io.invertase.gradle.build.ReactNativeModule$_applyAndroidVersions_closure2$_closure6: loaded 2 times (x 139B)
Class org.jetbrains.kotlin.gradle.internal.KotlinDependenciesManagementKt$configureDefaultVersionsResolutionStrategy$1$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.DefaultKotlinCompilationSourceSetsContainer$allKotlinSourceSetsImpl$1$1: loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.DefaultKotlinCompilationSourceSetsContainer: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$TVOS_SIMULATOR_ARM64              : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$Companion                         : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycle$Stage$Companion        : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.DeprecatedKotlinNativeTargetsChecker: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.targets.jvm.KotlinJvmCompilationWireJavaSourcesSideEffectKt$KotlinJvmCompilationWireJavaSourcesSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.targets.native.ConfigureFrameworkExportSideEffectKt$special$$inlined$KotlinTargetSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.tooling.core.AbstractExtras                                : loaded 2 times (x 137B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesLoader$shouldUseEmbeddableCompilerJar$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.fus.BuildUidService$Companion$registerIfAbsent$1    : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.internal.diagnostics.AgpCompatibilityCheck$AndroidGradlePluginVersionProvider: loaded 2 times (x 66B)
Class com.google.common.reflect.TypeToken$2                                           : loaded 2 times (x 106B)
Class com.google.common.cache.LocalCache$Segment                                      : loaded 2 times (x 150B)
Class org.jetbrains.kotlin.build.report.metrics.BuildTimes                            : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$ClasspathSnapshotProperties     : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinBuildStatsLoggerService$initSessionLogger$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.internal.StdlibDependencyManagementKt$addStdlibDependency$1$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$2$1     : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.TransformActionUsingKotlinToolingDiagnostics: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.dsl.PlatformSourceSetConventionsChecker$runChecks$6 : loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.plugin.sources.DefaultKotlinSourceSet_Decorated     : loaded 2 times (x 188B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinCompilationsKt                     : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.utils.GradleUtilsKt$registerClassLoaderScopedBuildService$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.targets.native.KonanPropertiesBuildService$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.statistics.metrics.StringOverridePolicy                    : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.DefaultKotlinBuildStatsBeanService: loaded 2 times (x 88B)
Class org.jetbrains.kotlin.gradle.plugin.PropertiesProvider$propertyWithDeprecatedValues$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.internal.diagnostics.AgpCompatibilityCheck$AndroidGradlePluginVersionProvider$Default: loaded 2 times (x 70B)
Class [Lorg.objectweb.asm.SymbolTable$Entry;                                          : loaded 2 times (x 65B)
Class com.google.common.collect.SortedIterable                                        : loaded 2 times (x 66B)
Class com.google.common.cache.CacheLoader                                             : loaded 2 times (x 70B)
Class com.google.common.collect.Iterators$4                                           : loaded 2 times (x 78B)
Class com.google.common.collect.ImmutableBiMap                                        : loaded 2 times (x 139B)
Class com.google.common.collect.AbstractListMultimap                                  : loaded 2 times (x 168B)
Class org.gradle.internal.installation.GradleInstallation$1                           : loaded 2 times (x 71B)
Class org.gradle.api.internal.classpath.ModuleRegistry                                : loaded 2 times (x 66B)
Class build_eupcmxgtu9q3uy0v8rg9z8i7j$_run_closure4$_closure30                        : loaded 2 times (x 135B)
Class io.invertase.gradle.build.ReactNativeModule$_applyReactNativeDependency_closure5: loaded 2 times (x 140B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycle$ProjectConfigurationResult: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.KotlinDependenciesManagementKt$configureDefaultVersionsResolutionStrategy$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.internal.StdlibDependencyManagementKt$configureStdlibDefaultDependency$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.utils.ConfigurationCacheOpaqueValue                 : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.model.builder.KotlinModelBuilder$Companion          : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCreateResourcesTaskSideEffectKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.native.internal.SetupKotlinNativePlatformDependenciesAndStdlibKt: loaded 2 times (x 67B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.mpp.resources.resolve.KotlinTargetResourcesResolutionStrategy;: loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinUsages                             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesLoader$Companion: loaded 2 times (x 67B)
Class [Lorg.jetbrains.kotlin.build.report.metrics.GradleBuildPerformanceMetric;       : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinBuildStatsBeanService$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.BuildFusService$Companion         : loaded 2 times (x 67B)
Class com.google.common.util.concurrent.internal.InternalFutureFailureAccess          : loaded 2 times (x 68B)
Class com.google.common.collect.Iterators$5                                           : loaded 2 times (x 78B)
Class com.google.common.collect.ImmutableSet$JdkBackedSetBuilderImpl                  : loaded 2 times (x 72B)
Class com.google.common.base.Splitter$SplittingIterator                               : loaded 2 times (x 80B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinVariantWithCoordinates             : loaded 2 times (x 104B)
Class build_eupcmxgtu9q3uy0v8rg9z8i7j$_run_closure4$_closure31                        : loaded 2 times (x 135B)
Class build_86psz0zg5ccmkipscnjtptl6f$_run_closure2$_closure9$_closure11              : loaded 2 times (x 136B)
Class org.jetbrains.kotlin.gradle.plugin.HasCompilerOptions                           : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.DefaultKotlinCompilationFriendPathsResolver$FriendArtifactResolver: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$WATCHOS_SIMULATOR_ARM64           : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.targets.js.ir.KotlinJsIrTarget                      : loaded 2 times (x 258B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.DefaultKotlinUsageContext                : loaded 2 times (x 104B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycleImpl$start$3            : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinJvmExtension                              : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinMultiplatformPluginWrapper             : loaded 2 times (x 82B)
Class [Lorg.jetbrains.kotlin.statistics.metrics.BooleanMetrics;                       : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$Companion: loaded 2 times (x 67B)
Class com.google.common.collect.Iterators$6                                           : loaded 2 times (x 77B)
Class com.google.common.cache.LocalCache$LocalManualCache$1                           : loaded 2 times (x 71B)
Class com.google.common.base.CharMatcher$ForPredicate                                 : loaded 2 times (x 108B)
Class build_6k1vuntnni446s9ebkvsvp2hk$_run_closure2                                   : loaded 2 times (x 135B)
Class build_eupcmxgtu9q3uy0v8rg9z8i7j$_run_closure4$_closure32                        : loaded 2 times (x 136B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationConfigurationsContainer: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.internal.StdlibDependencyManagementKt$addStdlibDependency$1: loaded 2 times (x 71B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycle$CoroutineStart;      : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.dsl.IosSourceSetConventionChecker                   : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinGradleProjectChecker       : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinMultiplatformSourceSetConventions         : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.sources.KotlinSourceSetFactory$Companion     : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.statistics.metrics.NumberOverridePolicy$SUM                : loaded 2 times (x 81B)
Class com.google.common.collect.ImmutableCollection                                   : loaded 2 times (x 121B)
Class com.google.common.base.CharMatcher$AnyOf                                        : loaded 2 times (x 108B)
Class org.gradle.api.internal.classpath.DefaultModuleRegistry                         : loaded 2 times (x 82B)
Class build_6k1vuntnni446s9ebkvsvp2hk$_run_closure3                                   : loaded 2 times (x 136B)
Class build_eupcmxgtu9q3uy0v8rg9z8i7j$_run_closure4$_closure33                        : loaded 2 times (x 136B)
Class build_9kn57pj55amg75ewdgalkhlsr$safeExtGet                                      : loaded 2 times (x 148B)
Class org.jetbrains.kotlin.compilerRunner.KotlinCompilerArgumentsLogLevel             : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.internal.state.TaskExecutionResults          : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.CompositePostConfigure: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.targets.native.internal.CInteropConfigurationsKt    : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.BuildFusService$Parameters$Inject : loaded 2 times (x 108B)
Class org.jetbrains.kotlin.build.report.metrics.GradleBuildTime$Companion             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.statistics.metrics.StatisticsValuesConsumer                : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.AndroidGradlePluginVersion$Companion         : loaded 2 times (x 67B)
Class com.google.common.util.concurrent.ListenableFuture                              : loaded 2 times (x 66B)
Class com.google.common.cache.ReferenceEntry                                          : loaded 2 times (x 66B)
Class com.google.common.collect.ImmutableSet$CachingAsList                            : loaded 2 times (x 143B)
Class com.google.common.base.Converter                                                : loaded 2 times (x 86B)
Class com.google.common.collect.Lists                                                 : loaded 2 times (x 67B)
Class de.undercouch.gradle.tasks.download.Verify                                      : loaded 2 times (x 326B)
Class build_9kn57pj55amg75ewdgalkhlsr$_run_closure3$_closure8                         : loaded 2 times (x 135B)
Class io.invertase.gradle.build.ReactNativePlugin                                     : loaded 2 times (x 92B)
Class org.jetbrains.kotlin.gradle.plugin.DefaultKotlinBasePlugin$addKotlinCompilerConfiguration$2$1$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompilerExecutionStrategy$Companion     : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.CompilerPluginConfig                         : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.model.builder.KotlinModelBuilder                    : loaded 2 times (x 72B)
Class [Lorg.jetbrains.kotlin.tooling.core.HasMutableExtras;                           : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.GradleDeprecatedPropertyChecker: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinJsKlibArtifactKt$KotlinJsKlibArtifact$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.targets.CreateTargetConfigurationsSideEffectKt      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.ApplyUserDefinedAttributesKt$UserDefinedAttributesSetupAction$1: loaded 2 times (x 91B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinAndroidTarget                      : loaded 2 times (x 161B)
Class org.jetbrains.kotlin.gradle.plugin.DefaultKotlinBasePlugin$addKotlinCompilerConfiguration$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.NonSynchronizedMetricsContainer   : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.gradle.plugin.internal.DefaultCompatibilityConventionRegistrar: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.PropertiesProvider                           : loaded 2 times (x 69B)
Class com.google.common.util.concurrent.AbstractFuture                                : loaded 2 times (x 102B)
Class com.google.common.base.FunctionalEquivalence                                    : loaded 2 times (x 79B)
Class com.google.common.collect.Iterators$9                                           : loaded 2 times (x 77B)
Class com.google.common.collect.AbstractMapBasedMultimap                              : loaded 2 times (x 135B)
Class com.google.common.collect.ImmutableAsList                                       : loaded 2 times (x 205B)
Class build_9kn57pj55amg75ewdgalkhlsr$_run_closure3$_closure9                         : loaded 2 times (x 136B)
Class io.invertase.gradle.build.ReactNativeModule                                     : loaded 2 times (x 89B)
Class [Lorg.jetbrains.kotlin.gradle.dsl.jvm.JvmTargetValidationMode;                  : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$javaVersion$1      : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.MppSourcesJarKt$sourcesJarTask$1         : loaded 2 times (x 91B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory$KotlinCompilationOutputFactory: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.konan.target.TargetSupportException                        : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.gradle.internal.IsInIdeaEnvironmentValueSource             : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilationToRunnableFiles             : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.tooling.BuildKotlinToolingMetadataTaskKt            : loaded 2 times (x 67B)
Class build_evl13vpxjidvxohlw1uueqdij$_run_closure2$_closure6                         : loaded 2 times (x 135B)
Class org.jetbrains.kotlin.gradle.targets.native.toolchain.KotlinNativeBundleBuildService$Companion$registerIfAbsent$2$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.statistics.BuildSessionLogger$Companion                    : loaded 2 times (x 67B)
Class org.objectweb.asm.RecordComponentVisitor                                        : loaded 2 times (x 73B)
Class com.google.common.reflect.TypeResolver$TypeTable                                : loaded 2 times (x 69B)
Class com.google.common.cache.CacheLoader$SupplierToCacheLoader                       : loaded 2 times (x 71B)
Class build_agisdczp7kn8uyp03dp4zx2vi$_run_closure4$_closure13$_closure17$_closure18  : loaded 2 times (x 136B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.SemverKt                             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.StdlibDependencyManagementKt$isStdlibAddedByUser$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinCompilationsKt$sam$java_util_concurrent_Callable$0: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompilerPluginData                      : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.MppSourcesJarKt                          : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.compilerRunner.CompilerSystemPropertiesService$Companion$registerIfAbsent$2$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilationInfo                        : loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.tasks.BaseKotlinCompile                             : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.internal.UsesBuildIdProviderService          : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationSourceSetInclusion$include$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory$KotlinCompilationDependencyConfigurationsFactory: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinProjectExtension$jvmToolchain$1           : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycleCoroutineContextElement$Key: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinToolingDiagnosticsSetupActionKt$KotlinToolingDiagnosticsSetupAction$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.tooling.core.ExtrasPropertyKt                              : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinProjectExtensionKt                        : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.ClassLoadersCachingBuildService$Companion$registerIfAbsent$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleBuildServices$Parameters_Decorated: loaded 2 times (x 130B)
Class org.jetbrains.kotlin.gradle.plugin.DefaultKotlinBasePlugin$addKotlinCompilerConfiguration$3: loaded 2 times (x 71B)
Class [Lorg.jetbrains.kotlin.statistics.metrics.IMetricContainerFactory;              : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.internal.IdeaKt                                     : loaded 2 times (x 67B)
Class com.google.common.reflect.TypeResolver$TypeTable$1                              : loaded 2 times (x 69B)
Class com.google.common.collect.UsingToStringOrdering                                 : loaded 2 times (x 110B)
Class com.google.common.collect.Maps$EntryTransformer                                 : loaded 2 times (x 66B)
Class org.gradle.internal.service.ServiceLookupException                              : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.gradle.internal.StdlibDependencyManagementKt$isStdlibAddedByUser$2: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.tasks.GradleCompileTaskProvider_Decorated           : loaded 2 times (x 117B)
Class org.jetbrains.kotlin.gradle.plugin.hierarchy.KotlinSourceSetTreeClassifierKt$sourceSetTreeClassifier$2: loaded 2 times (x 74B)
Class [Lorg.jetbrains.kotlin.tooling.core.KotlinToolingVersion$Maturity;              : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.konan.target.HostManager                                   : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.NpmDirectoryDependencyExtension      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinUsageContext$MavenScope            : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.targets.native.KotilnNativeConfigureBinariesSideEffectKt$special$$inlined$KotlinTargetSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinProjectSetupAction$Companion           : loaded 2 times (x 67B)
Class com.google.common.collect.ImmutableSortedSet                                    : loaded 2 times (x 295B)
Class [Lcom.google.common.cache.LocalCache$EntryFactory;                              : loaded 2 times (x 65B)
Class org.gradle.internal.classloader.ClassLoaderFactory                              : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$currentJvmJdkToolsJar$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.SubpluginEnvironment                         : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.internal.StdlibDependencyManagementKt$addStdlibDependency$1$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.sources.AbstractKotlinSourceSet              : loaded 2 times (x 137B)
Class org.jetbrains.kotlin.gradle.tooling.BuildKotlinToolingMetadataTaskKt$RegisterBuildKotlinToolingMetadataTask$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.compilerRunner.GradleCliCommonizerKt$maybeCreateCommonizerClasspathConfiguration$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesLoader$nativeCacheKind$1: loaded 2 times (x 71B)
Class [Lorg.jetbrains.kotlin.build.report.metrics.ValueType;                          : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.internal.diagnostics.AgpCompatibilityCheck          : loaded 2 times (x 67B)
Class com.google.common.collect.ImmutableMap$SerializedForm                           : loaded 2 times (x 69B)
Class com.google.common.cache.LocalCache$LocalManualCache                             : loaded 2 times (x 95B)
Class com.google.common.base.CharMatcher$IsNot                                        : loaded 2 times (x 107B)
Class com.google.common.base.CommonPattern                                            : loaded 2 times (x 70B)
Class org.gradle.internal.service.CachingServiceLocator                               : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.gradle.dsl.jvm.JvmTargetValidationMode                     : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.BuildFinishedListenerService$Companion$registerIfAbsent$2$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinTargetConfiguratorKt$WhenMappings      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.KotlinTestDependencyManagementKt$configureKotlinTestDependency$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.tooling.core.KotlinToolingVersionKt                        : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.OverriddenKotlinNativeHomeChecker$StdlibExistenceCheckerValueSource$Params_Decorated: loaded 2 times (x 130B)
Class org.jetbrains.kotlin.project.model.LanguageSettings                             : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycleKt$kotlinPluginLifecycle$2: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.targets.native.KonanPropertiesBuildService$Parameters: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.BuildFusService$Companion$registerIfAbsent$1$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.internal.CompatibilityConventionRegistrar$Factory: loaded 2 times (x 66B)
Class com.google.common.collect.AbstractMapBasedMultimap$KeySet                       : loaded 2 times (x 133B)
Class com.google.common.collect.RegularImmutableSortedSet                             : loaded 2 times (x 295B)
Class com.google.common.math.IntMath                                                  : loaded 2 times (x 67B)
Class io.invertase.gradle.build.ReactNativeShared$_applyPackageVersion_closure3$_closure6: loaded 2 times (x 140B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinMetadataTarget                     : loaded 2 times (x 163B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.CheckKotlinGradlePluginConfigurationErrorsKt$locateOrRegisterCheckKotlinGradlePluginErrorsTask$taskProvider$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.InternalKotlinCompilation$DefaultImpls   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationSourceSetInclusion$DefaultAddSourcesToCompileTask: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.internal.KotlinTestDependencyManagementKt$configureKotlinTestDependency$2: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.dsl.PlatformSourceSetConventionsChecker$runChecks$$inlined$CheckedPlatformInfo$default$1: loaded 2 times (x 74B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycle$Stage;               : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinTargetArtifact$Companion            : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCreateLifecycleTasksSideEffectKt$KotlinCreateLifecycleTasksSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.targets.metadata.KotlinMetadataTargetConfiguratorKt$metadataCompilationsCreated$2: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinGradlePluginDsl                           : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.tooling.core.MutableExtras                                 : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleBuildServices$detectKotlinPluginLoadedInMultipleProjects$onRegister$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.BuildFinishAndConfigurationTimeMetricsFlowAction$Parameters_Decorated: loaded 2 times (x 139B)
Class org.jetbrains.kotlin.gradle.plugin.BuildFinishAndConfigurationTimeMetricsFlowAction$Parameters: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.build.report.metrics.BuildTime                             : loaded 2 times (x 66B)
Class com.google.common.reflect.Types$TypeVariableInvocationHandler                   : loaded 2 times (x 71B)
Class com.google.common.collect.MapMakerInternalMap$InternalEntry                     : loaded 2 times (x 66B)
Class com.google.common.collect.ImmutableMapEntry                                     : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.CheckKotlinGradlePluginConfigurationErrorsKt$locateOrRegisterCheckKotlinGradlePluginErrorsTask$taskProvider$1$2: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.tasks.CompileUsingKotlinDaemon                      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinWithJavaTarget                     : loaded 2 times (x 162B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCreateNativeCompileTasksSideEffectKt$special$$inlined$KotlinCompilationSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.targets.KotlinTargetSideEffect$Companion            : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinSingleJavaTargetExtension                 : loaded 2 times (x 120B)
Class org.jetbrains.kotlin.gradle.utils.StoredPropertyKt$extrasStoredProperty$1       : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycle$IllegalLifecycleException: loaded 2 times (x 78B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesLoader$userProvidedNativeHome$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleFinishBuildHandler               : loaded 2 times (x 68B)
Class [Lorg.jetbrains.kotlin.build.report.metrics.BuildPerformanceMetric;             : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.statistics.metrics.StringOverridePolicy$CONCAT             : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinTopLevelExtension                         : loaded 2 times (x 66B)
Class com.google.common.base.Predicates                                               : loaded 2 times (x 67B)
Class com.google.common.collect.ImmutableMultimap                                     : loaded 2 times (x 143B)
Class com.google.common.base.Equivalence                                              : loaded 2 times (x 78B)
Class com.google.common.collect.ImmutableMapEntry$NonTerminalImmutableMapEntry        : loaded 2 times (x 81B)
Class com.google.common.base.Function                                                 : loaded 2 times (x 66B)
Class io.invertase.gradle.build.ReactNativeModule$_applyAndroidVersions_closure2      : loaded 2 times (x 139B)
Class org.jetbrains.kotlin.gradle.plugin.ProjectLocalConfigurationsKt                 : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.internal.KotlinDomApiDependencyManagementKt$addKotlinDomApiDependency$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.KotlinTargetAlreadyDeclaredChecker$runChecks$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinTargetWithBinaries                 : loaded 2 times (x 159B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.KotlinSourceSet;                           : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.JsEnvironmentChecker$1  : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinToolingDiagnostics$JsLikeEnvironmentNotChosenExplicitly: loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinTargetsContainerWithPresets            : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.utils.StoredLazyProperty$getValue$1                 : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.statistics.metrics.IMetricContainerFactory                 : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.statistics.MetricValueValidationFailed                     : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.gradle.plugin.PropertiesProvider$localProperties$2         : loaded 2 times (x 75B)
Class com.google.common.collect.IndexedImmutableSet$1                                 : loaded 2 times (x 208B)
Class com.google.common.cache.CacheBuilder$OneWeigher                                 : loaded 2 times (x 78B)
Class com.google.common.collect.RegularImmutableList                                  : loaded 2 times (x 208B)
Class build_6s7imafvclc6gm72hnzimmguy$_run_closure2$_closure10                        : loaded 2 times (x 135B)
Class de.undercouch.gradle.tasks.download.VerifyExtension                             : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.DefaultKotlinCompilationFriendPathsResolver$resolveFriendPaths$1$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.build.report.metrics.BuildMetricsReporterImpl              : loaded 2 times (x 94B)
Class org.jetbrains.kotlin.statistics.BuildSessionLogger$Companion$listProfileFiles$lambda$3$$inlined$sortedBy$1: loaded 2 times (x 85B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.KaptGenerateStubsConfig         : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$1$classpathConfiguration$jvmToolchain$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.SemVer                               : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesLoader$konanDataDirProperty$1$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.JsEnvironmentChecker$2  : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.utils.WhenPluginEnabledKt$whenPluginsEnabled$1      : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.AbstractKotlinCompileTool                     : loaded 2 times (x 362B)
Class org.jetbrains.kotlin.gradle.internal.report.BuildScanApi                        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.FlowActionBuildFusService$Companion$registerIfAbsentImpl$1$1: loaded 2 times (x 70B)
Class [Lorg.jetbrains.kotlin.gradle.report.BuildReportType;                           : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinBuildStatsConfiguration     : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$localProperties$2: loaded 2 times (x 75B)
Class com.google.common.reflect.Types$ClassOwnership$1LocalClass                      : loaded 2 times (x 67B)
Class com.google.common.collect.Maps$EntryFunction                                    : loaded 2 times (x 87B)
Class build_9kn57pj55amg75ewdgalkhlsr$_shouldUseCommonInterfaceFromReanimated_closure6: loaded 2 times (x 135B)
Class org.gradle.internal.classloader.ClasspathUtil                                   : loaded 2 times (x 67B)
Class build_agisdczp7kn8uyp03dp4zx2vi$_run_closure4$_closure10                        : loaded 2 times (x 136B)
Class build_6s7imafvclc6gm72hnzimmguy$_run_closure2$_closure11                        : loaded 2 times (x 135B)
Class io.invertase.gradle.build.ProjectExtension$getVersionsRoot$2                    : loaded 2 times (x 148B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinCompile                                   : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.DefaultKotlinCompilationFriendPathsResolver$DefaultFriendArtifactResolver: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.InternalGradlePropertiesUsageChecker$runChecks$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.utils.StringUtilsKt$lowerCamelCaseName$1            : loaded 2 times (x 121B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.JsEnvironmentChecker$3  : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinLegacyMetadataArtifactKt            : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.JvmTarget$Companion                             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesLoader$downloadFromMaven$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.internal.DefaultConfigurationCacheStartParameterAccessor_Decorated: loaded 2 times (x 121B)
Class org.jetbrains.kotlin.gradle.plugin.internal.DefaultConfigurationCacheStartParameterAccessor: loaded 2 times (x 73B)
Class org.jetbrains.kotlin.tooling.core.HasExtras                                     : loaded 2 times (x 66B)
Class com.google.common.util.concurrent.Uninterruptibles                              : loaded 2 times (x 67B)
Class build_agisdczp7kn8uyp03dp4zx2vi$_run_closure4$_closure11                        : loaded 2 times (x 135B)
Class build_6s7imafvclc6gm72hnzimmguy$_run_closure2$_closure12                        : loaded 2 times (x 135B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilationInfo$TCS$friendPaths$1      : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.statistics.metrics.ConcatMetricContainer$Companion         : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.fus.internal.SynchronizedConfigurationMetrics$add$1 : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.sources.DefaultLanguageSettingsBuilder$freeCompilerArgsProvider$1: loaded 2 times (x 91B)
Class org.jetbrains.kotlin.konan.target.HostManager$Companion                         : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.DevNpmDependencyExtension            : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.PreHmppDependenciesUsageChecker: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.targets.CreateArtifactsSideEffectKt                 : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.AbstractKotlinMultiplatformPluginWrapper     : loaded 2 times (x 82B)
Class org.jetbrains.kotlin.gradle.utils.GradleConfigurationUtilsKt$addGradlePluginMetadataAttributes$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.internal.DefaultProjectIsolationStartParameterAccessor: loaded 2 times (x 73B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinAndroidExtension                          : loaded 2 times (x 66B)
Class com.google.common.io.CharSource                                                 : loaded 2 times (x 81B)
Class com.google.common.collect.Maps                                                  : loaded 2 times (x 67B)
Class org.gradle.api.specs.Spec                                                       : loaded 2 times (x 66B)
Class build_agisdczp7kn8uyp03dp4zx2vi$_run_closure4$_closure12                        : loaded 2 times (x 135B)
Class build_6s7imafvclc6gm72hnzimmguy$_run_closure2$_closure13                        : loaded 2 times (x 136B)
Class build_eupcmxgtu9q3uy0v8rg9z8i7j$_run_closure13$_closure47                       : loaded 2 times (x 136B)
Class io.invertase.gradle.build.ReactNativeShared$_applyPackageVersion_closure3       : loaded 2 times (x 140B)
Class org.jetbrains.kotlin.gradle.fus.internal.SynchronizedConfigurationMetrics$add$2 : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$disableMultiModuleIC$2          : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.GradleCompileTaskProviderKt                   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.SubpluginEnvironment$Companion               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.ApplyUserDefinedAttributesKt$UserDefinedAttributesSetupAction$1$1$3: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationImpl$Params: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.konan.target.UtilsKt                                       : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tooling.BuildKotlinToolingMetadataTask              : loaded 2 times (x 313B)
Class org.jetbrains.kotlin.gradle.plugin.internal.CompatibilityConventionRegistrarKt  : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinJvmJarArtifactKt                    : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.CreateCInteropTasksSideEffectKt$special$$inlined$KotlinCompilationSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.FinalizeConfigurationFusMetricActionKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.resources.resolve.KotlinTargetResourcesResolutionStrategy$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.native.toolchain.KotlinNativeBundleBuildService$Companion$registerIfAbsent$2$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.build.report.metrics.GradleBuildPerformanceMetric$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinBuildStatsBeanService       : loaded 2 times (x 83B)
Class org.jetbrains.kotlin.gradle.plugin.internal.MavenPublicationComponentAccessor$Factory: loaded 2 times (x 66B)
Class build_6t0aaf1vxqnq7aqxdnrvcooc8                                                 : loaded 2 times (x 176B)
Class com.google.common.reflect.Types$JavaVersion                                     : loaded 2 times (x 79B)
Class com.google.common.collect.MapMaker                                              : loaded 2 times (x 68B)
Class [Lcom.google.common.collect.ImmutableEntry;                                     : loaded 2 times (x 65B)
Class build_agisdczp7kn8uyp03dp4zx2vi$_run_closure4$_closure13                        : loaded 2 times (x 136B)
Class de.undercouch.gradle.tasks.download.VerifyExtension_Decorated                   : loaded 2 times (x 117B)
Class build_eupcmxgtu9q3uy0v8rg9z8i7j$_run_closure13$_closure48                       : loaded 2 times (x 136B)
Class build_eupcmxgtu9q3uy0v8rg9z8i7j$_run_closure4$_closure25$_closure38             : loaded 2 times (x 135B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig$1$1 : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$kotlinOptions$1                 : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.DaemonReuseCounter                : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativeProperties$Companion      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.utils.StringUtilsKt                                 : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.ConfigureBuildSideEffectKt$ConfigureBuildSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.tooling.core.ExtrasLazyProperty                            : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.utils.CompletableFuture                             : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.build.report.metrics.BuildPerformanceMetric                : loaded 2 times (x 66B)
Class [Lorg.jetbrains.kotlin.statistics.metrics.StringOverridePolicy;                 : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.internal.JavaExecTaskParametersCompatibility : loaded 2 times (x 66B)
Class com.google.common.cache.LocalCache$LoadingValueReference                        : loaded 2 times (x 92B)
Class build_agisdczp7kn8uyp03dp4zx2vi$_run_closure4$_closure14                        : loaded 2 times (x 135B)
Class build_agisdczp7kn8uyp03dp4zx2vi$_run_closure4$_closure7                         : loaded 2 times (x 135B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig$getClasspathSnapshotDir$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig$1$2 : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.ConfigurationMetricsBuildFusParameters: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfigKt$explicitApiMode$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.OverriddenKotlinNativeHomeChecker$StdlibExistenceCheckerValueSource$Params$Inject: loaded 2 times (x 102B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.CommonMainOrTestWithDependsOnChecker: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinNativeHostSpecificMetadataArtifactKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCreateLifecycleTasksSideEffectKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinTarget                                 : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.utils.ConfigurationsKt$createResolvable$1           : loaded 2 times (x 74B)
Class [Lorg.jetbrains.kotlin.statistics.metrics.BooleanAnonymizationPolicy;           : loaded 2 times (x 65B)
Class com.google.common.reflect.Invokable$MethodInvokable                             : loaded 2 times (x 103B)
Class com.google.common.collect.MapMakerInternalMap$StrongKeyDummyValueEntry          : loaded 2 times (x 80B)
Class com.google.common.base.Platform                                                 : loaded 2 times (x 67B)
Class org.gradle.internal.classloader.FilteringClassLoader                            : loaded 2 times (x 101B)
Class org.jetbrains.kotlin.gradle.internal.KotlinDependenciesManagementKt$allNonProjectDependencies$1: loaded 2 times (x 70B)
Class [Lorg.jetbrains.kotlin.build.report.metrics.BuildAttributeKind;                 : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.tasks.K2MultiplatformStructure                      : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.compilerRunner.CompilerSystemPropertiesService$Parameters  : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationSourceSetsContainer: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinNativeKlibArtifactKt                : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.ProjectLocalConfigurations                   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinUsages$KotlinUsagesDisambiguation  : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.tooling.core.Extras$Key$Companion                          : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.tooling.core.Extras$Key                                    : loaded 2 times (x 68B)
Class [Lorg.objectweb.asm.AnnotationWriter;                                           : loaded 2 times (x 65B)
Class com.google.common.collect.CompoundOrdering                                      : loaded 2 times (x 111B)
Class com.google.common.collect.AbstractMapBasedMultimap$WrappedList                  : loaded 2 times (x 201B)
Class com.google.common.collect.Maps$8                                                : loaded 2 times (x 78B)
Class com.google.common.collect.RegularImmutableMap$KeySet                            : loaded 2 times (x 146B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig$1$4 : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.CheckKotlinGradlePluginConfigurationErrors: loaded 2 times (x 313B)
Class org.jetbrains.kotlin.gradle.plugin.sources.DefaultKotlinSourceSetKt             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinJvmOptions                                : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.HierarchyAttributeContainer$1            : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycle                        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinJsPluginWrapper                        : loaded 2 times (x 82B)
Class org.jetbrains.kotlin.gradle.dsl.ToolchainSupport                                : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.tooling.core.Extras                                        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.InternalKotlinTarget                     : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.statistics.metrics.NumberAnonymizationPolicy$RANDOM_10_PERCENT: loaded 2 times (x 81B)
Class org.jetbrains.kotlin.statistics.metrics.StringMetrics                           : loaded 2 times (x 76B)
Class com.google.common.base.Predicates$CompositionPredicate                          : loaded 2 times (x 84B)
Class [Lcom.google.common.collect.Iterators$EmptyModifiableIterator;                  : loaded 2 times (x 65B)
Class com.google.common.collect.ImmutableRangeSet$Builder                             : loaded 2 times (x 73B)
Class com.google.common.collect.Sets$SetView                                          : loaded 2 times (x 134B)
Class com.google.common.cache.LocalCache$StrongValueReference                         : loaded 2 times (x 86B)
Class com.google.common.base.Equivalence$Identity                                     : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.gradle.tasks.CompilerPluginOptions                         : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig$1$5 : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinTargetSoftwareComponentImpl        : loaded 2 times (x 86B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.DefaultKotlinUsageContext$PublishOnlyIf  : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinCommonCompilerOptionsHelper               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinJvmPlugin$Companion                    : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinToolingDiagnostics         : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinMetadataArtifactKt                  : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.CreateArtifactsSideEffectKt$CreateArtifactsSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$property$1$valueFromGradleAndLocalProperties$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.PropertiesProvider$useClasspathSnapshot$1    : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginWrapperKt$kotlinToolingVersion$2 : loaded 2 times (x 74B)
Class org.objectweb.asm.Type                                                          : loaded 2 times (x 68B)
Class com.google.common.graph.SuccessorsFunction                                      : loaded 2 times (x 66B)
Class com.google.common.base.PairwiseEquivalence                                      : loaded 2 times (x 79B)
Class org.gradle.internal.classloader.VisitableURLClassLoader$InstrumentingVisitableURLClassLoader: loaded 2 times (x 119B)
Class de.undercouch.gradle.tasks.download.DownloadExtension_Decorated                 : loaded 2 times (x 119B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig$1$6 : loaded 2 times (x 71B)
Class [Lorg.jetbrains.kotlin.gradle.tasks.KotlinCompilerExecutionStrategy;            : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.DefaultKotlinUsageContext$1              : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.tasks.FailedCompilationException                    : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationImpl$associateWith$2: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.hierarchy.KotlinSourceSetTreeClassifier      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationAssociator: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.konan.target.UtilsKt$evaluatePresetName$1                  : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinNativeTarget                       : loaded 2 times (x 169B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.KotlinSourceSetTreeDependsOnMismatchChecker$runChecks$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.PreHmppDependenciesUsageChecker$runChecks$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.utils.ProjectExtensionsKt                           : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinSourceSetTree$Companion                : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinGradleProjectChecker$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleBuildServices$Parameters         : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.BuildFusService$Companion$registerIfAbsent$1$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.statistics.fileloggers.MetricsContainer$Companion          : loaded 2 times (x 67B)
Class org.objectweb.asm.ByteVector                                                    : loaded 2 times (x 75B)
Class com.google.common.collect.MapMakerInternalMap$StrongKeyDummyValueEntry$Helper   : loaded 2 times (x 75B)
Class com.google.common.io.Files$FileByteSink                                         : loaded 2 times (x 73B)
Class [Lcom.google.common.cache.CacheBuilder$NullListener;                            : loaded 2 times (x 65B)
Class com.google.common.math.IntMath$1                                                : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.PropertiesProvider$booleanPropertyWithDeprecatedValues$1: loaded 2 times (x 74B)
Class org.gradle.internal.classloader.VisitableURLClassLoader$Spec                    : loaded 2 times (x 70B)
Class org.gradle.internal.classpath.DefaultClassPath                                  : loaded 2 times (x 86B)
Class build_6s7imafvclc6gm72hnzimmguy$_run_closure2$_closure9$_closure17              : loaded 2 times (x 135B)
Class io.invertase.gradle.build.ProjectExtension$getOption$3                          : loaded 2 times (x 148B)
Class org.jetbrains.kotlin.gradle.plugin.DefaultKotlinBasePlugin$addKotlinCompilerConfiguration$2$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinSoftwareComponentKt                : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.BuildFinishMetrics                : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationSourceSetInclusion$AddSourcesToCompileTask: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.konan.target.Architecture                                  : loaded 2 times (x 75B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.HasProject;                                : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.utils.MutableObservableSetImpl                      : loaded 2 times (x 159B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinTasksProvider                           : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinJsCompilerTypeHolder                   : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.dsl.DefaultToolchainSupport$wireToolchainToTasks$1  : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinCommonCompilerOptions                     : loaded 2 times (x 66B)
Class [Lorg.jetbrains.kotlin.build.report.metrics.GradleBuildTime;                    : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$MemoizedCallable: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.internal.DefaultMavenPublicationComponentAccessorFactory: loaded 2 times (x 72B)
Class org.objectweb.asm.Symbol                                                        : loaded 2 times (x 69B)
Class com.google.common.reflect.Types$ClassOwnership                                  : loaded 2 times (x 76B)
Class com.google.common.base.AbstractIterator                                         : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.fus.internal.SynchronizedConfigurationMetrics$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.AbstractKotlinCompile$friendPathsSet$1        : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.build.report.metrics.BuildAttributes                       : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.statistics.metrics.OrMetricContainer                       : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.cocoapods.KotlinCocoapodsPlugin$Companion    : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationImpl    : loaded 2 times (x 136B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory$PostConfigure;: loaded 2 times (x 65B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$MACOS_X64                         : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.konan.util.Named                                           : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinSourceSet$Companion                    : loaded 2 times (x 67B)
Class [Lorg.jetbrains.kotlin.gradle.report.BuildReportMode;                           : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.statistics.metrics.BooleanOverridePolicy                   : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.gradle.plugin.AbstractKotlinAndroidPluginWrapper           : loaded 2 times (x 82B)
Class com.google.common.collect.Sets$FilteredSet                                      : loaded 2 times (x 133B)
Class com.google.common.io.Closer                                                     : loaded 2 times (x 74B)
Class com.google.common.collect.ImmutableSet                                          : loaded 2 times (x 141B)
Class com.google.common.collect.RegularImmutableMap$Values                            : loaded 2 times (x 203B)
Class com.google.common.base.CharMatcher$BitSetMatcher                                : loaded 2 times (x 108B)
Class Build_gradle$7                                                                  : loaded 2 times (x 70B)
Class org.gradle.internal.classloader.ClassLoaderSpec                                 : loaded 2 times (x 67B)
Class io.invertase.gradle.build.ProjectExtension$getSharedInstance                    : loaded 2 times (x 142B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinStdlibConfigurationMetrics  : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$DefaultJavaToolchainSetter: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.build.report.metrics.BuildAttributeKind                    : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.DaemonReuseCounter$Companion      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinCompilationsKt$moduleNameForCompilation$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.sources.KotlinDependencyScopeKt              : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.TransformActionUsingKotlinToolingDiagnostics$Parameters: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.compilerRunner.CompilerSystemPropertiesService$Companion$registerIfAbsent$2$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinCompilationsKt$addSourcesToKotlinCompileTask$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.internal.KotlinDependenciesManagementKt$configureDefaultVersionsResolutionStrategy$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.utils.KotlinExtensionUtilsKt$forAllTargets$1        : loaded 2 times (x 91B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.CheckKotlinGradlePluginConfigurationErrorsKt$locateOrRegisterCheckKotlinGradlePluginErrorsTask$taskProvider$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.OverriddenKotlinNativeHomeChecker$StdlibExistenceCheckerValueSource$Params: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.dsl.PlatformSourceSetConventionsChecker$nativeTargetPresets$1$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tooling.BuildKotlinToolingMetadataTaskKt$buildKotlinToolingMetadataTask$1: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.internal.Kapt3GradleSubplugin$Companion             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCreateCompilationArchiveTaskKt$KotlinRegisterCompilationArchiveTasksExtension$1: loaded 2 times (x 70B)
Class [Lorg.jetbrains.kotlin.statistics.metrics.NumberOverridePolicy;                 : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$GradleProperty: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.utils.StoredPropertyKt$projectStoredProperty$1      : loaded 2 times (x 74B)
Class org.objectweb.asm.AnnotationWriter                                              : loaded 2 times (x 75B)
Class com.google.common.io.ByteSource$EmptyByteSource                                 : loaded 2 times (x 81B)
Class com.google.common.cache.CacheStats                                              : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.K2MultiplatformStructure$RefinesEdge          : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.SyncLanguageSettingsWithKotlinExtensionSetupActionKt$SyncLanguageSettingsWithKotlinExtensionSetupAction$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.TaskConfigAction$configureTask$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.DuplicateSourceSetChecker$runChecks$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.ToolingDiagnostic$Severity       : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.dsl.JvmTarget                                       : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.internal.ClassLoadersCachingBuildService            : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactories$Inject        : loaded 2 times (x 102B)
Class org.jetbrains.kotlin.gradle.plugin.internal.DefaultJavaExecTaskParametersCompatibility$Factory: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactoriesConfigurator$Companion: loaded 2 times (x 67B)
Class org.objectweb.asm.RecordComponentWriter                                         : loaded 2 times (x 74B)
Class settings_4rq5cjrcdj0mscfkcpdclh2wk$_run_closure1                                : loaded 2 times (x 135B)
Class com.google.common.collect.Interners                                             : loaded 2 times (x 67B)
Class com.google.common.io.Closer$Suppressor                                          : loaded 2 times (x 66B)
Class com.google.common.io.LineProcessor                                              : loaded 2 times (x 66B)
Class com.google.common.util.concurrent.AbstractFuture$Listener                       : loaded 2 times (x 68B)
Class com.google.common.base.CharMatcher$Or                                           : loaded 2 times (x 108B)
Class build_agisdczp7kn8uyp03dp4zx2vi$_run_closure4$_closure13$_closure17             : loaded 2 times (x 136B)
Class build_eupcmxgtu9q3uy0v8rg9z8i7j$_run_closure2$_closure20                        : loaded 2 times (x 135B)
Class io.invertase.gradle.build.ReactNativeShared$_applyDefaultExcludes_closure2      : loaded 2 times (x 137B)
Class [Lorg.jetbrains.kotlin.compilerRunner.KotlinCompilerArgumentsLogLevel;          : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile_Decorated                       : loaded 2 times (x 595B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.SyncLanguageSettingsWithKotlinExtensionSetupActionKt$SyncLanguageSettingsWithKotlinExtensionSetupAction$1$2: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.internal.BuildIdService$Companion            : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.OverriddenKotlinNativeHomeChecker$StdlibExistenceCheckerValueSource$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.js.dsl.KotlinWasmSubTargetContainerDsl      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.WasmWasiEnvironmentChecker$1: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.DisabledCinteropCommonizationInHmppProjectChecker: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.NoKotlinTargetsDeclaredChecker: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.targets.jvm.KotlinJvmTargetTestFixturesSideEffectKt$ConfigureJavaTestFixturesSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.utils.StoredPropertyStorage                         : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinSourceSetContainer                     : loaded 2 times (x 66B)
Class org.objectweb.asm.Attribute                                                     : loaded 2 times (x 73B)
Class com.google.common.collect.ForwardingSet                                         : loaded 2 times (x 147B)
Class com.google.common.base.Supplier                                                 : loaded 2 times (x 66B)
Class org.gradle.internal.classloader.VisitableURLClassLoader                         : loaded 2 times (x 113B)
Class build_eupcmxgtu9q3uy0v8rg9z8i7j$_run_closure2$_closure21                        : loaded 2 times (x 135B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleBuildServices$Inject             : loaded 2 times (x 96B)
Class build_evl13vpxjidvxohlw1uueqdij$_run_closure2$_closure9$_closure10              : loaded 2 times (x 135B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.plugins.ObservablePlugins         : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.sources.InternalKotlinSourceSetKt$awaitPlatformCompilations$1: loaded 2 times (x 84B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.sources.KotlinDependencyScope;             : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$configureCommonParameters$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.ConfigurationNaming: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.DefaultKotlinCompilationPostConfigureKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$WATCHOS_X64                       : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinGradleProjectCheckersRunnerKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.IdeaKt$isInIdeaEnvironment$1               : loaded 2 times (x 70B)
Class [Lorg.jetbrains.kotlin.tooling.core.HasExtras;                                  : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.IncorrectCompileOnlyDependenciesChecker: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.WasmWasiEnvironmentChecker$2: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinLegacyCompatibilityMetadataArtifactKt$KotlinLegacyCompatibilityMetadataArtifact$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.dsl.DefaultToolchainSupport$wireToolchainToTasks$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.utils.SingleActionPerProject                        : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.plugin.DefaultKotlinBasePlugin                      : loaded 2 times (x 78B)
Class com.google.common.collect.ImmutableList$SerializedForm                          : loaded 2 times (x 69B)
Class Settings_gradle                                                                 : loaded 2 times (x 124B)
Class org.gradle.internal.Cast                                                        : loaded 2 times (x 67B)
Class org.gradle.api.Action                                                           : loaded 2 times (x 66B)
Class build_eupcmxgtu9q3uy0v8rg9z8i7j$_run_closure2                                   : loaded 2 times (x 135B)
Class build_86psz0zg5ccmkipscnjtptl6f$_run_closure2$_closure5                         : loaded 2 times (x 135B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.mpp.KotlinUsageContext$MavenScope;         : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.statistics.metrics.StatisticsValuesConsumer$DefaultImpls   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$configureCommonParameters$2: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationCompilerOptionsFromTargetConfigurator: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.internal.IdeaKt$isInIdeaEnvironment$2               : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.utils.FailuresKt$ideaSyncClasspathModeUtil$1        : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinToolingDiagnosticsCollector$Inject: loaded 2 times (x 92B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinJsKlibArtifactKt                    : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinTargetContainerWithWasmPresetFunctions    : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.GradleUtilsKt                                : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService          : loaded 2 times (x 72B)
Class org.objectweb.asm.Handler                                                       : loaded 2 times (x 68B)
Class com.google.common.collect.MapMakerInternalMap                                   : loaded 2 times (x 157B)
Class com.google.common.base.Strings                                                  : loaded 2 times (x 67B)
Class com.google.common.collect.Sets                                                  : loaded 2 times (x 67B)
Class com.google.common.base.Ticker                                                   : loaded 2 times (x 68B)
Class com.google.common.base.CharMatcher$FastMatcher                                  : loaded 2 times (x 107B)
Class build_eupcmxgtu9q3uy0v8rg9z8i7j$_run_closure3                                   : loaded 2 times (x 136B)
Class build_86psz0zg5ccmkipscnjtptl6f$_run_closure2$_closure6                         : loaded 2 times (x 136B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$javaSources$1                   : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.incremental.IncrementalCompilationFeatures                 : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinTarget$DefaultImpls                    : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinToolingDiagnosticsCollector: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCreateCompilationArchiveTaskKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.tooling.core.MutableExtrasImpl                             : loaded 2 times (x 161B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinBuildStatsMXBean            : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.utils.FileUtilsKt                                   : loaded 2 times (x 67B)
Class com.google.common.cache.RemovalCause$1                                          : loaded 2 times (x 76B)
Class [Lcom.google.common.cache.LocalCache$Segment;                                   : loaded 2 times (x 65B)
Class [Lcom.google.common.cache.LocalCache$Strength;                                  : loaded 2 times (x 65B)
Class com.google.common.cache.CacheBuilder$1                                          : loaded 2 times (x 81B)
Class com.google.common.collect.ImmutableMap                                          : loaded 2 times (x 116B)
Class com.google.common.base.Predicate                                                : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.internal.ClassLoadersCachingBuildService$Companion$registerIfAbsent$2$1$1: loaded 2 times (x 71B)
Class org.gradle.internal.classpath.DefaultClassPath$ImmutableUniqueList$Builder      : loaded 2 times (x 71B)
Class build_6s7imafvclc6gm72hnzimmguy$_run_closure2$_closure7                         : loaded 2 times (x 136B)
Class build_eupcmxgtu9q3uy0v8rg9z8i7j$_run_closure4                                   : loaded 2 times (x 136B)
Class build_86psz0zg5ccmkipscnjtptl6f$_run_closure2$_closure7                         : loaded 2 times (x 135B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinCompilationFactory$DefaultImpls    : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.InternalGradlePropertiesUsageChecker: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.CreateCInteropTasksSideEffectKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.metadata.KotlinMetadataTargetConfiguratorKt : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.testing.internal.KotlinTestsRegistry$Companion      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinCommonCompilerToolOptions                 : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$Companion$registerIfAbsent$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.internal.DefaultConfigurationCacheStartParameterAccessorVariantFactory: loaded 2 times (x 72B)
Class com.google.common.cache.RemovalCause$2                                          : loaded 2 times (x 76B)
Class com.google.common.collect.MapMakerInternalMap$WeakValueReference                : loaded 2 times (x 66B)
Class com.google.common.collect.AbstractRangeSet                                      : loaded 2 times (x 110B)
Class com.google.common.cache.LocalCache                                              : loaded 2 times (x 183B)
Class com.google.common.cache.CacheBuilder$2                                          : loaded 2 times (x 75B)
Class com.google.common.base.CharMatcher$NegatedFastMatcher                           : loaded 2 times (x 109B)
Class build_6s7imafvclc6gm72hnzimmguy$_run_closure2$_closure8                         : loaded 2 times (x 135B)
Class build_6k1vuntnni446s9ebkvsvp2hk$_run_closure1$_closure8$_closure10              : loaded 2 times (x 135B)
Class build_eupcmxgtu9q3uy0v8rg9z8i7j$_run_closure9$_closure46                        : loaded 2 times (x 135B)
Class build_eupcmxgtu9q3uy0v8rg9z8i7j$_run_closure5                                   : loaded 2 times (x 136B)
Class org.jetbrains.kotlin.build.report.metrics.BuildPerformanceMetrics$Companion     : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.cli.common.arguments.K2JVMCompilerArguments                : loaded 2 times (x 77B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.DefaultKotlinCompilationSourceSetsContainer$source$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.internal.KotlinTestDependencyManagementKt           : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.NativeVersionChecker$runChecks$nativeVersion$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$ANDROID_X64                       : loaded 2 times (x 74B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.sources.ConsistencyCheck;                  : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.FlowActionBuildFusService$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.report.ReportingSettings                            : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.statistics.metrics.StringAnonymizationPolicy$ComponentVersionAnonymizer: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinBasePluginWrapper                      : loaded 2 times (x 82B)
Class com.google.common.cache.RemovalCause$3                                          : loaded 2 times (x 76B)
Class com.google.common.cache.CacheBuilder$3                                          : loaded 2 times (x 68B)
Class [Lcom.google.common.collect.AbstractMapEntry;                                   : loaded 2 times (x 65B)
Class com.google.common.collect.ImmutableEntry                                        : loaded 2 times (x 78B)
Class com.google.common.collect.ArrayListMultimap                                     : loaded 2 times (x 168B)
Class org.gradle.api.GradleException                                                  : loaded 2 times (x 78B)
Class build_6s7imafvclc6gm72hnzimmguy$_run_closure2$_closure9                         : loaded 2 times (x 135B)
Class build_eupcmxgtu9q3uy0v8rg9z8i7j$_run_closure6                                   : loaded 2 times (x 136B)
Class build_9kn57pj55amg75ewdgalkhlsr$_run_closure3$_closure10                        : loaded 2 times (x 135B)
Class build_86psz0zg5ccmkipscnjtptl6f$_run_closure2$_closure9                         : loaded 2 times (x 136B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.NonSynchronizedMetricsContainer$MetricDescriptor: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinToolingDiagnostics$JsEnvironmentNotChosenExplicitly: loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.plugin.ProjectLocalConfigurations$ProjectLocalCompatibility: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinJvmCompilerOptions                        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.internal.DefaultMavenPublicationComponentAccessor: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.internal.DefaultProjectIsolationStartParameterAccessor$Factory: loaded 2 times (x 72B)
Class com.google.common.cache.RemovalCause$4                                          : loaded 2 times (x 76B)
Class com.google.common.collect.CollectPreconditions                                  : loaded 2 times (x 67B)
Class build_agr3p375fzty99zrloowe6ju6$_run_closure1$_closure5$_closure6               : loaded 2 times (x 135B)
Class build_eupcmxgtu9q3uy0v8rg9z8i7j$_run_closure7                                   : loaded 2 times (x 136B)
Class io.invertase.gradle.common.Utilities                                            : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.buildtools.api.SourcesChanges                              : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycleImpl$launch$1           : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.konan.target.HostManager$Companion$targetAliases$2         : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.utils.Completable                                   : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.DefaultKotlinBasePlugin$addKotlinCompilerConfiguration$2$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinBuildStatsServicesRegistry$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.internal.DefaultCompatibilityConventionRegistrar$Factory: loaded 2 times (x 72B)
Class com.google.common.cache.RemovalCause$5                                          : loaded 2 times (x 76B)
Class com.google.common.collect.Maps$AbstractFilteredMap                              : loaded 2 times (x 123B)
Class [Lcom.google.common.collect.MapMakerInternalMap$Strength;                       : loaded 2 times (x 65B)
Class [Lcom.google.common.io.FileWriteMode;                                           : loaded 2 times (x 65B)
Class com.google.common.collect.SortedMapDifference                                   : loaded 2 times (x 66B)
Class build_6s7imafvclc6gm72hnzimmguy$safeAppExtGet                                   : loaded 2 times (x 148B)
Class build_6s7imafvclc6gm72hnzimmguy$_run_closure4$_closure21$_closure22             : loaded 2 times (x 135B)
Class build_eupcmxgtu9q3uy0v8rg9z8i7j$_run_closure8                                   : loaded 2 times (x 136B)
Class build_9kn57pj55amg75ewdgalkhlsr$_run_closure3$_closure12                        : loaded 2 times (x 135B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$DefaultJdkSetter   : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.cli.common.arguments.CommonCompilerArguments               : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.tooling.core.KotlinToolingVersion$Maturity                 : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.IncorrectCompileOnlyDependenciesChecker$runChecks$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinOnlyTarget                         : loaded 2 times (x 155B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.BaseNpmDependencyExtension           : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinVersion$Companion                         : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.InternalKotlinGradlePluginApi                       : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.targets.ConfigureNonPackedKlibConsumingSideEffectKt$ConfigureNonPackedKlibConsumingSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.dsl.DefaultToolchainSupport                         : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.internal.CustomPropertiesFileValueSource     : loaded 2 times (x 78B)
Class com.google.common.collect.Iterables$4                                           : loaded 2 times (x 77B)
Class com.google.common.collect.MapMakerInternalMap$StrongKeyDummyValueSegment        : loaded 2 times (x 138B)
Class com.google.common.cache.LocalCache$WriteThroughEntry                            : loaded 2 times (x 75B)
Class com.google.common.cache.LocalCache$HashIterator                                 : loaded 2 times (x 82B)
Class com.google.common.io.Files                                                      : loaded 2 times (x 67B)
Class com.google.common.cache.LocalCache$EntryFactory                                 : loaded 2 times (x 79B)
Class build_6k1vuntnni446s9ebkvsvp2hk$_run_closure2$_closure11                        : loaded 2 times (x 135B)
Class build_eupcmxgtu9q3uy0v8rg9z8i7j$_run_closure9                                   : loaded 2 times (x 135B)
Class build_9kn57pj55amg75ewdgalkhlsr$_run_closure3$_closure13                        : loaded 2 times (x 135B)
Class org.jetbrains.kotlin.gradle.tasks.TaskOutputsBackup                             : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.cocoapods.KotlinCocoapodsPlugin              : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.internal.CompilerOptionsDslHelpersKt                : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfigKt   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.StdlibDependencyManagementKt$configureStdlibVersionAlignment$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinProjectExtension$awaitSourceSets$1        : loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycle$CoroutineStart         : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.konan.target.Family                                        : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.TasksProviderKt$sam$org_gradle_api_Action$0   : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tooling.BuildKotlinToolingMetadataTask$FromKotlinExtension: loaded 2 times (x 313B)
Class org.jetbrains.kotlin.gradle.utils.FailuresKt                                    : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.native.internal.CInteropConfigurationsKt$special$$inlined$KotlinTargetSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesLoader$konanDataDirProperty$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.BuildFusService$Parameters_Decorated: loaded 2 times (x 139B)
Class org.jetbrains.kotlin.gradle.plugin.PropertiesProvider$keepIncrementalCompilationCachesInMemory$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$Params$Inject: loaded 2 times (x 102B)
Class org.jetbrains.kotlin.tooling.core.HasMutableExtras                              : loaded 2 times (x 66B)
Class com.google.common.reflect.Types$NativeTypeVariableEquals                        : loaded 2 times (x 67B)
Class com.google.common.reflect.TypeParameter                                         : loaded 2 times (x 68B)
Class com.google.common.collect.Iterables$5                                           : loaded 2 times (x 77B)
Class com.google.common.collect.NullsLastOrdering                                     : loaded 2 times (x 111B)
Class [Lcom.google.common.collect.MapMaker$Dummy;                                     : loaded 2 times (x 65B)
Class com.google.common.cache.LocalCache$AbstractReferenceEntry                       : loaded 2 times (x 103B)
Class com.google.common.base.CharMatcher$Whitespace                                   : loaded 2 times (x 108B)
Class org.jetbrains.kotlin.gradle.targets.native.KonanPropertiesBuildService$Companion$registerIfAbsent$1: loaded 2 times (x 71B)
Class org.gradle.internal.IoActions                                                   : loaded 2 times (x 67B)
Class build_eupcmxgtu9q3uy0v8rg9z8i7j$_run_closure4$_closure26$_closure39             : loaded 2 times (x 136B)
Class org.jetbrains.kotlin.statistics.BuildSession                                    : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.internal.Kapt3KotlinGradleSubpluginKt               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinJvmCompile                                : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$LINUX_ARM32_HFP                   : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.internal.Kapt3GradleSubplugin                       : loaded 2 times (x 79B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.CInteropInputChecker    : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.targets.native.internal.CInteropConfigurationsKt$special$$inlined$KotlinTargetSideEffect$2: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.FinalizeConfigurationFusMetricActionKt$FinalizeConfigurationFusMetricAction$1: loaded 2 times (x 91B)
Class org.jetbrains.kotlin.gradle.internal.KotlinDependenciesManagementKt$CustomizeKotlinDependenciesSetupAction$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradlePluginExtensionPointInternal$registeredExtensions$2: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.statistics.metrics.BooleanOverridePolicy$OR                : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.gradle.plugin.UsesVariantImplementationFactories           : loaded 2 times (x 66B)
Class org.objectweb.asm.SymbolTable                                                   : loaded 2 times (x 68B)
Class com.google.common.collect.ImmutableMapKeySet                                    : loaded 2 times (x 146B)
Class com.google.common.collect.ImmutableRangeSet$ComplementRanges                    : loaded 2 times (x 203B)
Class com.google.common.cache.CacheLoader$InvalidCacheLoadException                   : loaded 2 times (x 78B)
Class com.google.common.collect.ImmutableSet$RegularSetBuilderImpl                    : loaded 2 times (x 73B)
Class org.gradle.internal.classloader.SystemClassLoaderSpec                           : loaded 2 times (x 67B)
Class build_eupcmxgtu9q3uy0v8rg9z8i7j$_run_closure10                                  : loaded 2 times (x 135B)
Class org.jetbrains.kotlin.gradle.tasks.CacheableTasksKt$cacheOnlyIfEnabledForKotlin$1$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$1$1     : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory$PostConfigure: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.utils.KotlinExtensionUtilsKt                        : loaded 2 times (x 67B)
Class [Lorg.jetbrains.kotlin.konan.util.Named;                                        : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.NoKotlinTargetsDeclaredChecker$runChecks$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.AbstractKotlinTarget$kotlinComponents$2  : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPlatformType$CompatibilityRule         : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesLoader$actualNativeHomeDirectory$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesKt              : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.BuildEventsListenerRegistryHolder            : loaded 2 times (x 68B)
Class [Lorg.jetbrains.kotlin.statistics.metrics.BooleanOverridePolicy;                : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.internal.CustomPropertiesFileValueSource$Parameters$Inject: loaded 2 times (x 102B)
Class com.google.common.collect.FluentIterable                                        : loaded 2 times (x 77B)
Class com.google.common.cache.LocalCache$StrongEntry                                  : loaded 2 times (x 104B)
Class com.google.common.util.concurrent.AbstractFuture$TrustedFuture                  : loaded 2 times (x 110B)
Class com.google.common.cache.LoadingCache                                            : loaded 2 times (x 66B)
Class de.undercouch.gradle.tasks.download.VerifySpec                                  : loaded 2 times (x 66B)
Class build_eupcmxgtu9q3uy0v8rg9z8i7j$_run_closure11                                  : loaded 2 times (x 136B)
Class org.jetbrains.kotlin.gradle.tasks.GradleCompileTaskProvider                     : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinTargetSoftwareComponentImplKt$KotlinTargetSoftwareComponent$1: loaded 2 times (x 91B)
Class org.jetbrains.kotlin.statistics.metrics.ConcatMetricContainer                   : loaded 2 times (x 73B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$registerBuildToolsApiTransformations$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfigKt$sam$org_gradle_api_Action$0: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.internal.BuildIdService                      : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationDependencyConfigurationsFactoriesKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.DefaultKotlinCompilationFriendPathsResolver: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.WasmSourceSetsNotFoundChecker$runChecks$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$IOS_SIMULATOR_ARM64               : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.targets.jvm.KotlinJvmCompilationWireJavaSourcesSideEffectKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCreateCompilationArchiveTaskKt$KotlinCreateCompilationArchivesTask$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinVersion                                   : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesLoader$actualNativeHomeDirectory$2: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.utils.StoredPropertyKt                              : loaded 2 times (x 67B)
Class org.objectweb.asm.MethodWriter                                                  : loaded 2 times (x 102B)
Class com.google.common.io.ByteStreams$LimitedInputStream                             : loaded 2 times (x 88B)
Class [Lcom.google.common.cache.Weigher;                                              : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.AndroidPublicationNotConfiguredChecker: loaded 2 times (x 70B)
Class build_eupcmxgtu9q3uy0v8rg9z8i7j$_run_closure12                                  : loaded 2 times (x 135B)
Class org.jetbrains.kotlin.gradle.internal.build.SourcesUtilsKt                       : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.sources.KotlinDependencyScope$Companion      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$1$3     : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$registerBuildToolsApiTransformations$2: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinJvmCompile                              : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationSourceSetsContainerKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycleKt$launchInStage$1      : loaded 2 times (x 91B)
Class org.jetbrains.kotlin.gradle.targets.NativeForwardImplementationToApiElementsSideEffectKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesLoader$actualNativeHomeDirectory$3: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.report.BuildReportMode                              : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.statistics.metrics.StringAnonymizationPolicy$RegexControlled: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.statistics.ValueAnonymizer                                 : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactories$Companion$getProvider$2$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.internal.ProjectIsolationStartParameterAccessor$Factory: loaded 2 times (x 66B)
Class com.google.common.collect.AbstractMapBasedMultimap$RandomAccessWrappedList      : loaded 2 times (x 201B)
Class Build_gradle$6$1                                                                : loaded 2 times (x 75B)
Class build_eupcmxgtu9q3uy0v8rg9z8i7j$_run_closure13                                  : loaded 2 times (x 136B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$disableMultiModuleIC$2$1        : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$javaExecutable$1   : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.build.report.metrics.BuildAttribute                        : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinBuildStatsLoggerService$reportBuildFinished$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.internal.transforms.BuildToolsApiClasspathEntrySnapshotTransform$Inject1: loaded 2 times (x 109B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationRegisterInSourceSetsConfigurator: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.DefaultKotlinCompilationTaskNamesContainerFactory: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.targets.native.internal.CommonizerTasksKt           : loaded 2 times (x 67B)
Class [Lorg.jetbrains.kotlin.gradle.targets.js.npm.NpmDependency$Scope;               : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilerPluginSupportPlugin            : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.AbstractKotlinTarget$components$2        : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.targets.CreateNonPackedKlibVariantsSideEffectKt$CreateNonPackedKlibVariantsSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinUsages$setupAttributesMatchingStrategy$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.PropertiesProvider$preciseCompilationResultsBackup$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.internal.diagnostics.AgpCompatibilityCheck$runAgpCompatibilityCheckIfAgpIsApplied$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.utils.AndroidPluginIdsKt                            : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinBasePlugin                             : loaded 2 times (x 66B)
Class com.google.common.reflect.Types$JavaVersion$1                                   : loaded 2 times (x 79B)
Class com.google.common.collect.AbstractIterator$State                                : loaded 2 times (x 75B)
Class com.google.common.collect.RegularImmutableBiMap                                 : loaded 2 times (x 144B)
Class com.google.common.cache.LocalCache$ValueReference                               : loaded 2 times (x 66B)
Class com.google.common.base.CharMatcher$Negated                                      : loaded 2 times (x 109B)
Class com.google.common.base.CharMatcher$Is                                           : loaded 2 times (x 107B)
Class build_eupcmxgtu9q3uy0v8rg9z8i7j$_run_closure14                                  : loaded 2 times (x 136B)
Class org.jetbrains.kotlin.gradle.tasks.internal.KotlinJvmOptionsCompat               : loaded 2 times (x 99B)
Class org.jetbrains.kotlin.cli.common.arguments.CommonToolArguments                   : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.utils.FutureKt                                      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.native.internal.PlatformLibrariesGenerator$GeneratedPlatformLibrariesService: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.logging.GradleLoggingUtilsKt                        : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinAndroidPluginWrapper                   : loaded 2 times (x 82B)
Class com.google.common.reflect.Types$JavaVersion$2                                   : loaded 2 times (x 79B)
Class com.google.common.reflect.Types                                                 : loaded 2 times (x 67B)
Class com.google.common.hash.PrimitiveSink                                            : loaded 2 times (x 66B)
Class com.google.common.util.concurrent.AbstractFuture$Failure                        : loaded 2 times (x 68B)
Class com.google.common.base.PatternCompiler                                          : loaded 2 times (x 66B)
Class com.google.common.base.Equivalence$Equals                                       : loaded 2 times (x 78B)
Class com.google.common.collect.Hashing                                               : loaded 2 times (x 67B)
Class com.google.common.base.Preconditions                                            : loaded 2 times (x 67B)
Class build_eupcmxgtu9q3uy0v8rg9z8i7j$_run_closure15                                  : loaded 2 times (x 136B)
Class org.jetbrains.kotlin.compilerRunner.GradleCompilerRunner$Companion              : loaded 2 times (x 67B)
Class [Lorg.jetbrains.kotlin.gradle.dsl.PlatformSourceSetConventionsChecker$CheckedPlatformInfo;: loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinGradleProjectCheckerContext: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.DuplicateSourceSetChecker: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.ToolingDiagnosticFactory         : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.targets.KotlinTargetSideEffect                      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinTargetContainerWithNativeShortcuts        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.DefaultKotlinBasePlugin$setupAttributeMatchingStrategy$1$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.statistics.metrics.NumericalMetrics$Companion              : loaded 2 times (x 67B)
Class [Lorg.jetbrains.kotlin.statistics.metrics.NumberAnonymizationPolicy;            : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.statistics.metrics.NumberOverridePolicy$OVERRIDE           : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$property$1: loaded 2 times (x 75B)
Class org.objectweb.asm.Label                                                         : loaded 2 times (x 69B)
Class org.objectweb.asm.CurrentFrame                                                  : loaded 2 times (x 69B)
Class org.objectweb.asm.ClassTooLargeException                                        : loaded 2 times (x 79B)
Class com.google.common.reflect.Types$JavaVersion$3                                   : loaded 2 times (x 79B)
Class com.google.common.collect.ByFunctionOrdering                                    : loaded 2 times (x 111B)
Class com.google.common.collect.ImmutableSetMultimap                                  : loaded 2 times (x 176B)
Class com.google.common.io.CharSink                                                   : loaded 2 times (x 75B)
Class com.google.common.base.Joiner$1                                                 : loaded 2 times (x 76B)
Class com.google.common.collect.PeekingIterator                                       : loaded 2 times (x 66B)
Class com.google.common.base.CharMatcher                                              : loaded 2 times (x 107B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig$2$2$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.build.report.metrics.BuildAttributes$Companion             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompilationTask                         : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory$ProcessResourcesTaskNameFactory: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$property$2: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.internal.KotlinDependenciesManagementKt             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.DefaultKotlinBasePlugin$setupAttributeMatchingStrategy$1$2: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.targets.native.internal.PlatformLibrariesGenerator$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinBuildStatsLoggerService     : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.internal.CustomPropertiesFileValueSource$Inject: loaded 2 times (x 109B)
Class org.objectweb.asm.Frame                                                         : loaded 2 times (x 69B)
Class org.objectweb.asm.MethodVisitor                                                 : loaded 2 times (x 101B)
Class com.google.common.reflect.Types$JavaVersion$4                                   : loaded 2 times (x 79B)
Class com.google.common.io.ByteSource$AsCharSource                                    : loaded 2 times (x 82B)
Class com.google.common.collect.MapMakerInternalMap$InternalEntryHelper               : loaded 2 times (x 66B)
Class com.google.common.collect.Maps$IteratorBasedAbstractMap                         : loaded 2 times (x 121B)
Class com.google.common.collect.Table                                                 : loaded 2 times (x 66B)
Class com.google.common.collect.RangeGwtSerializationDependencies                     : loaded 2 times (x 67B)
Class com.google.common.base.Joiner$2                                                 : loaded 2 times (x 75B)
Class com.google.common.cache.LocalCache$EntryFactory$1                               : loaded 2 times (x 79B)
Class [Lcom.google.common.cache.RemovalListener;                                      : loaded 2 times (x 65B)
Class com.google.common.base.CharMatcher$1                                            : loaded 2 times (x 109B)
Class com.google.common.base.CharMatcher$NamedFastMatcher                             : loaded 2 times (x 108B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.InternalKotlinTargetKt$special$$inlined$extrasStoredFuture$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.compilerRunner.UsesCompilerSystemPropertiesService         : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationSourceSetInclusion$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$WATCHOS_DEVICE_ARM64              : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.MissingNativeStdlibChecker$runChecks$1: loaded 2 times (x 84B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.sources.android.AndroidVariantType;        : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.sources.android.AndroidVariantType           : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinTargetSoftwareComponent            : loaded 2 times (x 85B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinVariant                            : loaded 2 times (x 97B)
Class org.jetbrains.kotlin.gradle.targets.native.ConfigureFrameworkExportSideEffectKt : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.utils.WhenPluginEnabledKt                           : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.tooling.core.ExtrasProperty                                : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactories               : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.internal.diagnostics.KotlinCompilerEmbeddableCheck  : loaded 2 times (x 67B)
Class com.google.common.reflect.Types$JavaVersion$5                                   : loaded 2 times (x 67B)
Class [Lcom.google.common.reflect.Types$ClassOwnership;                               : loaded 2 times (x 65B)
Class com.google.common.collect.RangeSet                                              : loaded 2 times (x 66B)
Class com.google.common.cache.LocalCache$EntryFactory$2                               : loaded 2 times (x 79B)
Class org.gradle.api.internal.classpath.GlobalCacheRootsProvider                      : loaded 2 times (x 66B)
Class build_6s7imafvclc6gm72hnzimmguy$_safeAppExtGet_closure6                         : loaded 2 times (x 135B)
Class build_eupcmxgtu9q3uy0v8rg9z8i7j$_run_closure4$_closure24$_closure36$_closure37  : loaded 2 times (x 136B)
Class org.jetbrains.kotlin.gradle.internal.StdlibDependencyManagementKt$WhenMappings  : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.tooling.core.Extras$Entry$Companion                        : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.js.dsl.KotlinWasmWasiTargetDsl              : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.DisabledNativeTargetsChecker: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCreateSourcesJarTaskSideEffectKt$KotlinCreateSourcesJarTaskSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.internal.CompilerArgumentAware                      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.utils.ReportUtilsKt                                 : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.utils.StoredLazyProperty                            : loaded 2 times (x 71B)
Class org.objectweb.asm.ModuleWriter                                                  : loaded 2 times (x 78B)
Class com.google.common.collect.CollectCollectors                                     : loaded 2 times (x 67B)
Class com.google.common.cache.LocalCache$EntryFactory$3                               : loaded 2 times (x 79B)
Class com.google.common.base.Ticker$1                                                 : loaded 2 times (x 68B)
Class com.google.common.collect.ImmutableMapEntrySet$RegularEntrySet                  : loaded 2 times (x 147B)
Class com.google.common.collect.Maps$BiMapConverter                                   : loaded 2 times (x 86B)
Class org.jetbrains.kotlin.gradle.tasks.K2MultiplatformStructure_Decorated            : loaded 2 times (x 131B)
Class org.jetbrains.kotlin.compilerRunner.GradleCompilerRunner                        : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.dsl.AndroidMainSourceSetConventionsChecker$runChecks$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.plugin.sources.FragmentConsistencyChecks            : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.InternalKotlinTargetComponent            : loaded 2 times (x 89B)
Class org.jetbrains.kotlin.gradle.targets.CreateNonPackedKlibVariantsSideEffectKt     : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.CreateDefaultTestRunSideEffectKt$special$$inlined$KotlinTargetSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.utils.ProviderApiUtilsKt                            : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.native.UsesKonanPropertiesBuildService      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesLoader$forceDisableRunningInProcess$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.utils.PersistentCachesKt                            : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.tasks.TaskWithLocalState                   : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinBuildStatisticsUtilsKt      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.statistics.metrics.BooleanMetrics                          : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$Params   : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.internal.diagnostics.GradleCompatibilityCheck       : loaded 2 times (x 67B)
Class com.google.common.collect.SetMultimap                                           : loaded 2 times (x 66B)
Class com.google.common.cache.LocalCache$EntryFactory$4                               : loaded 2 times (x 79B)
Class com.google.common.collect.ImmutableSet$Builder                                  : loaded 2 times (x 81B)
Class com.google.common.base.CharMatcher$InRange                                      : loaded 2 times (x 107B)
Class build_agisdczp7kn8uyp03dp4zx2vi$_run_closure4$_closure14$_closure19             : loaded 2 times (x 135B)
Class org.jetbrains.kotlin.gradle.plugin.sources.KotlinDependencyScopeKt$WhenMappings : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.K2MultiplatformCompilationTask                : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.OverriddenKotlinNativeHomeChecker$StdlibExistenceCheckerValueSource$Inject: loaded 2 times (x 105B)
Class org.jetbrains.kotlin.gradle.utils.ConfigurationsKt$maybeCreateDependencyScope$1 : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.sources.FragmentConsistencyChecks$languageVersionCheck$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.DecoratedKotlinCompilation               : loaded 2 times (x 187B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.ExperimentalTryNextUsageChecker: loaded 2 times (x 70B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.diagnostics.ToolingDiagnostic$Severity;    : loaded 2 times (x 65B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.KotlinPlatformType;                        : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.UsesBuildFusService               : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.old.Pre232IdeaKotlinBuildStatsBeanService: loaded 2 times (x 91B)
Class org.jetbrains.kotlin.statistics.metrics.NumberOverridePolicy$AVERAGE            : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactories$Parameters$Inject: loaded 2 times (x 102B)
Class org.jetbrains.kotlin.gradle.internal.diagnostics.GradleCompatibilityCheck$CurrentGradleVersionProvider: loaded 2 times (x 66B)
Class com.google.common.io.ByteStreams$1                                              : loaded 2 times (x 81B)
Class com.google.common.collect.ReverseOrdering                                       : loaded 2 times (x 111B)
Class com.google.common.cache.LocalCache$EntryFactory$5                               : loaded 2 times (x 79B)
Class org.jetbrains.kotlin.gradle.internal.attributes.AttributeUtilsKt                : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.MppSourcesJarKt$sourcesJarTaskNamed$result$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$1       : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.UsesBuildFinishedListenerService             : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.ConfigurationNaming$Default: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationSourceSetInclusion: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.targets.jvm.KotlinJvmTarget                         : loaded 2 times (x 168B)
Class org.jetbrains.kotlin.gradle.targets.js.dsl.KotlinWasmJsTargetDsl                : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.DisabledNativeTargetsChecker$runChecks$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycleImpl$await$2$1          : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.sources.DefaultLanguageSettingsBuilder       : loaded 2 times (x 96B)
Class org.jetbrains.kotlin.gradle.plugin.sources.FragmentConsistencyChecks$languageVersionCheck$2: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.targets.CreateTargetConfigurationsSideEffectKt$CreateTargetConfigurationsSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinTargetContainerWithPresetFunctions        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinPublishingDsl                             : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.utils.GradleConfigurationUtilsKt                    : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.internal.CustomPropertiesFileValueSource$Parameters: loaded 2 times (x 66B)
Class com.google.common.collect.HashMultimap                                          : loaded 2 times (x 170B)
Class com.google.common.cache.LocalCache$EntryFactory$6                               : loaded 2 times (x 79B)
Class org.jetbrains.kotlin.gradle.utils.Completable$await$1                           : loaded 2 times (x 84B)
Class org.gradle.internal.installation.CurrentGradleInstallationLocator               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.compilerRunner.CompilerSystemPropertiesService$Companion   : loaded 2 times (x 67B)
Class build_86psz0zg5ccmkipscnjtptl6f$_run_closure3$_closure12                        : loaded 2 times (x 135B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$getToolsJarFromJvm$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.build.report.metrics.GcMetrics                             : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.statistics.metrics.SumMetricContainer                      : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.UsesKotlinToolingDiagnosticsKt   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$2       : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.BuildFinishedListenerService$Companion$registerIfAbsent$2$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$IOS_X64                           : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.internal.IsInIdeaEnvironmentValueSource$Inject      : loaded 2 times (x 96B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.InternalKotlinCompilation                : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.utils.WhenEvaluatedKt                               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.native.internal.AddKotlinPlatformIntegersSupportLibraryKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.AbstractKotlinJsPluginWrapper                : loaded 2 times (x 82B)
Class org.jetbrains.kotlin.gradle.plugin.internal.MavenPublicationComponentAccessor   : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.fus.BuildUidService                                 : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginWrapperKt                        : loaded 2 times (x 67B)
Class com.google.common.io.ByteSource$SlicedByteSource                                : loaded 2 times (x 81B)
Class com.google.common.collect.Maps$KeySet                                           : loaded 2 times (x 133B)
Class com.google.common.cache.LocalCache$EntryFactory$7                               : loaded 2 times (x 79B)
Class com.google.common.collect.ImmutableSet$EmptySetBuilderImpl                      : loaded 2 times (x 72B)
Class com.google.common.collect.ImmutableList                                         : loaded 2 times (x 202B)
Class org.gradle.internal.service.DefaultServiceLocator                               : loaded 2 times (x 79B)
Class org.jetbrains.kotlin.statistics.AnonymizerUtilsKt                               : loaded 2 times (x 67B)
Class build_9kn57pj55amg75ewdgalkhlsr$_run_closure2                                   : loaded 2 times (x 135B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$scriptExtensions$1              : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.K2MultiplatformStructure$Fragment             : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.compilerRunner.CompilerSystemPropertiesService$Companion$registerIfAbsent$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.util.capitalizeDecapitalize.CapitalizeDecapitalizeKt       : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.WasmJsEnvironmentChecker: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.AndroidPluginWithoutAndroidTargetChecker: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.FlowActionBuildFusService$getConfigurationTimeMetrics$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.SynchronizedMetricsContainer      : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.statistics.metrics.NumberOverridePolicy                    : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.statistics.metrics.StringAnonymizationPolicy$AllowedListAnonymizer$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.internal.DefaultProjectIsolationStartParameterAccessor_Decorated: loaded 2 times (x 121B)
Class org.jetbrains.kotlin.gradle.plugin.internal.ProjectIsolationStartParameterAccessorKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.testing.internal.KotlinTestsRegistry                : loaded 2 times (x 68B)
Class com.google.common.collect.Maps$EntrySet                                         : loaded 2 times (x 132B)
Class com.google.common.cache.LocalCache$EntryFactory$8                               : loaded 2 times (x 79B)
Class com.google.common.collect.ImmutableSet$SetBuilderImpl                           : loaded 2 times (x 72B)
Class org.gradle.internal.installation.CurrentGradleInstallation                      : loaded 2 times (x 69B)
Class build_agr3p375fzty99zrloowe6ju6$_run_closure1$_closure5                         : loaded 2 times (x 135B)
Class build_9kn57pj55amg75ewdgalkhlsr$_run_closure3                                   : loaded 2 times (x 136B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompilerExecutionStrategy               : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.InternalKotlinCompilationKt$awaitAllKotlinSourceSets$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.FusMetrics                        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.sources.DefaultKotlinSourceSet$special$$inlined$Iterable$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory$KotlinCompilerOptionsFactory$Options: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilation                            : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinTargetConfiguratorKt                   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.native.internal.PlatformLibrariesGenerator  : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.tooling.core.Extras$Type$Companion                         : loaded 2 times (x 67B)
Class com.google.common.collect.ForwardingObject                                      : loaded 2 times (x 68B)
Class com.google.common.cache.CacheLoader$1                                           : loaded 2 times (x 71B)
Class com.google.common.collect.ImmutableList$SubList                                 : loaded 2 times (x 204B)
Class org.gradle.util.internal.GUtil                                                  : loaded 2 times (x 67B)
Class build_9kn57pj55amg75ewdgalkhlsr$_run_closure4                                   : loaded 2 times (x 136B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.InternalKotlinTargetKt$isSourcesPublishableFuture$2: loaded 2 times (x 91B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinCompilationFactory                 : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.dsl.IosSourceSetConventionChecker$runChecks$1       : loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.AbstractKotlinCompilation                : loaded 2 times (x 187B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.UnusedSourceSetsChecker : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.utils.Future                                        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinBuildStatsConfiguration$Companion: loaded 2 times (x 67B)
Class settings_4rq5cjrcdj0mscfkcpdclh2wk                                              : loaded 2 times (x 175B)
Class com.google.common.io.ByteSource                                                 : loaded 2 times (x 80B)
Class com.google.common.cache.AbstractCache$StatsCounter                              : loaded 2 times (x 66B)
Class de.undercouch.gradle.tasks.download.DownloadExtension                           : loaded 2 times (x 70B)
Class build_eupcmxgtu9q3uy0v8rg9z8i7j$_run_closure5$_closure44                        : loaded 2 times (x 136B)
Class build_eupcmxgtu9q3uy0v8rg9z8i7j$_safeAppExtGet_closure16                        : loaded 2 times (x 135B)
Class build_9kn57pj55amg75ewdgalkhlsr$_resolveReactNativeDirectory_closure5           : loaded 2 times (x 135B)
Class io.invertase.gradle.common.PackageJson                                          : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.gradle.plugin.attributes.KlibPackaging                     : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinTargetSoftwareComponentImplKt      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.js.dsl.KotlinJsSubTargetContainerDsl        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.tooling.core.MutableExtrasImpl$Companion                   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.ClassLoadersCachingBuildService$Parameters : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleBuildServices$Companion$registerIfAbsent$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.statistics.fileloggers.MetricsContainer                    : loaded 2 times (x 73B)
Class org.jetbrains.kotlin.gradle.plugin.internal.ProjectIsolationStartParameterAccessor: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.utils.StoredProperty                                : loaded 2 times (x 66B)
Class com.google.common.reflect.Types$TypeVariableImpl                                : loaded 2 times (x 68B)
Class com.google.common.collect.MapMakerInternalMap$1                                 : loaded 2 times (x 79B)
Class com.google.common.collect.Iterators$MergingIterator                             : loaded 2 times (x 77B)
Class com.google.common.collect.RegularImmutableSet                                   : loaded 2 times (x 144B)
Class org.gradle.internal.installation.GradleInstallation                             : loaded 2 times (x 71B)
Class build_6k1vuntnni446s9ebkvsvp2hk$_run_closure1$_closure5$_closure9               : loaded 2 times (x 135B)
Class de.undercouch.gradle.tasks.download.Download                                    : loaded 2 times (x 407B)
Class build_eupcmxgtu9q3uy0v8rg9z8i7j$_run_closure5$_closure45                        : loaded 2 times (x 136B)
Class org.jetbrains.kotlin.gradle.plugin.ProjectLocalConfigurations$setupAttributesMatchingStrategy$1$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinJvmCompile$DefaultImpls                   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory$create$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinJavaToolchain                           : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.FlowActionBuildFusService$Inject  : loaded 2 times (x 100B)
Class org.jetbrains.kotlin.statistics.metrics.BooleanMetrics$Companion                : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.internal.CompatibilityConventionRegistrar    : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.AndroidGradlePluginVersion                   : loaded 2 times (x 71B)
Class com.google.common.collect.ForwardingCollection                                  : loaded 2 times (x 126B)
Class com.google.common.collect.LexicographicalOrdering                               : loaded 2 times (x 111B)
Class com.google.common.collect.Iterables                                             : loaded 2 times (x 67B)
Class com.google.common.base.Splitter$Strategy                                        : loaded 2 times (x 66B)
Class build_evl13vpxjidvxohlw1uueqdij$_run_closure2$_closure5                         : loaded 2 times (x 136B)
Class build_eupcmxgtu9q3uy0v8rg9z8i7j$_run_closure15$_closure49                       : loaded 2 times (x 135B)
Class io.invertase.gradle.build.ProjectExtension$setProject$0                         : loaded 2 times (x 148B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinJvmCompilation                     : loaded 2 times (x 196B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilation$Companion                  : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$WATCHOS_ARM64                     : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$MACOS_ARM64                       : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.OverriddenKotlinNativeHomeChecker: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinJvmJarArtifactKt$KotlinJvmJarArtifact$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinLegacyCompatibilityMetadataArtifactKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.native.SetupEmbedAndSignAppleFrameworkTaskSideEffectKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinProjectSetupActionKt$KotlinProjectSetupCoroutine$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.dsl.ToolchainSupport$Companion                      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinSourceSet                              : loaded 2 times (x 66B)

---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
OS uptime: 14 days 21:49 hours
Hyper-V role detected

CPU: total 4 (initial active 4) (4 cores per cpu, 1 threads per core) family 6 model 58 stepping 9 microcode 0x21, cx8, cmov, fxsr, mmx, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, tsc, tscinvbit, avx, aes, erms, clmul, vzeroupper, clflush, hv, rdtscp, f16c
Processor Information for all 4 processors :
  Max Mhz: 3101, Current Mhz: 3101, Mhz Limit: 3101

Memory: 4k page, system-wide physical 8161M (495M free)
TotalPageFile size 32737M (AvailPageFile size 116M)
current process WorkingSet (physical memory assigned to process): 234M, peak: 1062M
current process commit charge ("private bytes"): 1229M, peak: 1237M

vm_info: OpenJDK 64-Bit Server VM (21.0.5+-13047016-b750.29) for windows-amd64 JRE (21.0.5+-13047016-b750.29), built on 2025-02-11T21:12:39Z by "builder" with MS VC++ 16.10 / 16.11 (VS2019)

END.
