import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, Modal, FlatList, SafeAreaView, ActivityIndicator, Alert, StatusBar } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { fonts } from '../theme/fonts';
import { useTheme } from '../context/ThemeContext';
import AdBanner from '../components/AdBanner';
import { BannerAdSize } from 'react-native-google-mobile-ads';

const API_BASE = 'https://bible.helloao.org/api';
const READING_HISTORY_KEY = '@escola_sabatina_bible_history';

interface ReadingHistory {
  bookId: string;
  bookName: string;
  chapter: number;
  verse?: number;
  timestamp: Date;
}

const BibleScreen = ({ navigation, route }: any) => {
  const { colors, theme } = useTheme();
  const [translations, setTranslations] = useState<any[]>([]);
  const [bibleVersion, setBibleVersion] = useState('por_blj'); // default
  const [showVersionModal, setShowVersionModal] = useState(false);
  const [books, setBooks] = useState<any[]>([]);
  const [currentBookIdx, setCurrentBookIdx] = useState(0);

  // Debug do tema
  console.log('🎨 BibleScreen - Tema atual:', theme, 'Cor do texto:', colors.text);
  const [currentChapter, setCurrentChapter] = useState(1);
  const [verses, setVerses] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [showBookModal, setShowBookModal] = useState(false);
  const [showChapterModal, setShowChapterModal] = useState(false);
  const [fontSize, setFontSize] = useState(18);
  const [showReadingModal, setShowReadingModal] = useState(false);
  const [readingHistory, setReadingHistory] = useState<ReadingHistory | null>(null);

  // Carregar histórico de leitura
  const loadReadingHistory = async () => {
    try {
      const history = await AsyncStorage.getItem(READING_HISTORY_KEY);
      if (history) {
        const parsedHistory = JSON.parse(history);
        setReadingHistory({
          ...parsedHistory,
          timestamp: new Date(parsedHistory.timestamp)
        });
      }
    } catch (error) {
      console.error('Erro ao carregar histórico de leitura:', error);
    }
  };

  // Salvar histórico de leitura
  const saveReadingHistory = async (bookId: string, bookName: string, chapter: number, verse?: number) => {
    try {
      const history: ReadingHistory = {
        bookId,
        bookName,
        chapter,
        verse,
        timestamp: new Date()
      };
      await AsyncStorage.setItem(READING_HISTORY_KEY, JSON.stringify(history));
      setReadingHistory(history);
    } catch (error) {
      console.error('Erro ao salvar histórico de leitura:', error);
    }
  };

  // Navegar para referência específica
  const navigateToReference = (reference: string) => {
    // Padrões para extrair informações da referência
    const patterns = [
      // João 3:16
      /([1-3]?\s*[A-Za-zçÇãÃõÕáÁéÉíÍóÓúÚ]+)\s+(\d+):(\d+)/,
      // João 3
      /([1-3]?\s*[A-Za-zçÇãÃõÕáÁéÉíÍóÓúÚ]+)\s+(\d+)/,
    ];

    for (const pattern of patterns) {
      const match = reference.match(pattern);
      if (match) {
        const bookName = match[1].trim();
        const chapter = parseInt(match[2]);
        const verse = match[3] ? parseInt(match[3]) : undefined;

        // Encontrar o livro correspondente
        const bookIndex = books.findIndex(book => 
          book.name.toLowerCase().includes(bookName.toLowerCase()) ||
          bookName.toLowerCase().includes(book.name.toLowerCase())
        );

        if (bookIndex !== -1) {
          const book = books[bookIndex];
          setCurrentBookIdx(bookIndex);
          setCurrentChapter(chapter);
          
          // Atualizar o histórico de leitura com a nova referência
          saveReadingHistory(book.id, book.name, chapter, verse);
          return;
        }
      }
    }

    Alert.alert('Referência não encontrada', `Não foi possível localizar "${reference}" na Bíblia.`);
  };

  // Buscar traduções ao montar
  useEffect(() => {
    fetch(`${API_BASE}/available_translations.json`)
      .then(res => res.json())
      .then(data => setTranslations(data))
      .catch(() => Alert.alert('Erro', 'Não foi possível carregar as traduções.'));
  }, []);

  // Carregar histórico ao montar
  useEffect(() => {
    loadReadingHistory();
  }, []);

  // Verificar se há referência específica para navegar
  useEffect(() => {
    if (route.params?.reference) {
      navigateToReference(route.params.reference);
      // Limpar os parâmetros da rota após a navegação
      navigation.setParams({ reference: undefined });
    }
  }, [route.params?.reference, books, navigation]);

  // Buscar livros ao mudar tradução
  useEffect(() => {
    setLoading(true);
    fetch(`${API_BASE}/${bibleVersion}/books.json`)
      .then(res => res.json())
      .then(data => {
        setBooks(data.books);
        setCurrentBookIdx(0);
        setCurrentChapter(1);
      })
      .catch(() => Alert.alert('Erro', 'Não foi possível carregar os livros da Bíblia.'))
      .finally(() => setLoading(false));
  }, [bibleVersion]);

  // Buscar capítulo ao mudar livro/capítulo
  useEffect(() => {
    if (!books.length) return;
    setLoading(true);
    const book = books[currentBookIdx];
    fetch(`${API_BASE}/${bibleVersion}/${book.id}/${currentChapter}.json`)
      .then(res => res.json())
      .then(data => {
        setVerses(data.chapter && data.chapter.content ? data.chapter.content : []);
        // Salvar histórico de leitura apenas se não for uma navegação de referência
        if (!route.params?.reference) {
          saveReadingHistory(book.id, book.name, currentChapter);
        }
      })
      .catch(() => Alert.alert('Erro', 'Não foi possível carregar o capítulo.'))
      .finally(() => setLoading(false));
  }, [books, currentBookIdx, currentChapter, bibleVersion, route.params?.reference]);

  if (loading) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: colors.background }}>
        <ActivityIndicator size="large" color={colors.primary} />
      </View>
    );
  }

  const currentBook = books[currentBookIdx] || { name: '', numberOfChapters: 1, id: '' };
  const chapters = Array.from({ length: currentBook.numberOfChapters }, (_, i) => i + 1);

  // Estilos dinâmicos baseados no tema
  const styles = StyleSheet.create({
    topHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'flex-end',
      paddingHorizontal: 16,
      paddingTop: 40,
      paddingBottom: 8,
      backgroundColor: colors.background,
      zIndex: 10,
    },
    headerIconBtn: {
      marginLeft: 12,
      padding: 6,
      borderRadius: 20,
    },
    versionBtn: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: colors.surface,
      borderRadius: 16,
      paddingHorizontal: 12,
      paddingVertical: 4,
      marginLeft: 12,
    },
    versionText: {
      fontFamily: fonts.bold,
      fontSize: 14,
      color: colors.text,
      marginLeft: 6,
    },
    centerTitleContainer: {
      alignItems: 'center',
      paddingVertical: 16,
      backgroundColor: colors.background,
    },
    bookTitle: {
      fontFamily: fonts.bold,
      fontSize: 20,
      color: colors.text,
      marginBottom: 4,
    },
    chapterNumber: {
      fontFamily: fonts.regular,
      fontSize: 16,
      color: colors.textSecondary,
    },
    contentContainer: {
      paddingHorizontal: 20,
      paddingBottom: 100,
    },
    verseContainer: {
      flexDirection: 'row',
      marginBottom: 12,
      alignItems: 'flex-start',
    },
    verseNumber: {
      fontFamily: fonts.bold,
      fontSize: 12,
      color: colors.primary,
      marginRight: 8,
      marginTop: 2,
      minWidth: 20,
    },
    verseText: {
      fontFamily: fonts.regular,
      color: colors.text,
      lineHeight: 24,
      flex: 1,
    },
    bottomBar: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: 20,
      paddingVertical: 12,
      backgroundColor: colors.surface,
      borderTopWidth: 1,
      borderTopColor: colors.border,
    },
    bottomBarBtn: {
      padding: 8,
    },
    bottomBarCenter: {
      flex: 1,
      alignItems: 'center',
    },
    bottomBarText: {
      fontFamily: fonts.bold,
      fontSize: 16,
      color: colors.text,
    },
    modalOverlay: {
      flex: 1,
      backgroundColor: colors.overlay,
      justifyContent: 'center',
      alignItems: 'center',
    },
    modalContent: {
      backgroundColor: colors.card,
      borderRadius: 16,
      padding: 20,
      width: '90%',
      maxHeight: '80%',
    },
    modalHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 16,
    },
    modalTitle: {
      fontFamily: fonts.bold,
      fontSize: 18,
      color: colors.text,
    },
    sheetTitle: {
      fontFamily: fonts.bold,
      fontSize: 18,
      color: colors.text,
      marginBottom: 16,
      textAlign: 'center',
    },
    closeIconBtn: {
      padding: 4,
    },
    modalListContainer: {
      paddingBottom: 16,
    },
    modalItem: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingVertical: 12,
      paddingHorizontal: 16,
      borderRadius: 8,
      marginBottom: 4,
    },
    modalItemActive: {
      backgroundColor: colors.primary + '20',
    },
    modalItemText: {
      fontFamily: fonts.regular,
      fontSize: 16,
      color: colors.text,
      flex: 1,
    },
    modalItemTextActive: {
      color: colors.primary,
      fontFamily: fonts.bold,
    },
    checkIcon: {
      marginLeft: 8,
    },
    chapterGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      justifyContent: 'space-between',
    },
    chapterButton: {
      width: '18%',
      aspectRatio: 1,
      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: 8,
      marginBottom: 8,
      backgroundColor: colors.surface,
    },
    chapterButtonActive: {
      backgroundColor: colors.primary,
    },
    chapterButtonText: {
      fontFamily: fonts.regular,
      fontSize: 16,
      color: colors.text,
    },
    chapterButtonTextActive: {
      color: colors.textInverse,
      fontFamily: fonts.bold,
    },
    modalCloseBtn: {
      backgroundColor: colors.primary,
      borderRadius: 8,
      paddingVertical: 12,
      alignItems: 'center',
      marginTop: 16,
    },
    modalCloseText: {
      fontFamily: fonts.bold,
      fontSize: 16,
      color: colors.textInverse,
    },
  });

  // Renderização do versículo
  const renderVerse = (verse: { number: number; content: string[] }) => (
    <View key={verse.number} style={styles.verseContainer}>
      <Text style={styles.verseNumber}>{verse.number}</Text>
      <Text style={[styles.verseText, { fontSize }]}>{verse.content.join(' ')}</Text>
    </View>
  );

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: colors.background }}>
      <StatusBar
        barStyle={colors.text === '#FFFFFF' ? 'light-content' : 'dark-content'}
        backgroundColor={colors.background}
      />
      {/* Header Superior */}
      <View style={styles.topHeader}>
        <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'flex-end', flex: 1 }}>
          <TouchableOpacity style={styles.headerIconBtn}>
            <Icon name="magnify" size={24} color={colors.text} />
          </TouchableOpacity>
          <TouchableOpacity style={styles.headerIconBtn} onPress={() => setShowReadingModal(true)}>
            <Icon name="format-size" size={22} color={colors.text} />
          </TouchableOpacity>
          <TouchableOpacity style={styles.versionBtn} onPress={() => setShowVersionModal(true)}>
            <Icon name="earth" size={18} color={colors.text} />
            <Text style={styles.versionText}>{bibleVersion.toUpperCase()}</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Banner de Anúncio */}
      <AdBanner
        size={BannerAdSize.BANNER}
        style={{ marginVertical: 8 }}
      />

      {/* Modal de ajustes de leitura */}
      <Modal visible={showReadingModal} animationType="slide" transparent onRequestClose={() => setShowReadingModal(false)}>
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.sheetTitle}>Ajuste de Tamanho da Fonte</Text>
            <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 8 }}>
              <TouchableOpacity onPress={() => setFontSize(f => Math.max(f - 1, 14))} style={{ padding: 8 }}>
                <Icon name="minus-circle-outline" size={24} color={colors.primary} />
              </TouchableOpacity>
              <View style={{ flex: 1, alignItems: 'center' }}>
                <Text style={{ fontSize: 18 }}>{fontSize}px</Text>
              </View>
              <TouchableOpacity onPress={() => setFontSize(f => Math.min(f + 1, 28))} style={{ padding: 8 }}>
                <Icon name="plus-circle-outline" size={24} color={colors.primary} />
              </TouchableOpacity>
            </View>
            <TouchableOpacity style={styles.modalCloseBtn} onPress={() => setShowReadingModal(false)}>
              <Text style={styles.modalCloseText}>Fechar</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
      {/* Modal de Seleção de Versão */}
      <Modal visible={showVersionModal} animationType="slide" transparent onRequestClose={() => setShowVersionModal(false)}>
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Selecione a Versão</Text>
            <FlatList
              data={translations}
              keyExtractor={item => item.abbrev}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={[styles.modalItem, item.abbrev === bibleVersion && styles.modalItemActive]}
                  onPress={() => {
                    setBibleVersion(item.abbrev);
                    setShowVersionModal(false);
                  }}
                >
                  <Text style={[styles.modalItemText, item.abbrev === bibleVersion && styles.modalItemTextActive]}>{item.abbrev.toUpperCase()} - {item.name}</Text>
                </TouchableOpacity>
              )}
            />
            <TouchableOpacity style={styles.modalCloseBtn} onPress={() => setShowVersionModal(false)}>
              <Text style={styles.modalCloseText}>Fechar</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
      {/* Título Centralizado */}
      <View style={styles.centerTitleContainer}>
        <Text style={styles.bookTitle}>{currentBook.name}</Text>
        <Text style={styles.chapterNumber}>{currentChapter}</Text>
      </View>
      {/* Conteúdo da Bíblia */}
      <ScrollView contentContainerStyle={styles.contentContainer} showsVerticalScrollIndicator={false}>
        {verses.map(renderVerse)}
      </ScrollView>
      {/* Barra de navegação inferior */}
      <View style={styles.bottomBar}>
        <TouchableOpacity
          style={styles.bottomBarBtn}
          disabled={currentChapter === 1}
          onPress={() => setCurrentChapter((prev) => Math.max(1, prev - 1))}
        >
          <Icon name="chevron-left" size={28} color={currentChapter === 1 ? colors.textSecondary : colors.primary} />
        </TouchableOpacity>
        <TouchableOpacity style={styles.bottomBarCenter} onPress={() => setShowBookModal(true)}>
          <Text style={styles.bottomBarText}>{`${currentBook.name} ${currentChapter}`}</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.bottomBarBtn}
          disabled={currentChapter === currentBook.numberOfChapters}
          onPress={() => setCurrentChapter((prev) => Math.min(currentBook.numberOfChapters, prev + 1))}
        >
          <Icon name="chevron-right" size={28} color={currentChapter === currentBook.numberOfChapters ? colors.textSecondary : colors.primary} />
        </TouchableOpacity>
      </View>
      {/* Modal de Seleção de Livro */}
      <Modal visible={showBookModal} animationType="slide" transparent onRequestClose={() => setShowBookModal(false)}>
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Selecione o Livro</Text>
              <TouchableOpacity onPress={() => setShowBookModal(false)} style={styles.closeIconBtn}>
                <Icon name="close" size={24} color={colors.text} />
              </TouchableOpacity>
            </View>
            <FlatList
              data={books}
              keyExtractor={item => item.id}
              showsVerticalScrollIndicator={false}
              contentContainerStyle={styles.modalListContainer}
              renderItem={({ item, index }) => (
                <TouchableOpacity
                  style={[styles.modalItem, index === currentBookIdx && styles.modalItemActive]}
                  onPress={() => {
                    setCurrentBookIdx(index);
                    setCurrentChapter(1);
                    setShowBookModal(false);
                    setTimeout(() => setShowChapterModal(true), 400); // Abre a modal de capítulos após fechar a de livros
                  }}
                >
                  <Text style={[styles.modalItemText, index === currentBookIdx && styles.modalItemTextActive]}>{item.name}</Text>
                  {index === currentBookIdx && <Icon name="check" size={20} color={colors.primary} style={styles.checkIcon} />}
                </TouchableOpacity>
              )}
            />
          </View>
        </View>
      </Modal>
      {/* Modal de Seleção de Capítulo */}
      <Modal visible={showChapterModal} animationType="slide" transparent onRequestClose={() => setShowChapterModal(false)}>
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>{currentBook.name}</Text>
              <TouchableOpacity onPress={() => setShowChapterModal(false)} style={styles.closeIconBtn}>
                <Icon name="close" size={24} color={colors.text} />
              </TouchableOpacity>
            </View>
            <View style={styles.chapterGrid}>
              {chapters.map((chapter) => (
                <TouchableOpacity
                  key={chapter}
                  style={[styles.chapterButton, chapter === currentChapter && styles.chapterButtonActive]}
                  onPress={() => {
                    setCurrentChapter(chapter);
                    setShowChapterModal(false);
                  }}
                >
                  <Text style={[styles.chapterButtonText, chapter === currentChapter && styles.chapterButtonTextActive]}>
                    {chapter}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

export default BibleScreen;