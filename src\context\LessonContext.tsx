import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Licao } from '../models/Licao';

interface LessonContextType {
  currentLesson: Licao | null;
  setCurrentLesson: (lesson: Licao | null) => void;
  currentLessonGuid: string | null;
  isLoading: boolean;
}

const LessonContext = createContext<LessonContextType | undefined>(undefined);

const STORAGE_KEY = '@current_lesson';

interface LessonProviderProps {
  children: ReactNode;
}

export const LessonProvider: React.FC<LessonProviderProps> = ({ children }) => {
  const [currentLesson, setCurrentLessonState] = useState<Licao | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Carregar lição do AsyncStorage ao inicializar
  useEffect(() => {
    const loadStoredLesson = async () => {
      try {
        const storedLesson = await AsyncStorage.getItem(STORAGE_KEY);
        if (storedLesson) {
          const lesson = JSON.parse(storedLesson) as Licao;
          console.log('📖 LessonContext - Lição carregada do storage:', lesson.title, 'GUID:', lesson.guid);
          setCurrentLessonState(lesson);
        } else {
          console.log('📖 LessonContext - Nenhuma lição encontrada no storage');
        }
      } catch (error) {
        console.error('❌ Erro ao carregar lição do storage:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadStoredLesson();
  }, []);

  // Função para definir a lição atual e salvar no AsyncStorage
  const setCurrentLesson = async (lesson: Licao | null) => {
    try {
      setCurrentLessonState(lesson);
      
      if (lesson) {
        await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(lesson));
        console.log('📖 LessonContext - Lição salva no storage:', lesson.title, 'GUID:', lesson.guid);
      } else {
        await AsyncStorage.removeItem(STORAGE_KEY);
        console.log('📖 LessonContext - Lição removida do storage');
      }
    } catch (error) {
      console.error('❌ Erro ao salvar lição no storage:', error);
    }
  };

  const value: LessonContextType = {
    currentLesson,
    setCurrentLesson,
    currentLessonGuid: currentLesson?.guid || null,
    isLoading,
  };

  return (
    <LessonContext.Provider value={value}>
      {children}
    </LessonContext.Provider>
  );
};

export const useLesson = (): LessonContextType => {
  const context = useContext(LessonContext);
  if (context === undefined) {
    throw new Error('useLesson deve ser usado dentro de um LessonProvider');
  }
  return context;
};
