# 🎨 Gerador de Ícones do App

Este script gera automaticamente os ícones do aplicativo em todos os tamanhos necessários para Android e iOS.

## 📖 Ícone Atual

O ícone atual é um **livro aberto** com:
- **Fundo verde** (#2D5A3D) com cantos arredondados
- **<PERSON><PERSON> branco** no centro representando estudo bíblico
- **<PERSON><PERSON> de texto** para ícones maiores
- **Design minimalista** e profissional

## 🚀 Como Usar

### Pré-requisitos
```bash
pip install Pillow
```

### Gerar Ícones
```bash
python scripts/generate-icons.py
```

### Aplicar no App
```bash
# Android
npx react-native run-android

# iOS
npx react-native run-ios
```

## 📱 Tamanhos Gerados

### Android
- **mipmap-mdpi**: 48x48px
- **mipmap-hdpi**: 72x72px  
- **mipmap-xhdpi**: 96x96px
- **mipmap-xxhdpi**: 144x144px
- **mipmap-xxxhdpi**: 192x192px

### iOS
- **20x20**: @1x, @2x, @3x
- **29x29**: @1x, @2x, @3x
- **40x40**: @1x, @2x, @3x
- **60x60**: @2x, @3x
- **76x76**: @1x, @2x
- **83.5x83.5**: @2x
- **1024x1024**: @1x (App Store)

## 📁 Arquivos Gerados

```
android/app/src/main/res/
├── mipmap-mdpi/
│   ├── ic_launcher.png
│   └── ic_launcher_round.png
├── mipmap-hdpi/
│   ├── ic_launcher.png
│   └── ic_launcher_round.png
└── ...

ios/EscolaSabatina/Images.xcassets/AppIcon.appiconset/
├── <EMAIL>
├── <EMAIL>
├── <EMAIL>
├── Contents.json
└── ...
```

## 🎨 Personalizar o Ícone

Para modificar o design do ícone, edite a função `create_book_icon()` no arquivo `generate-icons.py`:

```python
def create_book_icon(output_path, size):
    # Modificar cores
    bg_color = (45, 90, 61)  # Verde do fundo
    book_color = (255, 255, 255)  # Cor do livro
    
    # Modificar proporções
    corner_radius = size // 6  # Raio dos cantos
    margin = size // 8  # Margem do ícone
    
    # Adicionar elementos personalizados
    # ...
```

## 🔧 Solução de Problemas

### Erro de Dependências
```bash
pip install --upgrade Pillow
```

### Ícone Não Aparece
1. Limpar cache: `npx react-native start --reset-cache`
2. Recompilar: `npx react-native run-android`
3. Verificar se os arquivos foram gerados nas pastas corretas

### iOS - Ícone Não Atualiza
1. Limpar build: `cd ios && xcodebuild clean`
2. Recompilar: `npx react-native run-ios`

## ✅ Verificação

Após executar o script, você deve ver:
- ✅ Ícones gerados em todas as resoluções
- ✅ Arquivos PNG criados nas pastas corretas
- ✅ Contents.json criado para iOS
- ✅ Novo ícone aparece no launcher após recompilação

## 📝 Notas

- O script usa apenas **Pillow** (sem dependências nativas)
- Funciona no **Windows, macOS e Linux**
- Gera ícones **otimizados** para cada plataforma
- **Cantos arredondados** automáticos para Android
- **Múltiplas resoluções** para suporte a todos os dispositivos
