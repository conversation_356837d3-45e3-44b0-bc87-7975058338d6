import React from 'react';
import { View, StyleSheet, Image } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { colors } from '../theme/colors';

interface LogoProps {
  size?: number;
  showBackground?: boolean;
  useIcon?: boolean; // Para usar ícone quando não tiver a imagem
}

const Logo: React.FC<LogoProps> = ({ 
  size = 40, 
  showBackground = false, 
  useIcon = true 
}) => {
  
  // Se tiver a imagem da logo, use ela
  // const logoSource = require('../../assets/logo.png');
  
  if (useIcon) {
    // Usar ícone temporário até ter a imagem
    return (
      <View style={[
        styles.container,
        { 
          width: size, 
          height: size,
          backgroundColor: showBackground ? colors.primary : 'transparent',
          borderRadius: showBackground ? size * 0.2 : 0,
        }
      ]}>
        <Icon 
          name="book-open-variant" 
          size={size * 0.7} 
          color={showBackground ? '#fff' : colors.primary} 
        />
      </View>
    );
  }

  // Quando tiver a imagem real, descomente isso:
  /*
  return (
    <View style={[
      styles.container,
      { 
        width: size, 
        height: size,
        backgroundColor: showBackground ? '#fff' : 'transparent',
        borderRadius: showBackground ? size * 0.2 : 0,
      }
    ]}>
      <Image 
        source={logoSource}
        style={{
          width: size * 0.8,
          height: size * 0.8,
        }}
        resizeMode="contain"
      />
    </View>
  );
  */
};

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default Logo;
