import React from 'react';
import { View, Text, StyleSheet, StatusBar, TouchableOpacity, ScrollView, Dimensions } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { fonts } from '../theme/fonts';
import { useTheme } from '../context/ThemeContext';
import Logo from '../components/Logo';
import AdBanner from '../components/AdBanner';
import { BannerAdSize } from 'react-native-google-mobile-ads';
import { useAuth } from '../context/AuthContext';
import { useLesson } from '../context/LessonContext';
import { getTrimestreAtual } from '../services/trimestre';
import { useEffect, useState } from 'react';
import { Trimestre } from '../models/Trimestre';
import { getLicaoDaSemana } from '../services/licao';
import { Licao } from '../models/Licao';
import { CompositeNavigationProp, useNavigation } from '@react-navigation/native';
import { BottomTabNavigationProp } from '@react-navigation/bottom-tabs';
import { StackNavigationProp } from '@react-navigation/stack';
import { useInterstitialAd } from '../hooks/useInterstitialAd';

// Defina os tipos das rotas
// Ajuste conforme sua estrutura de navegação

type RootTabParamList = {
  Home: undefined;
  Lição: { screen?: string; params?: { guid?: string } } | undefined;
  Bíblia: undefined;
  Buscar: undefined;
  Mais: undefined;
};

type LessonStackParamList = {
  Lição: { guid?: string } | undefined;
};

const { width } = Dimensions.get('window');

const HomeScreen = () => {
  const { token } = useAuth();
  const { colors } = useTheme();
  const { setCurrentLesson } = useLesson();

  // Array de acesso rápido com cores dinâmicas
  const quickAccess = [
    { key: 'bible', label: 'Minha Bíblia', icon: 'book', color: colors.accentBlue },
    { key: 'notes', label: 'Minhas Anotações', icon: 'pencil-outline', color: colors.accentGreen },
    { key: 'videos', label: 'Vídeos', icon: 'play-circle-outline', color: colors.accentPurple },
    { key: 'calendar', label: 'Próximas Lições', icon: 'calendar-month-outline', color: colors.secondary },
  ];
  const [trimestre, setTrimestre] = useState<Trimestre | null>(null);
  const [licaoSemana, setLicaoSemana] = useState<Licao | null>(null);
  const navigation = useNavigation<
    CompositeNavigationProp<
      BottomTabNavigationProp<RootTabParamList, 'Lição'>,
      StackNavigationProp<LessonStackParamList, 'Lição'>
    >
  >();

  // Hook para anúncios intersticiais
  const { navigateWithAd } = useInterstitialAd({
    showOnNavigate: true,
    excludeRoutes: ['Home'], // Não mostrar anúncio ao voltar para Home
  });

  useEffect(() => {
    if (token) {
      getTrimestreAtual(token).then(setTrimestre).catch(console.error);
    }
  }, [token]);

  useEffect(() => {
    if (token && trimestre) {
      getLicaoDaSemana(token, trimestre.guid)
        .then((licao) => {
          console.log('🏠 HomeScreen - Lição carregada:', licao?.title, 'GUID:', licao?.guid);
          setLicaoSemana(licao);
          // Salvar no contexto global para acesso em outras telas
          setCurrentLesson(licao);
        })
        .catch(console.error);
    }
  }, [token, trimestre, setCurrentLesson]);

  // Estilos dinâmicos baseados no tema
  const styles = StyleSheet.create({
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: 20,
      paddingTop: 16,
      paddingBottom: 12,
      backgroundColor: colors.background,
    },
    headerTitle: {
      fontFamily: fonts.bold,
      fontSize: 22,
      color: colors.primary,
      letterSpacing: 0.5,
    },
    headerIconBtn: {
      marginLeft: 16,
      padding: 4,
    },
    bannerShadow: {
      shadowColor: colors.shadow,
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.12,
      shadowRadius: 12,
      elevation: 4,
      borderRadius: 18,
      marginTop: 8,
      marginBottom: 24,
    },
    bannerBg: {
      borderRadius: 18,
      padding: 24,
      backgroundColor: colors.card,
      overflow: 'hidden',
    },
    bannerTitle: {
      fontFamily: fonts.bold,
      fontSize: 18,
      color: colors.accent,
      marginBottom: 4,
    },
    lessonTitle: {
      fontFamily: fonts.bold,
      fontSize: 20,
      color: colors.text,
      marginBottom: 8,
      lineHeight: 26,
    },
    progressText: {
      fontFamily: fonts.regular,
      fontSize: 14,
      color: colors.textSecondary,
      marginBottom: 16,
    },
    progressBarBg: {
      height: 6,
      backgroundColor: colors.border,
      borderRadius: 3,
      marginBottom: 16,
    },
    progressBarFill: {
      height: 6,
      backgroundColor: colors.primary,
      borderRadius: 3,
    },
    continueBtn: {
      backgroundColor: colors.primary,
      paddingVertical: 12,
      paddingHorizontal: 24,
      borderRadius: 12,
      alignSelf: 'flex-start',
    },
    continueBtnText: {
      fontFamily: fonts.bold,
      fontSize: 16,
      color: colors.textInverse,
    },
    cardsGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      justifyContent: 'space-between',
      marginTop: 8,
    },
    card: {
      width: (width - 60) / 2,
      borderRadius: 14,
      paddingVertical: 24,
      alignItems: 'center',
      marginBottom: 16,
      borderWidth: 1.5,
      backgroundColor: colors.card,
      shadowColor: colors.shadow,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.08,
      shadowRadius: 6,
      elevation: 1,
    },
    cardIconCircle: {
      width: 54,
      height: 54,
      borderRadius: 27,
      alignItems: 'center',
      justifyContent: 'center',
      marginBottom: 10,
    },
    cardLabel: {
      fontFamily: fonts.regular,
      fontSize: 15,
      color: colors.text,
      textAlign: 'center',
    },
  });

  return (
    <View style={{ flex: 1, backgroundColor: colors.background }}>
      <StatusBar
        barStyle={colors.background === '#FFFFFF' ? 'dark-content' : 'light-content'}
        backgroundColor={colors.background}
      />
      {/* Top Bar (StatusBar já cobre, mas pode customizar aqui se quiser) */}
      {/* App Header */}
      <View style={styles.header}>
        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          {/* Logo do app */}
          <Logo size={32} style={{ marginRight: 8 }} />
          <Text style={styles.headerTitle}>Escola Sabatina</Text>
        </View>
        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          <TouchableOpacity style={styles.headerIconBtn}>
            <Icon name="account-circle-outline" size={26} color={colors.text} />
          </TouchableOpacity>
        </View>
      </View>

      {/* Banner de Anúncio */}
      <AdBanner
        size={BannerAdSize.BANNER}
        style={{ marginVertical: 8 }}
      />

      <ScrollView contentContainerStyle={{ paddingHorizontal: 20, paddingBottom: 24 }} showsVerticalScrollIndicator={false}>
        {/* Banner da Lição da Semana */}
        <View style={styles.bannerShadow}>
          <View style={styles.bannerBg}>
            <Text style={styles.bannerTitle}>Comentários da Semana</Text>
            <Text style={styles.lessonTitle}>
              {licaoSemana ? licaoSemana.title.replace(/^Lição\s*/i, '') : 'Carregando...'}
            </Text>
            <Text style={styles.progressText}>
              {licaoSemana && licaoSemana.numero ? `${Math.round((licaoSemana.numero / 13) * 100)}% Concluído` : '0% Concluído'}
            </Text>
            <View style={styles.progressBarBg}>
              <View style={[styles.progressBarFill, { width: `${licaoSemana && licaoSemana.numero ? Math.round((licaoSemana.numero / 13) * 100) : 0}%` }]} />
            </View>
            <TouchableOpacity style={styles.continueBtn} onPress={() => {
              if (licaoSemana) {
                navigateWithAd('Comentários', { screen: 'Comentários', params: { guid: licaoSemana.guid } });
              }
            }}>
              <Text style={styles.continueBtnText}>Continuar Lendo</Text>
            </TouchableOpacity>
          </View>
        </View>
        {/* Cards de Acesso Rápido */}
        <View style={styles.cardsGrid}>
          {quickAccess.map((item) => (
            <TouchableOpacity
              key={item.key}
              style={[styles.card, { borderColor: item.color }]}
              onPress={() => {
                if (item.key === 'notes') {
                  navigateWithAd('Anotações');
                }
                if (item.key === 'videos') {
                  navigateWithAd('Videos');
                }
                if (item.key === 'bible') {
                  navigateWithAd('Bíblia');
                }
                // Adicione navegação para outros cards se desejar
              }}
            >
              <View style={[styles.cardIconCircle, { backgroundColor: item.color + '22' }]}> 
                <Icon name={item.icon} size={32} color={item.color} />
              </View>
              <Text style={styles.cardLabel}>{item.label}</Text>
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>
    </View>
  );
};

// Estilos movidos para dentro do componente para suporte a tema dinâmico

export default HomeScreen;