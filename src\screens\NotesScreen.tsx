import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
  TextInput,
  FlatList,
  Modal,
  Alert,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { fonts } from '../theme/fonts';
import { useTheme } from '../context/ThemeContext';
import AdBanner from '../components/AdBanner';
import { BannerAdSize } from 'react-native-google-mobile-ads';

interface Note {
  id: string;
  title: string;
  content: string;
  lesson: string;
  tags: string[];
  createdAt: Date;
  updatedAt: Date;
  hasMedia: boolean;
}

const STORAGE_KEY = '@escola_sabatina_notes';
const FONT_SIZE_KEY = '@escola_sabatina_font_size';

const NotesScreen = ({ navigation }: any) => {
  const { colors } = useTheme();
  const [notes, setNotes] = useState<Note[]>([]);
  const [searchText, setSearchText] = useState('');
  const [filterLesson, setFilterLesson] = useState('');
  const [showFilterModal, setShowFilterModal] = useState(false);
  const [filteredNotes, setFilteredNotes] = useState<Note[]>([]);
  const [loading, setLoading] = useState(true);
  const [fontSize, setFontSize] = useState(16);

  // Carregar anotações do AsyncStorage
  const loadNotes = async () => {
    try {
      setLoading(true);
      const storedNotes = await AsyncStorage.getItem(STORAGE_KEY);
      if (storedNotes) {
        const parsedNotes = JSON.parse(storedNotes).map((note: any) => ({
          ...note,
          createdAt: new Date(note.createdAt),
          updatedAt: new Date(note.updatedAt),
        }));
        setNotes(parsedNotes);
      } else {
        // Dados de exemplo para demonstração (apenas na primeira vez)
        const sampleNotes: Note[] = [
          {
            id: '1',
            title: 'Milagres à margem do lago',
            content: 'Jesus realizou vários milagres importantes à margem do lago de Genesaré. O primeiro milagre foi a pesca milagrosa que demonstrou o poder de Cristo sobre a natureza...',
            lesson: '05 - Milagres à margem do lago',
            tags: ['#milagres', '#jesus', '#pesca'],
            createdAt: new Date('2024-01-15'),
            updatedAt: new Date('2024-01-15'),
            hasMedia: false,
          },
          {
            id: '2',
            title: 'A parábola do semeador',
            content: 'Esta parábola nos ensina sobre os diferentes tipos de solo que representam os diferentes tipos de coração humano. Precisamos preparar nosso coração para receber a Palavra...',
            lesson: '03 - A parábola do semeador',
            tags: ['#parábolas', '#semeador', '#coração'],
            createdAt: new Date('2024-01-10'),
            updatedAt: new Date('2024-01-12'),
            hasMedia: true,
          },
        ];
        setNotes(sampleNotes);
        await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(sampleNotes));
      }
    } catch (error) {
      console.error('Erro ao carregar anotações:', error);
      Alert.alert('Erro', 'Não foi possível carregar as anotações.');
    } finally {
      setLoading(false);
    }
  };

  // Carregar preferência de tamanho da fonte
  const loadFontSize = async () => {
    try {
      const savedFontSize = await AsyncStorage.getItem(FONT_SIZE_KEY);
      if (savedFontSize) {
        setFontSize(parseInt(savedFontSize));
      }
    } catch (error) {
      console.error('Erro ao carregar tamanho da fonte:', error);
    }
  };

  // Salvar anotações no AsyncStorage
  const saveNotes = async (newNotes: Note[]) => {
    try {
      await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(newNotes));
    } catch (error) {
      console.error('Erro ao salvar anotações:', error);
      Alert.alert('Erro', 'Não foi possível salvar as anotações.');
    }
  };

  // Adicionar nova anotação
  const addNote = async (newNote: Note) => {
    const updatedNotes = [newNote, ...notes];
    setNotes(updatedNotes);
    await saveNotes(updatedNotes);
  };

  // Atualizar anotação existente
  const updateNote = async (updatedNote: Note) => {
    const updatedNotes = notes.map(note => 
      note.id === updatedNote.id ? updatedNote : note
    );
    setNotes(updatedNotes);
    await saveNotes(updatedNotes);
  };

  // Excluir anotação
  const deleteNote = async (noteId: string) => {
    const updatedNotes = notes.filter(note => note.id !== noteId);
    setNotes(updatedNotes);
    await saveNotes(updatedNotes);
  };

  useEffect(() => {
    loadNotes();
    loadFontSize();
  }, []);

  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      loadNotes();
    });

    return unsubscribe;
  }, [navigation]);

  useEffect(() => {
    filterNotes();
  }, [notes, searchText, filterLesson]);

  const filterNotes = () => {
    let filtered = notes;

    if (searchText) {
      filtered = filtered.filter(
        note =>
          note.title.toLowerCase().includes(searchText.toLowerCase()) ||
          note.content.toLowerCase().includes(searchText.toLowerCase()) ||
          note.tags.some(tag => tag.toLowerCase().includes(searchText.toLowerCase()))
      );
    }

    if (filterLesson) {
      filtered = filtered.filter(note => note.lesson === filterLesson);
    }

    setFilteredNotes(filtered);
  };

  const getLessons = () => {
    const lessons = notes.map(note => note.lesson);
    return [...new Set(lessons)];
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });
  };

  const renderNoteItem = ({ item }: { item: Note }) => (
    <TouchableOpacity
      style={styles.noteItem}
      onPress={() => navigation.navigate('EditNote', { note: item })}
    >
      <View style={styles.noteHeader}>
        <View style={styles.noteTitleContainer}>
          <Text style={styles.noteTitle} numberOfLines={1}>
            {item.title}
          </Text>
          {item.hasMedia && (
            <Icon name="image" size={16} color={colors.primary} style={styles.mediaIcon} />
          )}
        </View>
        <Text style={styles.noteDate}>{formatDate(item.updatedAt)}</Text>
      </View>
      
      <Text style={styles.noteLesson}>{item.lesson}</Text>
      
      <Text style={[styles.noteContent, { fontSize: fontSize - 2 }]} numberOfLines={3}>
        {item.content}
      </Text>
      
      {item.tags.length > 0 && (
        <View style={styles.tagsContainer}>
          {item.tags.slice(0, 3).map((tag, index) => (
            <View key={index} style={styles.tag}>
              <Text style={styles.tagText}>{tag}</Text>
            </View>
          ))}
          {item.tags.length > 3 && (
            <Text style={styles.moreTags}>+{item.tags.length - 3}</Text>
          )}
        </View>
      )}
    </TouchableOpacity>
  );

  // Estilos dinâmicos baseados no tema
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: 16,
      paddingVertical: 12,
      backgroundColor: colors.card,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    backButton: {
      padding: 8,
      borderRadius: 20,
    },
    headerTitle: {
      fontFamily: fonts.bold,
      fontSize: 18,
      color: colors.text,
    },
    addButton: {
      padding: 8,
      borderRadius: 20,
    },
    searchContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 16,
      paddingVertical: 12,
      gap: 12,
    },
    searchBar: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: colors.card,
      borderRadius: 12,
      paddingHorizontal: 12,
      paddingVertical: 8,
    },
    searchInput: {
      flex: 1,
      marginLeft: 8,
      fontFamily: fonts.regular,
      fontSize: 16,
      color: colors.text,
    },
    filterButton: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: colors.primary + '15',
      borderRadius: 12,
      paddingHorizontal: 12,
      paddingVertical: 8,
      gap: 4,
    },
    filterButtonText: {
      fontFamily: fonts.regular,
      fontSize: 14,
      color: colors.primary,
    },
    notesList: {
      paddingHorizontal: 16,
      paddingBottom: 20,
    },
    noteItem: {
      backgroundColor: colors.card,
      borderRadius: 16,
      padding: 16,
      marginBottom: 12,
      borderWidth: 1,
      borderColor: colors.border,
    },
    noteHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginBottom: 8,
    },
    noteTitleContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
    },
    noteTitle: {
      fontFamily: fonts.bold,
      fontSize: 16,
      color: colors.text,
      flex: 1,
    },
    mediaIcon: {
      marginLeft: 8,
    },
    noteDate: {
      fontFamily: fonts.regular,
      fontSize: 12,
      color: colors.textSecondary,
    },
    noteLesson: {
      fontFamily: fonts.regular,
      fontSize: 14,
      color: colors.primary,
      marginBottom: 8,
    },
    noteContent: {
      fontFamily: fonts.regular,
      fontSize: 14,
      color: colors.text,
      lineHeight: 20,
      marginBottom: 12,
    },
    tagsContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      flexWrap: 'wrap',
      gap: 6,
    },
    tag: {
      backgroundColor: colors.primary + '15',
      borderRadius: 12,
      paddingHorizontal: 8,
      paddingVertical: 4,
    },
    tagText: {
      fontFamily: fonts.regular,
      fontSize: 12,
      color: colors.primary,
    },
    moreTags: {
      fontFamily: fonts.regular,
      fontSize: 12,
      color: colors.textSecondary,
    },
    emptyState: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingVertical: 60,
    },
    emptyStateTitle: {
      fontFamily: fonts.bold,
      fontSize: 18,
      color: colors.text,
      marginTop: 16,
      marginBottom: 8,
    },
    emptyStateSubtitle: {
      fontFamily: fonts.regular,
      fontSize: 14,
      color: colors.textSecondary,
      textAlign: 'center',
      marginBottom: 24,
      paddingHorizontal: 32,
    },
    createFirstNoteBtn: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: colors.primary,
      borderRadius: 12,
      paddingHorizontal: 20,
      paddingVertical: 12,
      gap: 8,
    },
    createFirstNoteText: {
      fontFamily: fonts.bold,
      fontSize: 16,
      color: colors.textInverse,
    },
    modalOverlay: {
      flex: 1,
      backgroundColor: colors.overlay,
      justifyContent: 'flex-end',
    },
    modalContent: {
      backgroundColor: colors.card,
      borderTopLeftRadius: 24,
      borderTopRightRadius: 24,
      paddingTop: 20,
      paddingBottom: 40,
      maxHeight: '70%',
    },
    modalHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: 20,
      paddingBottom: 16,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    modalTitle: {
      fontFamily: fonts.bold,
      fontSize: 18,
      color: colors.text,
    },
    closeIconBtn: {
      padding: 4,
      borderRadius: 20,
    },
    filterList: {
      paddingHorizontal: 20,
      paddingTop: 16,
    },
    filterItem: {
      paddingVertical: 16,
      paddingHorizontal: 16,
      borderRadius: 12,
      marginBottom: 8,
      backgroundColor: colors.background,
    },
    filterItemActive: {
      backgroundColor: colors.primary + '15',
      borderWidth: 1,
      borderColor: colors.primary + '30',
    },
    filterItemText: {
      fontFamily: fonts.regular,
      fontSize: 16,
      color: colors.text,
    },
    filterItemTextActive: {
      color: colors.primary,
      fontFamily: fonts.bold,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingVertical: 60,
    },
    loadingText: {
      fontFamily: fonts.regular,
      fontSize: 16,
      color: colors.textSecondary,
      marginTop: 16,
    },
  });

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Icon name="notebook-outline" size={64} color={colors.textSecondary} />
      <Text style={styles.emptyStateTitle}>Nenhuma anotação encontrada</Text>
      <Text style={styles.emptyStateSubtitle}>
        {searchText || filterLesson
          ? 'Tente ajustar os filtros de busca'
          : 'Crie sua primeira anotação para começar'}
      </Text>
      {!searchText && !filterLesson && (
        <TouchableOpacity
          style={styles.createFirstNoteBtn}
          onPress={() => navigation.navigate('EditNote', { note: null })}
        >
          <Icon name="plus" size={20} color={colors.textInverse} />
          <Text style={styles.createFirstNoteText}>Criar primeira anotação</Text>
        </TouchableOpacity>
      )}
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
            <Icon name="arrow-left" size={24} color={colors.text} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Minhas Anotações</Text>
          <TouchableOpacity
            style={styles.addButton}
            onPress={() => navigation.navigate('EditNote', { note: null })}
          >
            <Icon name="plus" size={24} color={colors.primary} />
          </TouchableOpacity>
        </View>
        <View style={styles.loadingContainer}>
          <Icon name="loading" size={48} color={colors.primary} />
          <Text style={styles.loadingText}>Carregando anotações...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Icon name="arrow-left" size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Minhas Anotações</Text>
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => navigation.navigate('EditNote', { note: null })}
        >
          <Icon name="plus" size={24} color={colors.primary} />
        </TouchableOpacity>
      </View>

      {/* Banner de Anúncio */}
      <AdBanner
        size={BannerAdSize.BANNER}
        style={{ marginVertical: 8 }}
      />

      {/* Search and Filters */}
      <View style={styles.searchContainer}>
        <View style={styles.searchBar}>
          <Icon name="magnify" size={20} color={colors.textSecondary} />
          <TextInput
            style={styles.searchInput}
            placeholder="Buscar anotações..."
            placeholderTextColor={colors.textSecondary}
            value={searchText}
            onChangeText={setSearchText}
          />
          {searchText.length > 0 && (
            <TouchableOpacity onPress={() => setSearchText('')}>
              <Icon name="close" size={20} color={colors.textSecondary} />
            </TouchableOpacity>
          )}
        </View>
        
        <TouchableOpacity
          style={styles.filterButton}
          onPress={() => setShowFilterModal(true)}
        >
          <Icon name="filter-variant" size={20} color={colors.primary} />
          <Text style={styles.filterButtonText}>
            {filterLesson ? 'Filtrado' : 'Filtrar'}
          </Text>
        </TouchableOpacity>
      </View>

      {/* Notes List */}
      <FlatList
        data={filteredNotes}
        renderItem={renderNoteItem}
        keyExtractor={item => item.id}
        contentContainerStyle={styles.notesList}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={renderEmptyState}
      />

      {/* Filter Modal */}
      <Modal
        visible={showFilterModal}
        animationType="slide"
        transparent
        onRequestClose={() => setShowFilterModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Filtrar por Lição</Text>
              <TouchableOpacity
                onPress={() => setShowFilterModal(false)}
                style={styles.closeIconBtn}
              >
                <Icon name="close" size={24} color={colors.text} />
              </TouchableOpacity>
            </View>
            
            <ScrollView style={styles.filterList}>
              <TouchableOpacity
                style={[styles.filterItem, !filterLesson && styles.filterItemActive]}
                onPress={() => {
                  setFilterLesson('');
                  setShowFilterModal(false);
                }}
              >
                <Text style={[styles.filterItemText, !filterLesson && styles.filterItemTextActive]}>
                  Todas as lições
                </Text>
              </TouchableOpacity>
              
              {getLessons().map((lesson, index) => (
                <TouchableOpacity
                  key={index}
                  style={[styles.filterItem, filterLesson === lesson && styles.filterItemActive]}
                  onPress={() => {
                    setFilterLesson(lesson);
                    setShowFilterModal(false);
                  }}
                >
                  <Text style={[styles.filterItemText, filterLesson === lesson && styles.filterItemTextActive]}>
                    {lesson}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

export default NotesScreen;