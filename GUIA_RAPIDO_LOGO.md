# 🚀 Guia <PERSON>o - Implementar Logo

## ⚡ **<PERSON><PERSON><PERSON><PERSON>pid<PERSON>:**

### **1. Use o App Icon Generator Online:**
👉 **Acesse**: https://appicon.co/

### **2. Faça Upload da Sua Imagem:**
- <PERSON><PERSON><PERSON>e a imagem da logo verde com o livro
- Escolha "Android" como plataforma
- Clique em "Generate"

### **3. Baixe e Extraia:**
- Baixe o arquivo ZIP
- Extraia os arquivos

### **4. Substitua os Arquivos:**
Copie os arquivos para as respectivas pastas:

```
Arquivo baixado → Destino no projeto
ic_launcher.png (48x48) → android/app/src/main/res/mipmap-mdpi/ic_launcher.png
ic_launcher.png (72x72) → android/app/src/main/res/mipmap-hdpi/ic_launcher.png
ic_launcher.png (96x96) → android/app/src/main/res/mipmap-xhdpi/ic_launcher.png
ic_launcher.png (144x144) → android/app/src/main/res/mipmap-xxhdpi/ic_launcher.png
ic_launcher.png (192x192) → android/app/src/main/res/mipmap-xxxhdpi/ic_launcher.png
```

### **5. Teste:**
```bash
npx react-native run-android
```

## 🎯 **O que Você Verá:**

### **✅ Splash Screen:**
- Logo verde com livro branco
- "Escola Sabatina" em branco
- "Estudos Bíblicos" como subtítulo
- Animação de loading

### **✅ Header do App:**
- Logo pequena no canto esquerdo
- Título "Escola Sabatina"

### **✅ Ícone do App:**
- Sua logo aparecerá na lista de apps
- Ícone adaptativo no Android 8.0+

## 🔧 **Se Quiser Personalizar:**

### **Alterar Cores do Splash:**
Edite `src/screens/SplashScreen.tsx` linha 45:
```typescript
backgroundColor: colors.primary, // Mude esta cor
```

### **Alterar Texto:**
Edite `src/screens/SplashScreen.tsx` linhas 30-31:
```typescript
<Text style={styles.appName}>Escola Sabatina</Text>
<Text style={styles.subtitle}>Estudos Bíblicos</Text>
```

### **Usar Imagem Real da Logo:**
Edite `src/components/Logo.tsx` e descomente as linhas 32-50.

## ⏱️ **Tempo Estimado:**
- **5 minutos** para gerar os ícones
- **2 minutos** para copiar os arquivos
- **1 minuto** para testar

**Total: 8 minutos para ter a logo completa!** 🎉
