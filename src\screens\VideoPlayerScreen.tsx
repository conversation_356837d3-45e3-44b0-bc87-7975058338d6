import React, { useState } from 'react';
import { View, StyleSheet, SafeAreaView, TouchableOpacity, Text, Alert } from 'react-native';
import YoutubePlayer from 'react-native-youtube-iframe';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { colors } from '../theme/colors';

const VideoPlayerScreen = ({ route, navigation }: any) => {
  const { videoId, title } = route.params;
  const [playing, setPlaying] = useState(false);
  const [error, setError] = useState('');

  console.log('VideoPlayerScreen - YouTube Video ID:', videoId);
  console.log('VideoPlayerScreen - Título:', title);

  const onStateChange = (state: string) => {
    console.log('Estado do player mudou para:', state);
    if (state === 'ended') {
      setPlaying(false);
    }
  };

  const onError = (error: string) => {
    console.error('Erro no YouTube player:', error);
    setError('Erro ao reproduzir o vídeo do YouTube');
    Alert.alert(
      'Erro de Reprodução',
      `Não foi possível reproduzir o vídeo do YouTube.\n\nErro: ${error}\n\nVerifique sua conexão com a internet.`,
      [
        { text: 'OK', onPress: () => navigation.goBack() }
      ]
    );
  };

  const onReady = () => {
    console.log('YouTube player está pronto');
    setError('');
  };

  return (
    <SafeAreaView style={styles.container}>
      <TouchableOpacity style={styles.closeBtn} onPress={() => navigation.goBack()}>
        <Icon name="close" size={32} color={colors.text} />
      </TouchableOpacity>

      <View style={styles.titleContainer}>
        <Text style={styles.titleText} numberOfLines={2}>{title}</Text>
      </View>

      <View style={styles.videoContainer}>
        {error ? (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>{error}</Text>
            <TouchableOpacity style={styles.retryBtn} onPress={() => navigation.goBack()}>
              <Text style={styles.retryText}>Voltar</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <YoutubePlayer
            height={250}
            play={playing}
            videoId={videoId}
            onChangeState={onStateChange}
            onError={onError}
            onReady={onReady}
            webViewStyle={styles.webView}
            webViewProps={{
              androidLayerType: 'hardware'
            }}
          />
        )}
      </View>

      <View style={styles.controlsContainer}>
        <TouchableOpacity
          style={styles.playButton}
          onPress={() => setPlaying(!playing)}
        >
          <Icon
            name={playing ? "pause" : "play"}
            size={24}
            color="#fff"
          />
          <Text style={styles.playButtonText}>
            {playing ? "Pausar" : "Reproduzir"}
          </Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  closeBtn: {
    position: 'absolute',
    top: 24,
    right: 24,
    zIndex: 10,
    backgroundColor: 'rgba(0,0,0,0.5)',
    borderRadius: 20,
    padding: 8,
  },
  titleContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginTop: 60,
  },
  titleText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  videoContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 16,
  },
  webView: {
    backgroundColor: '#000',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    color: '#fff',
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
  },
  retryBtn: {
    backgroundColor: colors.primary,
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  retryText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  controlsContainer: {
    paddingHorizontal: 16,
    paddingVertical: 20,
    alignItems: 'center',
  },
  playButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.primary,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 25,
  },
  playButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
});

export default VideoPlayerScreen; 