import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Alert, Clipboard } from 'react-native';
import { useFirebaseMessaging } from '../hooks/useFirebaseMessaging';
import { colors } from '../theme/colors';
import { fonts } from '../theme/fonts';

const NotificationTest: React.FC = () => {
  const { fcmToken, hasPermission, isLoading, subscribeToTopic, unsubscribeFromTopic } = useFirebaseMessaging();

  const copyTokenToClipboard = () => {
    if (fcmToken) {
      Clipboard.setString(fcmToken);
      Alert.alert('Token Copiado', 'Token FCM copiado para a área de transferência');
    }
  };

  const handleSubscribeGeneral = async () => {
    try {
      await subscribeToTopic('escola_sabatina_geral');
      Alert.alert('Sucesso', 'Inscrito em notificações gerais');
    } catch (error) {
      Alert.alert('Erro', 'Falha ao se inscrever');
    }
  };

  const handleSubscribeLessons = async () => {
    try {
      await subscribeToTopic('licoes_semanais');
      Alert.alert('Sucesso', 'Inscrito em lições semanais');
    } catch (error) {
      Alert.alert('Erro', 'Falha ao se inscrever');
    }
  };

  const handleUnsubscribeGeneral = async () => {
    try {
      await unsubscribeFromTopic('escola_sabatina_geral');
      Alert.alert('Sucesso', 'Desinscrito de notificações gerais');
    } catch (error) {
      Alert.alert('Erro', 'Falha ao se desinscrever');
    }
  };

  const handleUnsubscribeLessons = async () => {
    try {
      await unsubscribeFromTopic('licoes_semanais');
      Alert.alert('Sucesso', 'Desinscrito de lições semanais');
    } catch (error) {
      Alert.alert('Erro', 'Falha ao se desinscrever');
    }
  };

  if (isLoading) {
    return (
      <View style={styles.container}>
        <Text style={styles.loadingText}>Carregando Firebase Messaging...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Text style={styles.title}>🔔 Teste de Notificações</Text>
      
      <View style={styles.statusContainer}>
        <Text style={styles.statusLabel}>Status da Permissão:</Text>
        <Text style={[styles.statusValue, { color: hasPermission ? colors.primary : colors.error }]}>
          {hasPermission ? '✅ Permitida' : '❌ Negada'}
        </Text>
      </View>

      {fcmToken && (
        <View style={styles.tokenContainer}>
          <Text style={styles.tokenLabel}>Token FCM:</Text>
          <TouchableOpacity onPress={copyTokenToClipboard} style={styles.tokenButton}>
            <Text style={styles.tokenText} numberOfLines={3}>
              {fcmToken}
            </Text>
            <Text style={styles.copyHint}>Toque para copiar</Text>
          </TouchableOpacity>
        </View>
      )}

      <View style={styles.buttonsContainer}>
        <Text style={styles.sectionTitle}>Tópicos de Notificação:</Text>
        
        <View style={styles.topicContainer}>
          <Text style={styles.topicName}>📢 Notificações Gerais</Text>
          <View style={styles.topicButtons}>
            <TouchableOpacity onPress={handleSubscribeGeneral} style={[styles.button, styles.subscribeButton]}>
              <Text style={styles.buttonText}>Inscrever</Text>
            </TouchableOpacity>
            <TouchableOpacity onPress={handleUnsubscribeGeneral} style={[styles.button, styles.unsubscribeButton]}>
              <Text style={styles.buttonText}>Desinscrever</Text>
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.topicContainer}>
          <Text style={styles.topicName}>📖 Lições Semanais</Text>
          <View style={styles.topicButtons}>
            <TouchableOpacity onPress={handleSubscribeLessons} style={[styles.button, styles.subscribeButton]}>
              <Text style={styles.buttonText}>Inscrever</Text>
            </TouchableOpacity>
            <TouchableOpacity onPress={handleUnsubscribeLessons} style={[styles.button, styles.unsubscribeButton]}>
              <Text style={styles.buttonText}>Desinscrever</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>

      <View style={styles.infoContainer}>
        <Text style={styles.infoTitle}>ℹ️ Como testar:</Text>
        <Text style={styles.infoText}>
          1. Copie o token FCM acima{'\n'}
          2. Vá para Firebase Console → Cloud Messaging{'\n'}
          3. Clique em "Enviar primeira mensagem"{'\n'}
          4. Cole o token no campo "Token FCM"{'\n'}
          5. Configure título e mensagem{'\n'}
          6. Envie a notificação
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    backgroundColor: colors.background,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: colors.text,
    fontFamily: fonts.bold,
    textAlign: 'center',
    marginBottom: 20,
  },
  loadingText: {
    fontSize: 16,
    color: colors.textSecondary,
    textAlign: 'center',
    fontFamily: fonts.regular,
  },
  statusContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
    padding: 12,
    backgroundColor: colors.card,
    borderRadius: 8,
  },
  statusLabel: {
    fontSize: 16,
    color: colors.text,
    fontFamily: fonts.medium,
  },
  statusValue: {
    fontSize: 16,
    fontFamily: fonts.bold,
  },
  tokenContainer: {
    marginBottom: 20,
  },
  tokenLabel: {
    fontSize: 16,
    color: colors.text,
    fontFamily: fonts.medium,
    marginBottom: 8,
  },
  tokenButton: {
    backgroundColor: colors.card,
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.border,
  },
  tokenText: {
    fontSize: 12,
    color: colors.textSecondary,
    fontFamily: fonts.regular,
    marginBottom: 4,
  },
  copyHint: {
    fontSize: 12,
    color: colors.primary,
    fontFamily: fonts.medium,
    textAlign: 'center',
  },
  buttonsContainer: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.text,
    fontFamily: fonts.bold,
    marginBottom: 12,
  },
  topicContainer: {
    marginBottom: 16,
    padding: 12,
    backgroundColor: colors.card,
    borderRadius: 8,
  },
  topicName: {
    fontSize: 16,
    color: colors.text,
    fontFamily: fonts.medium,
    marginBottom: 8,
  },
  topicButtons: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  button: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
    minWidth: 100,
  },
  subscribeButton: {
    backgroundColor: colors.primary,
  },
  unsubscribeButton: {
    backgroundColor: colors.error,
  },
  buttonText: {
    color: '#fff',
    fontFamily: fonts.medium,
    textAlign: 'center',
  },
  infoContainer: {
    padding: 12,
    backgroundColor: colors.card,
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: colors.primary,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.text,
    fontFamily: fonts.bold,
    marginBottom: 8,
  },
  infoText: {
    fontSize: 14,
    color: colors.textSecondary,
    fontFamily: fonts.regular,
    lineHeight: 20,
  },
});

export default NotificationTest;
