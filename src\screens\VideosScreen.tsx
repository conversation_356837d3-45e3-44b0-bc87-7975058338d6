import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity, ActivityIndicator, Linking, Image, Alert, StatusBar } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { fonts } from '../theme/fonts';
import { useTheme } from '../context/ThemeContext';

// Configuração do YouTube
// Para teste, vamos usar vídeos de exemplo
const SAMPLE_VIDEOS = [
  {
    id: 'dQw4w9WgXcQ',
    title: '<PERSON> - Never Gonna Give You Up',
    thumbnail: 'https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg',
    channelTitle: '<PERSON>'
  },
  {
    id: 'kJQP7kiw5Fk',
    title: '<PERSON>i - Des<PERSON>cito ft. Daddy Yankee',
    thumbnail: 'https://img.youtube.com/vi/kJQP7kiw5Fk/maxresdefault.jpg',
    channelTitle: '<PERSON>'
  },
  {
    id: 'fJ9rUzIMcZQ',
    title: 'Queen – Bohemian Rhapsody',
    thumbnail: 'https://img.youtube.com/vi/fJ9rUzIMcZQ/maxresdefault.jpg',
    channelTitle: 'Queen Official'
  }
];

interface Video {
  id: string;
  title: string;
  thumbnail: string;
  channelTitle: string;
}

const VideosScreen = ({ navigation }: any) => {
  const { colors, theme } = useTheme();
  const [videos, setVideos] = useState<Video[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // Debug do tema
  console.log('📺 VideosScreen - Tema atual:', theme, 'Cor do texto:', colors.text);

  useEffect(() => {
    loadVideos();
  }, []);

  const loadVideos = async () => {
    setLoading(true);
    setError('');

    try {
      // Simular carregamento
      await new Promise(resolve => setTimeout(resolve, 1000));
      setVideos(SAMPLE_VIDEOS);
    } catch (err) {
      setError('Erro ao carregar vídeos. Tente novamente.');
      console.error('Erro ao carregar vídeos:', err);
    } finally {
      setLoading(false);
    }
  };

  const openVideo = async (videoId: string, title: string) => {
    const urls = [
      `vnd.youtube://${videoId}`,
      `https://www.youtube.com/watch?v=${videoId}`,
      `https://m.youtube.com/watch?v=${videoId}`
    ];

    for (const url of urls) {
      try {
        const supported = await Linking.canOpenURL(url);
        if (supported) {
          await Linking.openURL(url);
          return;
        }
      } catch (err) {
        console.log(`Erro ao tentar abrir ${url}:`, err);
      }
    }

    // Se chegou aqui, nenhuma URL funcionou
    Alert.alert(
      'Erro ao Abrir Vídeo',
      `Não foi possível abrir o vídeo.\n\nTítulo: ${title}\n\nVerifique se você tem um navegador instalado.`,
      [{ text: 'OK' }]
    );
  };

  // Estilos dinâmicos baseados no tema
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: 20,
      paddingTop: 16,
      paddingBottom: 12,
      backgroundColor: colors.card,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    headerLeft: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    headerRight: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    backButton: {
      marginRight: 16,
      padding: 8,
      borderRadius: 20,
    },
    headerTitle: {
      fontFamily: fonts.bold,
      fontSize: 22,
      color: colors.text,
      letterSpacing: 0.5,
    },
    headerIconBtn: {
      padding: 8,
      borderRadius: 20,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: colors.background,
    },
    loadingText: {
      marginTop: 16,
      fontSize: 16,
      color: colors.textSecondary,
      fontFamily: fonts.regular,
    },
    errorContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: 20,
      backgroundColor: colors.background,
    },
    errorText: {
      fontSize: 16,
      color: colors.error,
      textAlign: 'center',
      marginBottom: 20,
      fontFamily: fonts.regular,
    },
    retryButton: {
      backgroundColor: colors.primary,
      paddingHorizontal: 20,
      paddingVertical: 12,
      borderRadius: 8,
    },
    retryButtonText: {
      color: colors.textInverse,
      fontSize: 16,
      fontFamily: fonts.bold,
    },
    videosList: {
      flex: 1,
      backgroundColor: colors.background,
    },
    listContent: {
      paddingVertical: 8,
    },
    videoCard: {
      backgroundColor: colors.card,
      marginHorizontal: 16,
      marginVertical: 8,
      borderRadius: 12,
      overflow: 'hidden',
      elevation: 2,
      shadowColor: colors.shadow,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
    },
    thumbnail: {
      width: '100%',
      height: 200,
      backgroundColor: colors.surface,
    },
    infoContainer: {
      padding: 16,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    textContainer: {
      flex: 1,
      marginRight: 12,
    },
    videoTitle: {
      fontSize: 16,
      fontFamily: fonts.bold,
      color: colors.text,
      marginBottom: 4,
      lineHeight: 22,
    },
    channelTitle: {
      fontSize: 14,
      color: colors.textSecondary,
      fontFamily: fonts.regular,
    },
    playButton: {
      position: 'absolute',
      top: '50%',
      left: '50%',
      transform: [{ translateX: -30 }, { translateY: -30 }],
      backgroundColor: 'rgba(0, 0, 0, 0.7)',
      borderRadius: 30,
      width: 60,
      height: 60,
      justifyContent: 'center',
      alignItems: 'center',
    },
  });

  return (
    <View style={styles.container}>
      <StatusBar
        barStyle={colors.text === '#FFFFFF' ? 'light-content' : 'dark-content'}
        backgroundColor={colors.background}
      />

      {/* Header padrão do app */}
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
            <Icon name="arrow-left" size={24} color={colors.text} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Vídeos</Text>
        </View>
        <View style={styles.headerRight}>
          <TouchableOpacity style={styles.headerIconBtn}>
            <Icon name="magnify" size={24} color={colors.text} />
          </TouchableOpacity>
        </View>
      </View>

      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingText}>Carregando vídeos...</Text>
        </View>
      ) : error ? (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={loadVideos}>
            <Text style={styles.retryButtonText}>Tentar Novamente</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <FlatList
          data={videos}
          keyExtractor={item => item.id}
          contentContainerStyle={styles.listContent}
          renderItem={({ item }) => (
            <TouchableOpacity style={styles.videoCard} onPress={() => openVideo(item.id, item.title)}>
              <View style={{ position: 'relative' }}>
                <Image source={{ uri: item.thumbnail }} style={styles.thumbnail} />
                <View style={styles.playButton}>
                  <Icon name="play" size={24} color="#FFFFFF" />
                </View>
              </View>
              <View style={styles.infoContainer}>
                <View style={styles.textContainer}>
                  <Text style={styles.videoTitle} numberOfLines={2}>{item.title}</Text>
                  <Text style={styles.channelTitle}>{item.channelTitle}</Text>
                </View>
                <Icon name="play-circle-outline" size={28} color={colors.primary} />
              </View>
            </TouchableOpacity>
          )}
        />
      )}
    </View>
  );
};

export default VideosScreen;