#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 1722256 bytes. Error detail: Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:168), pid=9544, tid=10176
#
# JRE version: OpenJDK Runtime Environment (21.0.5) (build 21.0.5+-13047016-b750.29)
# Java VM: OpenJDK 64-Bit Server VM (21.0.5+-13047016-b750.29, mixed mode, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: -XX:MaxMetaspaceSize=512m -XX:+HeapDumpOnOutOfMemoryError --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED -Xmx1536m -Dfile.encoding=UTF-8 -Duser.country=BR -Duser.language=pt -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.9-bin\90cnw93cvbtalezasaz0blq0a\gradle-8.9\lib\agents\gradle-instrumentation-agent-8.9.jar org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.9

Host: Intel(R) Xeon(R) CPU E3-1220 V2 @ 3.10GHz, 4 cores, 7G,  Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
Time: Thu Aug  7 15:42:31 2025 Hora oficial do Brasil elapsed time: 166.081537 seconds (0d 0h 2m 46s)

---------------  T H R E A D  ---------------

Current thread (0x0000021f5407afd0):  JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=10176, stack(0x000000681d700000,0x000000681d800000) (1024K)]


Current CompileTask:
C2:166081 34785       4       sun.security.ec.ECOperations::setSum (685 bytes)

Stack: [0x000000681d700000,0x000000681d800000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6cfb29]
V  [jvm.dll+0x85df93]
V  [jvm.dll+0x8604ee]
V  [jvm.dll+0x860bd3]
V  [jvm.dll+0x27e6b6]
V  [jvm.dll+0xbff6d]
V  [jvm.dll+0xc04a3]
V  [jvm.dll+0x3b5fc1]
V  [jvm.dll+0x381f97]
V  [jvm.dll+0x38140a]
V  [jvm.dll+0x247c62]
V  [jvm.dll+0x247231]
V  [jvm.dll+0x1c5ee4]
V  [jvm.dll+0x25697c]
V  [jvm.dll+0x254ec6]
V  [jvm.dll+0x3f0ce6]
V  [jvm.dll+0x806368]
V  [jvm.dll+0x6ce3fd]
C  [ucrtbase.dll+0x21bb2]
C  [KERNEL32.DLL+0x17374]
C  [ntdll.dll+0x4cc91]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x0000021f59c056b0, length=94, elements={
0x0000021f37a9de70, 0x0000021f5404ac00, 0x0000021f5404ce00, 0x0000021f5404dff0,
0x0000021f5404ea40, 0x0000021f5404fc30, 0x0000021f54050e20, 0x0000021f5407afd0,
0x0000021f5407d210, 0x0000021f541a5bd0, 0x0000021f54355200, 0x0000021f5607edc0,
0x0000021f55e8d8c0, 0x0000021f55e8e900, 0x0000021f56062460, 0x0000021f560476c0,
0x0000021f560f2b40, 0x0000021f561ff490, 0x0000021f561e2400, 0x0000021f562309b0,
0x0000021f56231040, 0x0000021f5622fc90, 0x0000021f56231d60, 0x0000021f562323f0,
0x0000021f56232a80, 0x0000021f56230320, 0x0000021f56233110, 0x0000021f57bf0ab0,
0x0000021f57befd90, 0x0000021f57bf45c0, 0x0000021f57bf5970, 0x0000021f57bf1140,
0x0000021f57bf2b80, 0x0000021f57bf4c50, 0x0000021f5b5e1940, 0x0000021f5b5e0c20,
0x0000021f5b5e2cf0, 0x0000021f5b5e7520, 0x0000021f5b5e1fd0, 0x0000021f5b5e3380,
0x0000021f57f53c60, 0x0000021f57f528b0, 0x0000021f57f542f0, 0x0000021f57f58b20,
0x0000021f57f51b90, 0x0000021f57f535d0, 0x0000021f5b5e5ae0, 0x0000021f5b5e6170,
0x0000021f5b5e6800, 0x0000021f5b5e6e90, 0x0000021f57bf0420, 0x0000021f5b4f1570,
0x0000021f63e9b4f0, 0x0000021f63e9fd20, 0x0000021f63e9d5c0, 0x0000021f63e9bb80,
0x0000021f63e9f690, 0x0000021f63e9e2e0, 0x0000021f63e9cf30, 0x0000021f63e9dc50,
0x0000021f63e9f000, 0x0000021f63d81880, 0x0000021f63d81f10, 0x0000021f638a14a0,
0x0000021f638a2850, 0x0000021f6389d990, 0x0000021f6389d300, 0x0000021f638a0780,
0x0000021f638a0e10, 0x0000021f6389c5e0, 0x0000021f6389cc70, 0x0000021f63e9c210,
0x0000021f63ea03b0, 0x0000021f63ea0a40, 0x0000021f63e9a7d0, 0x0000021f63e99ab0,
0x0000021f57981be0, 0x0000021f57985d80, 0x0000021f57981550, 0x0000021f57982900,
0x0000021f57982f90, 0x0000021f57983620, 0x0000021f57bf1e60, 0x0000021f57bf3210,
0x0000021f5682cb80, 0x0000021f56830690, 0x0000021f56833b10, 0x0000021f5682d210,
0x0000021f5682d8a0, 0x0000021f56830d20, 0x0000021f6a30d7b0, 0x0000021f6a30bd70,
0x0000021f6a30f1f0, 0x0000021f57f55010
}

Java Threads: ( => current thread )
  0x0000021f37a9de70 JavaThread "main"                              [_thread_blocked, id=7428, stack(0x000000681c900000,0x000000681ca00000) (1024K)]
  0x0000021f5404ac00 JavaThread "Reference Handler"          daemon [_thread_blocked, id=4748, stack(0x000000681d100000,0x000000681d200000) (1024K)]
  0x0000021f5404ce00 JavaThread "Finalizer"                  daemon [_thread_blocked, id=16852, stack(0x000000681d200000,0x000000681d300000) (1024K)]
  0x0000021f5404dff0 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=12136, stack(0x000000681d300000,0x000000681d400000) (1024K)]
  0x0000021f5404ea40 JavaThread "Attach Listener"            daemon [_thread_blocked, id=8540, stack(0x000000681d400000,0x000000681d500000) (1024K)]
  0x0000021f5404fc30 JavaThread "Service Thread"             daemon [_thread_blocked, id=9132, stack(0x000000681d500000,0x000000681d600000) (1024K)]
  0x0000021f54050e20 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=7360, stack(0x000000681d600000,0x000000681d700000) (1024K)]
=>0x0000021f5407afd0 JavaThread "C2 CompilerThread0"         daemon [_thread_in_native, id=10176, stack(0x000000681d700000,0x000000681d800000) (1024K)]
  0x0000021f5407d210 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=1460, stack(0x000000681d800000,0x000000681d900000) (1024K)]
  0x0000021f541a5bd0 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=8040, stack(0x000000681d900000,0x000000681da00000) (1024K)]
  0x0000021f54355200 JavaThread "Notification Thread"        daemon [_thread_blocked, id=15872, stack(0x000000681da00000,0x000000681db00000) (1024K)]
  0x0000021f5607edc0 JavaThread "Daemon health stats"               [_thread_blocked, id=1544, stack(0x000000681df00000,0x000000681e000000) (1024K)]
  0x0000021f55e8d8c0 JavaThread "Incoming local TCP Connector on port 57783"        [_thread_in_native, id=16232, stack(0x000000681e000000,0x000000681e100000) (1024K)]
  0x0000021f55e8e900 JavaThread "Daemon periodic checks"            [_thread_blocked, id=7832, stack(0x000000681e100000,0x000000681e200000) (1024K)]
  0x0000021f56062460 JavaThread "Daemon"                            [_thread_blocked, id=8152, stack(0x000000681e200000,0x000000681e300000) (1024K)]
  0x0000021f560476c0 JavaThread "Handler for socket connection from /127.0.0.1:57783 to /127.0.0.1:57784"        [_thread_in_native, id=2804, stack(0x000000681e300000,0x000000681e400000) (1024K)]
  0x0000021f560f2b40 JavaThread "Cancel handler"                    [_thread_blocked, id=17400, stack(0x000000681e400000,0x000000681e500000) (1024K)]
  0x0000021f561ff490 JavaThread "Daemon worker"                     [_thread_in_native, id=15524, stack(0x000000681e500000,0x000000681e600000) (1024K)]
  0x0000021f561e2400 JavaThread "Asynchronous log dispatcher for DefaultDaemonConnection: socket connection from /127.0.0.1:57783 to /127.0.0.1:57784"        [_thread_blocked, id=2860, stack(0x000000681e600000,0x000000681e700000) (1024K)]
  0x0000021f562309b0 JavaThread "Stdin handler"                     [_thread_blocked, id=15704, stack(0x000000681e700000,0x000000681e800000) (1024K)]
  0x0000021f56231040 JavaThread "Daemon client event forwarder"        [_thread_blocked, id=10124, stack(0x000000681e800000,0x000000681e900000) (1024K)]
  0x0000021f5622fc90 JavaThread "Cache worker for journal cache (C:\Users\<USER>\.gradle\caches\journal-1)"        [_thread_blocked, id=5468, stack(0x000000681e900000,0x000000681ea00000) (1024K)]
  0x0000021f56231d60 JavaThread "File lock request listener"        [_thread_in_native, id=1164, stack(0x000000681ea00000,0x000000681eb00000) (1024K)]
  0x0000021f562323f0 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\.gradle\caches\8.9\fileHashes)"        [_thread_blocked, id=16832, stack(0x000000681eb00000,0x000000681ec00000) (1024K)]
  0x0000021f56232a80 JavaThread "Cache worker for file hash cache (F:\PROJETO\React\EscolaSabatina\android\.gradle\8.9\fileHashes)"        [_thread_blocked, id=14816, stack(0x000000681ec00000,0x000000681ed00000) (1024K)]
  0x0000021f56230320 JavaThread "Cache worker for Build Output Cleanup Cache (F:\PROJETO\React\EscolaSabatina\android\.gradle\buildOutputCleanup)"        [_thread_blocked, id=3336, stack(0x000000681ed00000,0x000000681ee00000) (1024K)]
  0x0000021f56233110 JavaThread "File watcher server"        daemon [_thread_in_native, id=7172, stack(0x000000681ee00000,0x000000681ef00000) (1024K)]
  0x0000021f57bf0ab0 JavaThread "File watcher consumer"      daemon [_thread_blocked, id=15212, stack(0x000000681ef00000,0x000000681f000000) (1024K)]
  0x0000021f57befd90 JavaThread "Cache worker for checksums cache (F:\PROJETO\React\EscolaSabatina\android\.gradle\8.9\checksums)"        [_thread_blocked, id=15832, stack(0x000000681f000000,0x000000681f100000) (1024K)]
  0x0000021f57bf45c0 JavaThread "Cache worker for file content cache (C:\Users\<USER>\.gradle\caches\8.9\fileContent)"        [_thread_blocked, id=16248, stack(0x000000681f100000,0x000000681f200000) (1024K)]
  0x0000021f57bf5970 JavaThread "Cache worker for cache directory md-rule (C:\Users\<USER>\.gradle\caches\8.9\md-rule)"        [_thread_blocked, id=12208, stack(0x000000681f200000,0x000000681f300000) (1024K)]
  0x0000021f57bf1140 JavaThread "Cache worker for cache directory md-supplier (C:\Users\<USER>\.gradle\caches\8.9\md-supplier)"        [_thread_blocked, id=10236, stack(0x000000681f300000,0x000000681f400000) (1024K)]
  0x0000021f57bf2b80 JavaThread "jar transforms"                    [_thread_blocked, id=3764, stack(0x000000681de00000,0x000000681df00000) (1024K)]
  0x0000021f57bf4c50 JavaThread "Cache worker for Build Output Cleanup Cache (F:\PROJETO\React\EscolaSabatina\node_modules\@react-native\gradle-plugin\.gradle\buildOutputCleanup)"        [_thread_blocked, id=2660, stack(0x000000681f400000,0x000000681f500000) (1024K)]
  0x0000021f5b5e1940 JavaThread "Unconstrained build operations"        [_thread_blocked, id=14368, stack(0x000000681f500000,0x000000681f600000) (1024K)]
  0x0000021f5b5e0c20 JavaThread "Unconstrained build operations Thread 2"        [_thread_blocked, id=6032, stack(0x000000681f600000,0x000000681f700000) (1024K)]
  0x0000021f5b5e2cf0 JavaThread "Unconstrained build operations Thread 3"        [_thread_blocked, id=12952, stack(0x000000681f700000,0x000000681f800000) (1024K)]
  0x0000021f5b5e7520 JavaThread "Unconstrained build operations Thread 4"        [_thread_blocked, id=14204, stack(0x000000681c600000,0x000000681c700000) (1024K)]
  0x0000021f5b5e1fd0 JavaThread "Unconstrained build operations Thread 5"        [_thread_blocked, id=13920, stack(0x000000681f800000,0x000000681f900000) (1024K)]
  0x0000021f5b5e3380 JavaThread "Unconstrained build operations Thread 6"        [_thread_in_native, id=4368, stack(0x000000681f900000,0x000000681fa00000) (1024K)]
  0x0000021f57f53c60 JavaThread "Kotlin DSL Writer"                 [_thread_blocked, id=14124, stack(0x000000681fa00000,0x000000681fb00000) (1024K)]
  0x0000021f57f528b0 JavaThread "Unconstrained build operations Thread 7"        [_thread_blocked, id=16876, stack(0x000000681fb00000,0x000000681fc00000) (1024K)]
  0x0000021f57f542f0 JavaThread "Unconstrained build operations Thread 8"        [_thread_blocked, id=11564, stack(0x000000681fc00000,0x000000681fd00000) (1024K)]
  0x0000021f57f58b20 JavaThread "Unconstrained build operations Thread 9"        [_thread_blocked, id=964, stack(0x000000681fd00000,0x000000681fe00000) (1024K)]
  0x0000021f57f51b90 JavaThread "Unconstrained build operations Thread 10"        [_thread_blocked, id=10084, stack(0x000000681fe00000,0x000000681ff00000) (1024K)]
  0x0000021f57f535d0 JavaThread "Unconstrained build operations Thread 11"        [_thread_blocked, id=16216, stack(0x000000681ff00000,0x0000006820000000) (1024K)]
  0x0000021f5b5e5ae0 JavaThread "Unconstrained build operations Thread 12"        [_thread_blocked, id=8120, stack(0x0000006820000000,0x0000006820100000) (1024K)]
  0x0000021f5b5e6170 JavaThread "Unconstrained build operations Thread 13"        [_thread_blocked, id=16420, stack(0x0000006820100000,0x0000006820200000) (1024K)]
  0x0000021f5b5e6800 JavaThread "Unconstrained build operations Thread 14"        [_thread_blocked, id=14032, stack(0x0000006820200000,0x0000006820300000) (1024K)]
  0x0000021f5b5e6e90 JavaThread "Unconstrained build operations Thread 15"        [_thread_blocked, id=384, stack(0x0000006820300000,0x0000006820400000) (1024K)]
  0x0000021f57bf0420 JavaThread "Unconstrained build operations Thread 16"        [_thread_blocked, id=14228, stack(0x0000006820400000,0x0000006820500000) (1024K)]
  0x0000021f5b4f1570 JavaThread "Unconstrained build operations Thread 17"        [_thread_blocked, id=3444, stack(0x0000006820500000,0x0000006820600000) (1024K)]
  0x0000021f63e9b4f0 JavaThread "Unconstrained build operations Thread 18"        [_thread_blocked, id=15040, stack(0x0000006820600000,0x0000006820700000) (1024K)]
  0x0000021f63e9fd20 JavaThread "Unconstrained build operations Thread 19"        [_thread_blocked, id=14000, stack(0x0000006820700000,0x0000006820800000) (1024K)]
  0x0000021f63e9d5c0 JavaThread "Unconstrained build operations Thread 20"        [_thread_blocked, id=14048, stack(0x0000006820800000,0x0000006820900000) (1024K)]
  0x0000021f63e9bb80 JavaThread "Unconstrained build operations Thread 21"        [_thread_blocked, id=2108, stack(0x0000006820900000,0x0000006820a00000) (1024K)]
  0x0000021f63e9f690 JavaThread "Unconstrained build operations Thread 22"        [_thread_blocked, id=10136, stack(0x0000006820a00000,0x0000006820b00000) (1024K)]
  0x0000021f63e9e2e0 JavaThread "Unconstrained build operations Thread 23"        [_thread_blocked, id=3552, stack(0x0000006820b00000,0x0000006820c00000) (1024K)]
  0x0000021f63e9cf30 JavaThread "Unconstrained build operations Thread 24"        [_thread_blocked, id=2052, stack(0x0000006820c00000,0x0000006820d00000) (1024K)]
  0x0000021f63e9dc50 JavaThread "Unconstrained build operations Thread 25"        [_thread_blocked, id=15712, stack(0x0000006820d00000,0x0000006820e00000) (1024K)]
  0x0000021f63e9f000 JavaThread "Unconstrained build operations Thread 26"        [_thread_blocked, id=5484, stack(0x0000006820e00000,0x0000006820f00000) (1024K)]
  0x0000021f63d81880 JavaThread "build event listener"              [_thread_blocked, id=14788, stack(0x0000006821200000,0x0000006821300000) (1024K)]
  0x0000021f63d81f10 JavaThread "Memory manager"                    [_thread_blocked, id=16916, stack(0x0000006820f00000,0x0000006821000000) (1024K)]
  0x0000021f638a14a0 JavaThread "ForkJoinPool.commonPool-worker-1" daemon [_thread_blocked, id=16120, stack(0x0000006821300000,0x0000006821400000) (1024K)]
  0x0000021f638a2850 JavaThread "ForkJoinPool.commonPool-worker-3" daemon [_thread_blocked, id=15772, stack(0x0000006821500000,0x0000006821600000) (1024K)]
  0x0000021f6389d990 JavaThread "included builds"                   [_thread_blocked, id=6736, stack(0x0000006821900000,0x0000006821a00000) (1024K)]
  0x0000021f6389d300 JavaThread "Execution worker"                  [_thread_blocked, id=6540, stack(0x0000006821a00000,0x0000006821b00000) (1024K)]
  0x0000021f638a0780 JavaThread "Execution worker Thread 2"         [_thread_blocked, id=4136, stack(0x0000006821b00000,0x0000006821c00000) (1024K)]
  0x0000021f638a0e10 JavaThread "Execution worker Thread 3"         [_thread_blocked, id=8860, stack(0x0000006821c00000,0x0000006821d00000) (1024K)]
  0x0000021f6389c5e0 JavaThread "Cache worker for execution history cache (F:\PROJETO\React\EscolaSabatina\node_modules\@react-native\gradle-plugin\.gradle\8.9\executionHistory)"        [_thread_blocked, id=13952, stack(0x000000681c800000,0x000000681c900000) (1024K)]
  0x0000021f6389cc70 JavaThread "Unconstrained build operations Thread 27"        [_thread_blocked, id=1720, stack(0x0000006821e00000,0x0000006821f00000) (1024K)]
  0x0000021f63e9c210 JavaThread "Unconstrained build operations Thread 28"        [_thread_blocked, id=9580, stack(0x0000006821f00000,0x0000006822000000) (1024K)]
  0x0000021f63ea03b0 JavaThread "Unconstrained build operations Thread 29"        [_thread_blocked, id=12040, stack(0x0000006822000000,0x0000006822100000) (1024K)]
  0x0000021f63ea0a40 JavaThread "Unconstrained build operations Thread 30"        [_thread_blocked, id=1368, stack(0x0000006822100000,0x0000006822200000) (1024K)]
  0x0000021f63e9a7d0 JavaThread "Unconstrained build operations Thread 31"        [_thread_blocked, id=7656, stack(0x0000006822200000,0x0000006822300000) (1024K)]
  0x0000021f63e99ab0 JavaThread "Unconstrained build operations Thread 32"        [_thread_blocked, id=5924, stack(0x0000006822300000,0x0000006822400000) (1024K)]
  0x0000021f57981be0 JavaThread "Unconstrained build operations Thread 33"        [_thread_blocked, id=13664, stack(0x0000006822400000,0x0000006822500000) (1024K)]
  0x0000021f57985d80 JavaThread "Unconstrained build operations Thread 34"        [_thread_blocked, id=17208, stack(0x0000006822500000,0x0000006822600000) (1024K)]
  0x0000021f57981550 JavaThread "Unconstrained build operations Thread 35"        [_thread_blocked, id=9024, stack(0x0000006822600000,0x0000006822700000) (1024K)]
  0x0000021f57982900 JavaThread "Unconstrained build operations Thread 36"        [_thread_blocked, id=11380, stack(0x0000006822700000,0x0000006822800000) (1024K)]
  0x0000021f57982f90 JavaThread "Unconstrained build operations Thread 37"        [_thread_blocked, id=10120, stack(0x0000006822800000,0x0000006822900000) (1024K)]
  0x0000021f57983620 JavaThread "Unconstrained build operations Thread 38"        [_thread_blocked, id=2032, stack(0x0000006822900000,0x0000006822a00000) (1024K)]
  0x0000021f57bf1e60 JavaThread "Unconstrained build operations Thread 39"        [_thread_blocked, id=9084, stack(0x0000006822a00000,0x0000006822b00000) (1024K)]
  0x0000021f57bf3210 JavaThread "Unconstrained build operations Thread 40"        [_thread_blocked, id=3564, stack(0x0000006822b00000,0x0000006822c00000) (1024K)]
  0x0000021f5682cb80 JavaThread "WorkerExecutor Queue"              [_thread_blocked, id=9952, stack(0x0000006821d00000,0x0000006821e00000) (1024K)]
  0x0000021f56830690 JavaThread "RMI Scheduler(0)"           daemon [_thread_blocked, id=15012, stack(0x0000006822c00000,0x0000006822d00000) (1024K)]
  0x0000021f56833b10 JavaThread "RMI GC Daemon"              daemon [_thread_blocked, id=13892, stack(0x0000006822e00000,0x0000006822f00000) (1024K)]
  0x0000021f5682d210 JavaThread "RMI TCP Accept-0"           daemon [_thread_in_native, id=15720, stack(0x0000006822f00000,0x0000006823000000) (1024K)]
  0x0000021f5682d8a0 JavaThread "RMI Reaper"                        [_thread_blocked, id=6624, stack(0x0000006823000000,0x0000006823100000) (1024K)]
  0x0000021f56830d20 JavaThread "RMI TCP Connection(idle)"   daemon [_thread_blocked, id=10684, stack(0x0000006823200000,0x0000006823300000) (1024K)]
  0x0000021f6a30d7b0 JavaThread "WorkerExecutor Queue Thread 2"        [_thread_blocked, id=17092, stack(0x0000006823100000,0x0000006823200000) (1024K)]
  0x0000021f6a30bd70 JavaThread "RMI TCP Connection(2)-127.0.0.1" daemon [_thread_in_native, id=10344, stack(0x0000006823400000,0x0000006823500000) (1024K)]
  0x0000021f6a30f1f0 JavaThread "jar transforms Thread 2"           [_thread_blocked, id=11952, stack(0x0000006823300000,0x0000006823400000) (1024K)]
  0x0000021f57f55010 JavaThread "jar transforms Thread 3"           [_thread_blocked, id=6184, stack(0x0000006821400000,0x0000006821500000) (1024K)]
Total: 94

Other Threads:
  0x0000021f4fe5e8a0 VMThread "VM Thread"                           [id=14748, stack(0x000000681d000000,0x000000681d100000) (1024K)]
  0x0000021f4fdca920 WatcherThread "VM Periodic Task Thread"        [id=15076, stack(0x000000681cf00000,0x000000681d000000) (1024K)]
  0x0000021f37ae9b70 WorkerThread "GC Thread#0"                     [id=12592, stack(0x000000681ca00000,0x000000681cb00000) (1024K)]
  0x0000021f545937c0 WorkerThread "GC Thread#1"                     [id=11520, stack(0x000000681db00000,0x000000681dc00000) (1024K)]
  0x0000021f54593b60 WorkerThread "GC Thread#2"                     [id=1924, stack(0x000000681dc00000,0x000000681dd00000) (1024K)]
  0x0000021f551e2f00 WorkerThread "GC Thread#3"                     [id=14112, stack(0x000000681dd00000,0x000000681de00000) (1024K)]
  0x0000021f37af6b50 ConcurrentGCThread "G1 Main Marker"            [id=16180, stack(0x000000681cb00000,0x000000681cc00000) (1024K)]
  0x0000021f37af7650 WorkerThread "G1 Conc#0"                       [id=11252, stack(0x000000681cc00000,0x000000681cd00000) (1024K)]
  0x0000021f37b5e4c0 ConcurrentGCThread "G1 Refine#0"               [id=300, stack(0x000000681cd00000,0x000000681ce00000) (1024K)]
  0x0000021f4fc94630 ConcurrentGCThread "G1 Service"                [id=14948, stack(0x000000681ce00000,0x000000681cf00000) (1024K)]
Total: 10

Threads with active compile tasks:
C2 CompilerThread0  166120 34785       4       sun.security.ec.ECOperations::setSum (685 bytes)
Total: 1

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x00000000a0000000, size: 1536 MB, Compressed Oops mode: 32-bit

CDS archive(s) not mapped
Compressed class space mapped at: 0x0000000100000000-0x000000011a000000, reserved size: 436207616
Narrow klass base: 0x0000000000000000, Narrow klass shift: 3, Narrow klass range: 0x11a000000

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 5 size 8 Array Of Cards #cards 12 size 40 Howl #buckets 4 coarsen threshold 1843 Howl Bitmap #cards 512 size 80 coarsen threshold 460 Card regions per heap region 1 cards per card region 2048
 CPUs: 4 total, 4 available
 Memory: 8161M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Heap Region Size: 1M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 128M
 Heap Max Capacity: 1536M
 Pre-touch: Disabled
 Parallel Workers: 4
 Concurrent Workers: 1
 Concurrent Refinement Workers: 4
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 559104K, used 236197K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 64 young (65536K), 20 survivors (20480K)
 Metaspace       used 195597K, committed 198272K, reserved 622592K
  class space    used 23970K, committed 25280K, reserved 425984K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom
|   0|0x00000000a0000000, 0x00000000a0100000, 0x00000000a0100000|100%|HS|  |TAMS 0x00000000a0000000| PB 0x00000000a0000000| Complete 
|   1|0x00000000a0100000, 0x00000000a0200000, 0x00000000a0200000|100%|HC|  |TAMS 0x00000000a0100000| PB 0x00000000a0100000| Complete 
|   2|0x00000000a0200000, 0x00000000a0300000, 0x00000000a0300000|100%|HC|  |TAMS 0x00000000a0200000| PB 0x00000000a0200000| Complete 
|   3|0x00000000a0300000, 0x00000000a0400000, 0x00000000a0400000|100%|HC|  |TAMS 0x00000000a0300000| PB 0x00000000a0300000| Complete 
|   4|0x00000000a0400000, 0x00000000a0500000, 0x00000000a0500000|100%| O|  |TAMS 0x00000000a0400000| PB 0x00000000a0400000| Untracked 
|   5|0x00000000a0500000, 0x00000000a0600000, 0x00000000a0600000|100%| O|  |TAMS 0x00000000a0500000| PB 0x00000000a0500000| Untracked 
|   6|0x00000000a0600000, 0x00000000a0700000, 0x00000000a0700000|100%| O|  |TAMS 0x00000000a0600000| PB 0x00000000a0600000| Untracked 
|   7|0x00000000a0700000, 0x00000000a0800000, 0x00000000a0800000|100%| O|  |TAMS 0x00000000a0700000| PB 0x00000000a0700000| Untracked 
|   8|0x00000000a0800000, 0x00000000a0900000, 0x00000000a0900000|100%|HS|  |TAMS 0x00000000a0800000| PB 0x00000000a0800000| Complete 
|   9|0x00000000a0900000, 0x00000000a0a00000, 0x00000000a0a00000|100%| O|  |TAMS 0x00000000a0900000| PB 0x00000000a0900000| Untracked 
|  10|0x00000000a0a00000, 0x00000000a0b00000, 0x00000000a0b00000|100%| O|  |TAMS 0x00000000a0a00000| PB 0x00000000a0a00000| Untracked 
|  11|0x00000000a0b00000, 0x00000000a0c00000, 0x00000000a0c00000|100%| O|  |TAMS 0x00000000a0b00000| PB 0x00000000a0b00000| Untracked 
|  12|0x00000000a0c00000, 0x00000000a0d00000, 0x00000000a0d00000|100%| O|  |TAMS 0x00000000a0c00000| PB 0x00000000a0c00000| Untracked 
|  13|0x00000000a0d00000, 0x00000000a0e00000, 0x00000000a0e00000|100%| O|  |TAMS 0x00000000a0d00000| PB 0x00000000a0d00000| Untracked 
|  14|0x00000000a0e00000, 0x00000000a0f00000, 0x00000000a0f00000|100%| O|  |TAMS 0x00000000a0e00000| PB 0x00000000a0e00000| Untracked 
|  15|0x00000000a0f00000, 0x00000000a1000000, 0x00000000a1000000|100%| O|  |TAMS 0x00000000a0f00000| PB 0x00000000a0f00000| Untracked 
|  16|0x00000000a1000000, 0x00000000a1100000, 0x00000000a1100000|100%| O|  |TAMS 0x00000000a1000000| PB 0x00000000a1000000| Untracked 
|  17|0x00000000a1100000, 0x00000000a1200000, 0x00000000a1200000|100%| O|  |TAMS 0x00000000a1100000| PB 0x00000000a1100000| Untracked 
|  18|0x00000000a1200000, 0x00000000a1300000, 0x00000000a1300000|100%| O|  |TAMS 0x00000000a1200000| PB 0x00000000a1200000| Untracked 
|  19|0x00000000a1300000, 0x00000000a1400000, 0x00000000a1400000|100%| O|  |TAMS 0x00000000a1300000| PB 0x00000000a1300000| Untracked 
|  20|0x00000000a1400000, 0x00000000a1500000, 0x00000000a1500000|100%| O|  |TAMS 0x00000000a1400000| PB 0x00000000a1400000| Untracked 
|  21|0x00000000a1500000, 0x00000000a1600000, 0x00000000a1600000|100%| O|  |TAMS 0x00000000a1500000| PB 0x00000000a1500000| Untracked 
|  22|0x00000000a1600000, 0x00000000a1700000, 0x00000000a1700000|100%| O|  |TAMS 0x00000000a1600000| PB 0x00000000a1600000| Untracked 
|  23|0x00000000a1700000, 0x00000000a1800000, 0x00000000a1800000|100%| O|  |TAMS 0x00000000a1700000| PB 0x00000000a1700000| Untracked 
|  24|0x00000000a1800000, 0x00000000a1900000, 0x00000000a1900000|100%| O|  |TAMS 0x00000000a1800000| PB 0x00000000a1800000| Untracked 
|  25|0x00000000a1900000, 0x00000000a1a00000, 0x00000000a1a00000|100%| O|  |TAMS 0x00000000a1900000| PB 0x00000000a1900000| Untracked 
|  26|0x00000000a1a00000, 0x00000000a1b00000, 0x00000000a1b00000|100%| O|  |TAMS 0x00000000a1a00000| PB 0x00000000a1a00000| Untracked 
|  27|0x00000000a1b00000, 0x00000000a1c00000, 0x00000000a1c00000|100%| O|  |TAMS 0x00000000a1b00000| PB 0x00000000a1b00000| Untracked 
|  28|0x00000000a1c00000, 0x00000000a1d00000, 0x00000000a1d00000|100%| O|  |TAMS 0x00000000a1c00000| PB 0x00000000a1c00000| Untracked 
|  29|0x00000000a1d00000, 0x00000000a1e00000, 0x00000000a1e00000|100%| O|  |TAMS 0x00000000a1d00000| PB 0x00000000a1d00000| Untracked 
|  30|0x00000000a1e00000, 0x00000000a1f00000, 0x00000000a1f00000|100%| O|  |TAMS 0x00000000a1e00000| PB 0x00000000a1e00000| Untracked 
|  31|0x00000000a1f00000, 0x00000000a2000000, 0x00000000a2000000|100%| O|  |TAMS 0x00000000a1f00000| PB 0x00000000a1f00000| Untracked 
|  32|0x00000000a2000000, 0x00000000a2100000, 0x00000000a2100000|100%| O|  |TAMS 0x00000000a2000000| PB 0x00000000a2000000| Untracked 
|  33|0x00000000a2100000, 0x00000000a2200000, 0x00000000a2200000|100%| O|  |TAMS 0x00000000a2100000| PB 0x00000000a2100000| Untracked 
|  34|0x00000000a2200000, 0x00000000a2300000, 0x00000000a2300000|100%| O|  |TAMS 0x00000000a2200000| PB 0x00000000a2200000| Untracked 
|  35|0x00000000a2300000, 0x00000000a2400000, 0x00000000a2400000|100%| O|  |TAMS 0x00000000a2300000| PB 0x00000000a2300000| Untracked 
|  36|0x00000000a2400000, 0x00000000a2500000, 0x00000000a2500000|100%| O|  |TAMS 0x00000000a2400000| PB 0x00000000a2400000| Untracked 
|  37|0x00000000a2500000, 0x00000000a2600000, 0x00000000a2600000|100%| O|  |TAMS 0x00000000a2500000| PB 0x00000000a2500000| Untracked 
|  38|0x00000000a2600000, 0x00000000a2700000, 0x00000000a2700000|100%| O|  |TAMS 0x00000000a2600000| PB 0x00000000a2600000| Untracked 
|  39|0x00000000a2700000, 0x00000000a2800000, 0x00000000a2800000|100%| O|  |TAMS 0x00000000a2700000| PB 0x00000000a2700000| Untracked 
|  40|0x00000000a2800000, 0x00000000a2900000, 0x00000000a2900000|100%| O|  |TAMS 0x00000000a2800000| PB 0x00000000a2800000| Untracked 
|  41|0x00000000a2900000, 0x00000000a2a00000, 0x00000000a2a00000|100%| O|  |TAMS 0x00000000a2900000| PB 0x00000000a2900000| Untracked 
|  42|0x00000000a2a00000, 0x00000000a2b00000, 0x00000000a2b00000|100%| O|  |TAMS 0x00000000a2a00000| PB 0x00000000a2a00000| Untracked 
|  43|0x00000000a2b00000, 0x00000000a2c00000, 0x00000000a2c00000|100%| O|  |TAMS 0x00000000a2b00000| PB 0x00000000a2b00000| Untracked 
|  44|0x00000000a2c00000, 0x00000000a2d00000, 0x00000000a2d00000|100%| O|  |TAMS 0x00000000a2c00000| PB 0x00000000a2c00000| Untracked 
|  45|0x00000000a2d00000, 0x00000000a2e00000, 0x00000000a2e00000|100%| O|  |TAMS 0x00000000a2d00000| PB 0x00000000a2d00000| Untracked 
|  46|0x00000000a2e00000, 0x00000000a2f00000, 0x00000000a2f00000|100%| O|  |TAMS 0x00000000a2e00000| PB 0x00000000a2e00000| Untracked 
|  47|0x00000000a2f00000, 0x00000000a3000000, 0x00000000a3000000|100%| O|  |TAMS 0x00000000a2f00000| PB 0x00000000a2f00000| Untracked 
|  48|0x00000000a3000000, 0x00000000a3100000, 0x00000000a3100000|100%| O|  |TAMS 0x00000000a3000000| PB 0x00000000a3000000| Untracked 
|  49|0x00000000a3100000, 0x00000000a3200000, 0x00000000a3200000|100%| O|  |TAMS 0x00000000a3100000| PB 0x00000000a3100000| Untracked 
|  50|0x00000000a3200000, 0x00000000a3300000, 0x00000000a3300000|100%| O|  |TAMS 0x00000000a3200000| PB 0x00000000a3200000| Untracked 
|  51|0x00000000a3300000, 0x00000000a3400000, 0x00000000a3400000|100%| O|  |TAMS 0x00000000a3300000| PB 0x00000000a3300000| Untracked 
|  52|0x00000000a3400000, 0x00000000a3500000, 0x00000000a3500000|100%| O|  |TAMS 0x00000000a3400000| PB 0x00000000a3400000| Untracked 
|  53|0x00000000a3500000, 0x00000000a3600000, 0x00000000a3600000|100%| O|  |TAMS 0x00000000a3500000| PB 0x00000000a3500000| Untracked 
|  54|0x00000000a3600000, 0x00000000a3700000, 0x00000000a3700000|100%|HS|  |TAMS 0x00000000a3600000| PB 0x00000000a3600000| Complete 
|  55|0x00000000a3700000, 0x00000000a3800000, 0x00000000a3800000|100%|HC|  |TAMS 0x00000000a3700000| PB 0x00000000a3700000| Complete 
|  56|0x00000000a3800000, 0x00000000a3900000, 0x00000000a3900000|100%|HC|  |TAMS 0x00000000a3800000| PB 0x00000000a3800000| Complete 
|  57|0x00000000a3900000, 0x00000000a3a00000, 0x00000000a3a00000|100%| O|  |TAMS 0x00000000a3900000| PB 0x00000000a3900000| Untracked 
|  58|0x00000000a3a00000, 0x00000000a3b00000, 0x00000000a3b00000|100%| O|  |TAMS 0x00000000a3a00000| PB 0x00000000a3a00000| Untracked 
|  59|0x00000000a3b00000, 0x00000000a3c00000, 0x00000000a3c00000|100%| O|  |TAMS 0x00000000a3b00000| PB 0x00000000a3b00000| Untracked 
|  60|0x00000000a3c00000, 0x00000000a3d00000, 0x00000000a3d00000|100%| O|  |TAMS 0x00000000a3c00000| PB 0x00000000a3c00000| Untracked 
|  61|0x00000000a3d00000, 0x00000000a3e00000, 0x00000000a3e00000|100%| O|  |TAMS 0x00000000a3d00000| PB 0x00000000a3d00000| Untracked 
|  62|0x00000000a3e00000, 0x00000000a3f00000, 0x00000000a3f00000|100%| O|  |TAMS 0x00000000a3e00000| PB 0x00000000a3e00000| Untracked 
|  63|0x00000000a3f00000, 0x00000000a4000000, 0x00000000a4000000|100%| O|  |TAMS 0x00000000a3f00000| PB 0x00000000a3f00000| Untracked 
|  64|0x00000000a4000000, 0x00000000a4100000, 0x00000000a4100000|100%| O|  |TAMS 0x00000000a4000000| PB 0x00000000a4000000| Untracked 
|  65|0x00000000a4100000, 0x00000000a4200000, 0x00000000a4200000|100%| O|  |TAMS 0x00000000a4100000| PB 0x00000000a4100000| Untracked 
|  66|0x00000000a4200000, 0x00000000a4300000, 0x00000000a4300000|100%| O|  |TAMS 0x00000000a4200000| PB 0x00000000a4200000| Untracked 
|  67|0x00000000a4300000, 0x00000000a4400000, 0x00000000a4400000|100%|HS|  |TAMS 0x00000000a4300000| PB 0x00000000a4300000| Complete 
|  68|0x00000000a4400000, 0x00000000a4500000, 0x00000000a4500000|100%| O|  |TAMS 0x00000000a4400000| PB 0x00000000a4400000| Untracked 
|  69|0x00000000a4500000, 0x00000000a4600000, 0x00000000a4600000|100%| O|  |TAMS 0x00000000a4500000| PB 0x00000000a4500000| Untracked 
|  70|0x00000000a4600000, 0x00000000a4700000, 0x00000000a4700000|100%|HS|  |TAMS 0x00000000a4600000| PB 0x00000000a4600000| Complete 
|  71|0x00000000a4700000, 0x00000000a4800000, 0x00000000a4800000|100%|HC|  |TAMS 0x00000000a4700000| PB 0x00000000a4700000| Complete 
|  72|0x00000000a4800000, 0x00000000a4900000, 0x00000000a4900000|100%|HC|  |TAMS 0x00000000a4800000| PB 0x00000000a4800000| Complete 
|  73|0x00000000a4900000, 0x00000000a4a00000, 0x00000000a4a00000|100%| O|  |TAMS 0x00000000a4900000| PB 0x00000000a4900000| Untracked 
|  74|0x00000000a4a00000, 0x00000000a4b00000, 0x00000000a4b00000|100%| O|  |TAMS 0x00000000a4a00000| PB 0x00000000a4a00000| Untracked 
|  75|0x00000000a4b00000, 0x00000000a4c00000, 0x00000000a4c00000|100%| O|  |TAMS 0x00000000a4b00000| PB 0x00000000a4b00000| Untracked 
|  76|0x00000000a4c00000, 0x00000000a4d00000, 0x00000000a4d00000|100%| O|  |TAMS 0x00000000a4c00000| PB 0x00000000a4c00000| Untracked 
|  77|0x00000000a4d00000, 0x00000000a4e00000, 0x00000000a4e00000|100%| O|  |TAMS 0x00000000a4d00000| PB 0x00000000a4d00000| Untracked 
|  78|0x00000000a4e00000, 0x00000000a4f00000, 0x00000000a4f00000|100%| O|  |TAMS 0x00000000a4e00000| PB 0x00000000a4e00000| Untracked 
|  79|0x00000000a4f00000, 0x00000000a5000000, 0x00000000a5000000|100%| O|  |TAMS 0x00000000a4f00000| PB 0x00000000a4f00000| Untracked 
|  80|0x00000000a5000000, 0x00000000a5100000, 0x00000000a5100000|100%| O|  |TAMS 0x00000000a5000000| PB 0x00000000a5000000| Untracked 
|  81|0x00000000a5100000, 0x00000000a5200000, 0x00000000a5200000|100%| O|  |TAMS 0x00000000a5100000| PB 0x00000000a5100000| Untracked 
|  82|0x00000000a5200000, 0x00000000a5300000, 0x00000000a5300000|100%| O|  |TAMS 0x00000000a5200000| PB 0x00000000a5200000| Untracked 
|  83|0x00000000a5300000, 0x00000000a5400000, 0x00000000a5400000|100%| O|  |TAMS 0x00000000a5300000| PB 0x00000000a5300000| Untracked 
|  84|0x00000000a5400000, 0x00000000a5500000, 0x00000000a5500000|100%| O|  |TAMS 0x00000000a5400000| PB 0x00000000a5400000| Untracked 
|  85|0x00000000a5500000, 0x00000000a5600000, 0x00000000a5600000|100%| O|  |TAMS 0x00000000a5500000| PB 0x00000000a5500000| Untracked 
|  86|0x00000000a5600000, 0x00000000a5700000, 0x00000000a5700000|100%| O|  |TAMS 0x00000000a5600000| PB 0x00000000a5600000| Untracked 
|  87|0x00000000a5700000, 0x00000000a5800000, 0x00000000a5800000|100%| O|  |TAMS 0x00000000a5700000| PB 0x00000000a5700000| Untracked 
|  88|0x00000000a5800000, 0x00000000a5900000, 0x00000000a5900000|100%| O|  |TAMS 0x00000000a5800000| PB 0x00000000a5800000| Untracked 
|  89|0x00000000a5900000, 0x00000000a5a00000, 0x00000000a5a00000|100%| O|  |TAMS 0x00000000a5900000| PB 0x00000000a5900000| Untracked 
|  90|0x00000000a5a00000, 0x00000000a5b00000, 0x00000000a5b00000|100%| O|  |TAMS 0x00000000a5a00000| PB 0x00000000a5a00000| Untracked 
|  91|0x00000000a5b00000, 0x00000000a5c00000, 0x00000000a5c00000|100%| O|  |TAMS 0x00000000a5b00000| PB 0x00000000a5b00000| Untracked 
|  92|0x00000000a5c00000, 0x00000000a5d00000, 0x00000000a5d00000|100%| O|  |TAMS 0x00000000a5c00000| PB 0x00000000a5c00000| Untracked 
|  93|0x00000000a5d00000, 0x00000000a5e00000, 0x00000000a5e00000|100%| O|  |TAMS 0x00000000a5d00000| PB 0x00000000a5d00000| Untracked 
|  94|0x00000000a5e00000, 0x00000000a5f00000, 0x00000000a5f00000|100%| O|  |TAMS 0x00000000a5e00000| PB 0x00000000a5e00000| Untracked 
|  95|0x00000000a5f00000, 0x00000000a6000000, 0x00000000a6000000|100%| O|  |TAMS 0x00000000a5f00000| PB 0x00000000a5f00000| Untracked 
|  96|0x00000000a6000000, 0x00000000a6100000, 0x00000000a6100000|100%| O|  |TAMS 0x00000000a6000000| PB 0x00000000a6000000| Untracked 
|  97|0x00000000a6100000, 0x00000000a6200000, 0x00000000a6200000|100%| O|  |TAMS 0x00000000a6100000| PB 0x00000000a6100000| Untracked 
|  98|0x00000000a6200000, 0x00000000a6300000, 0x00000000a6300000|100%| O|  |TAMS 0x00000000a6200000| PB 0x00000000a6200000| Untracked 
|  99|0x00000000a6300000, 0x00000000a6400000, 0x00000000a6400000|100%| O|  |TAMS 0x00000000a6300000| PB 0x00000000a6300000| Untracked 
| 100|0x00000000a6400000, 0x00000000a6500000, 0x00000000a6500000|100%|HS|  |TAMS 0x00000000a6400000| PB 0x00000000a6400000| Complete 
| 101|0x00000000a6500000, 0x00000000a6600000, 0x00000000a6600000|100%|HC|  |TAMS 0x00000000a6500000| PB 0x00000000a6500000| Complete 
| 102|0x00000000a6600000, 0x00000000a6700000, 0x00000000a6700000|100%|HC|  |TAMS 0x00000000a6600000| PB 0x00000000a6600000| Complete 
| 103|0x00000000a6700000, 0x00000000a6800000, 0x00000000a6800000|100%| O|Cm|TAMS 0x00000000a6700000| PB 0x00000000a6700000| Complete 
| 104|0x00000000a6800000, 0x00000000a6900000, 0x00000000a6900000|100%| O|  |TAMS 0x00000000a6800000| PB 0x00000000a6800000| Untracked 
| 105|0x00000000a6900000, 0x00000000a6a00000, 0x00000000a6a00000|100%| O|  |TAMS 0x00000000a6900000| PB 0x00000000a6900000| Untracked 
| 106|0x00000000a6a00000, 0x00000000a6b00000, 0x00000000a6b00000|100%| O|  |TAMS 0x00000000a6a00000| PB 0x00000000a6a00000| Untracked 
| 107|0x00000000a6b00000, 0x00000000a6c00000, 0x00000000a6c00000|100%| O|  |TAMS 0x00000000a6b00000| PB 0x00000000a6b00000| Untracked 
| 108|0x00000000a6c00000, 0x00000000a6d00000, 0x00000000a6d00000|100%| O|  |TAMS 0x00000000a6c00000| PB 0x00000000a6c00000| Untracked 
| 109|0x00000000a6d00000, 0x00000000a6e00000, 0x00000000a6e00000|100%| O|  |TAMS 0x00000000a6d00000| PB 0x00000000a6d00000| Untracked 
| 110|0x00000000a6e00000, 0x00000000a6f00000, 0x00000000a6f00000|100%| O|  |TAMS 0x00000000a6e00000| PB 0x00000000a6e00000| Untracked 
| 111|0x00000000a6f00000, 0x00000000a7000000, 0x00000000a7000000|100%| O|  |TAMS 0x00000000a6f00000| PB 0x00000000a6f00000| Untracked 
| 112|0x00000000a7000000, 0x00000000a7100000, 0x00000000a7100000|100%| O|  |TAMS 0x00000000a7000000| PB 0x00000000a7000000| Untracked 
| 113|0x00000000a7100000, 0x00000000a7200000, 0x00000000a7200000|100%| O|  |TAMS 0x00000000a7100000| PB 0x00000000a7100000| Untracked 
| 114|0x00000000a7200000, 0x00000000a7300000, 0x00000000a7300000|100%| O|  |TAMS 0x00000000a7200000| PB 0x00000000a7200000| Untracked 
| 115|0x00000000a7300000, 0x00000000a7400000, 0x00000000a7400000|100%| O|  |TAMS 0x00000000a7300000| PB 0x00000000a7300000| Untracked 
| 116|0x00000000a7400000, 0x00000000a7500000, 0x00000000a7500000|100%| O|  |TAMS 0x00000000a7400000| PB 0x00000000a7400000| Untracked 
| 117|0x00000000a7500000, 0x00000000a7600000, 0x00000000a7600000|100%| O|  |TAMS 0x00000000a7500000| PB 0x00000000a7500000| Untracked 
| 118|0x00000000a7600000, 0x00000000a7700000, 0x00000000a7700000|100%| O|  |TAMS 0x00000000a7600000| PB 0x00000000a7600000| Untracked 
| 119|0x00000000a7700000, 0x00000000a7800000, 0x00000000a7800000|100%| O|  |TAMS 0x00000000a7700000| PB 0x00000000a7700000| Untracked 
| 120|0x00000000a7800000, 0x00000000a7900000, 0x00000000a7900000|100%| O|  |TAMS 0x00000000a7800000| PB 0x00000000a7800000| Untracked 
| 121|0x00000000a7900000, 0x00000000a7a00000, 0x00000000a7a00000|100%| O|  |TAMS 0x00000000a7900000| PB 0x00000000a7900000| Untracked 
| 122|0x00000000a7a00000, 0x00000000a7b00000, 0x00000000a7b00000|100%| O|  |TAMS 0x00000000a7a00000| PB 0x00000000a7a00000| Untracked 
| 123|0x00000000a7b00000, 0x00000000a7c00000, 0x00000000a7c00000|100%| O|Cm|TAMS 0x00000000a7b00000| PB 0x00000000a7b00000| Complete 
| 124|0x00000000a7c00000, 0x00000000a7d00000, 0x00000000a7d00000|100%| O|  |TAMS 0x00000000a7c00000| PB 0x00000000a7c00000| Untracked 
| 125|0x00000000a7d00000, 0x00000000a7e00000, 0x00000000a7e00000|100%| O|  |TAMS 0x00000000a7d00000| PB 0x00000000a7d00000| Untracked 
| 126|0x00000000a7e00000, 0x00000000a7f00000, 0x00000000a7f00000|100%| O|Cm|TAMS 0x00000000a7e00000| PB 0x00000000a7e00000| Complete 
| 127|0x00000000a7f00000, 0x00000000a8000000, 0x00000000a8000000|100%| O|  |TAMS 0x00000000a7f00000| PB 0x00000000a7f00000| Untracked 
| 128|0x00000000a8000000, 0x00000000a8100000, 0x00000000a8100000|100%| O|  |TAMS 0x00000000a8000000| PB 0x00000000a8000000| Untracked 
| 129|0x00000000a8100000, 0x00000000a8200000, 0x00000000a8200000|100%| O|  |TAMS 0x00000000a8100000| PB 0x00000000a8100000| Untracked 
| 130|0x00000000a8200000, 0x00000000a8300000, 0x00000000a8300000|100%| O|Cm|TAMS 0x00000000a8200000| PB 0x00000000a8200000| Complete 
| 131|0x00000000a8300000, 0x00000000a8300000, 0x00000000a8400000|  0%| F|  |TAMS 0x00000000a8300000| PB 0x00000000a8300000| Untracked 
| 132|0x00000000a8400000, 0x00000000a8400000, 0x00000000a8500000|  0%| F|  |TAMS 0x00000000a8400000| PB 0x00000000a8400000| Untracked 
| 133|0x00000000a8500000, 0x00000000a8500000, 0x00000000a8600000|  0%| F|  |TAMS 0x00000000a8500000| PB 0x00000000a8500000| Untracked 
| 134|0x00000000a8600000, 0x00000000a8700000, 0x00000000a8700000|100%| O|  |TAMS 0x00000000a8600000| PB 0x00000000a8600000| Untracked 
| 135|0x00000000a8700000, 0x00000000a8800000, 0x00000000a8800000|100%| O|  |TAMS 0x00000000a8700000| PB 0x00000000a8700000| Untracked 
| 136|0x00000000a8800000, 0x00000000a8900000, 0x00000000a8900000|100%| O|  |TAMS 0x00000000a8800000| PB 0x00000000a8800000| Untracked 
| 137|0x00000000a8900000, 0x00000000a8a00000, 0x00000000a8a00000|100%| O|  |TAMS 0x00000000a8900000| PB 0x00000000a8900000| Untracked 
| 138|0x00000000a8a00000, 0x00000000a8b00000, 0x00000000a8b00000|100%| O|  |TAMS 0x00000000a8a00000| PB 0x00000000a8a00000| Untracked 
| 139|0x00000000a8b00000, 0x00000000a8b29608, 0x00000000a8c00000| 16%| O|  |TAMS 0x00000000a8b00000| PB 0x00000000a8b00000| Untracked 
| 140|0x00000000a8c00000, 0x00000000a8c00000, 0x00000000a8d00000|  0%| F|  |TAMS 0x00000000a8c00000| PB 0x00000000a8c00000| Untracked 
| 141|0x00000000a8d00000, 0x00000000a8e00000, 0x00000000a8e00000|100%| O|  |TAMS 0x00000000a8d00000| PB 0x00000000a8d00000| Untracked 
| 142|0x00000000a8e00000, 0x00000000a8f00000, 0x00000000a8f00000|100%| O|Cm|TAMS 0x00000000a8e00000| PB 0x00000000a8e00000| Complete 
| 143|0x00000000a8f00000, 0x00000000a9000000, 0x00000000a9000000|100%| O|  |TAMS 0x00000000a8f00000| PB 0x00000000a8f00000| Untracked 
| 144|0x00000000a9000000, 0x00000000a9100000, 0x00000000a9100000|100%| O|  |TAMS 0x00000000a9000000| PB 0x00000000a9000000| Untracked 
| 145|0x00000000a9100000, 0x00000000a9200000, 0x00000000a9200000|100%| O|  |TAMS 0x00000000a9100000| PB 0x00000000a9100000| Untracked 
| 146|0x00000000a9200000, 0x00000000a9200000, 0x00000000a9300000|  0%| F|  |TAMS 0x00000000a9200000| PB 0x00000000a9200000| Untracked 
| 147|0x00000000a9300000, 0x00000000a9400000, 0x00000000a9400000|100%| O|  |TAMS 0x00000000a9300000| PB 0x00000000a9300000| Untracked 
| 148|0x00000000a9400000, 0x00000000a9500000, 0x00000000a9500000|100%| O|  |TAMS 0x00000000a9400000| PB 0x00000000a9400000| Untracked 
| 149|0x00000000a9500000, 0x00000000a9600000, 0x00000000a9600000|100%| O|  |TAMS 0x00000000a9500000| PB 0x00000000a9500000| Untracked 
| 150|0x00000000a9600000, 0x00000000a9600000, 0x00000000a9700000|  0%| F|  |TAMS 0x00000000a9600000| PB 0x00000000a9600000| Untracked 
| 151|0x00000000a9700000, 0x00000000a9800000, 0x00000000a9800000|100%| O|  |TAMS 0x00000000a9700000| PB 0x00000000a9700000| Untracked 
| 152|0x00000000a9800000, 0x00000000a9900000, 0x00000000a9900000|100%| O|  |TAMS 0x00000000a9800000| PB 0x00000000a9800000| Untracked 
| 153|0x00000000a9900000, 0x00000000a9a00000, 0x00000000a9a00000|100%| O|  |TAMS 0x00000000a9900000| PB 0x00000000a9900000| Untracked 
| 154|0x00000000a9a00000, 0x00000000a9b00000, 0x00000000a9b00000|100%| O|  |TAMS 0x00000000a9a00000| PB 0x00000000a9a00000| Untracked 
| 155|0x00000000a9b00000, 0x00000000a9c00000, 0x00000000a9c00000|100%| O|  |TAMS 0x00000000a9b00000| PB 0x00000000a9b00000| Untracked 
| 156|0x00000000a9c00000, 0x00000000a9d00000, 0x00000000a9d00000|100%| O|  |TAMS 0x00000000a9c00000| PB 0x00000000a9c00000| Untracked 
| 157|0x00000000a9d00000, 0x00000000a9e00000, 0x00000000a9e00000|100%| O|  |TAMS 0x00000000a9d00000| PB 0x00000000a9d00000| Untracked 
| 158|0x00000000a9e00000, 0x00000000a9f00000, 0x00000000a9f00000|100%| O|  |TAMS 0x00000000a9e00000| PB 0x00000000a9e00000| Untracked 
| 159|0x00000000a9f00000, 0x00000000aa000000, 0x00000000aa000000|100%| O|  |TAMS 0x00000000a9f00000| PB 0x00000000a9f00000| Untracked 
| 160|0x00000000aa000000, 0x00000000aa100000, 0x00000000aa100000|100%| O|  |TAMS 0x00000000aa000000| PB 0x00000000aa000000| Untracked 
| 161|0x00000000aa100000, 0x00000000aa200000, 0x00000000aa200000|100%| O|  |TAMS 0x00000000aa100000| PB 0x00000000aa100000| Untracked 
| 162|0x00000000aa200000, 0x00000000aa200000, 0x00000000aa300000|  0%| F|  |TAMS 0x00000000aa200000| PB 0x00000000aa200000| Untracked 
| 163|0x00000000aa300000, 0x00000000aa400000, 0x00000000aa400000|100%| O|  |TAMS 0x00000000aa300000| PB 0x00000000aa300000| Untracked 
| 164|0x00000000aa400000, 0x00000000aa500000, 0x00000000aa500000|100%| O|  |TAMS 0x00000000aa400000| PB 0x00000000aa400000| Untracked 
| 165|0x00000000aa500000, 0x00000000aa600000, 0x00000000aa600000|100%| O|  |TAMS 0x00000000aa500000| PB 0x00000000aa500000| Untracked 
| 166|0x00000000aa600000, 0x00000000aa700000, 0x00000000aa700000|100%| O|  |TAMS 0x00000000aa600000| PB 0x00000000aa600000| Untracked 
| 167|0x00000000aa700000, 0x00000000aa800000, 0x00000000aa800000|100%| O|  |TAMS 0x00000000aa700000| PB 0x00000000aa700000| Untracked 
| 168|0x00000000aa800000, 0x00000000aa900000, 0x00000000aa900000|100%| O|  |TAMS 0x00000000aa800000| PB 0x00000000aa800000| Untracked 
| 169|0x00000000aa900000, 0x00000000aaa00000, 0x00000000aaa00000|100%| O|  |TAMS 0x00000000aa900000| PB 0x00000000aa900000| Untracked 
| 170|0x00000000aaa00000, 0x00000000aab00000, 0x00000000aab00000|100%| O|  |TAMS 0x00000000aaa00000| PB 0x00000000aaa00000| Untracked 
| 171|0x00000000aab00000, 0x00000000aac00000, 0x00000000aac00000|100%| O|  |TAMS 0x00000000aab00000| PB 0x00000000aab00000| Untracked 
| 172|0x00000000aac00000, 0x00000000aad00000, 0x00000000aad00000|100%| O|  |TAMS 0x00000000aac00000| PB 0x00000000aac00000| Untracked 
| 173|0x00000000aad00000, 0x00000000aad00000, 0x00000000aae00000|  0%| F|  |TAMS 0x00000000aad00000| PB 0x00000000aad00000| Untracked 
| 174|0x00000000aae00000, 0x00000000aaf00000, 0x00000000aaf00000|100%| O|  |TAMS 0x00000000aae00000| PB 0x00000000aae00000| Untracked 
| 175|0x00000000aaf00000, 0x00000000ab000000, 0x00000000ab000000|100%| O|Cm|TAMS 0x00000000aaf00000| PB 0x00000000aaf00000| Complete 
| 176|0x00000000ab000000, 0x00000000ab000000, 0x00000000ab100000|  0%| F|  |TAMS 0x00000000ab000000| PB 0x00000000ab000000| Untracked 
| 177|0x00000000ab100000, 0x00000000ab100000, 0x00000000ab200000|  0%| F|  |TAMS 0x00000000ab100000| PB 0x00000000ab100000| Untracked 
| 178|0x00000000ab200000, 0x00000000ab200000, 0x00000000ab300000|  0%| F|  |TAMS 0x00000000ab200000| PB 0x00000000ab200000| Untracked 
| 179|0x00000000ab300000, 0x00000000ab300000, 0x00000000ab400000|  0%| F|  |TAMS 0x00000000ab300000| PB 0x00000000ab300000| Untracked 
| 180|0x00000000ab400000, 0x00000000ab400000, 0x00000000ab500000|  0%| F|  |TAMS 0x00000000ab400000| PB 0x00000000ab400000| Untracked 
| 181|0x00000000ab500000, 0x00000000ab500000, 0x00000000ab600000|  0%| F|  |TAMS 0x00000000ab500000| PB 0x00000000ab500000| Untracked 
| 182|0x00000000ab600000, 0x00000000ab700000, 0x00000000ab700000|100%| O|  |TAMS 0x00000000ab600000| PB 0x00000000ab600000| Untracked 
| 183|0x00000000ab700000, 0x00000000ab700000, 0x00000000ab800000|  0%| F|  |TAMS 0x00000000ab700000| PB 0x00000000ab700000| Untracked 
| 184|0x00000000ab800000, 0x00000000ab800000, 0x00000000ab900000|  0%| F|  |TAMS 0x00000000ab800000| PB 0x00000000ab800000| Untracked 
| 185|0x00000000ab900000, 0x00000000ab900000, 0x00000000aba00000|  0%| F|  |TAMS 0x00000000ab900000| PB 0x00000000ab900000| Untracked 
| 186|0x00000000aba00000, 0x00000000aba00000, 0x00000000abb00000|  0%| F|  |TAMS 0x00000000aba00000| PB 0x00000000aba00000| Untracked 
| 187|0x00000000abb00000, 0x00000000abb00000, 0x00000000abc00000|  0%| F|  |TAMS 0x00000000abb00000| PB 0x00000000abb00000| Untracked 
| 188|0x00000000abc00000, 0x00000000abc00000, 0x00000000abd00000|  0%| F|  |TAMS 0x00000000abc00000| PB 0x00000000abc00000| Untracked 
| 189|0x00000000abd00000, 0x00000000abd00000, 0x00000000abe00000|  0%| F|  |TAMS 0x00000000abd00000| PB 0x00000000abd00000| Untracked 
| 190|0x00000000abe00000, 0x00000000abe00000, 0x00000000abf00000|  0%| F|  |TAMS 0x00000000abe00000| PB 0x00000000abe00000| Untracked 
| 191|0x00000000abf00000, 0x00000000abf00000, 0x00000000ac000000|  0%| F|  |TAMS 0x00000000abf00000| PB 0x00000000abf00000| Untracked 
| 192|0x00000000ac000000, 0x00000000ac000000, 0x00000000ac100000|  0%| F|  |TAMS 0x00000000ac000000| PB 0x00000000ac000000| Untracked 
| 193|0x00000000ac100000, 0x00000000ac100000, 0x00000000ac200000|  0%| F|  |TAMS 0x00000000ac100000| PB 0x00000000ac100000| Untracked 
| 194|0x00000000ac200000, 0x00000000ac200000, 0x00000000ac300000|  0%| F|  |TAMS 0x00000000ac200000| PB 0x00000000ac200000| Untracked 
| 195|0x00000000ac300000, 0x00000000ac300000, 0x00000000ac400000|  0%| F|  |TAMS 0x00000000ac300000| PB 0x00000000ac300000| Untracked 
| 196|0x00000000ac400000, 0x00000000ac400000, 0x00000000ac500000|  0%| F|  |TAMS 0x00000000ac400000| PB 0x00000000ac400000| Untracked 
| 197|0x00000000ac500000, 0x00000000ac500000, 0x00000000ac600000|  0%| F|  |TAMS 0x00000000ac500000| PB 0x00000000ac500000| Untracked 
| 198|0x00000000ac600000, 0x00000000ac600000, 0x00000000ac700000|  0%| F|  |TAMS 0x00000000ac600000| PB 0x00000000ac600000| Untracked 
| 199|0x00000000ac700000, 0x00000000ac700000, 0x00000000ac800000|  0%| F|  |TAMS 0x00000000ac700000| PB 0x00000000ac700000| Untracked 
| 200|0x00000000ac800000, 0x00000000ac800000, 0x00000000ac900000|  0%| F|  |TAMS 0x00000000ac800000| PB 0x00000000ac800000| Untracked 
| 201|0x00000000ac900000, 0x00000000ac900000, 0x00000000aca00000|  0%| F|  |TAMS 0x00000000ac900000| PB 0x00000000ac900000| Untracked 
| 202|0x00000000aca00000, 0x00000000aca00000, 0x00000000acb00000|  0%| F|  |TAMS 0x00000000aca00000| PB 0x00000000aca00000| Untracked 
| 203|0x00000000acb00000, 0x00000000acb00000, 0x00000000acc00000|  0%| F|  |TAMS 0x00000000acb00000| PB 0x00000000acb00000| Untracked 
| 204|0x00000000acc00000, 0x00000000acc00000, 0x00000000acd00000|  0%| F|  |TAMS 0x00000000acc00000| PB 0x00000000acc00000| Untracked 
| 205|0x00000000acd00000, 0x00000000acd00000, 0x00000000ace00000|  0%| F|  |TAMS 0x00000000acd00000| PB 0x00000000acd00000| Untracked 
| 206|0x00000000ace00000, 0x00000000ace00000, 0x00000000acf00000|  0%| F|  |TAMS 0x00000000ace00000| PB 0x00000000ace00000| Untracked 
| 207|0x00000000acf00000, 0x00000000acf00000, 0x00000000ad000000|  0%| F|  |TAMS 0x00000000acf00000| PB 0x00000000acf00000| Untracked 
| 208|0x00000000ad000000, 0x00000000ad000000, 0x00000000ad100000|  0%| F|  |TAMS 0x00000000ad000000| PB 0x00000000ad000000| Untracked 
| 209|0x00000000ad100000, 0x00000000ad100000, 0x00000000ad200000|  0%| F|  |TAMS 0x00000000ad100000| PB 0x00000000ad100000| Untracked 
| 210|0x00000000ad200000, 0x00000000ad200000, 0x00000000ad300000|  0%| F|  |TAMS 0x00000000ad200000| PB 0x00000000ad200000| Untracked 
| 211|0x00000000ad300000, 0x00000000ad300000, 0x00000000ad400000|  0%| F|  |TAMS 0x00000000ad300000| PB 0x00000000ad300000| Untracked 
| 212|0x00000000ad400000, 0x00000000ad400000, 0x00000000ad500000|  0%| F|  |TAMS 0x00000000ad400000| PB 0x00000000ad400000| Untracked 
| 213|0x00000000ad500000, 0x00000000ad500000, 0x00000000ad600000|  0%| F|  |TAMS 0x00000000ad500000| PB 0x00000000ad500000| Untracked 
| 214|0x00000000ad600000, 0x00000000ad600000, 0x00000000ad700000|  0%| F|  |TAMS 0x00000000ad600000| PB 0x00000000ad600000| Untracked 
| 215|0x00000000ad700000, 0x00000000ad700000, 0x00000000ad800000|  0%| F|  |TAMS 0x00000000ad700000| PB 0x00000000ad700000| Untracked 
| 216|0x00000000ad800000, 0x00000000ad800000, 0x00000000ad900000|  0%| F|  |TAMS 0x00000000ad800000| PB 0x00000000ad800000| Untracked 
| 217|0x00000000ad900000, 0x00000000ad900000, 0x00000000ada00000|  0%| F|  |TAMS 0x00000000ad900000| PB 0x00000000ad900000| Untracked 
| 218|0x00000000ada00000, 0x00000000ada00000, 0x00000000adb00000|  0%| F|  |TAMS 0x00000000ada00000| PB 0x00000000ada00000| Untracked 
| 219|0x00000000adb00000, 0x00000000adb00000, 0x00000000adc00000|  0%| F|  |TAMS 0x00000000adb00000| PB 0x00000000adb00000| Untracked 
| 220|0x00000000adc00000, 0x00000000adc00000, 0x00000000add00000|  0%| F|  |TAMS 0x00000000adc00000| PB 0x00000000adc00000| Untracked 
| 221|0x00000000add00000, 0x00000000add00000, 0x00000000ade00000|  0%| F|  |TAMS 0x00000000add00000| PB 0x00000000add00000| Untracked 
| 222|0x00000000ade00000, 0x00000000ade00000, 0x00000000adf00000|  0%| F|  |TAMS 0x00000000ade00000| PB 0x00000000ade00000| Untracked 
| 223|0x00000000adf00000, 0x00000000adf00000, 0x00000000ae000000|  0%| F|  |TAMS 0x00000000adf00000| PB 0x00000000adf00000| Untracked 
| 224|0x00000000ae000000, 0x00000000ae000000, 0x00000000ae100000|  0%| F|  |TAMS 0x00000000ae000000| PB 0x00000000ae000000| Untracked 
| 225|0x00000000ae100000, 0x00000000ae100000, 0x00000000ae200000|  0%| F|  |TAMS 0x00000000ae100000| PB 0x00000000ae100000| Untracked 
| 226|0x00000000ae200000, 0x00000000ae200000, 0x00000000ae300000|  0%| F|  |TAMS 0x00000000ae200000| PB 0x00000000ae200000| Untracked 
| 227|0x00000000ae300000, 0x00000000ae300000, 0x00000000ae400000|  0%| F|  |TAMS 0x00000000ae300000| PB 0x00000000ae300000| Untracked 
| 228|0x00000000ae400000, 0x00000000ae400000, 0x00000000ae500000|  0%| F|  |TAMS 0x00000000ae400000| PB 0x00000000ae400000| Untracked 
| 229|0x00000000ae500000, 0x00000000ae500000, 0x00000000ae600000|  0%| F|  |TAMS 0x00000000ae500000| PB 0x00000000ae500000| Untracked 
| 230|0x00000000ae600000, 0x00000000ae600000, 0x00000000ae700000|  0%| F|  |TAMS 0x00000000ae600000| PB 0x00000000ae600000| Untracked 
| 231|0x00000000ae700000, 0x00000000ae700000, 0x00000000ae800000|  0%| F|  |TAMS 0x00000000ae700000| PB 0x00000000ae700000| Untracked 
| 232|0x00000000ae800000, 0x00000000ae800000, 0x00000000ae900000|  0%| F|  |TAMS 0x00000000ae800000| PB 0x00000000ae800000| Untracked 
| 233|0x00000000ae900000, 0x00000000ae900000, 0x00000000aea00000|  0%| F|  |TAMS 0x00000000ae900000| PB 0x00000000ae900000| Untracked 
| 234|0x00000000aea00000, 0x00000000aea00000, 0x00000000aeb00000|  0%| F|  |TAMS 0x00000000aea00000| PB 0x00000000aea00000| Untracked 
| 235|0x00000000aeb00000, 0x00000000aeb00000, 0x00000000aec00000|  0%| F|  |TAMS 0x00000000aeb00000| PB 0x00000000aeb00000| Untracked 
| 236|0x00000000aec00000, 0x00000000aec00000, 0x00000000aed00000|  0%| F|  |TAMS 0x00000000aec00000| PB 0x00000000aec00000| Untracked 
| 237|0x00000000aed00000, 0x00000000aed00000, 0x00000000aee00000|  0%| F|  |TAMS 0x00000000aed00000| PB 0x00000000aed00000| Untracked 
| 238|0x00000000aee00000, 0x00000000aee00000, 0x00000000aef00000|  0%| F|  |TAMS 0x00000000aee00000| PB 0x00000000aee00000| Untracked 
| 239|0x00000000aef00000, 0x00000000aef00000, 0x00000000af000000|  0%| F|  |TAMS 0x00000000aef00000| PB 0x00000000aef00000| Untracked 
| 240|0x00000000af000000, 0x00000000af000000, 0x00000000af100000|  0%| F|  |TAMS 0x00000000af000000| PB 0x00000000af000000| Untracked 
| 241|0x00000000af100000, 0x00000000af100000, 0x00000000af200000|  0%| F|  |TAMS 0x00000000af100000| PB 0x00000000af100000| Untracked 
| 242|0x00000000af200000, 0x00000000af200000, 0x00000000af300000|  0%| F|  |TAMS 0x00000000af200000| PB 0x00000000af200000| Untracked 
| 243|0x00000000af300000, 0x00000000af300000, 0x00000000af400000|  0%| F|  |TAMS 0x00000000af300000| PB 0x00000000af300000| Untracked 
| 244|0x00000000af400000, 0x00000000af400000, 0x00000000af500000|  0%| F|  |TAMS 0x00000000af400000| PB 0x00000000af400000| Untracked 
| 245|0x00000000af500000, 0x00000000af500000, 0x00000000af600000|  0%| F|  |TAMS 0x00000000af500000| PB 0x00000000af500000| Untracked 
| 246|0x00000000af600000, 0x00000000af600000, 0x00000000af700000|  0%| F|  |TAMS 0x00000000af600000| PB 0x00000000af600000| Untracked 
| 247|0x00000000af700000, 0x00000000af700000, 0x00000000af800000|  0%| F|  |TAMS 0x00000000af700000| PB 0x00000000af700000| Untracked 
| 248|0x00000000af800000, 0x00000000af800000, 0x00000000af900000|  0%| F|  |TAMS 0x00000000af800000| PB 0x00000000af800000| Untracked 
| 249|0x00000000af900000, 0x00000000af900000, 0x00000000afa00000|  0%| F|  |TAMS 0x00000000af900000| PB 0x00000000af900000| Untracked 
| 250|0x00000000afa00000, 0x00000000afa00000, 0x00000000afb00000|  0%| F|  |TAMS 0x00000000afa00000| PB 0x00000000afa00000| Untracked 
| 251|0x00000000afb00000, 0x00000000afb00000, 0x00000000afc00000|  0%| F|  |TAMS 0x00000000afb00000| PB 0x00000000afb00000| Untracked 
| 252|0x00000000afc00000, 0x00000000afc00000, 0x00000000afd00000|  0%| F|  |TAMS 0x00000000afc00000| PB 0x00000000afc00000| Untracked 
| 253|0x00000000afd00000, 0x00000000afd00000, 0x00000000afe00000|  0%| F|  |TAMS 0x00000000afd00000| PB 0x00000000afd00000| Untracked 
| 254|0x00000000afe00000, 0x00000000afe00000, 0x00000000aff00000|  0%| F|  |TAMS 0x00000000afe00000| PB 0x00000000afe00000| Untracked 
| 255|0x00000000aff00000, 0x00000000aff00000, 0x00000000b0000000|  0%| F|  |TAMS 0x00000000aff00000| PB 0x00000000aff00000| Untracked 
| 256|0x00000000b0000000, 0x00000000b0000000, 0x00000000b0100000|  0%| F|  |TAMS 0x00000000b0000000| PB 0x00000000b0000000| Untracked 
| 257|0x00000000b0100000, 0x00000000b0100000, 0x00000000b0200000|  0%| F|  |TAMS 0x00000000b0100000| PB 0x00000000b0100000| Untracked 
| 258|0x00000000b0200000, 0x00000000b0200000, 0x00000000b0300000|  0%| F|  |TAMS 0x00000000b0200000| PB 0x00000000b0200000| Untracked 
| 259|0x00000000b0300000, 0x00000000b0300000, 0x00000000b0400000|  0%| F|  |TAMS 0x00000000b0300000| PB 0x00000000b0300000| Untracked 
| 260|0x00000000b0400000, 0x00000000b0400000, 0x00000000b0500000|  0%| F|  |TAMS 0x00000000b0400000| PB 0x00000000b0400000| Untracked 
| 261|0x00000000b0500000, 0x00000000b0500000, 0x00000000b0600000|  0%| F|  |TAMS 0x00000000b0500000| PB 0x00000000b0500000| Untracked 
| 262|0x00000000b0600000, 0x00000000b0600000, 0x00000000b0700000|  0%| F|  |TAMS 0x00000000b0600000| PB 0x00000000b0600000| Untracked 
| 263|0x00000000b0700000, 0x00000000b0700000, 0x00000000b0800000|  0%| F|  |TAMS 0x00000000b0700000| PB 0x00000000b0700000| Untracked 
| 264|0x00000000b0800000, 0x00000000b0800000, 0x00000000b0900000|  0%| F|  |TAMS 0x00000000b0800000| PB 0x00000000b0800000| Untracked 
| 265|0x00000000b0900000, 0x00000000b0900000, 0x00000000b0a00000|  0%| F|  |TAMS 0x00000000b0900000| PB 0x00000000b0900000| Untracked 
| 266|0x00000000b0a00000, 0x00000000b0a00000, 0x00000000b0b00000|  0%| F|  |TAMS 0x00000000b0a00000| PB 0x00000000b0a00000| Untracked 
| 267|0x00000000b0b00000, 0x00000000b0b00000, 0x00000000b0c00000|  0%| F|  |TAMS 0x00000000b0b00000| PB 0x00000000b0b00000| Untracked 
| 268|0x00000000b0c00000, 0x00000000b0c00000, 0x00000000b0d00000|  0%| F|  |TAMS 0x00000000b0c00000| PB 0x00000000b0c00000| Untracked 
| 269|0x00000000b0d00000, 0x00000000b0d00000, 0x00000000b0e00000|  0%| F|  |TAMS 0x00000000b0d00000| PB 0x00000000b0d00000| Untracked 
| 270|0x00000000b0e00000, 0x00000000b0e00000, 0x00000000b0f00000|  0%| F|  |TAMS 0x00000000b0e00000| PB 0x00000000b0e00000| Untracked 
| 271|0x00000000b0f00000, 0x00000000b0f00000, 0x00000000b1000000|  0%| F|  |TAMS 0x00000000b0f00000| PB 0x00000000b0f00000| Untracked 
| 272|0x00000000b1000000, 0x00000000b1000000, 0x00000000b1100000|  0%| F|  |TAMS 0x00000000b1000000| PB 0x00000000b1000000| Untracked 
| 273|0x00000000b1100000, 0x00000000b1100000, 0x00000000b1200000|  0%| F|  |TAMS 0x00000000b1100000| PB 0x00000000b1100000| Untracked 
| 274|0x00000000b1200000, 0x00000000b1200000, 0x00000000b1300000|  0%| F|  |TAMS 0x00000000b1200000| PB 0x00000000b1200000| Untracked 
| 275|0x00000000b1300000, 0x00000000b1300000, 0x00000000b1400000|  0%| F|  |TAMS 0x00000000b1300000| PB 0x00000000b1300000| Untracked 
| 276|0x00000000b1400000, 0x00000000b1400000, 0x00000000b1500000|  0%| F|  |TAMS 0x00000000b1400000| PB 0x00000000b1400000| Untracked 
| 277|0x00000000b1500000, 0x00000000b1500000, 0x00000000b1600000|  0%| F|  |TAMS 0x00000000b1500000| PB 0x00000000b1500000| Untracked 
| 278|0x00000000b1600000, 0x00000000b1600000, 0x00000000b1700000|  0%| F|  |TAMS 0x00000000b1600000| PB 0x00000000b1600000| Untracked 
| 279|0x00000000b1700000, 0x00000000b1700000, 0x00000000b1800000|  0%| F|  |TAMS 0x00000000b1700000| PB 0x00000000b1700000| Untracked 
| 280|0x00000000b1800000, 0x00000000b1800000, 0x00000000b1900000|  0%| F|  |TAMS 0x00000000b1800000| PB 0x00000000b1800000| Untracked 
| 281|0x00000000b1900000, 0x00000000b1900000, 0x00000000b1a00000|  0%| F|  |TAMS 0x00000000b1900000| PB 0x00000000b1900000| Untracked 
| 282|0x00000000b1a00000, 0x00000000b1a00000, 0x00000000b1b00000|  0%| F|  |TAMS 0x00000000b1a00000| PB 0x00000000b1a00000| Untracked 
| 283|0x00000000b1b00000, 0x00000000b1b00000, 0x00000000b1c00000|  0%| F|  |TAMS 0x00000000b1b00000| PB 0x00000000b1b00000| Untracked 
| 284|0x00000000b1c00000, 0x00000000b1c00000, 0x00000000b1d00000|  0%| F|  |TAMS 0x00000000b1c00000| PB 0x00000000b1c00000| Untracked 
| 285|0x00000000b1d00000, 0x00000000b1d00000, 0x00000000b1e00000|  0%| F|  |TAMS 0x00000000b1d00000| PB 0x00000000b1d00000| Untracked 
| 286|0x00000000b1e00000, 0x00000000b1e00000, 0x00000000b1f00000|  0%| F|  |TAMS 0x00000000b1e00000| PB 0x00000000b1e00000| Untracked 
| 287|0x00000000b1f00000, 0x00000000b1f00000, 0x00000000b2000000|  0%| F|  |TAMS 0x00000000b1f00000| PB 0x00000000b1f00000| Untracked 
| 288|0x00000000b2000000, 0x00000000b2000000, 0x00000000b2100000|  0%| F|  |TAMS 0x00000000b2000000| PB 0x00000000b2000000| Untracked 
| 289|0x00000000b2100000, 0x00000000b2100000, 0x00000000b2200000|  0%| F|  |TAMS 0x00000000b2100000| PB 0x00000000b2100000| Untracked 
| 290|0x00000000b2200000, 0x00000000b2200000, 0x00000000b2300000|  0%| F|  |TAMS 0x00000000b2200000| PB 0x00000000b2200000| Untracked 
| 291|0x00000000b2300000, 0x00000000b2300000, 0x00000000b2400000|  0%| F|  |TAMS 0x00000000b2300000| PB 0x00000000b2300000| Untracked 
| 292|0x00000000b2400000, 0x00000000b2400000, 0x00000000b2500000|  0%| F|  |TAMS 0x00000000b2400000| PB 0x00000000b2400000| Untracked 
| 293|0x00000000b2500000, 0x00000000b2500000, 0x00000000b2600000|  0%| F|  |TAMS 0x00000000b2500000| PB 0x00000000b2500000| Untracked 
| 294|0x00000000b2600000, 0x00000000b2600000, 0x00000000b2700000|  0%| F|  |TAMS 0x00000000b2600000| PB 0x00000000b2600000| Untracked 
| 295|0x00000000b2700000, 0x00000000b2700000, 0x00000000b2800000|  0%| F|  |TAMS 0x00000000b2700000| PB 0x00000000b2700000| Untracked 
| 296|0x00000000b2800000, 0x00000000b2800000, 0x00000000b2900000|  0%| F|  |TAMS 0x00000000b2800000| PB 0x00000000b2800000| Untracked 
| 297|0x00000000b2900000, 0x00000000b2900000, 0x00000000b2a00000|  0%| F|  |TAMS 0x00000000b2900000| PB 0x00000000b2900000| Untracked 
| 298|0x00000000b2a00000, 0x00000000b2a00000, 0x00000000b2b00000|  0%| F|  |TAMS 0x00000000b2a00000| PB 0x00000000b2a00000| Untracked 
| 299|0x00000000b2b00000, 0x00000000b2b00000, 0x00000000b2c00000|  0%| F|  |TAMS 0x00000000b2b00000| PB 0x00000000b2b00000| Untracked 
| 300|0x00000000b2c00000, 0x00000000b2c00000, 0x00000000b2d00000|  0%| F|  |TAMS 0x00000000b2c00000| PB 0x00000000b2c00000| Untracked 
| 301|0x00000000b2d00000, 0x00000000b2d00000, 0x00000000b2e00000|  0%| F|  |TAMS 0x00000000b2d00000| PB 0x00000000b2d00000| Untracked 
| 302|0x00000000b2e00000, 0x00000000b2e00000, 0x00000000b2f00000|  0%| F|  |TAMS 0x00000000b2e00000| PB 0x00000000b2e00000| Untracked 
| 303|0x00000000b2f00000, 0x00000000b2f00000, 0x00000000b3000000|  0%| F|  |TAMS 0x00000000b2f00000| PB 0x00000000b2f00000| Untracked 
| 304|0x00000000b3000000, 0x00000000b3000000, 0x00000000b3100000|  0%| F|  |TAMS 0x00000000b3000000| PB 0x00000000b3000000| Untracked 
| 305|0x00000000b3100000, 0x00000000b3100000, 0x00000000b3200000|  0%| F|  |TAMS 0x00000000b3100000| PB 0x00000000b3100000| Untracked 
| 306|0x00000000b3200000, 0x00000000b3200000, 0x00000000b3300000|  0%| F|  |TAMS 0x00000000b3200000| PB 0x00000000b3200000| Untracked 
| 307|0x00000000b3300000, 0x00000000b3300000, 0x00000000b3400000|  0%| F|  |TAMS 0x00000000b3300000| PB 0x00000000b3300000| Untracked 
| 308|0x00000000b3400000, 0x00000000b3400000, 0x00000000b3500000|  0%| F|  |TAMS 0x00000000b3400000| PB 0x00000000b3400000| Untracked 
| 309|0x00000000b3500000, 0x00000000b3500000, 0x00000000b3600000|  0%| F|  |TAMS 0x00000000b3500000| PB 0x00000000b3500000| Untracked 
| 310|0x00000000b3600000, 0x00000000b3600000, 0x00000000b3700000|  0%| F|  |TAMS 0x00000000b3600000| PB 0x00000000b3600000| Untracked 
| 311|0x00000000b3700000, 0x00000000b3700000, 0x00000000b3800000|  0%| F|  |TAMS 0x00000000b3700000| PB 0x00000000b3700000| Untracked 
| 312|0x00000000b3800000, 0x00000000b3800000, 0x00000000b3900000|  0%| F|  |TAMS 0x00000000b3800000| PB 0x00000000b3800000| Untracked 
| 313|0x00000000b3900000, 0x00000000b3900000, 0x00000000b3a00000|  0%| F|  |TAMS 0x00000000b3900000| PB 0x00000000b3900000| Untracked 
| 314|0x00000000b3a00000, 0x00000000b3a00000, 0x00000000b3b00000|  0%| F|  |TAMS 0x00000000b3a00000| PB 0x00000000b3a00000| Untracked 
| 315|0x00000000b3b00000, 0x00000000b3b00000, 0x00000000b3c00000|  0%| F|  |TAMS 0x00000000b3b00000| PB 0x00000000b3b00000| Untracked 
| 316|0x00000000b3c00000, 0x00000000b3c00000, 0x00000000b3d00000|  0%| F|  |TAMS 0x00000000b3c00000| PB 0x00000000b3c00000| Untracked 
| 317|0x00000000b3d00000, 0x00000000b3d00000, 0x00000000b3e00000|  0%| F|  |TAMS 0x00000000b3d00000| PB 0x00000000b3d00000| Untracked 
| 318|0x00000000b3e00000, 0x00000000b3e00000, 0x00000000b3f00000|  0%| F|  |TAMS 0x00000000b3e00000| PB 0x00000000b3e00000| Untracked 
| 319|0x00000000b3f00000, 0x00000000b3f00000, 0x00000000b4000000|  0%| F|  |TAMS 0x00000000b3f00000| PB 0x00000000b3f00000| Untracked 
| 320|0x00000000b4000000, 0x00000000b4000000, 0x00000000b4100000|  0%| F|  |TAMS 0x00000000b4000000| PB 0x00000000b4000000| Untracked 
| 321|0x00000000b4100000, 0x00000000b4100000, 0x00000000b4200000|  0%| F|  |TAMS 0x00000000b4100000| PB 0x00000000b4100000| Untracked 
| 322|0x00000000b4200000, 0x00000000b4200000, 0x00000000b4300000|  0%| F|  |TAMS 0x00000000b4200000| PB 0x00000000b4200000| Untracked 
| 323|0x00000000b4300000, 0x00000000b4300000, 0x00000000b4400000|  0%| F|  |TAMS 0x00000000b4300000| PB 0x00000000b4300000| Untracked 
| 324|0x00000000b4400000, 0x00000000b4400000, 0x00000000b4500000|  0%| F|  |TAMS 0x00000000b4400000| PB 0x00000000b4400000| Untracked 
| 325|0x00000000b4500000, 0x00000000b4500000, 0x00000000b4600000|  0%| F|  |TAMS 0x00000000b4500000| PB 0x00000000b4500000| Untracked 
| 326|0x00000000b4600000, 0x00000000b4600000, 0x00000000b4700000|  0%| F|  |TAMS 0x00000000b4600000| PB 0x00000000b4600000| Untracked 
| 327|0x00000000b4700000, 0x00000000b4700000, 0x00000000b4800000|  0%| F|  |TAMS 0x00000000b4700000| PB 0x00000000b4700000| Untracked 
| 328|0x00000000b4800000, 0x00000000b4800000, 0x00000000b4900000|  0%| F|  |TAMS 0x00000000b4800000| PB 0x00000000b4800000| Untracked 
| 329|0x00000000b4900000, 0x00000000b4900000, 0x00000000b4a00000|  0%| F|  |TAMS 0x00000000b4900000| PB 0x00000000b4900000| Untracked 
| 330|0x00000000b4a00000, 0x00000000b4a00000, 0x00000000b4b00000|  0%| F|  |TAMS 0x00000000b4a00000| PB 0x00000000b4a00000| Untracked 
| 331|0x00000000b4b00000, 0x00000000b4b00000, 0x00000000b4c00000|  0%| F|  |TAMS 0x00000000b4b00000| PB 0x00000000b4b00000| Untracked 
| 332|0x00000000b4c00000, 0x00000000b4c00000, 0x00000000b4d00000|  0%| F|  |TAMS 0x00000000b4c00000| PB 0x00000000b4c00000| Untracked 
| 333|0x00000000b4d00000, 0x00000000b4d00000, 0x00000000b4e00000|  0%| F|  |TAMS 0x00000000b4d00000| PB 0x00000000b4d00000| Untracked 
| 334|0x00000000b4e00000, 0x00000000b4e00000, 0x00000000b4f00000|  0%| F|  |TAMS 0x00000000b4e00000| PB 0x00000000b4e00000| Untracked 
| 335|0x00000000b4f00000, 0x00000000b4f00000, 0x00000000b5000000|  0%| F|  |TAMS 0x00000000b4f00000| PB 0x00000000b4f00000| Untracked 
| 336|0x00000000b5000000, 0x00000000b5000000, 0x00000000b5100000|  0%| F|  |TAMS 0x00000000b5000000| PB 0x00000000b5000000| Untracked 
| 337|0x00000000b5100000, 0x00000000b5100000, 0x00000000b5200000|  0%| F|  |TAMS 0x00000000b5100000| PB 0x00000000b5100000| Untracked 
| 338|0x00000000b5200000, 0x00000000b5200000, 0x00000000b5300000|  0%| F|  |TAMS 0x00000000b5200000| PB 0x00000000b5200000| Untracked 
| 339|0x00000000b5300000, 0x00000000b5300000, 0x00000000b5400000|  0%| F|  |TAMS 0x00000000b5300000| PB 0x00000000b5300000| Untracked 
| 340|0x00000000b5400000, 0x00000000b5400000, 0x00000000b5500000|  0%| F|  |TAMS 0x00000000b5400000| PB 0x00000000b5400000| Untracked 
| 341|0x00000000b5500000, 0x00000000b5500000, 0x00000000b5600000|  0%| F|  |TAMS 0x00000000b5500000| PB 0x00000000b5500000| Untracked 
| 342|0x00000000b5600000, 0x00000000b5600000, 0x00000000b5700000|  0%| F|  |TAMS 0x00000000b5600000| PB 0x00000000b5600000| Untracked 
| 343|0x00000000b5700000, 0x00000000b5700000, 0x00000000b5800000|  0%| F|  |TAMS 0x00000000b5700000| PB 0x00000000b5700000| Untracked 
| 344|0x00000000b5800000, 0x00000000b5800000, 0x00000000b5900000|  0%| F|  |TAMS 0x00000000b5800000| PB 0x00000000b5800000| Untracked 
| 345|0x00000000b5900000, 0x00000000b5900000, 0x00000000b5a00000|  0%| F|  |TAMS 0x00000000b5900000| PB 0x00000000b5900000| Untracked 
| 346|0x00000000b5a00000, 0x00000000b5a00000, 0x00000000b5b00000|  0%| F|  |TAMS 0x00000000b5a00000| PB 0x00000000b5a00000| Untracked 
| 347|0x00000000b5b00000, 0x00000000b5b00000, 0x00000000b5c00000|  0%| F|  |TAMS 0x00000000b5b00000| PB 0x00000000b5b00000| Untracked 
| 348|0x00000000b5c00000, 0x00000000b5c00000, 0x00000000b5d00000|  0%| F|  |TAMS 0x00000000b5c00000| PB 0x00000000b5c00000| Untracked 
| 349|0x00000000b5d00000, 0x00000000b5d00000, 0x00000000b5e00000|  0%| F|  |TAMS 0x00000000b5d00000| PB 0x00000000b5d00000| Untracked 
| 350|0x00000000b5e00000, 0x00000000b5e00000, 0x00000000b5f00000|  0%| F|  |TAMS 0x00000000b5e00000| PB 0x00000000b5e00000| Untracked 
| 351|0x00000000b5f00000, 0x00000000b5f00000, 0x00000000b6000000|  0%| F|  |TAMS 0x00000000b5f00000| PB 0x00000000b5f00000| Untracked 
| 352|0x00000000b6000000, 0x00000000b6000000, 0x00000000b6100000|  0%| F|  |TAMS 0x00000000b6000000| PB 0x00000000b6000000| Untracked 
| 353|0x00000000b6100000, 0x00000000b6100000, 0x00000000b6200000|  0%| F|  |TAMS 0x00000000b6100000| PB 0x00000000b6100000| Untracked 
| 354|0x00000000b6200000, 0x00000000b6200000, 0x00000000b6300000|  0%| F|  |TAMS 0x00000000b6200000| PB 0x00000000b6200000| Untracked 
| 355|0x00000000b6300000, 0x00000000b6300000, 0x00000000b6400000|  0%| F|  |TAMS 0x00000000b6300000| PB 0x00000000b6300000| Untracked 
| 356|0x00000000b6400000, 0x00000000b6400000, 0x00000000b6500000|  0%| F|  |TAMS 0x00000000b6400000| PB 0x00000000b6400000| Untracked 
| 357|0x00000000b6500000, 0x00000000b6500000, 0x00000000b6600000|  0%| F|  |TAMS 0x00000000b6500000| PB 0x00000000b6500000| Untracked 
| 358|0x00000000b6600000, 0x00000000b6600000, 0x00000000b6700000|  0%| F|  |TAMS 0x00000000b6600000| PB 0x00000000b6600000| Untracked 
| 359|0x00000000b6700000, 0x00000000b6700000, 0x00000000b6800000|  0%| F|  |TAMS 0x00000000b6700000| PB 0x00000000b6700000| Untracked 
| 360|0x00000000b6800000, 0x00000000b6800000, 0x00000000b6900000|  0%| F|  |TAMS 0x00000000b6800000| PB 0x00000000b6800000| Untracked 
| 361|0x00000000b6900000, 0x00000000b6900000, 0x00000000b6a00000|  0%| F|  |TAMS 0x00000000b6900000| PB 0x00000000b6900000| Untracked 
| 362|0x00000000b6a00000, 0x00000000b6a00000, 0x00000000b6b00000|  0%| F|  |TAMS 0x00000000b6a00000| PB 0x00000000b6a00000| Untracked 
| 363|0x00000000b6b00000, 0x00000000b6b00000, 0x00000000b6c00000|  0%| F|  |TAMS 0x00000000b6b00000| PB 0x00000000b6b00000| Untracked 
| 364|0x00000000b6c00000, 0x00000000b6c00000, 0x00000000b6d00000|  0%| F|  |TAMS 0x00000000b6c00000| PB 0x00000000b6c00000| Untracked 
| 365|0x00000000b6d00000, 0x00000000b6d00000, 0x00000000b6e00000|  0%| F|  |TAMS 0x00000000b6d00000| PB 0x00000000b6d00000| Untracked 
| 366|0x00000000b6e00000, 0x00000000b6e00000, 0x00000000b6f00000|  0%| F|  |TAMS 0x00000000b6e00000| PB 0x00000000b6e00000| Untracked 
| 367|0x00000000b6f00000, 0x00000000b6f00000, 0x00000000b7000000|  0%| F|  |TAMS 0x00000000b6f00000| PB 0x00000000b6f00000| Untracked 
| 368|0x00000000b7000000, 0x00000000b7000000, 0x00000000b7100000|  0%| F|  |TAMS 0x00000000b7000000| PB 0x00000000b7000000| Untracked 
| 369|0x00000000b7100000, 0x00000000b7100000, 0x00000000b7200000|  0%| F|  |TAMS 0x00000000b7100000| PB 0x00000000b7100000| Untracked 
| 370|0x00000000b7200000, 0x00000000b7200000, 0x00000000b7300000|  0%| F|  |TAMS 0x00000000b7200000| PB 0x00000000b7200000| Untracked 
| 371|0x00000000b7300000, 0x00000000b7300000, 0x00000000b7400000|  0%| F|  |TAMS 0x00000000b7300000| PB 0x00000000b7300000| Untracked 
| 372|0x00000000b7400000, 0x00000000b7400000, 0x00000000b7500000|  0%| F|  |TAMS 0x00000000b7400000| PB 0x00000000b7400000| Untracked 
| 373|0x00000000b7500000, 0x00000000b7500000, 0x00000000b7600000|  0%| F|  |TAMS 0x00000000b7500000| PB 0x00000000b7500000| Untracked 
| 374|0x00000000b7600000, 0x00000000b7600000, 0x00000000b7700000|  0%| F|  |TAMS 0x00000000b7600000| PB 0x00000000b7600000| Untracked 
| 375|0x00000000b7700000, 0x00000000b7700000, 0x00000000b7800000|  0%| F|  |TAMS 0x00000000b7700000| PB 0x00000000b7700000| Untracked 
| 376|0x00000000b7800000, 0x00000000b7800000, 0x00000000b7900000|  0%| F|  |TAMS 0x00000000b7800000| PB 0x00000000b7800000| Untracked 
| 377|0x00000000b7900000, 0x00000000b7900000, 0x00000000b7a00000|  0%| F|  |TAMS 0x00000000b7900000| PB 0x00000000b7900000| Untracked 
| 378|0x00000000b7a00000, 0x00000000b7a00000, 0x00000000b7b00000|  0%| F|  |TAMS 0x00000000b7a00000| PB 0x00000000b7a00000| Untracked 
| 379|0x00000000b7b00000, 0x00000000b7b00000, 0x00000000b7c00000|  0%| F|  |TAMS 0x00000000b7b00000| PB 0x00000000b7b00000| Untracked 
| 380|0x00000000b7c00000, 0x00000000b7c00000, 0x00000000b7d00000|  0%| F|  |TAMS 0x00000000b7c00000| PB 0x00000000b7c00000| Untracked 
| 381|0x00000000b7d00000, 0x00000000b7d00000, 0x00000000b7e00000|  0%| F|  |TAMS 0x00000000b7d00000| PB 0x00000000b7d00000| Untracked 
| 382|0x00000000b7e00000, 0x00000000b7e00000, 0x00000000b7f00000|  0%| F|  |TAMS 0x00000000b7e00000| PB 0x00000000b7e00000| Untracked 
| 383|0x00000000b7f00000, 0x00000000b7f00000, 0x00000000b8000000|  0%| F|  |TAMS 0x00000000b7f00000| PB 0x00000000b7f00000| Untracked 
| 384|0x00000000b8000000, 0x00000000b8000000, 0x00000000b8100000|  0%| F|  |TAMS 0x00000000b8000000| PB 0x00000000b8000000| Untracked 
| 385|0x00000000b8100000, 0x00000000b8100000, 0x00000000b8200000|  0%| F|  |TAMS 0x00000000b8100000| PB 0x00000000b8100000| Untracked 
| 386|0x00000000b8200000, 0x00000000b8200000, 0x00000000b8300000|  0%| F|  |TAMS 0x00000000b8200000| PB 0x00000000b8200000| Untracked 
| 387|0x00000000b8300000, 0x00000000b8300000, 0x00000000b8400000|  0%| F|  |TAMS 0x00000000b8300000| PB 0x00000000b8300000| Untracked 
| 388|0x00000000b8400000, 0x00000000b8400000, 0x00000000b8500000|  0%| F|  |TAMS 0x00000000b8400000| PB 0x00000000b8400000| Untracked 
| 389|0x00000000b8500000, 0x00000000b8500000, 0x00000000b8600000|  0%| F|  |TAMS 0x00000000b8500000| PB 0x00000000b8500000| Untracked 
| 390|0x00000000b8600000, 0x00000000b8600000, 0x00000000b8700000|  0%| F|  |TAMS 0x00000000b8600000| PB 0x00000000b8600000| Untracked 
| 391|0x00000000b8700000, 0x00000000b8700000, 0x00000000b8800000|  0%| F|  |TAMS 0x00000000b8700000| PB 0x00000000b8700000| Untracked 
| 392|0x00000000b8800000, 0x00000000b8800000, 0x00000000b8900000|  0%| F|  |TAMS 0x00000000b8800000| PB 0x00000000b8800000| Untracked 
| 393|0x00000000b8900000, 0x00000000b8900000, 0x00000000b8a00000|  0%| F|  |TAMS 0x00000000b8900000| PB 0x00000000b8900000| Untracked 
| 394|0x00000000b8a00000, 0x00000000b8a00000, 0x00000000b8b00000|  0%| F|  |TAMS 0x00000000b8a00000| PB 0x00000000b8a00000| Untracked 
| 395|0x00000000b8b00000, 0x00000000b8b00000, 0x00000000b8c00000|  0%| F|  |TAMS 0x00000000b8b00000| PB 0x00000000b8b00000| Untracked 
| 396|0x00000000b8c00000, 0x00000000b8c80000, 0x00000000b8d00000| 50%| S|CS|TAMS 0x00000000b8c00000| PB 0x00000000b8c00000| Complete 
| 397|0x00000000b8d00000, 0x00000000b8e00000, 0x00000000b8e00000|100%| S|CS|TAMS 0x00000000b8d00000| PB 0x00000000b8d00000| Complete 
| 398|0x00000000b8e00000, 0x00000000b8f00000, 0x00000000b8f00000|100%| S|CS|TAMS 0x00000000b8e00000| PB 0x00000000b8e00000| Complete 
| 399|0x00000000b8f00000, 0x00000000b9000000, 0x00000000b9000000|100%| S|CS|TAMS 0x00000000b8f00000| PB 0x00000000b8f00000| Complete 
| 400|0x00000000b9000000, 0x00000000b9100000, 0x00000000b9100000|100%| S|CS|TAMS 0x00000000b9000000| PB 0x00000000b9000000| Complete 
| 401|0x00000000b9100000, 0x00000000b9200000, 0x00000000b9200000|100%| S|CS|TAMS 0x00000000b9100000| PB 0x00000000b9100000| Complete 
| 402|0x00000000b9200000, 0x00000000b9300000, 0x00000000b9300000|100%| S|CS|TAMS 0x00000000b9200000| PB 0x00000000b9200000| Complete 
| 403|0x00000000b9300000, 0x00000000b9400000, 0x00000000b9400000|100%| S|CS|TAMS 0x00000000b9300000| PB 0x00000000b9300000| Complete 
| 404|0x00000000b9400000, 0x00000000b9500000, 0x00000000b9500000|100%| S|CS|TAMS 0x00000000b9400000| PB 0x00000000b9400000| Complete 
| 405|0x00000000b9500000, 0x00000000b9600000, 0x00000000b9600000|100%| S|CS|TAMS 0x00000000b9500000| PB 0x00000000b9500000| Complete 
| 406|0x00000000b9600000, 0x00000000b9700000, 0x00000000b9700000|100%| S|CS|TAMS 0x00000000b9600000| PB 0x00000000b9600000| Complete 
| 407|0x00000000b9700000, 0x00000000b9800000, 0x00000000b9800000|100%| S|CS|TAMS 0x00000000b9700000| PB 0x00000000b9700000| Complete 
| 408|0x00000000b9800000, 0x00000000b9900000, 0x00000000b9900000|100%| S|CS|TAMS 0x00000000b9800000| PB 0x00000000b9800000| Complete 
| 409|0x00000000b9900000, 0x00000000b9a00000, 0x00000000b9a00000|100%| S|CS|TAMS 0x00000000b9900000| PB 0x00000000b9900000| Complete 
| 410|0x00000000b9a00000, 0x00000000b9b00000, 0x00000000b9b00000|100%| S|CS|TAMS 0x00000000b9a00000| PB 0x00000000b9a00000| Complete 
| 411|0x00000000b9b00000, 0x00000000b9c00000, 0x00000000b9c00000|100%| S|CS|TAMS 0x00000000b9b00000| PB 0x00000000b9b00000| Complete 
| 412|0x00000000b9c00000, 0x00000000b9d00000, 0x00000000b9d00000|100%| S|CS|TAMS 0x00000000b9c00000| PB 0x00000000b9c00000| Complete 
| 413|0x00000000b9d00000, 0x00000000b9e00000, 0x00000000b9e00000|100%| S|CS|TAMS 0x00000000b9d00000| PB 0x00000000b9d00000| Complete 
| 414|0x00000000b9e00000, 0x00000000b9f00000, 0x00000000b9f00000|100%| S|CS|TAMS 0x00000000b9e00000| PB 0x00000000b9e00000| Complete 
| 415|0x00000000b9f00000, 0x00000000ba000000, 0x00000000ba000000|100%| S|CS|TAMS 0x00000000b9f00000| PB 0x00000000b9f00000| Complete 
| 416|0x00000000ba000000, 0x00000000ba000000, 0x00000000ba100000|  0%| F|  |TAMS 0x00000000ba000000| PB 0x00000000ba000000| Untracked 
| 417|0x00000000ba100000, 0x00000000ba100000, 0x00000000ba200000|  0%| F|  |TAMS 0x00000000ba100000| PB 0x00000000ba100000| Untracked 
| 418|0x00000000ba200000, 0x00000000ba200000, 0x00000000ba300000|  0%| F|  |TAMS 0x00000000ba200000| PB 0x00000000ba200000| Untracked 
| 419|0x00000000ba300000, 0x00000000ba300000, 0x00000000ba400000|  0%| F|  |TAMS 0x00000000ba300000| PB 0x00000000ba300000| Untracked 
| 420|0x00000000ba400000, 0x00000000ba400000, 0x00000000ba500000|  0%| F|  |TAMS 0x00000000ba400000| PB 0x00000000ba400000| Untracked 
| 421|0x00000000ba500000, 0x00000000ba500000, 0x00000000ba600000|  0%| F|  |TAMS 0x00000000ba500000| PB 0x00000000ba500000| Untracked 
| 422|0x00000000ba600000, 0x00000000ba600000, 0x00000000ba700000|  0%| F|  |TAMS 0x00000000ba600000| PB 0x00000000ba600000| Untracked 
| 423|0x00000000ba700000, 0x00000000ba700000, 0x00000000ba800000|  0%| F|  |TAMS 0x00000000ba700000| PB 0x00000000ba700000| Untracked 
| 424|0x00000000ba800000, 0x00000000ba800000, 0x00000000ba900000|  0%| F|  |TAMS 0x00000000ba800000| PB 0x00000000ba800000| Untracked 
| 425|0x00000000ba900000, 0x00000000ba900000, 0x00000000baa00000|  0%| F|  |TAMS 0x00000000ba900000| PB 0x00000000ba900000| Untracked 
| 426|0x00000000baa00000, 0x00000000baa00000, 0x00000000bab00000|  0%| F|  |TAMS 0x00000000baa00000| PB 0x00000000baa00000| Untracked 
| 427|0x00000000bab00000, 0x00000000bab00000, 0x00000000bac00000|  0%| F|  |TAMS 0x00000000bab00000| PB 0x00000000bab00000| Untracked 
| 428|0x00000000bac00000, 0x00000000bac00000, 0x00000000bad00000|  0%| F|  |TAMS 0x00000000bac00000| PB 0x00000000bac00000| Untracked 
| 429|0x00000000bad00000, 0x00000000bad00000, 0x00000000bae00000|  0%| F|  |TAMS 0x00000000bad00000| PB 0x00000000bad00000| Untracked 
| 430|0x00000000bae00000, 0x00000000bae00000, 0x00000000baf00000|  0%| F|  |TAMS 0x00000000bae00000| PB 0x00000000bae00000| Untracked 
| 431|0x00000000baf00000, 0x00000000baf00000, 0x00000000bb000000|  0%| F|  |TAMS 0x00000000baf00000| PB 0x00000000baf00000| Untracked 
| 432|0x00000000bb000000, 0x00000000bb000000, 0x00000000bb100000|  0%| F|  |TAMS 0x00000000bb000000| PB 0x00000000bb000000| Untracked 
| 433|0x00000000bb100000, 0x00000000bb100000, 0x00000000bb200000|  0%| F|  |TAMS 0x00000000bb100000| PB 0x00000000bb100000| Untracked 
| 434|0x00000000bb200000, 0x00000000bb200000, 0x00000000bb300000|  0%| F|  |TAMS 0x00000000bb200000| PB 0x00000000bb200000| Untracked 
| 435|0x00000000bb300000, 0x00000000bb300000, 0x00000000bb400000|  0%| F|  |TAMS 0x00000000bb300000| PB 0x00000000bb300000| Untracked 
| 436|0x00000000bb400000, 0x00000000bb400000, 0x00000000bb500000|  0%| F|  |TAMS 0x00000000bb400000| PB 0x00000000bb400000| Untracked 
| 437|0x00000000bb500000, 0x00000000bb500000, 0x00000000bb600000|  0%| F|  |TAMS 0x00000000bb500000| PB 0x00000000bb500000| Untracked 
| 438|0x00000000bb600000, 0x00000000bb600000, 0x00000000bb700000|  0%| F|  |TAMS 0x00000000bb600000| PB 0x00000000bb600000| Untracked 
| 439|0x00000000bb700000, 0x00000000bb700000, 0x00000000bb800000|  0%| F|  |TAMS 0x00000000bb700000| PB 0x00000000bb700000| Untracked 
| 440|0x00000000bb800000, 0x00000000bb800000, 0x00000000bb900000|  0%| F|  |TAMS 0x00000000bb800000| PB 0x00000000bb800000| Untracked 
| 441|0x00000000bb900000, 0x00000000bb900000, 0x00000000bba00000|  0%| F|  |TAMS 0x00000000bb900000| PB 0x00000000bb900000| Untracked 
| 442|0x00000000bba00000, 0x00000000bba00000, 0x00000000bbb00000|  0%| F|  |TAMS 0x00000000bba00000| PB 0x00000000bba00000| Untracked 
| 443|0x00000000bbb00000, 0x00000000bbb00000, 0x00000000bbc00000|  0%| F|  |TAMS 0x00000000bbb00000| PB 0x00000000bbb00000| Untracked 
| 444|0x00000000bbc00000, 0x00000000bbc00000, 0x00000000bbd00000|  0%| F|  |TAMS 0x00000000bbc00000| PB 0x00000000bbc00000| Untracked 
| 445|0x00000000bbd00000, 0x00000000bbd00000, 0x00000000bbe00000|  0%| F|  |TAMS 0x00000000bbd00000| PB 0x00000000bbd00000| Untracked 
| 446|0x00000000bbe00000, 0x00000000bbe00000, 0x00000000bbf00000|  0%| F|  |TAMS 0x00000000bbe00000| PB 0x00000000bbe00000| Untracked 
| 447|0x00000000bbf00000, 0x00000000bbf00000, 0x00000000bc000000|  0%| F|  |TAMS 0x00000000bbf00000| PB 0x00000000bbf00000| Untracked 
| 448|0x00000000bc000000, 0x00000000bc000000, 0x00000000bc100000|  0%| F|  |TAMS 0x00000000bc000000| PB 0x00000000bc000000| Untracked 
| 449|0x00000000bc100000, 0x00000000bc100000, 0x00000000bc200000|  0%| F|  |TAMS 0x00000000bc100000| PB 0x00000000bc100000| Untracked 
| 450|0x00000000bc200000, 0x00000000bc200000, 0x00000000bc300000|  0%| F|  |TAMS 0x00000000bc200000| PB 0x00000000bc200000| Untracked 
| 451|0x00000000bc300000, 0x00000000bc300000, 0x00000000bc400000|  0%| F|  |TAMS 0x00000000bc300000| PB 0x00000000bc300000| Untracked 
| 452|0x00000000bc400000, 0x00000000bc400000, 0x00000000bc500000|  0%| F|  |TAMS 0x00000000bc400000| PB 0x00000000bc400000| Untracked 
| 453|0x00000000bc500000, 0x00000000bc500000, 0x00000000bc600000|  0%| F|  |TAMS 0x00000000bc500000| PB 0x00000000bc500000| Untracked 
| 454|0x00000000bc600000, 0x00000000bc600000, 0x00000000bc700000|  0%| F|  |TAMS 0x00000000bc600000| PB 0x00000000bc600000| Untracked 
| 455|0x00000000bc700000, 0x00000000bc700000, 0x00000000bc800000|  0%| F|  |TAMS 0x00000000bc700000| PB 0x00000000bc700000| Untracked 
| 456|0x00000000bc800000, 0x00000000bc800000, 0x00000000bc900000|  0%| F|  |TAMS 0x00000000bc800000| PB 0x00000000bc800000| Untracked 
| 457|0x00000000bc900000, 0x00000000bc900000, 0x00000000bca00000|  0%| F|  |TAMS 0x00000000bc900000| PB 0x00000000bc900000| Untracked 
| 458|0x00000000bca00000, 0x00000000bca00000, 0x00000000bcb00000|  0%| F|  |TAMS 0x00000000bca00000| PB 0x00000000bca00000| Untracked 
| 459|0x00000000bcb00000, 0x00000000bcb00000, 0x00000000bcc00000|  0%| F|  |TAMS 0x00000000bcb00000| PB 0x00000000bcb00000| Untracked 
| 460|0x00000000bcc00000, 0x00000000bcc00000, 0x00000000bcd00000|  0%| F|  |TAMS 0x00000000bcc00000| PB 0x00000000bcc00000| Untracked 
| 461|0x00000000bcd00000, 0x00000000bcd00000, 0x00000000bce00000|  0%| F|  |TAMS 0x00000000bcd00000| PB 0x00000000bcd00000| Untracked 
| 462|0x00000000bce00000, 0x00000000bce00000, 0x00000000bcf00000|  0%| F|  |TAMS 0x00000000bce00000| PB 0x00000000bce00000| Untracked 
| 463|0x00000000bcf00000, 0x00000000bcf00000, 0x00000000bd000000|  0%| F|  |TAMS 0x00000000bcf00000| PB 0x00000000bcf00000| Untracked 
| 464|0x00000000bd000000, 0x00000000bd000000, 0x00000000bd100000|  0%| F|  |TAMS 0x00000000bd000000| PB 0x00000000bd000000| Untracked 
| 465|0x00000000bd100000, 0x00000000bd100000, 0x00000000bd200000|  0%| F|  |TAMS 0x00000000bd100000| PB 0x00000000bd100000| Untracked 
| 466|0x00000000bd200000, 0x00000000bd200000, 0x00000000bd300000|  0%| F|  |TAMS 0x00000000bd200000| PB 0x00000000bd200000| Untracked 
| 467|0x00000000bd300000, 0x00000000bd300000, 0x00000000bd400000|  0%| F|  |TAMS 0x00000000bd300000| PB 0x00000000bd300000| Untracked 
| 468|0x00000000bd400000, 0x00000000bd400000, 0x00000000bd500000|  0%| F|  |TAMS 0x00000000bd400000| PB 0x00000000bd400000| Untracked 
| 469|0x00000000bd500000, 0x00000000bd500000, 0x00000000bd600000|  0%| F|  |TAMS 0x00000000bd500000| PB 0x00000000bd500000| Untracked 
| 470|0x00000000bd600000, 0x00000000bd600000, 0x00000000bd700000|  0%| F|  |TAMS 0x00000000bd600000| PB 0x00000000bd600000| Untracked 
| 471|0x00000000bd700000, 0x00000000bd700000, 0x00000000bd800000|  0%| F|  |TAMS 0x00000000bd700000| PB 0x00000000bd700000| Untracked 
| 472|0x00000000bd800000, 0x00000000bd800000, 0x00000000bd900000|  0%| F|  |TAMS 0x00000000bd800000| PB 0x00000000bd800000| Untracked 
| 473|0x00000000bd900000, 0x00000000bd900000, 0x00000000bda00000|  0%| F|  |TAMS 0x00000000bd900000| PB 0x00000000bd900000| Untracked 
| 474|0x00000000bda00000, 0x00000000bda00000, 0x00000000bdb00000|  0%| F|  |TAMS 0x00000000bda00000| PB 0x00000000bda00000| Untracked 
| 475|0x00000000bdb00000, 0x00000000bdb00000, 0x00000000bdc00000|  0%| F|  |TAMS 0x00000000bdb00000| PB 0x00000000bdb00000| Untracked 
| 476|0x00000000bdc00000, 0x00000000bdc00000, 0x00000000bdd00000|  0%| F|  |TAMS 0x00000000bdc00000| PB 0x00000000bdc00000| Untracked 
| 477|0x00000000bdd00000, 0x00000000bdd00000, 0x00000000bde00000|  0%| F|  |TAMS 0x00000000bdd00000| PB 0x00000000bdd00000| Untracked 
| 478|0x00000000bde00000, 0x00000000bde00000, 0x00000000bdf00000|  0%| F|  |TAMS 0x00000000bde00000| PB 0x00000000bde00000| Untracked 
| 479|0x00000000bdf00000, 0x00000000bdf00000, 0x00000000be000000|  0%| F|  |TAMS 0x00000000bdf00000| PB 0x00000000bdf00000| Untracked 
| 480|0x00000000be000000, 0x00000000be000000, 0x00000000be100000|  0%| F|  |TAMS 0x00000000be000000| PB 0x00000000be000000| Untracked 
| 481|0x00000000be100000, 0x00000000be100000, 0x00000000be200000|  0%| F|  |TAMS 0x00000000be100000| PB 0x00000000be100000| Untracked 
| 482|0x00000000be200000, 0x00000000be200000, 0x00000000be300000|  0%| F|  |TAMS 0x00000000be200000| PB 0x00000000be200000| Untracked 
| 483|0x00000000be300000, 0x00000000be300000, 0x00000000be400000|  0%| F|  |TAMS 0x00000000be300000| PB 0x00000000be300000| Untracked 
| 484|0x00000000be400000, 0x00000000be400000, 0x00000000be500000|  0%| F|  |TAMS 0x00000000be400000| PB 0x00000000be400000| Untracked 
| 485|0x00000000be500000, 0x00000000be500000, 0x00000000be600000|  0%| F|  |TAMS 0x00000000be500000| PB 0x00000000be500000| Untracked 
| 486|0x00000000be600000, 0x00000000be600000, 0x00000000be700000|  0%| F|  |TAMS 0x00000000be600000| PB 0x00000000be600000| Untracked 
| 487|0x00000000be700000, 0x00000000be700000, 0x00000000be800000|  0%| F|  |TAMS 0x00000000be700000| PB 0x00000000be700000| Untracked 
| 488|0x00000000be800000, 0x00000000be800000, 0x00000000be900000|  0%| F|  |TAMS 0x00000000be800000| PB 0x00000000be800000| Untracked 
| 489|0x00000000be900000, 0x00000000be900000, 0x00000000bea00000|  0%| F|  |TAMS 0x00000000be900000| PB 0x00000000be900000| Untracked 
| 490|0x00000000bea00000, 0x00000000bea00000, 0x00000000beb00000|  0%| F|  |TAMS 0x00000000bea00000| PB 0x00000000bea00000| Untracked 
| 491|0x00000000beb00000, 0x00000000beb00000, 0x00000000bec00000|  0%| F|  |TAMS 0x00000000beb00000| PB 0x00000000beb00000| Untracked 
| 492|0x00000000bec00000, 0x00000000bec00000, 0x00000000bed00000|  0%| F|  |TAMS 0x00000000bec00000| PB 0x00000000bec00000| Untracked 
| 493|0x00000000bed00000, 0x00000000bed00000, 0x00000000bee00000|  0%| F|  |TAMS 0x00000000bed00000| PB 0x00000000bed00000| Untracked 
| 494|0x00000000bee00000, 0x00000000bee00000, 0x00000000bef00000|  0%| F|  |TAMS 0x00000000bee00000| PB 0x00000000bee00000| Untracked 
| 495|0x00000000bef00000, 0x00000000bef00000, 0x00000000bf000000|  0%| F|  |TAMS 0x00000000bef00000| PB 0x00000000bef00000| Untracked 
| 496|0x00000000bf000000, 0x00000000bf000000, 0x00000000bf100000|  0%| F|  |TAMS 0x00000000bf000000| PB 0x00000000bf000000| Untracked 
| 497|0x00000000bf100000, 0x00000000bf100000, 0x00000000bf200000|  0%| F|  |TAMS 0x00000000bf100000| PB 0x00000000bf100000| Untracked 
| 498|0x00000000bf200000, 0x00000000bf200000, 0x00000000bf300000|  0%| F|  |TAMS 0x00000000bf200000| PB 0x00000000bf200000| Untracked 
| 499|0x00000000bf300000, 0x00000000bf300000, 0x00000000bf400000|  0%| F|  |TAMS 0x00000000bf300000| PB 0x00000000bf300000| Untracked 
| 500|0x00000000bf400000, 0x00000000bf400000, 0x00000000bf500000|  0%| F|  |TAMS 0x00000000bf400000| PB 0x00000000bf400000| Untracked 
| 501|0x00000000bf500000, 0x00000000bf500000, 0x00000000bf600000|  0%| F|  |TAMS 0x00000000bf500000| PB 0x00000000bf500000| Untracked 
| 502|0x00000000bf600000, 0x00000000bf673bb0, 0x00000000bf700000| 45%| E|  |TAMS 0x00000000bf600000| PB 0x00000000bf600000| Complete 
| 503|0x00000000bf700000, 0x00000000bf800000, 0x00000000bf800000|100%| E|CS|TAMS 0x00000000bf700000| PB 0x00000000bf700000| Complete 
| 504|0x00000000bf800000, 0x00000000bf900000, 0x00000000bf900000|100%| E|CS|TAMS 0x00000000bf800000| PB 0x00000000bf800000| Complete 
| 505|0x00000000bf900000, 0x00000000bfa00000, 0x00000000bfa00000|100%| E|CS|TAMS 0x00000000bf900000| PB 0x00000000bf900000| Complete 
| 506|0x00000000bfa00000, 0x00000000bfb00000, 0x00000000bfb00000|100%| E|CS|TAMS 0x00000000bfa00000| PB 0x00000000bfa00000| Complete 
| 507|0x00000000bfb00000, 0x00000000bfc00000, 0x00000000bfc00000|100%| E|CS|TAMS 0x00000000bfb00000| PB 0x00000000bfb00000| Complete 
| 508|0x00000000bfc00000, 0x00000000bfd00000, 0x00000000bfd00000|100%| E|CS|TAMS 0x00000000bfc00000| PB 0x00000000bfc00000| Complete 
| 509|0x00000000bfd00000, 0x00000000bfe00000, 0x00000000bfe00000|100%| E|CS|TAMS 0x00000000bfd00000| PB 0x00000000bfd00000| Complete 
| 510|0x00000000bfe00000, 0x00000000bff00000, 0x00000000bff00000|100%| E|CS|TAMS 0x00000000bfe00000| PB 0x00000000bfe00000| Complete 
| 511|0x00000000bff00000, 0x00000000c0000000, 0x00000000c0000000|100%| E|CS|TAMS 0x00000000bff00000| PB 0x00000000bff00000| Complete 
| 512|0x00000000c0000000, 0x00000000c0100000, 0x00000000c0100000|100%| E|CS|TAMS 0x00000000c0000000| PB 0x00000000c0000000| Complete 
| 513|0x00000000c0100000, 0x00000000c0200000, 0x00000000c0200000|100%| E|CS|TAMS 0x00000000c0100000| PB 0x00000000c0100000| Complete 
| 514|0x00000000c0200000, 0x00000000c0300000, 0x00000000c0300000|100%| E|CS|TAMS 0x00000000c0200000| PB 0x00000000c0200000| Complete 
| 515|0x00000000c0300000, 0x00000000c0400000, 0x00000000c0400000|100%| E|CS|TAMS 0x00000000c0300000| PB 0x00000000c0300000| Complete 
| 516|0x00000000c0400000, 0x00000000c0500000, 0x00000000c0500000|100%| E|CS|TAMS 0x00000000c0400000| PB 0x00000000c0400000| Complete 
| 517|0x00000000c0500000, 0x00000000c0600000, 0x00000000c0600000|100%| E|CS|TAMS 0x00000000c0500000| PB 0x00000000c0500000| Complete 
| 518|0x00000000c0600000, 0x00000000c0700000, 0x00000000c0700000|100%| E|CS|TAMS 0x00000000c0600000| PB 0x00000000c0600000| Complete 
| 519|0x00000000c0700000, 0x00000000c0800000, 0x00000000c0800000|100%| E|CS|TAMS 0x00000000c0700000| PB 0x00000000c0700000| Complete 
| 520|0x00000000c0800000, 0x00000000c0900000, 0x00000000c0900000|100%| E|CS|TAMS 0x00000000c0800000| PB 0x00000000c0800000| Complete 
| 521|0x00000000c0900000, 0x00000000c0a00000, 0x00000000c0a00000|100%| E|CS|TAMS 0x00000000c0900000| PB 0x00000000c0900000| Complete 
| 522|0x00000000c0a00000, 0x00000000c0b00000, 0x00000000c0b00000|100%| E|CS|TAMS 0x00000000c0a00000| PB 0x00000000c0a00000| Complete 
| 523|0x00000000c0b00000, 0x00000000c0c00000, 0x00000000c0c00000|100%| E|CS|TAMS 0x00000000c0b00000| PB 0x00000000c0b00000| Complete 
| 524|0x00000000c0c00000, 0x00000000c0d00000, 0x00000000c0d00000|100%| E|CS|TAMS 0x00000000c0c00000| PB 0x00000000c0c00000| Complete 
| 525|0x00000000c0d00000, 0x00000000c0e00000, 0x00000000c0e00000|100%| E|CS|TAMS 0x00000000c0d00000| PB 0x00000000c0d00000| Complete 
| 526|0x00000000c0e00000, 0x00000000c0f00000, 0x00000000c0f00000|100%| E|CS|TAMS 0x00000000c0e00000| PB 0x00000000c0e00000| Complete 
| 527|0x00000000c0f00000, 0x00000000c1000000, 0x00000000c1000000|100%| E|CS|TAMS 0x00000000c0f00000| PB 0x00000000c0f00000| Complete 
| 528|0x00000000c1000000, 0x00000000c1100000, 0x00000000c1100000|100%| E|CS|TAMS 0x00000000c1000000| PB 0x00000000c1000000| Complete 
| 529|0x00000000c1100000, 0x00000000c1200000, 0x00000000c1200000|100%| E|CS|TAMS 0x00000000c1100000| PB 0x00000000c1100000| Complete 
| 530|0x00000000c1200000, 0x00000000c1300000, 0x00000000c1300000|100%| E|CS|TAMS 0x00000000c1200000| PB 0x00000000c1200000| Complete 
| 531|0x00000000c1300000, 0x00000000c1400000, 0x00000000c1400000|100%| E|CS|TAMS 0x00000000c1300000| PB 0x00000000c1300000| Complete 
| 532|0x00000000c1400000, 0x00000000c1500000, 0x00000000c1500000|100%| E|CS|TAMS 0x00000000c1400000| PB 0x00000000c1400000| Complete 
| 533|0x00000000c1500000, 0x00000000c1600000, 0x00000000c1600000|100%| E|CS|TAMS 0x00000000c1500000| PB 0x00000000c1500000| Complete 
| 534|0x00000000c1600000, 0x00000000c1700000, 0x00000000c1700000|100%| E|CS|TAMS 0x00000000c1600000| PB 0x00000000c1600000| Complete 
| 535|0x00000000c1700000, 0x00000000c1800000, 0x00000000c1800000|100%| E|CS|TAMS 0x00000000c1700000| PB 0x00000000c1700000| Complete 
| 536|0x00000000c1800000, 0x00000000c1900000, 0x00000000c1900000|100%| E|CS|TAMS 0x00000000c1800000| PB 0x00000000c1800000| Complete 
| 537|0x00000000c1900000, 0x00000000c1a00000, 0x00000000c1a00000|100%| E|CS|TAMS 0x00000000c1900000| PB 0x00000000c1900000| Complete 
| 538|0x00000000c1a00000, 0x00000000c1b00000, 0x00000000c1b00000|100%| E|CS|TAMS 0x00000000c1a00000| PB 0x00000000c1a00000| Complete 
| 539|0x00000000c1b00000, 0x00000000c1c00000, 0x00000000c1c00000|100%| E|CS|TAMS 0x00000000c1b00000| PB 0x00000000c1b00000| Complete 
| 540|0x00000000c1c00000, 0x00000000c1d00000, 0x00000000c1d00000|100%| E|CS|TAMS 0x00000000c1c00000| PB 0x00000000c1c00000| Complete 
| 541|0x00000000c1d00000, 0x00000000c1e00000, 0x00000000c1e00000|100%| E|CS|TAMS 0x00000000c1d00000| PB 0x00000000c1d00000| Complete 
| 542|0x00000000c1e00000, 0x00000000c1f00000, 0x00000000c1f00000|100%| E|CS|TAMS 0x00000000c1e00000| PB 0x00000000c1e00000| Complete 
| 543|0x00000000c1f00000, 0x00000000c2000000, 0x00000000c2000000|100%| E|CS|TAMS 0x00000000c1f00000| PB 0x00000000c1f00000| Complete 
| 544|0x00000000c2000000, 0x00000000c2100000, 0x00000000c2100000|100%| E|CS|TAMS 0x00000000c2000000| PB 0x00000000c2000000| Complete 
|1535|0x00000000fff00000, 0x0000000100000000, 0x0000000100000000|100%| E|CS|TAMS 0x00000000fff00000| PB 0x00000000fff00000| Complete 

Card table byte_map: [0x0000021f4bcf0000,0x0000021f4bff0000] _byte_map_base: 0x0000021f4b7f0000

Marking Bits: (CMBitMap*) 0x0000021f37aea270
 Bits: [0x0000021f4bff0000, 0x0000021f4d7f0000)

Polling page: 0x0000021f37930000

Metaspace:

Usage:
  Non-class:    167.60 MB used.
      Class:     23.41 MB used.
       Both:    191.01 MB used.

Virtual space:
  Non-class space:      192.00 MB reserved,     168.94 MB ( 88%) committed,  3 nodes.
      Class space:      416.00 MB reserved,      24.69 MB (  6%) committed,  1 nodes.
             Both:      608.00 MB reserved,     193.62 MB ( 32%) committed. 

Chunk freelists:
   Non-Class:  7.00 MB
       Class:  7.20 MB
        Both:  14.20 MB

MaxMetaspaceSize: 512.00 MB
CompressedClassSpaceSize: 416.00 MB
Initial GC threshold: 21.00 MB
Current GC threshold: 321.88 MB
CDS: off
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 12.
num_arena_births: 4896.
num_arena_deaths: 6.
num_vsnodes_births: 4.
num_vsnodes_deaths: 0.
num_space_committed: 3096.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 19.
num_chunks_taken_from_freelist: 12364.
num_chunk_merges: 12.
num_chunk_splits: 8795.
num_chunks_enlarged: 6323.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=18220Kb max_used=18448Kb free=101779Kb
 bounds [0x0000021f43ea0000, 0x0000021f450b0000, 0x0000021f4b3d0000]
CodeHeap 'profiled nmethods': size=120000Kb used=50344Kb max_used=52023Kb free=69655Kb
 bounds [0x0000021f3c3d0000, 0x0000021f3f6d0000, 0x0000021f43900000]
CodeHeap 'non-nmethods': size=5760Kb used=3226Kb max_used=3292Kb free=2533Kb
 bounds [0x0000021f43900000, 0x0000021f43c50000, 0x0000021f43ea0000]
 total_blobs=22585 nmethods=21337 adapters=1147
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 165.734 Thread 0x0000021f5407afd0 nmethod 34781 0x0000021f44f12390 code [0x0000021f44f12540, 0x0000021f44f12898]
Event: 165.734 Thread 0x0000021f5407afd0 34785       4       sun.security.ec.ECOperations::setSum (685 bytes)
Event: 165.791 Thread 0x0000021f5407d210 34788       3       java.math.MutableBigInteger::divideKnuth (8 bytes)
Event: 165.791 Thread 0x0000021f5407d210 nmethod 34788 0x0000021f3db5bd90 code [0x0000021f3db5bf40, 0x0000021f3db5c088]
Event: 165.791 Thread 0x0000021f5407d210 34789       3       javax.crypto.Mac::getInstance (96 bytes)
Event: 165.792 Thread 0x0000021f5407d210 nmethod 34789 0x0000021f3f145f90 code [0x0000021f3f1462e0, 0x0000021f3f147428]
Event: 165.792 Thread 0x0000021f5407d210 34792   !   3       sun.security.ssl.SSLSocketInputRecord::readFully (75 bytes)
Event: 165.792 Thread 0x0000021f5407d210 nmethod 34792 0x0000021f3f145310 code [0x0000021f3f145540, 0x0000021f3f145bc8]
Event: 165.792 Thread 0x0000021f5407d210 34791   !   3       sun.security.ssl.SSLSocketInputRecord::decodeInputRecord (949 bytes)
Event: 165.796 Thread 0x0000021f5407d210 nmethod 34791 0x0000021f3f172f90 code [0x0000021f3f1738e0, 0x0000021f3f178c30]
Event: 165.796 Thread 0x0000021f5407d210 34794   !   3       sun.security.provider.certpath.AdaptableX509CertSelector::match (103 bytes)
Event: 165.797 Thread 0x0000021f5407d210 nmethod 34794 0x0000021f3f144390 code [0x0000021f3f144600, 0x0000021f3f144f50]
Event: 165.797 Thread 0x0000021f5407d210 34795   !   3       sun.security.provider.certpath.AdaptableX509CertSelector::matchSubjectKeyID (184 bytes)
Event: 165.797 Thread 0x0000021f5407d210 nmethod 34795 0x0000021f3df8bc90 code [0x0000021f3df8bee0, 0x0000021f3df8c5c0]
Event: 165.797 Thread 0x0000021f5407d210 34790       3       javax.crypto.Mac::<init> (36 bytes)
Event: 165.797 Thread 0x0000021f5407d210 nmethod 34790 0x0000021f3df8b610 code [0x0000021f3df8b7c0, 0x0000021f3df8bb48]
Event: 165.863 Thread 0x0000021f5407d210 34797       3       sun.net.spi.DefaultProxySelector::select (280 bytes)
Event: 165.865 Thread 0x0000021f5407d210 nmethod 34797 0x0000021f3f14e790 code [0x0000021f3f14ecc0, 0x0000021f3f150e40]
Event: 165.866 Thread 0x0000021f5407d210 34798       3       sun.net.spi.DefaultProxySelector$3::<init> (26 bytes)
Event: 165.866 Thread 0x0000021f5407d210 nmethod 34798 0x0000021f3f14e010 code [0x0000021f3f14e1c0, 0x0000021f3f14e498]

GC Heap History (20 events):
Event: 146.347 GC heap before
{Heap before GC invocations=186 (full 0):
 garbage-first heap   total 559104K, used 500736K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 319 young (326656K), 8 survivors (8192K)
 Metaspace       used 193635K, committed 196288K, reserved 622592K
  class space    used 23764K, committed 25088K, reserved 425984K
}
Event: 146.356 GC heap after
{Heap after GC invocations=187 (full 0):
 garbage-first heap   total 559104K, used 183808K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 9 young (9216K), 9 survivors (9216K)
 Metaspace       used 193635K, committed 196288K, reserved 622592K
  class space    used 23764K, committed 25088K, reserved 425984K
}
Event: 149.330 GC heap before
{Heap before GC invocations=188 (full 0):
 garbage-first heap   total 559104K, used 508416K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 326 young (333824K), 9 survivors (9216K)
 Metaspace       used 193694K, committed 196288K, reserved 622592K
  class space    used 23767K, committed 25088K, reserved 425984K
}
Event: 149.347 GC heap after
{Heap after GC invocations=189 (full 0):
 garbage-first heap   total 559104K, used 186880K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 12 young (12288K), 12 survivors (12288K)
 Metaspace       used 193694K, committed 196288K, reserved 622592K
  class space    used 23767K, committed 25088K, reserved 425984K
}
Event: 150.241 GC heap before
{Heap before GC invocations=189 (full 0):
 garbage-first heap   total 559104K, used 509440K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 327 young (334848K), 12 survivors (12288K)
 Metaspace       used 193694K, committed 196288K, reserved 622592K
  class space    used 23767K, committed 25088K, reserved 425984K
}
Event: 150.259 GC heap after
{Heap after GC invocations=190 (full 0):
 garbage-first heap   total 559104K, used 187260K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 17 young (17408K), 17 survivors (17408K)
 Metaspace       used 193694K, committed 196288K, reserved 622592K
  class space    used 23767K, committed 25088K, reserved 425984K
}
Event: 150.916 GC heap before
{Heap before GC invocations=190 (full 0):
 garbage-first heap   total 559104K, used 504700K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 327 young (334848K), 17 survivors (17408K)
 Metaspace       used 193696K, committed 196288K, reserved 622592K
  class space    used 23767K, committed 25088K, reserved 425984K
}
Event: 150.929 GC heap after
{Heap after GC invocations=191 (full 0):
 garbage-first heap   total 559104K, used 190332K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 20 young (20480K), 20 survivors (20480K)
 Metaspace       used 193696K, committed 196288K, reserved 622592K
  class space    used 23767K, committed 25088K, reserved 425984K
}
Event: 151.525 GC heap before
{Heap before GC invocations=191 (full 0):
 garbage-first heap   total 559104K, used 504700K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 327 young (334848K), 20 survivors (20480K)
 Metaspace       used 193696K, committed 196288K, reserved 622592K
  class space    used 23767K, committed 25088K, reserved 425984K
}
Event: 151.541 GC heap after
{Heap after GC invocations=192 (full 0):
 garbage-first heap   total 559104K, used 195452K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 25 young (25600K), 25 survivors (25600K)
 Metaspace       used 193696K, committed 196288K, reserved 622592K
  class space    used 23767K, committed 25088K, reserved 425984K
}
Event: 152.172 GC heap before
{Heap before GC invocations=192 (full 0):
 garbage-first heap   total 559104K, used 503676K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 326 young (333824K), 25 survivors (25600K)
 Metaspace       used 193696K, committed 196288K, reserved 622592K
  class space    used 23767K, committed 25088K, reserved 425984K
}
Event: 152.191 GC heap after
{Heap after GC invocations=193 (full 0):
 garbage-first heap   total 559104K, used 198791K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 23 young (23552K), 23 survivors (23552K)
 Metaspace       used 193696K, committed 196288K, reserved 622592K
  class space    used 23767K, committed 25088K, reserved 425984K
}
Event: 152.964 GC heap before
{Heap before GC invocations=193 (full 0):
 garbage-first heap   total 559104K, used 505991K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 323 young (330752K), 23 survivors (23552K)
 Metaspace       used 193713K, committed 196352K, reserved 622592K
  class space    used 23767K, committed 25088K, reserved 425984K
}
Event: 152.985 GC heap after
{Heap after GC invocations=194 (full 0):
 garbage-first heap   total 559104K, used 196281K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 24 young (24576K), 24 survivors (24576K)
 Metaspace       used 193713K, committed 196352K, reserved 622592K
  class space    used 23767K, committed 25088K, reserved 425984K
}
Event: 154.679 GC heap before
{Heap before GC invocations=195 (full 0):
 garbage-first heap   total 559104K, used 500409K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 326 young (333824K), 24 survivors (24576K)
 Metaspace       used 193713K, committed 196352K, reserved 622592K
  class space    used 23767K, committed 25088K, reserved 425984K
}
Event: 154.695 GC heap after
{Heap after GC invocations=196 (full 0):
 garbage-first heap   total 559104K, used 193162K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 22 young (22528K), 22 survivors (22528K)
 Metaspace       used 193713K, committed 196352K, reserved 622592K
  class space    used 23767K, committed 25088K, reserved 425984K
}
Event: 163.725 GC heap before
{Heap before GC invocations=196 (full 0):
 garbage-first heap   total 559104K, used 507530K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 327 young (334848K), 22 survivors (22528K)
 Metaspace       used 194085K, committed 196672K, reserved 622592K
  class space    used 23782K, committed 25088K, reserved 425984K
}
Event: 163.744 GC heap after
{Heap after GC invocations=197 (full 0):
 garbage-first heap   total 559104K, used 191141K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 20 young (20480K), 20 survivors (20480K)
 Metaspace       used 194085K, committed 196672K, reserved 622592K
  class space    used 23782K, committed 25088K, reserved 425984K
}
Event: 164.808 GC heap before
{Heap before GC invocations=197 (full 0):
 garbage-first heap   total 559104K, used 322213K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 150 young (153600K), 20 survivors (20480K)
 Metaspace       used 194467K, committed 197120K, reserved 622592K
  class space    used 23830K, committed 25152K, reserved 425984K
}
Event: 164.821 GC heap after
{Heap after GC invocations=198 (full 0):
 garbage-first heap   total 559104K, used 191141K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 20 young (20480K), 20 survivors (20480K)
 Metaspace       used 194467K, committed 197120K, reserved 622592K
  class space    used 23830K, committed 25152K, reserved 425984K
}

Dll operation events (3 events):
Event: 0.010 Loaded shared library C:\Program Files\Android\Android Studio\jbr\bin\java.dll
Event: 0.017 Loaded shared library C:\Program Files\Android\Android Studio\jbr\bin\zip.dll
Event: 0.691 Loaded shared library C:\Program Files\Android\Android Studio\jbr\bin\verify.dll

Deoptimization events (20 events):
Event: 165.597 Thread 0x0000021f57f55010 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000021f44db2eec relative=0x000000000000084c
Event: 165.597 Thread 0x0000021f57f55010 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000021f44db2eec method=org.gradle.cache.internal.filelock.LockStateAccess.writeState(Ljava/io/RandomAccessFile;Lorg/gradle/cache/internal/filelock/LockState;)V @ 24 c2
Event: 165.597 Thread 0x0000021f57f55010 DEOPT PACKING pc=0x0000021f44db2eec sp=0x00000068214fe650
Event: 165.597 Thread 0x0000021f57f55010 DEOPT UNPACKING pc=0x0000021f439546a2 sp=0x00000068214fe638 mode 2
Event: 165.597 Thread 0x0000021f57f55010 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000021f44f01a8c relative=0x000000000000054c
Event: 165.597 Thread 0x0000021f57f55010 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000021f44f01a8c method=java.io.DataOutputStream.writeBoolean(Z)V @ 13 c2
Event: 165.597 Thread 0x0000021f57f55010 DEOPT PACKING pc=0x0000021f44f01a8c sp=0x00000068214fe570
Event: 165.597 Thread 0x0000021f57f55010 DEOPT UNPACKING pc=0x0000021f439546a2 sp=0x00000068214fe558 mode 2
Event: 165.600 Thread 0x0000021f57f55010 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000021f44917fa0 relative=0x0000000000006300
Event: 165.600 Thread 0x0000021f57f55010 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000021f44917fa0 method=org.gradle.internal.classpath.transforms.BaseClasspathElementTransform.processClassFile(Lorg/gradle/internal/classpath/ClasspathBuilder$EntryBuilder;Lorg/gradle/internal
Event: 165.600 Thread 0x0000021f57f55010 DEOPT PACKING pc=0x0000021f44917fa0 sp=0x00000068214fe3c0
Event: 165.600 Thread 0x0000021f57f55010 DEOPT UNPACKING pc=0x0000021f439546a2 sp=0x00000068214fe298 mode 2
Event: 165.602 Thread 0x0000021f57f55010 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000021f44917fa0 relative=0x0000000000006300
Event: 165.602 Thread 0x0000021f57f55010 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000021f44917fa0 method=org.gradle.internal.classpath.transforms.BaseClasspathElementTransform.processClassFile(Lorg/gradle/internal/classpath/ClasspathBuilder$EntryBuilder;Lorg/gradle/internal
Event: 165.602 Thread 0x0000021f57f55010 DEOPT PACKING pc=0x0000021f44917fa0 sp=0x00000068214fe3c0
Event: 165.602 Thread 0x0000021f57f55010 DEOPT UNPACKING pc=0x0000021f439546a2 sp=0x00000068214fe298 mode 2
Event: 165.712 Thread 0x0000021f561ff490 Uncommon trap: trap_request=0xfffffff4 fr.pc=0x0000021f4507bed4 relative=0x0000000000000b94
Event: 165.712 Thread 0x0000021f561ff490 Uncommon trap: reason=null_check action=make_not_entrant pc=0x0000021f4507bed4 method=org.gradle.tooling.internal.provider.runner.ClientBuildEventGenerator$Enabled.accept(Lorg/gradle/internal/operations/BuildOperationDescriptor;)Lorg/gradle/tooling/intern
Event: 165.712 Thread 0x0000021f561ff490 DEOPT PACKING pc=0x0000021f4507bed4 sp=0x000000681e5f7140
Event: 165.712 Thread 0x0000021f561ff490 DEOPT UNPACKING pc=0x0000021f439546a2 sp=0x000000681e5f7078 mode 2

Classes loaded (20 events):
Event: 160.355 Loading class java/util/concurrent/ConcurrentLinkedQueue$Itr
Event: 160.356 Loading class java/util/concurrent/ConcurrentLinkedQueue$Itr done
Event: 164.885 Loading class java/util/Currency
Event: 164.886 Loading class java/util/Currency done
Event: 164.891 Loading class java/util/concurrent/atomic/AtomicLongArray
Event: 164.891 Loading class java/util/concurrent/atomic/AtomicLongArray done
Event: 164.965 Loading class java/util/AbstractMap$2
Event: 164.965 Loading class java/util/AbstractMap$2 done
Event: 164.965 Loading class java/util/AbstractMap$2$1
Event: 164.965 Loading class java/util/AbstractMap$2$1 done
Event: 165.527 Loading class jdk/internal/math/FloatToDecimal
Event: 165.527 Loading class jdk/internal/math/FloatToDecimal done
Event: 165.658 Loading class java/util/AbstractCollectionBeanInfo
Event: 165.658 Loading class java/util/AbstractCollectionBeanInfo done
Event: 165.658 Loading class java/util/AbstractCollectionBeanInfo
Event: 165.658 Loading class java/util/AbstractCollectionBeanInfo done
Event: 165.658 Loading class java/util/AbstractCollectionCustomizer
Event: 165.658 Loading class java/util/AbstractCollectionCustomizer done
Event: 165.658 Loading class java/util/AbstractCollectionCustomizer
Event: 165.658 Loading class java/util/AbstractCollectionCustomizer done

Classes unloaded (5 events):
Event: 9.332 Thread 0x0000021f4fe5e8a0 Unloading class 0x0000000100754948 '_BuildScript_$_run_closure2'
Event: 9.332 Thread 0x0000021f4fe5e8a0 Unloading class 0x0000000100754548 '_BuildScript_$_run_closure1'
Event: 9.332 Thread 0x0000021f4fe5e8a0 Unloading class 0x0000000100754000 '_BuildScript_'
Event: 147.038 Thread 0x0000021f4fe5e8a0 Unloading class 0x0000000101778800 'java/lang/invoke/LambdaForm$DMH+0x0000000101778800'
Event: 147.038 Thread 0x0000021f4fe5e8a0 Unloading class 0x0000000101778400 'java/lang/invoke/LambdaForm$DMH+0x0000000101778400'

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 165.637 Thread 0x0000021f561ff490 Exception <a 'java/lang/ClassNotFoundException'{0x00000000c02c3420}: org/gradle/api/internal/initialization/DefaultScriptHandler_DecoratedCustomizer> (0x00000000c02c3420) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 165.654 Thread 0x0000021f561ff490 Exception <a 'java/lang/ClassNotFoundException'{0x00000000bfd44680}: org/gradle/api/internal/artifacts/dsl/DefaultRepositoryHandler_DecoratedBeanInfo> (0x00000000bfd44680) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 165.655 Thread 0x0000021f561ff490 Exception <a 'java/lang/ClassNotFoundException'{0x00000000bfd5b930}: org/gradle/api/internal/artifacts/dsl/DefaultRepositoryHandlerBeanInfo> (0x00000000bfd5b930) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 165.656 Thread 0x0000021f561ff490 Exception <a 'java/lang/ClassNotFoundException'{0x00000000bfd72e10}: org/gradle/api/internal/artifacts/DefaultArtifactRepositoryContainerBeanInfo> (0x00000000bfd72e10) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 165.656 Thread 0x0000021f561ff490 Exception <a 'java/lang/ClassNotFoundException'{0x00000000bfd87898}: org/gradle/api/internal/DefaultNamedDomainObjectListBeanInfo> (0x00000000bfd87898) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 165.657 Thread 0x0000021f561ff490 Exception <a 'java/lang/ClassNotFoundException'{0x00000000bfd9c268}: org/gradle/api/internal/DefaultNamedDomainObjectCollectionBeanInfo> (0x00000000bfd9c268) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 165.658 Thread 0x0000021f561ff490 Exception <a 'java/lang/ClassNotFoundException'{0x00000000bfdb1518}: org/gradle/api/internal/DefaultDomainObjectCollectionBeanInfo> (0x00000000bfdb1518) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 165.658 Thread 0x0000021f561ff490 Exception <a 'java/lang/ClassNotFoundException'{0x00000000bfdb9c28}: java/util/AbstractCollectionBeanInfo> (0x00000000bfdb9c28) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 165.658 Thread 0x0000021f561ff490 Exception <a 'java/lang/ClassNotFoundException'{0x00000000bfdc1da0}: java/util/AbstractCollectionCustomizer> (0x00000000bfdc1da0) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 165.661 Thread 0x0000021f561ff490 Exception <a 'java/lang/ClassNotFoundException'{0x00000000bfde6758}: org/gradle/api/internal/DefaultDomainObjectCollectionCustomizer> (0x00000000bfde6758) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 165.664 Thread 0x0000021f561ff490 Exception <a 'java/lang/ClassNotFoundException'{0x00000000bfc22ca8}: org/gradle/api/internal/DefaultNamedDomainObjectCollectionCustomizer> (0x00000000bfc22ca8) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 165.667 Thread 0x0000021f561ff490 Exception <a 'java/lang/ClassNotFoundException'{0x00000000bfc6d300}: org/gradle/api/internal/DefaultNamedDomainObjectListCustomizer> (0x00000000bfc6d300) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 165.671 Thread 0x0000021f561ff490 Exception <a 'java/lang/ClassNotFoundException'{0x00000000bfcba280}: org/gradle/api/internal/artifacts/DefaultArtifactRepositoryContainerCustomizer> (0x00000000bfcba280) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 165.673 Thread 0x0000021f561ff490 Exception <a 'java/lang/ClassNotFoundException'{0x00000000bfb035a8}: org/gradle/api/internal/artifacts/dsl/DefaultRepositoryHandlerCustomizer> (0x00000000bfb035a8) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 165.676 Thread 0x0000021f561ff490 Exception <a 'java/lang/ClassNotFoundException'{0x00000000bfb5b288}: org/gradle/api/internal/artifacts/dsl/DefaultRepositoryHandler_DecoratedCustomizer> (0x00000000bfb5b288) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 165.688 Thread 0x0000021f561ff490 Exception <a 'java/lang/ClassNotFoundException'{0x00000000bfa073f0}: org/gradle/api/internal/artifacts/dsl/dependencies/DefaultDependencyHandler_DecoratedBeanInfo> (0x00000000bfa073f0) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 165.689 Thread 0x0000021f561ff490 Exception <a 'java/lang/ClassNotFoundException'{0x00000000bfa1ec00}: org/gradle/api/internal/artifacts/dsl/dependencies/DefaultDependencyHandlerBeanInfo> (0x00000000bfa1ec00) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 165.690 Thread 0x0000021f561ff490 Exception <a 'java/lang/ClassNotFoundException'{0x00000000bfa363d8}: org/gradle/api/internal/artifacts/dsl/dependencies/DefaultDependencyHandlerCustomizer> (0x00000000bfa363d8) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 165.692 Thread 0x0000021f561ff490 Exception <a 'java/lang/ClassNotFoundException'{0x00000000bfa811d8}: org/gradle/api/internal/artifacts/dsl/dependencies/DefaultDependencyHandler_DecoratedCustomizer> (0x00000000bfa811d8) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 165.712 Thread 0x0000021f561ff490 Implicit null exception at 0x0000021f4507b669 to 0x0000021f4507beb8

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 161.698 Executing VM operation: Cleanup
Event: 161.698 Executing VM operation: Cleanup done
Event: 162.658 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 162.658 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 163.658 Executing VM operation: Cleanup
Event: 163.663 Executing VM operation: Cleanup done
Event: 163.717 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause)
Event: 163.744 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause) done
Event: 164.745 Executing VM operation: ICBufferFull
Event: 164.746 Executing VM operation: ICBufferFull done
Event: 164.808 Executing VM operation: G1TryInitiateConcMark (G1 Humongous Allocation)
Event: 164.821 Executing VM operation: G1TryInitiateConcMark (G1 Humongous Allocation) done
Event: 165.083 Executing VM operation: G1PauseRemark
Event: 165.100 Executing VM operation: G1PauseRemark done
Event: 165.218 Executing VM operation: G1PauseCleanup
Event: 165.218 Executing VM operation: G1PauseCleanup done
Event: 165.546 Executing VM operation: ICBufferFull
Event: 165.546 Executing VM operation: ICBufferFull done
Event: 165.717 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 165.717 Executing VM operation: HandshakeAllThreads (Deoptimize) done

Memory protections (0 events):
No events

Nmethod flushes (20 events):
Event: 165.097 Thread 0x0000021f4fe5e8a0 flushing  nmethod 0x0000021f3f336410
Event: 165.097 Thread 0x0000021f4fe5e8a0 flushing  nmethod 0x0000021f3f345490
Event: 165.097 Thread 0x0000021f4fe5e8a0 flushing  nmethod 0x0000021f3f345890
Event: 165.097 Thread 0x0000021f4fe5e8a0 flushing  nmethod 0x0000021f3f349c90
Event: 165.097 Thread 0x0000021f4fe5e8a0 flushing  nmethod 0x0000021f3f34cb10
Event: 165.097 Thread 0x0000021f4fe5e8a0 flushing  nmethod 0x0000021f3f34ce90
Event: 165.097 Thread 0x0000021f4fe5e8a0 flushing  nmethod 0x0000021f3f34ec10
Event: 165.097 Thread 0x0000021f4fe5e8a0 flushing  nmethod 0x0000021f3f350010
Event: 165.097 Thread 0x0000021f4fe5e8a0 flushing  nmethod 0x0000021f3f350390
Event: 165.097 Thread 0x0000021f4fe5e8a0 flushing  nmethod 0x0000021f3f350b90
Event: 165.097 Thread 0x0000021f4fe5e8a0 flushing  nmethod 0x0000021f3f351610
Event: 165.097 Thread 0x0000021f4fe5e8a0 flushing  nmethod 0x0000021f3f354d10
Event: 165.097 Thread 0x0000021f4fe5e8a0 flushing  nmethod 0x0000021f3f355710
Event: 165.097 Thread 0x0000021f4fe5e8a0 flushing  nmethod 0x0000021f3f356290
Event: 165.097 Thread 0x0000021f4fe5e8a0 flushing  nmethod 0x0000021f3f357590
Event: 165.097 Thread 0x0000021f4fe5e8a0 flushing  nmethod 0x0000021f3f5d6e10
Event: 165.097 Thread 0x0000021f4fe5e8a0 flushing  nmethod 0x0000021f3f65f090
Event: 165.097 Thread 0x0000021f4fe5e8a0 flushing  nmethod 0x0000021f3f697790
Event: 165.097 Thread 0x0000021f4fe5e8a0 flushing  nmethod 0x0000021f3f698f10
Event: 165.097 Thread 0x0000021f4fe5e8a0 flushing  nmethod 0x0000021f3f69cb10

Events (20 events):
Event: 152.523 Thread 0x0000021f5407d210 Thread added: 0x0000021f63523060
Event: 153.215 Thread 0x0000021f63523060 Thread exited: 0x0000021f63523060
Event: 153.329 Thread 0x0000021f5407afd0 Thread added: 0x0000021f63523060
Event: 155.294 Thread 0x0000021f638a0780 Thread added: 0x0000021f6a30d7b0
Event: 155.308 Thread 0x0000021f5682cb80 Thread added: 0x0000021f6a30b6e0
Event: 155.320 Thread 0x0000021f5682d210 Thread added: 0x0000021f6a30bd70
Event: 159.429 Thread 0x0000021f63523060 Thread exited: 0x0000021f63523060
Event: 160.329 Thread 0x0000021f5407afd0 Thread added: 0x0000021f63523060
Event: 160.464 Thread 0x0000021f63523060 Thread exited: 0x0000021f63523060
Event: 162.067 Thread 0x0000021f5407d210 Thread added: 0x0000021f63523060
Event: 162.842 Thread 0x0000021f63523060 Thread exited: 0x0000021f63523060
Event: 162.876 Thread 0x0000021f5407d210 Thread added: 0x0000021f63523060
Event: 163.798 Thread 0x0000021f6a30b6e0 Thread exited: 0x0000021f6a30b6e0
Event: 164.519 Thread 0x0000021f63523060 Thread exited: 0x0000021f63523060
Event: 164.738 Thread 0x0000021f561ff490 Thread added: 0x0000021f6a30f1f0
Event: 165.134 Thread 0x0000021f6389e020 Thread exited: 0x0000021f6389e020
Event: 165.135 Thread 0x0000021f6389ed40 Thread exited: 0x0000021f6389ed40
Event: 165.141 Thread 0x0000021f638a2ee0 Thread exited: 0x0000021f638a2ee0
Event: 165.230 Thread 0x0000021f638a1b30 Thread exited: 0x0000021f638a1b30
Event: 165.594 Thread 0x0000021f561ff490 Thread added: 0x0000021f57f55010


Dynamic libraries:
0x00007ff7370f0000 - 0x00007ff7370fa000 	C:\Program Files\Android\Android Studio\jbr\bin\java.exe
0x00007ffef0c10000 - 0x00007ffef0e08000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ffef09c0000 - 0x00007ffef0a82000 	C:\Windows\System32\KERNEL32.DLL
0x00007ffeee760000 - 0x00007ffeeea56000 	C:\Windows\System32\KERNELBASE.dll
0x00007ffeeb5a0000 - 0x00007ffeeb634000 	C:\Windows\SYSTEM32\apphelp.dll
0x00007ffeee320000 - 0x00007ffeee420000 	C:\Windows\System32\ucrtbase.dll
0x00007ffec9b50000 - 0x00007ffec9b6b000 	C:\Program Files\Android\Android Studio\jbr\bin\VCRUNTIME140.dll
0x00007ffec9c50000 - 0x00007ffec9c68000 	C:\Program Files\Android\Android Studio\jbr\bin\jli.dll
0x00007ffeef0b0000 - 0x00007ffeef24d000 	C:\Windows\System32\USER32.dll
0x00007ffeee580000 - 0x00007ffeee5a2000 	C:\Windows\System32\win32u.dll
0x00007ffef0930000 - 0x00007ffef095b000 	C:\Windows\System32\GDI32.dll
0x00007ffeee5b0000 - 0x00007ffeee6c9000 	C:\Windows\System32\gdi32full.dll
0x00007ffeeea90000 - 0x00007ffeeeb2d000 	C:\Windows\System32\msvcp_win.dll
0x00007ffee0760000 - 0x00007ffee09fa000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7\COMCTL32.dll
0x00007ffeef5f0000 - 0x00007ffeef68e000 	C:\Windows\System32\msvcrt.dll
0x00007ffeeedc0000 - 0x00007ffeeedef000 	C:\Windows\System32\IMM32.DLL
0x00007ffeca310000 - 0x00007ffeca31c000 	C:\Program Files\Android\Android Studio\jbr\bin\vcruntime140_1.dll
0x00007ffeb65b0000 - 0x00007ffeb663d000 	C:\Program Files\Android\Android Studio\jbr\bin\msvcp140.dll
0x00007ffe84e60000 - 0x00007ffe85aea000 	C:\Program Files\Android\Android Studio\jbr\bin\server\jvm.dll
0x00007ffeef530000 - 0x00007ffeef5e1000 	C:\Windows\System32\ADVAPI32.dll
0x00007ffeef010000 - 0x00007ffeef0af000 	C:\Windows\System32\sechost.dll
0x00007ffeeeee0000 - 0x00007ffeef003000 	C:\Windows\System32\RPCRT4.dll
0x00007ffeeea60000 - 0x00007ffeeea87000 	C:\Windows\System32\bcrypt.dll
0x00007ffeef4c0000 - 0x00007ffeef52b000 	C:\Windows\System32\WS2_32.dll
0x00007ffeee100000 - 0x00007ffeee14b000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ffee2be0000 - 0x00007ffee2c07000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ffee5700000 - 0x00007ffee570a000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ffeee0e0000 - 0x00007ffeee0f2000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ffeec100000 - 0x00007ffeec112000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ffec9ed0000 - 0x00007ffec9eda000 	C:\Program Files\Android\Android Studio\jbr\bin\jimage.dll
0x00007ffedfc10000 - 0x00007ffedfe11000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ffec4fb0000 - 0x00007ffec4fe4000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ffeee6d0000 - 0x00007ffeee752000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ffec9b40000 - 0x00007ffec9b4e000 	C:\Program Files\Android\Android Studio\jbr\bin\instrument.dll
0x00007ffec9a70000 - 0x00007ffec9a90000 	C:\Program Files\Android\Android Studio\jbr\bin\java.dll
0x00007ffec9a50000 - 0x00007ffec9a68000 	C:\Program Files\Android\Android Studio\jbr\bin\zip.dll
0x00007ffef00d0000 - 0x00007ffef083e000 	C:\Windows\System32\SHELL32.dll
0x00007ffeec300000 - 0x00007ffeecaa4000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ffeefd70000 - 0x00007ffef00c3000 	C:\Windows\System32\combase.dll
0x00007ffeedb20000 - 0x00007ffeedb4b000 	C:\Windows\SYSTEM32\Wldp.dll
0x00007ffeeedf0000 - 0x00007ffeeeebd000 	C:\Windows\System32\OLEAUT32.dll
0x00007ffeeec30000 - 0x00007ffeeecdd000 	C:\Windows\System32\SHCORE.dll
0x00007ffeeece0000 - 0x00007ffeeed3b000 	C:\Windows\System32\shlwapi.dll
0x00007ffeee1d0000 - 0x00007ffeee1f4000 	C:\Windows\SYSTEM32\profapi.dll
0x00007ffec9b30000 - 0x00007ffec9b40000 	C:\Program Files\Android\Android Studio\jbr\bin\net.dll
0x00007ffee77d0000 - 0x00007ffee78da000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007ffeed900000 - 0x00007ffeed96a000 	C:\Windows\system32\mswsock.dll
0x00007ffec45c0000 - 0x00007ffec45d6000 	C:\Program Files\Android\Android Studio\jbr\bin\nio.dll
0x00007ffec8230000 - 0x00007ffec8240000 	C:\Program Files\Android\Android Studio\jbr\bin\verify.dll
0x00007ffeca3e0000 - 0x00007ffeca407000 	C:\Users\<USER>\.gradle\native\c067742578af261105cb4f569cf0c3c89f3d7b1fecec35dd04571415982c5e48\windows-amd64\native-platform.dll
0x00007ffeac420000 - 0x00007ffeac564000 	C:\Users\<USER>\.gradle\native\100fb08df4bc3b14c8652ba06237920a3bd2aa13389f12d3474272988ae205f9\windows-amd64\native-platform-file-events.dll
0x00007ffec73f0000 - 0x00007ffec73f9000 	C:\Program Files\Android\Android Studio\jbr\bin\management.dll
0x00007ffec73b0000 - 0x00007ffec73bb000 	C:\Program Files\Android\Android Studio\jbr\bin\management_ext.dll
0x00007ffef0920000 - 0x00007ffef0928000 	C:\Windows\System32\PSAPI.DLL
0x00007ffeedeb0000 - 0x00007ffeedec8000 	C:\Windows\SYSTEM32\CRYPTSP.dll
0x00007ffeed240000 - 0x00007ffeed278000 	C:\Windows\system32\rsaenh.dll
0x00007ffeee150000 - 0x00007ffeee17e000 	C:\Windows\SYSTEM32\USERENV.dll
0x00007ffeeda90000 - 0x00007ffeeda9c000 	C:\Windows\SYSTEM32\CRYPTBASE.dll
0x00007ffeed600000 - 0x00007ffeed63b000 	C:\Windows\SYSTEM32\IPHLPAPI.DLL
0x00007ffeef8e0000 - 0x00007ffeef8e8000 	C:\Windows\System32\NSI.dll
0x00007ffec7150000 - 0x00007ffec7159000 	C:\Program Files\Android\Android Studio\jbr\bin\extnet.dll
0x00007ffec68d0000 - 0x00007ffec68d7000 	C:\Windows\system32\wshunix.dll
0x00007ffeed640000 - 0x00007ffeed70a000 	C:\Windows\SYSTEM32\DNSAPI.dll
0x00007ffee1060000 - 0x00007ffee106a000 	C:\Windows\System32\rasadhlp.dll
0x00007ffee70c0000 - 0x00007ffee7140000 	C:\Windows\System32\fwpuclnt.dll
0x00007ffedeb00000 - 0x00007ffedeb07000 	C:\Program Files\Android\Android Studio\jbr\bin\rmi.dll
0x00007ffeed490000 - 0x00007ffeed4c3000 	C:\Windows\SYSTEM32\ntmarta.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Android\Android Studio\jbr\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7;C:\Program Files\Android\Android Studio\jbr\bin\server;C:\Users\<USER>\.gradle\native\c067742578af261105cb4f569cf0c3c89f3d7b1fecec35dd04571415982c5e48\windows-amd64;C:\Users\<USER>\.gradle\native\100fb08df4bc3b14c8652ba06237920a3bd2aa13389f12d3474272988ae205f9\windows-amd64

VM Arguments:
jvm_args: -XX:MaxMetaspaceSize=512m -XX:+HeapDumpOnOutOfMemoryError --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED -Xmx1536m -Dfile.encoding=UTF-8 -Duser.country=BR -Duser.language=pt -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.9-bin\90cnw93cvbtalezasaz0blq0a\gradle-8.9\lib\agents\gradle-instrumentation-agent-8.9.jar 
java_command: org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.9
java_class_path (initial): C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.9-bin\90cnw93cvbtalezasaz0blq0a\gradle-8.9\lib\gradle-daemon-main-8.9.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 3                                         {product} {ergonomic}
   size_t CompressedClassSpaceSize                 = 436207616                                 {product} {ergonomic}
     uint ConcGCThreads                            = 1                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 4                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 1048576                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
     bool HeapDumpOnOutOfMemoryError               = true                                   {manageable} {command line}
   size_t InitialHeapSize                          = 134217728                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 1610612736                                {product} {command line}
   size_t MaxMetaspaceSize                         = 536870912                                 {product} {command line}
   size_t MaxNewSize                               = 965738496                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 1048576                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5832780                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122912730                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122912730                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 1610612736                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=C:\Program Files\Java\jdk-11.0.10
PATH=c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\Microsoft SQL Server\130\Tools\Binn\;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn\;C:\Program Files\dotnet\;C:\Program Files (x86)\Microsoft SQL Server\150\DTS\Binn\;C:\Program Files\Azure Data Studio\bin;C:\Program Files\Git\cmd;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\110\Tools\Binn\;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\130\Tools\Binn\;C:\Program Files (x86)\Microsoft SQL Server\140\Tools\Binn\;C:\Program Files\Microsoft SQL Server\140\Tools\Binn\;C:\Program Files\Microsoft SQL Server\140\DTS\Binn\;C:\Program Files\Microsoft\Web Platform Installer\;C:\Program Files (x86)\dotnet\;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;C:\Program Files\Calibre2\;C:\Program Files\Java\jdk-11\bin;C:\Prog;C:\Program Files\Docker\Docker\resources\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\.local\share\solana\install\active_release\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\;C:\ProgramData\DockerDesktop\version-bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\Users\ADM\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\.dotnet\tools;C:\MinGW\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin
USERNAME=ADM
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 58 Stepping 9, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

JNI global refs:
JNI global refs: 28, weak refs: 1

JNI global refs memory usage: 835, weak refs: 833

Process memory usage:
Resident Set Size: 874596K (10% of 8357708K total physical memory with 364152K free physical memory)

OOME stack traces (most recent first):
Classloader memory used:
Loader org.gradle.internal.classloader.VisitableURLClassLoader                         : 118M
Loader bootstrap                                                                       : 35956K
Loader org.gradle.initialization.MixInLegacyTypesClassLoader                           : 14403K
Loader org.gradle.internal.classloader.VisitableURLClassLoader$InstrumentingVisitableURLClassLoader: 8645K
Loader jdk.internal.loader.ClassLoaders$AppClassLoader                                 : 7705K
Loader java.net.URLClassLoader                                                         : 5192K
Loader jdk.internal.loader.ClassLoaders$PlatformClassLoader                            : 1021K
Loader jdk.internal.jrtfs.JrtFileSystemProvider$JrtFsLoader                            : 340K
Loader org.gradle.internal.classloader.VisitableURLClassLoader                         : 337K
Loader jdk.internal.reflect.DelegatingClassLoader                                      : 308K
Loader org.gradle.groovy.scripts.internal.DefaultScriptCompilationHandler$ScriptClassLoader: 76048B
Loader groovy.lang.GroovyClassLoader$InnerLoader                                       : 28592B
Loader org.codehaus.groovy.runtime.callsite.CallSiteClassLoader                        : 10632B

Classes loaded by more than one classloader:
Class Program                                                                         : loaded 12 times (x 68B)
Class Build_gradle$1                                                                  : loaded 10 times (x 74B)
Class Build_gradle                                                                    : loaded 10 times (x 126B)
Class org.gradle.kotlin.dsl.VersionCatalogAccessorsKt                                 : loaded 5 times (x 67B)
Class Build_gradle$4                                                                  : loaded 4 times (x 70B)
Class Build_gradle$6                                                                  : loaded 4 times (x 70B)
Class Build_gradle$2                                                                  : loaded 4 times (x 75B)
Class org.gradle.kotlin.dsl.Accessors96b3ii45gitqpy1kb3tvcvtxvKt                      : loaded 4 times (x 67B)
Class org.gradle.kotlin.dsl.ImplementationConfigurationAccessorsKt                    : loaded 4 times (x 67B)
Class org.gradle.kotlin.dsl.Accessors9osmdt78klrrxidc9srcw3tsqKt                      : loaded 4 times (x 67B)
Class Build_gradle$3                                                                  : loaded 4 times (x 70B)
Class Build_gradle$5                                                                  : loaded 4 times (x 70B)
Class org.gradle.kotlin.dsl.TestImplementationConfigurationAccessorsKt                : loaded 3 times (x 67B)
Class kotlin.sequences.SequencesKt__SequenceBuilderKt                                 : loaded 2 times (x 67B)
Class kotlin.collections.CollectionsKt__ReversedViewsKt                               : loaded 2 times (x 67B)
Class jdk.internal.jimage.BasicImageReader                                            : loaded 2 times (x 91B)
Class org.gradle.internal.classloader.ClassLoaderHierarchy                            : loaded 2 times (x 66B)
Class [Lorg.jetbrains.kotlin.metadata.ProtoBuf$Modality;                              : loaded 2 times (x 65B)
Class org.jetbrains.org.objectweb.asm.tree.Util                                       : loaded 2 times (x 67B)
Class org.gradle.api.internal.classpath.ManifestUtil                                  : loaded 2 times (x 67B)
Class com.google.gson.stream.MalformedJsonException                                   : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Effect$InvocationKind$1                  : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.protobuf.GeneratedMessageLite$1                            : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$FunctionOrBuilder                        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.protobuf.GeneratedMessageLite$ExtendableMessageOrBuilder   : loaded 2 times (x 66B)
Class org.jetbrains.org.objectweb.asm.tree.FieldInsnNode                              : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.protobuf.ByteString$Output                                 : loaded 2 times (x 82B)
Class Build_gradle$2$1$1                                                              : loaded 2 times (x 70B)
Class jdk.internal.jimage.decompressor.StringSharingDecompressorFactory               : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Effect$EffectType                        : loaded 2 times (x 78B)
Class org.gradle.internal.classloader.DefaultClassLoaderFactory                       : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.metadata.jvm.deserialization.JvmNameResolver               : loaded 2 times (x 76B)
Class org.jetbrains.org.objectweb.asm.ClassVisitor                                    : loaded 2 times (x 84B)
Class kotlin.sequences.SequencesKt___SequencesKt                                      : loaded 2 times (x 67B)
Class kotlin.collections.AbstractList$Companion                                       : loaded 2 times (x 67B)
Class org.gradle.internal.classloader.ClassLoaderVisitor                              : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.metadata.deserialization.Flags$EnumLiteFlagField           : loaded 2 times (x 72B)
Class org.jetbrains.org.objectweb.asm.tree.FrameNode                                  : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Type$Argument$1                          : loaded 2 times (x 140B)
Class org.jetbrains.kotlin.metadata.jvm.JvmProtoBuf$StringTableTypesOrBuilder         : loaded 2 times (x 66B)
Class org.jetbrains.org.objectweb.asm.ClassReader                                     : loaded 2 times (x 88B)
Class org.gradle.api.internal.classpath.EffectiveClassPath                            : loaded 2 times (x 86B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$EnumEntry                                : loaded 2 times (x 114B)
Class [Lorg.jetbrains.kotlin.descriptors.CallableMemberDescriptor$Kind;               : loaded 2 times (x 65B)
Class kotlin.text.StringsKt__AppendableKt                                             : loaded 2 times (x 67B)
Class [Lkotlin.reflect.KCallable;                                                     : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.util.ModuleVisibilityHelper                                : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.descriptors.DescriptorVisibilities$1                       : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.protobuf.CodedOutputStream$ByteBufferOutputStream          : loaded 2 times (x 82B)
Class org.gradle.api.internal.DefaultClassPathProvider                                : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.descriptors.DescriptorVisibilities$2                       : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Modality                                 : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.metadata.jvm.JvmProtoBuf$JvmFieldSignature                 : loaded 2 times (x 104B)
Class org.jetbrains.kotlin.protobuf.ExtensionRegistryLite                             : loaded 2 times (x 70B)
Class kotlin.sequences.Sequence                                                       : loaded 2 times (x 66B)
Class kotlin.text.StringsKt__StringNumberConversionsJVMKt                             : loaded 2 times (x 67B)
Class [Lorg.gradle.api.internal.ClassPathProvider;                                    : loaded 2 times (x 65B)
Class org.gradle.api.internal.ClassPathProvider                                       : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.metadata.jvm.deserialization.ClassMapperLite               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.descriptors.DescriptorVisibilities$3                       : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$MemberKind                               : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.metadata.jvm.deserialization.JvmNameResolverBase$Companion : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.load.java.JvmAnnotationNames                               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.cli.common.CompilerSystemProperties                        : loaded 2 times (x 76B)
Class jdk.internal.jrtfs.SystemImage                                                  : loaded 2 times (x 70B)
Class org.gradle.internal.service.UnknownServiceException                             : loaded 2 times (x 79B)
Class org.jetbrains.kotlin.descriptors.DescriptorVisibilities$4                       : loaded 2 times (x 75B)
Class org.jetbrains.org.objectweb.asm.tree.TableSwitchInsnNode                        : loaded 2 times (x 75B)
Class [Lorg.jetbrains.kotlin.protobuf.WireFormat$JavaType;                            : loaded 2 times (x 65B)
Class [Lorg.jetbrains.org.objectweb.asm.Attribute;                                    : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.descriptors.DescriptorVisibilities$5                       : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.metadata.jvm.JvmProtoBuf$JvmPropertySignatureOrBuilder     : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.protobuf.GeneratedMessageLite$ExtendableMessage            : loaded 2 times (x 109B)
Class org.jetbrains.kotlin.cli.common.messages.CompilerMessageSeverity                : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Annotation$Argument$Value                : loaded 2 times (x 123B)
Class org.jetbrains.kotlin.descriptors.DescriptorVisibilities$6                       : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.protobuf.CodedOutputStream$OutOfSpaceException             : loaded 2 times (x 78B)
Class kotlin.collections.ArraysKt___ArraysJvmKt                                       : loaded 2 times (x 67B)
Class kotlin.collections.CollectionsKt__CollectionsKt                                 : loaded 2 times (x 67B)
Class kotlin.text.StringsKt__RegexExtensionsJVMKt                                     : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.descriptors.DescriptorVisibilities$7                       : loaded 2 times (x 75B)
Class kotlin._Assertions                                                              : loaded 2 times (x 67B)
Class jdk.internal.jimage.ImageReader$LinkNode                                        : loaded 2 times (x 80B)
Class org.gradle.internal.classpath.TransformedClassPath                              : loaded 2 times (x 92B)
Class kotlin.jvm.internal.FunctionReference                                           : loaded 2 times (x 118B)
Class kotlin.jvm.internal.FunctionBase                                                : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.descriptors.DescriptorVisibilities$8                       : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$MemberKind$1                             : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.metadata.jvm.JvmProtoBuf$StringTableTypes$RecordOrBuilder  : loaded 2 times (x 66B)
Class [Lorg.jetbrains.org.objectweb.asm.SymbolTable$Entry;                            : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.resolve.jvm.JvmClassName                                   : loaded 2 times (x 72B)
Class kotlin.enums.EnumEntriesList                                                    : loaded 2 times (x 219B)
Class org.jetbrains.kotlin.com.google.common.base.Preconditions                       : loaded 2 times (x 67B)
Class jdk.internal.jrtfs.SystemImage$1                                                : loaded 2 times (x 74B)
Class _BuildScript_                                                                   : loaded 2 times (x 170B)
Class org.jetbrains.kotlin.protobuf.GeneratedMessageLite$ExtendableBuilder            : loaded 2 times (x 134B)
Class org.jetbrains.kotlin.descriptors.DescriptorVisibilities$9                       : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.metadata.jvm.JvmProtoBuf$StringTableTypes                  : loaded 2 times (x 106B)
Class kotlin.comparisons.ComparisonsKt___ComparisonsJvmKt                             : loaded 2 times (x 67B)
Class kotlin.collections.CollectionsKt__IteratorsJVMKt                                : loaded 2 times (x 67B)
Class jdk.internal.jrtfs.SystemImage$2                                                : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.utils.CollectionsKt                                        : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.metadata.jvm.JvmProtoBuf$StringTableTypes$1                : loaded 2 times (x 140B)
Class org.jetbrains.org.objectweb.asm.tree.LabelNode                                  : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$PropertyOrBuilder                        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Constructor$1                            : loaded 2 times (x 140B)
Class org.jetbrains.kotlin.build.report.metrics.GradleBuildTime                       : loaded 2 times (x 82B)
Class org.gradle.internal.service.ServiceLocator                                      : loaded 2 times (x 66B)
Class kotlin.Pair                                                                     : loaded 2 times (x 68B)
Class [Lkotlin.Function;                                                              : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.load.kotlin.header.ReadKotlinClassHeaderAnnotationVisitor$CollectStringArrayAnnotationVisitor: loaded 2 times (x 81B)
Class jdk.internal.jrtfs.JrtFileSystemProvider                                        : loaded 2 times (x 97B)
Class [Lorg.jetbrains.org.objectweb.asm.tree.AbstractInsnNode;                        : loaded 2 times (x 65B)
Class kotlin.io.CloseableKt                                                           : loaded 2 times (x 67B)
Class kotlin.collections.CollectionsKt__MutableCollectionsKt                          : loaded 2 times (x 67B)
Class kotlin.reflect.KCallable                                                        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.metadata.deserialization.ProtoBufUtilKt                    : loaded 2 times (x 67B)
Class kotlin.io.TerminateException                                                    : loaded 2 times (x 78B)
Class kotlin.io.FilesKt__FilePathComponentsKt                                         : loaded 2 times (x 67B)
Class org.gradle.kotlin.dsl.Accessorse8w47d3slt021lb0cbtcdsmobKt                      : loaded 2 times (x 67B)
Class org.jetbrains.org.objectweb.asm.tree.InsnList                                   : loaded 2 times (x 99B)
Class org.jetbrains.kotlin.protobuf.AbstractMessageLite$Builder                       : loaded 2 times (x 118B)
Class org.jetbrains.kotlin.protobuf.Internal$EnumLiteMap                              : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.protobuf.GeneratedMessageLite                              : loaded 2 times (x 97B)
Class org.jetbrains.kotlin.cli.common.messages.MessageCollector$Companion             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.descriptors.Visibilities$Unknown                           : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.descriptors.Visibilities$Inherited                         : loaded 2 times (x 75B)
Class org.jetbrains.org.objectweb.asm.tree.MethodInsnNode                             : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.protobuf.Parser                                            : loaded 2 times (x 66B)
Class org.jetbrains.org.objectweb.asm.tree.MethodNode                                 : loaded 2 times (x 106B)
Class org.jetbrains.kotlin.load.kotlin.KotlinJvmBinaryClass                           : loaded 2 times (x 66B)
Class [Lorg.jetbrains.kotlin.build.report.metrics.BuildTime;                          : loaded 2 times (x 65B)
Class kotlin.io.FilesKt__FileTreeWalkKt                                               : loaded 2 times (x 67B)
Class jdk.internal.jimage.ImageLocation                                               : loaded 2 times (x 86B)
Class jdk.internal.jrtfs.JrtDirectoryStream$1                                         : loaded 2 times (x 79B)
Class [Lorg.jetbrains.kotlin.metadata.ProtoBuf$VersionRequirement$Level;              : loaded 2 times (x 65B)
Class org.jetbrains.org.objectweb.asm.ClassTooLargeException                          : loaded 2 times (x 79B)
Class org.jetbrains.kotlin.protobuf.WireFormat$JavaType                               : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.metadata.jvm.deserialization.JvmMemberSignature$Method     : loaded 2 times (x 71B)
Class org.gradle.internal.InternalTransformer                                         : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.descriptors.Visibilities$PrivateToThis                     : loaded 2 times (x 75B)
Class org.jetbrains.org.objectweb.asm.tree.IntInsnNode                                : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.protobuf.SmallSortedMap$1                                  : loaded 2 times (x 126B)
Class kotlin.collections.EmptyList                                                    : loaded 2 times (x 170B)
Class kotlin.collections.MapsKt__MapsJVMKt                                            : loaded 2 times (x 67B)
Class jdk.internal.jimage.ImageBufferCache$1                                          : loaded 2 times (x 79B)
Class org.gradle.internal.classloader.ClasspathUtil$1                                 : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Effect$EffectType$1                      : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$EffectOrBuilder                          : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.protobuf.BoundedByteString                                 : loaded 2 times (x 108B)
Class jdk.internal.jimage.ImageBufferCache$2                                          : loaded 2 times (x 87B)
Class org.gradle.api.internal.classpath.DefaultModuleRegistry$DefaultModule           : loaded 2 times (x 82B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Annotation$Argument$Value$1              : loaded 2 times (x 140B)
Class org.jetbrains.kotlin.descriptors.Visibilities$InvisibleFake                     : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.protobuf.UninitializedMessageException                     : loaded 2 times (x 80B)
Class org.jetbrains.kotlin.metadata.jvm.deserialization.JvmNameResolverKt             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$TypeParameter$Variance                   : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.name.ClassId                                               : loaded 2 times (x 68B)
Class [Lorg.jetbrains.kotlin.metadata.ProtoBuf$Effect$InvocationKind;                 : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$VersionRequirement$VersionKind           : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.descriptors.Named                                          : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.serialization.deserialization.ProtoEnumFlagsUtilsKt        : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Visibility$1                             : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.metadata.jvm.JvmProtoBuf$StringTableTypes$Record$Operation$1: loaded 2 times (x 70B)
Class org.jetbrains.org.objectweb.asm.tree.JumpInsnNode                               : loaded 2 times (x 74B)
Class kotlin.collections.IndexingIterable                                             : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.metadata.deserialization.NameResolver                      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.protobuf.GeneratedMessageLite$ExtensionDescriptor          : loaded 2 times (x 80B)
Class org.jetbrains.kotlin.protobuf.InvalidProtocolBufferException                    : loaded 2 times (x 80B)
Class org.jetbrains.org.objectweb.asm.FieldVisitor                                    : loaded 2 times (x 73B)
Class kotlin.collections.ArraysKt__ArraysKt                                           : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.protobuf.ByteString$ByteIterator                           : loaded 2 times (x 66B)
Class kotlin.collections.SetsKt__SetsJVMKt                                            : loaded 2 times (x 67B)
Class kotlin.Function                                                                 : loaded 2 times (x 66B)
Class org.gradle.api.internal.classpath.Module                                        : loaded 2 times (x 66B)
Class kotlin.internal.ProgressionUtilKt                                               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.metadata.deserialization.Flags$FlagField                   : loaded 2 times (x 69B)
Class kotlin.io.FileAlreadyExistsException                                            : loaded 2 times (x 78B)
Class org.gradle.api.internal.classpath.UnknownModuleException                        : loaded 2 times (x 78B)
Class com.google.gson.stream.JsonReader$1                                             : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$TypeAliasOrBuilder                       : loaded 2 times (x 66B)
Class [Lorg.jetbrains.kotlin.metadata.ProtoBuf$Class$Kind;                            : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.config.ApiVersion                                          : loaded 2 times (x 82B)
Class jdk.internal.jimage.ImageHeader                                                 : loaded 2 times (x 67B)
Class kotlin.collections.IndexingIterator                                             : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.protobuf.LiteralByteString                                 : loaded 2 times (x 108B)
Class org.jetbrains.kotlin.protobuf.LazyFieldLite                                     : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.build.report.metrics.BuildMetricsReporter                  : loaded 2 times (x 66B)
Class [Lkotlin.reflect.KAnnotatedElement;                                             : loaded 2 times (x 65B)
Class org.jetbrains.org.objectweb.asm.tree.VarInsnNode                                : loaded 2 times (x 74B)
Class org.jetbrains.org.objectweb.asm.FieldWriter                                     : loaded 2 times (x 74B)
Class org.jetbrains.org.objectweb.asm.MethodVisitor                                   : loaded 2 times (x 101B)
Class kotlin.io.ByteStreamsKt                                                         : loaded 2 times (x 67B)
Class org.gradle.api.internal.DefaultClassPathRegistry                                : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Type$Argument$Projection$1               : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.protobuf.SmallSortedMap$EmptySet$1                         : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.protobuf.AbstractMessageLite$Builder$LimitedInputStream    : loaded 2 times (x 88B)
Class org.jetbrains.kotlin.cli.common.messages.MessageCollector                       : loaded 2 times (x 66B)
Class org.gradle.api.internal.ClassPathRegistry                                       : loaded 2 times (x 66B)
Class org.gradle.internal.classpath.DefaultClassPath$ImmutableUniqueList              : loaded 2 times (x 195B)
Class org.jetbrains.kotlin.descriptors.Visibilities$Protected                         : loaded 2 times (x 75B)
Class org.jetbrains.org.objectweb.asm.ByteVector                                      : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$PackageOrBuilder                         : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.metadata.jvm.JvmProtoBuf$JvmPropertySignature              : loaded 2 times (x 110B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$ValueParameterOrBuilder                  : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.protobuf.SmallSortedMap$EmptySet$2                         : loaded 2 times (x 74B)
Class kotlin.text.StringsKt__StringsJVMKt                                             : loaded 2 times (x 67B)
Class jdk.internal.jimage.decompressor.ResourceDecompressor                           : loaded 2 times (x 66B)
Class [Lorg.jetbrains.org.objectweb.asm.AnnotationWriter;                             : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.descriptors.DeclarationDescriptor                          : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.descriptors.DescriptorVisibilities                         : loaded 2 times (x 67B)
Class org.jetbrains.org.objectweb.asm.tree.LookupSwitchInsnNode                       : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.load.kotlin.KotlinJvmBinaryClass$AnnotationArrayArgumentVisitor: loaded 2 times (x 66B)
Class kotlin.collections.SetsKt                                                       : loaded 2 times (x 67B)
Class kotlin.text.StringsKt__IndentKt                                                 : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$ContractOrBuilder                        : loaded 2 times (x 66B)
Class org.gradle.internal.classpath.ClassPath                                         : loaded 2 times (x 66B)
Class org.gradle.api.UncheckedIOException                                             : loaded 2 times (x 78B)
Class kotlin.reflect.KFunction                                                        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.metadata.deserialization.TypeTable                         : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.load.kotlin.FileBasedKotlinClass$1                         : loaded 2 times (x 84B)
Class kotlin.sequences.SequencesKt__SequencesKt$asSequence$$inlined$Sequence$1        : loaded 2 times (x 71B)
Class com.google.gson.internal.JsonReaderInternalAccess                               : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Type$ArgumentOrBuilder                   : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.protobuf.GeneratedMessageLite$GeneratedExtension           : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.load.kotlin.FileBasedKotlinClass$2                         : loaded 2 times (x 84B)
Class kotlin.collections.MapsKt__MapsKt                                               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Class                                    : loaded 2 times (x 179B)
Class org.jetbrains.org.objectweb.asm.ClassWriter                                     : loaded 2 times (x 102B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Property$1                               : loaded 2 times (x 140B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Type                                     : loaded 2 times (x 141B)
Class org.jetbrains.kotlin.load.kotlin.FileBasedKotlinClass$3                         : loaded 2 times (x 74B)
Class Build_gradle$2$1                                                                : loaded 2 times (x 70B)
Class jdk.internal.jimage.ImageStringsReader                                          : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.load.kotlin.FileBasedKotlinClass$4                         : loaded 2 times (x 84B)
Class kotlin.UninitializedPropertyAccessException                                     : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Annotation$Argument                      : loaded 2 times (x 104B)
Class org.jetbrains.org.objectweb.asm.tree.LocalVariableNode                          : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Class$Kind                               : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.protobuf.GeneratedMessageLite$Builder                      : loaded 2 times (x 124B)
Class org.jetbrains.kotlin.protobuf.WireFormat$FieldType$1                            : loaded 2 times (x 79B)
Class org.jetbrains.kotlin.metadata.jvm.JvmProtoBuf$JvmMethodSignatureOrBuilder       : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.protobuf.ByteString                                        : loaded 2 times (x 104B)
Class [Lorg.jetbrains.kotlin.cli.common.messages.CompilerMessageSeverity;             : loaded 2 times (x 65B)
Class jdk.internal.jimage.ImageReader                                                 : loaded 2 times (x 71B)
Class [Lorg.jetbrains.kotlin.metadata.ProtoBuf$Visibility;                            : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$TypeParameter                            : loaded 2 times (x 126B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$ValueParameter$1                         : loaded 2 times (x 140B)
Class [Lorg.jetbrains.kotlin.protobuf.WireFormat$FieldType;                           : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.protobuf.WireFormat$FieldType$2                            : loaded 2 times (x 79B)
Class jdk.internal.jimage.ImageReader$SharedImageReader                               : loaded 2 times (x 91B)
Class org.gradle.internal.installation.GradleInstallation$1                           : loaded 2 times (x 71B)
Class org.gradle.api.internal.classpath.ModuleRegistry                                : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.protobuf.WireFormat$FieldType$3                            : loaded 2 times (x 79B)
Class org.jetbrains.org.objectweb.asm.AnnotationVisitor                               : loaded 2 times (x 74B)
Class jdk.internal.jimage.decompressor.ResourceDecompressorFactory                    : loaded 2 times (x 70B)
Class [Lorg.jetbrains.kotlin.metadata.ProtoBuf$Annotation$Argument$Value$Type;        : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$VersionRequirement$Level                 : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$VersionRequirementOrBuilder              : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.descriptors.Visibilities$Internal                          : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Contract$1                               : loaded 2 times (x 140B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Type$1                                   : loaded 2 times (x 140B)
Class org.jetbrains.kotlin.protobuf.WireFormat$FieldType$4                            : loaded 2 times (x 79B)
Class org.jetbrains.kotlin.metadata.jvm.JvmProtoBuf$JvmMethodSignature$1              : loaded 2 times (x 140B)
Class org.jetbrains.kotlin.load.kotlin.FileBasedKotlinClass$3$1                       : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.name.FqNameUnsafe                                          : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Expression                               : loaded 2 times (x 116B)
Class kotlin.io.FilesKt__UtilsKt                                                      : loaded 2 times (x 67B)
Class kotlin.collections.CollectionsKt__CollectionsJVMKt                              : loaded 2 times (x 67B)
Class jdk.internal.jimage.ImageReader$Node                                            : loaded 2 times (x 80B)
Class org.gradle.api.internal.classpath.DefaultModuleRegistry                         : loaded 2 times (x 79B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Annotation$Argument$1                    : loaded 2 times (x 140B)
Class org.jetbrains.kotlin.metadata.deserialization.Flags$BooleanFlagField            : loaded 2 times (x 72B)
Class kotlin.collections.IndexedValue                                                 : loaded 2 times (x 68B)
Class org.jetbrains.org.objectweb.asm.AnnotationWriter                                : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Function$1                               : loaded 2 times (x 140B)
Class org.jetbrains.kotlin.metadata.jvm.deserialization.JvmProtoBufUtil               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.load.kotlin.KotlinJvmBinaryClass$AnnotationVisitor         : loaded 2 times (x 66B)
Class kotlin.sequences.SequencesKt                                                    : loaded 2 times (x 67B)
Class kotlin.collections.CollectionsKt__IteratorsKt                                   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.build.report.metrics.GradleBuildTime$Companion             : loaded 2 times (x 67B)
Class jdk.internal.jimage.BasicImageReader$1                                          : loaded 2 times (x 73B)
Class kotlin.text.StringsKt                                                           : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.descriptors.Visibilities$Private                           : loaded 2 times (x 75B)
Class kotlin.LazyKt__LazyJVMKt                                                        : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$VersionRequirement$VersionKind$1         : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Annotation$1                             : loaded 2 times (x 140B)
Class org.jetbrains.kotlin.protobuf.AbstractParser                                    : loaded 2 times (x 140B)
Class kotlin.io.FilesKt__FileReadWriteKt                                              : loaded 2 times (x 67B)
Class kotlin.text.StringsKt__StringNumberConversionsKt                                : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.cli.common.CompilerSystemProperties$value$1                : loaded 2 times (x 121B)
Class jdk.internal.jimage.decompressor.Decompressor                                   : loaded 2 times (x 68B)
Class jdk.internal.jimage.ImageReader$Resource                                        : loaded 2 times (x 80B)
Class org.gradle.internal.service.ServiceLookupException                              : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Expression$ConstantValue                 : loaded 2 times (x 78B)
Class [Lorg.jetbrains.kotlin.protobuf.Internal$EnumLite;                              : loaded 2 times (x 65B)
Class jdk.internal.jrtfs.JrtFileAttributes                                            : loaded 2 times (x 79B)
Class org.gradle.internal.classloader.ClassLoaderFactory                              : loaded 2 times (x 66B)
Class org.gradle.internal.service.CachingServiceLocator                               : loaded 2 times (x 78B)
Class kotlin.jvm.internal.CallableReference                                           : loaded 2 times (x 102B)
Class org.jetbrains.kotlin.descriptors.Visibilities$Public                            : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.protobuf.ExtensionRegistryLite$ObjectIntPair               : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Class$1                                  : loaded 2 times (x 140B)
Class org.jetbrains.kotlin.load.kotlin.header.ReadKotlinClassHeaderAnnotationVisitor$KotlinMetadataArgumentVisitor$1: loaded 2 times (x 81B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$VersionRequirement$1                     : loaded 2 times (x 140B)
Class org.jetbrains.kotlin.metadata.jvm.JvmProtoBuf$StringTableTypes$Record$1         : loaded 2 times (x 140B)
Class org.jetbrains.org.objectweb.asm.tree.AnnotationNode                             : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.load.kotlin.header.ReadKotlinClassHeaderAnnotationVisitor$KotlinMetadataArgumentVisitor$2: loaded 2 times (x 81B)
Class org.jetbrains.kotlin.build.report.metrics.BuildTime                             : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.descriptors.CallableMemberDescriptor$Kind                  : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$TypeParameterOrBuilder                   : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Property                                 : loaded 2 times (x 147B)
Class org.jetbrains.org.objectweb.asm.Type                                            : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.name.FqName                                                : loaded 2 times (x 68B)
Class kotlin.enums.EnumEntries                                                        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.build.report.metrics.DoNothingBuildMetricsReporter         : loaded 2 times (x 82B)
Class org.jetbrains.org.objectweb.asm.TypePath                                        : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.metadata.jvm.deserialization.UtfEncodingKt                 : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.metadata.jvm.JvmProtoBuf$JvmFieldSignature$1               : loaded 2 times (x 140B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$TypeTable                                : loaded 2 times (x 105B)
Class org.jetbrains.kotlin.metadata.jvm.JvmProtoBuf$JvmMethodSignature                : loaded 2 times (x 104B)
Class org.jetbrains.kotlin.protobuf.MessageLiteOrBuilder                              : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.load.kotlin.header.ReadKotlinClassHeaderAnnotationVisitor$KotlinMetadataArgumentVisitor: loaded 2 times (x 82B)
Class [Lorg.jetbrains.kotlin.cli.common.CompilerSystemProperties;                     : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$AnnotationOrBuilder                      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.protobuf.WireFormat$FieldType                              : loaded 2 times (x 79B)
Class org.jetbrains.kotlin.load.kotlin.FileBasedKotlinClass                           : loaded 2 times (x 84B)
Class kotlin.sequences.FilteringSequence$iterator$1                                   : loaded 2 times (x 75B)
Class jdk.internal.jimage.ImageBufferCache                                            : loaded 2 times (x 67B)
Class jdk.internal.jrtfs.JrtPath                                                      : loaded 2 times (x 114B)
Class org.gradle.internal.classloader.ClasspathUtil                                   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$TypeAlias                                : loaded 2 times (x 136B)
Class [Lorg.jetbrains.kotlin.metadata.ProtoBuf$TypeParameter$Variance;                : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Constructor                              : loaded 2 times (x 123B)
Class org.jetbrains.kotlin.metadata.jvm.deserialization.JvmMemberSignature            : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.load.kotlin.header.KotlinClassHeader$Kind                  : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.load.kotlin.KotlinJvmBinaryClass$AnnotationArgumentVisitor : loaded 2 times (x 66B)
Class kotlin.io.FileSystemException                                                   : loaded 2 times (x 78B)
Class kotlin.jvm.internal.DefaultConstructorMarker                                    : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.resolve.scopes.receivers.ReceiverValue                     : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.metadata.jvm.JvmProtoBuf$JvmMethodSignature$Builder        : loaded 2 times (x 130B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$VersionRequirementTableOrBuilder         : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.metadata.jvm.JvmProtoBuf$JvmFieldSignatureOrBuilder        : loaded 2 times (x 66B)
Class org.jetbrains.org.objectweb.asm.ModuleVisitor                                   : loaded 2 times (x 77B)
Class org.gradle.api.specs.Spec                                                       : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.descriptors.DelegatedDescriptorVisibility                  : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.serialization.deserialization.ProtoEnumFlagsUtilsKt$WhenMappings: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.load.kotlin.header.KotlinClassHeader                       : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.name.Name                                                  : loaded 2 times (x 71B)
Class jdk.internal.jimage.ImageReader$SharedImageReader$LocationVisitor               : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.metadata.deserialization.Flags                             : loaded 2 times (x 67B)
Class org.jetbrains.org.objectweb.asm.Symbol                                          : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$TypeTable$1                              : loaded 2 times (x 140B)
Class kotlin.collections.EmptyIterator                                                : loaded 2 times (x 85B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Function                                 : loaded 2 times (x 148B)
Class kotlin.SynchronizedLazyImpl                                                     : loaded 2 times (x 72B)
Class kotlin.text.StringsKt__RegexExtensionsKt                                        : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.load.kotlin.FileBasedKotlinClass$OuterAndInnerName         : loaded 2 times (x 68B)
Class jdk.internal.jimage.decompressor.ResourceDecompressorRepository                 : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Effect$InvocationKind                    : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$VersionRequirementTable                  : loaded 2 times (x 103B)
Class org.jetbrains.kotlin.protobuf.LazyField                                         : loaded 2 times (x 79B)
Class org.jetbrains.kotlin.load.kotlin.header.ReadKotlinClassHeaderAnnotationVisitor  : loaded 2 times (x 76B)
Class kotlin.sequences.SequencesKt__SequencesJVMKt                                    : loaded 2 times (x 67B)
Class kotlin.collections.AbstractList                                                 : loaded 2 times (x 193B)
Class kotlin.collections.AbstractCollection                                           : loaded 2 times (x 113B)
Class org.jetbrains.org.objectweb.asm.TypeReference                                   : loaded 2 times (x 76B)
Class jdk.internal.jimage.ImageReader$Directory                                       : loaded 2 times (x 80B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Expression$ConstantValue$1               : loaded 2 times (x 70B)
Class kotlin.collections.ArrayAsCollection                                            : loaded 2 times (x 101B)
Class org.jetbrains.kotlin.descriptors.DescriptorVisibilities$10                      : loaded 2 times (x 72B)
Class org.jetbrains.org.objectweb.asm.tree.LineNumberNode                             : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$TypeParameter$1                          : loaded 2 times (x 140B)
Class kotlin.LazyKt__LazyKt                                                           : loaded 2 times (x 67B)
Class kotlin.text.StringsKt__StringBuilderJVMKt                                       : loaded 2 times (x 67B)
Class kotlin.jvm.internal.CollectionToArray                                           : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.com.google.common.base.Function                            : loaded 2 times (x 66B)
Class [Lcom.google.gson.stream.JsonToken;                                             : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.descriptors.DescriptorVisibilities$11                      : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.metadata.deserialization.BinaryVersion$Companion           : loaded 2 times (x 67B)
Class kotlin.UNINITIALIZED_VALUE                                                      : loaded 2 times (x 67B)
Class org.gradle.internal.classloader.FilteringClassLoader                            : loaded 2 times (x 101B)
Class org.gradle.internal.classloader.VisitableURLClassLoader$InstrumentingVisitableURLClassLoader: loaded 2 times (x 119B)
Class org.jetbrains.kotlin.descriptors.DescriptorVisibilities$12                      : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$ClassOrBuilder                           : loaded 2 times (x 66B)
Class kotlin.comparisons.ComparisonsKt__ComparisonsKt                                 : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$ValueParameter                           : loaded 2 times (x 124B)
Class kotlin.ranges.RangesKt__RangesKt                                                : loaded 2 times (x 67B)
Class kotlin.collections.SetsKt___SetsKt                                              : loaded 2 times (x 67B)
Class kotlin.sequences.SequencesKt__SequencesKt                                       : loaded 2 times (x 67B)
Class kotlin.text.StringsKt__StringsKt                                                : loaded 2 times (x 67B)
Class org.gradle.internal.classloader.VisitableURLClassLoader$Spec                    : loaded 2 times (x 70B)
Class org.gradle.internal.classpath.DefaultClassPath                                  : loaded 2 times (x 86B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Annotation$Argument$ValueOrBuilder       : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$TypeAlias$1                              : loaded 2 times (x 140B)
Class org.jetbrains.org.objectweb.asm.Handler                                         : loaded 2 times (x 68B)
Class kotlin.comparisons.ComparisonsKt                                                : loaded 2 times (x 67B)
Class kotlin.comparisons.ComparisonsKt___ComparisonsKt                                : loaded 2 times (x 67B)
Class kotlin.sequences.FilteringSequence                                              : loaded 2 times (x 71B)
Class [Lorg.jetbrains.kotlin.build.report.metrics.GradleBuildTime;                    : loaded 2 times (x 65B)
Class kotlin.NotImplementedError                                                      : loaded 2 times (x 78B)
Class jdk.internal.jimage.decompressor.ZipDecompressorFactory                         : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Effect$1                                 : loaded 2 times (x 140B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Visibility                               : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$ConstructorOrBuilder                     : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.metadata.jvm.JvmProtoBuf                                   : loaded 2 times (x 67B)
Class Build_gradle$7                                                                  : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.config.ApiVersion$Companion                                : loaded 2 times (x 67B)
Class org.gradle.internal.classloader.ClassLoaderSpec                                 : loaded 2 times (x 67B)
Class kotlin.jvm.internal.FunctionReferenceImpl                                       : loaded 2 times (x 118B)
Class org.jetbrains.kotlin.descriptors.DeclarationDescriptorWithVisibility            : loaded 2 times (x 66B)
Class org.jetbrains.org.objectweb.asm.tree.AbstractInsnNode                           : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.protobuf.SmallSortedMap                                    : loaded 2 times (x 126B)
Class kotlin.collections.CollectionsKt___CollectionsJvmKt                             : loaded 2 times (x 67B)
Class settings_4rq5cjrcdj0mscfkcpdclh2wk$_run_closure1                                : loaded 2 times (x 135B)
Class kotlin.Unit                                                                     : loaded 2 times (x 67B)
Class kotlin.TuplesKt                                                                 : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.protobuf.SmallSortedMap$EmptySet                           : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.protobuf.MessageLite                                       : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.load.kotlin.header.KotlinClassHeader$Kind$Companion        : loaded 2 times (x 67B)
Class kotlin.sequences.SequencesKt___SequencesJvmKt                                   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.cli.common.ModuleVisibilityHelperImpl                      : loaded 2 times (x 70B)
Class kotlin.ranges.RangesKt                                                          : loaded 2 times (x 67B)
Class org.gradle.internal.classloader.VisitableURLClassLoader                         : loaded 2 times (x 113B)
Class kotlin.collections.ArraysUtilJVM                                                : loaded 2 times (x 67B)
Class kotlin.jvm.functions.Function0                                                  : loaded 2 times (x 66B)
Class Settings_gradle                                                                 : loaded 2 times (x 124B)
Class org.gradle.internal.Cast                                                        : loaded 2 times (x 67B)
Class org.gradle.api.Action                                                           : loaded 2 times (x 66B)
Class [Lorg.jetbrains.kotlin.metadata.ProtoBuf$VersionRequirement$VersionKind;        : loaded 2 times (x 65B)
Class org.jetbrains.org.objectweb.asm.tree.TypeInsnNode                               : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.protobuf.CodedOutputStream                                 : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Annotation                               : loaded 2 times (x 105B)
Class kotlin.jvm.functions.Function1                                                  : loaded 2 times (x 66B)
Class jdk.internal.jimage.decompressor.ZipDecompressor                                : loaded 2 times (x 71B)
Class org.gradle.internal.agents.InstrumentingClassLoader                             : loaded 2 times (x 66B)
Class [Lorg.jetbrains.kotlin.metadata.jvm.JvmProtoBuf$StringTableTypes$Record$Operation;: loaded 2 times (x 65B)
Class [Lorg.jetbrains.kotlin.load.kotlin.header.KotlinClassHeader$Kind;               : loaded 2 times (x 65B)
Class kotlin.jvm.functions.Function2                                                  : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.descriptors.SourceElement                                  : loaded 2 times (x 66B)
Class org.gradle.internal.classpath.DefaultClassPath$ImmutableUniqueList$Builder      : loaded 2 times (x 71B)
Class com.google.gson.stream.JsonToken                                                : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.serialization.deserialization.ProtoEnumFlags               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.protobuf.Internal$EnumLite                                 : loaded 2 times (x 66B)
Class kotlin.jvm.functions.Function3                                                  : loaded 2 times (x 66B)
Class kotlin.collections.MapsKt___MapsJvmKt                                           : loaded 2 times (x 67B)
Class org.jetbrains.org.objectweb.asm.tree.IincInsnNode                               : loaded 2 times (x 74B)
Class org.jetbrains.org.objectweb.asm.tree.MultiANewArrayInsnNode                     : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.protobuf.RopeByteString                                    : loaded 2 times (x 106B)
Class kotlin.collections.EmptySet                                                     : loaded 2 times (x 118B)
Class org.jetbrains.kotlin.metadata.jvm.deserialization.JvmNameResolverBase           : loaded 2 times (x 76B)
Class kotlin.collections.CollectionsKt___CollectionsKt                                : loaded 2 times (x 67B)
Class org.gradle.api.GradleException                                                  : loaded 2 times (x 78B)
Class kotlin.reflect.KDeclarationContainer                                            : loaded 2 times (x 66B)
Class org.jetbrains.org.objectweb.asm.tree.LocalVariableAnnotationNode                : loaded 2 times (x 78B)
Class org.jetbrains.org.objectweb.asm.SymbolTable$Entry                               : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.metadata.deserialization.BinaryVersion                     : loaded 2 times (x 70B)
Class kotlin.text.StringsKt___StringsJvmKt                                            : loaded 2 times (x 67B)
Class jdk.internal.jimage.ImageStrings                                                : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$EnumEntry$1                              : loaded 2 times (x 140B)
Class org.jetbrains.org.objectweb.asm.tree.InvokeDynamicInsnNode                      : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Type$Argument$Projection                 : loaded 2 times (x 78B)
Class kotlin.collections.MapsKt___MapsKt                                              : loaded 2 times (x 67B)
Class kotlin.io.FilesKt                                                               : loaded 2 times (x 67B)
Class kotlin.collections.ArraysKt___ArraysKt                                          : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.metadata.jvm.JvmProtoBuf$JvmPropertySignature$Builder      : loaded 2 times (x 137B)
Class [Lorg.jetbrains.org.objectweb.asm.Symbol;                                       : loaded 2 times (x 65B)
Class kotlin.collections.MapsKt__MapWithDefaultKt                                     : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$VersionRequirementTable$1                : loaded 2 times (x 140B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Contract                                 : loaded 2 times (x 103B)
Class kotlin.collections.CollectionsKt__MutableCollectionsJVMKt                       : loaded 2 times (x 67B)
Class kotlin.enums.EnumEntriesKt                                                      : loaded 2 times (x 67B)
Class kotlin.text.StringsKt___StringsKt                                               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$TypeParameter$Variance$1                 : loaded 2 times (x 70B)
Class org.gradle.internal.IoActions                                                   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.metadata.jvm.deserialization.JvmNameResolverBase$WhenMappings: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.descriptors.Visibilities$Local                             : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.protobuf.WireFormat                                        : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.metadata.jvm.deserialization.BitEncoding                   : loaded 2 times (x 67B)
Class org.jetbrains.org.objectweb.asm.Attribute                                       : loaded 2 times (x 71B)
Class org.jetbrains.org.objectweb.asm.Label                                           : loaded 2 times (x 68B)
Class org.gradle.internal.classloader.SystemClassLoaderSpec                           : loaded 2 times (x 67B)
Class [Lorg.jetbrains.kotlin.metadata.ProtoBuf$Effect$EffectType;                     : loaded 2 times (x 65B)
Class kotlin.reflect.KAnnotatedElement                                                : loaded 2 times (x 66B)
Class org.jetbrains.org.objectweb.asm.Frame                                           : loaded 2 times (x 69B)
Class kotlin.jvm.internal.Intrinsics                                                  : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Type$Argument                            : loaded 2 times (x 106B)
Class org.jetbrains.kotlin.load.kotlin.FileBasedKotlinClass$InnerClassesInfo          : loaded 2 times (x 70B)
Class kotlin.collections.MapsKt                                                       : loaded 2 times (x 67B)
Class kotlin.collections.CollectionsKt__IterablesKt                                   : loaded 2 times (x 67B)
Class _BuildScript_$_run_closure1                                                     : loaded 2 times (x 128B)
Class jdk.internal.jrtfs.JrtFileSystem                                                : loaded 2 times (x 98B)
Class org.jetbrains.kotlin.resolve.scopes.receivers.Receiver                          : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Modality$1                               : loaded 2 times (x 70B)
Class org.jetbrains.org.objectweb.asm.tree.TypeAnnotationNode                         : loaded 2 times (x 77B)
Class kotlin.KotlinNullPointerException                                               : loaded 2 times (x 79B)
Class org.jetbrains.kotlin.cli.common.CompilerSystemProperties$Companion              : loaded 2 times (x 67B)
Class jdk.internal.jimage.decompressor.CompressedResourceHeader                       : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.metadata.jvm.JvmProtoBuf$1                                 : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$VersionRequirement                       : loaded 2 times (x 112B)
Class kotlin.ranges.RangesKt___RangesKt                                               : loaded 2 times (x 67B)
Class org.jetbrains.org.objectweb.asm.Opcodes                                         : loaded 2 times (x 66B)
Class [Lorg.jetbrains.org.objectweb.asm.AnnotationVisitor;                            : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.descriptors.annotations.Annotated                          : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.descriptors.SourceElement$1                                : loaded 2 times (x 70B)
Class kotlin.collections.ArraysKt                                                     : loaded 2 times (x 67B)
Class com.google.gson.stream.JsonReader                                               : loaded 2 times (x 92B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Expression$1                             : loaded 2 times (x 140B)
Class org.jetbrains.kotlin.descriptors.DescriptorVisibility                           : loaded 2 times (x 74B)
Class kotlin.collections.SetsKt__SetsKt                                               : loaded 2 times (x 67B)
Class kotlin.LazyKt                                                                   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$ExpressionOrBuilder                      : loaded 2 times (x 66B)
Class [Lorg.jetbrains.kotlin.metadata.ProtoBuf$Type$Argument$Projection;              : loaded 2 times (x 65B)
Class org.jetbrains.org.objectweb.asm.RecordComponentWriter                           : loaded 2 times (x 74B)
Class kotlin.collections.ArraysKt__ArraysJVMKt                                        : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.cli.common.messages.MessageCollector$Companion$NONE$1      : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$EnumEntryOrBuilder                       : loaded 2 times (x 66B)
Class kotlin.jvm.internal.CallableReference$NoReceiver                                : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.descriptors.ValidateableDescriptor                         : loaded 2 times (x 66B)
Class org.jetbrains.org.objectweb.asm.MethodWriter                                    : loaded 2 times (x 102B)
Class org.jetbrains.kotlin.metadata.jvm.JvmProtoBuf$JvmPropertySignature$1            : loaded 2 times (x 140B)
Class kotlin.text.StringsKt__StringBuilderKt                                          : loaded 2 times (x 67B)
Class kotlin.Lazy                                                                     : loaded 2 times (x 66B)
Class kotlin.NoWhenBranchMatchedException                                             : loaded 2 times (x 78B)
Class [Lorg.jetbrains.kotlin.metadata.ProtoBuf$Expression$ConstantValue;              : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Effect                                   : loaded 2 times (x 109B)
Class org.jetbrains.org.objectweb.asm.tree.InsnNode                                   : loaded 2 times (x 74B)
Class [Lkotlin.jvm.functions.Function1;                                               : loaded 2 times (x 65B)
Class org.jetbrains.org.objectweb.asm.RecordComponentVisitor                          : loaded 2 times (x 73B)
Class org.jetbrains.kotlin.descriptors.Visibility                                     : loaded 2 times (x 75B)
Class org.jetbrains.org.objectweb.asm.MethodTooLargeException                         : loaded 2 times (x 79B)
Class org.jetbrains.kotlin.protobuf.RopeByteString$RopeInputStream                    : loaded 2 times (x 88B)
Class org.jetbrains.kotlin.protobuf.CodedInputStream                                  : loaded 2 times (x 68B)
Class org.jetbrains.org.objectweb.asm.Context                                         : loaded 2 times (x 68B)
Class kotlin.sequences.ConstrainedOnceSequence                                        : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$1                                        : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.mpp.DeclarationSymbolMarker                                : loaded 2 times (x 66B)
Class org.jetbrains.org.objectweb.asm.CurrentFrame                                    : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.protobuf.MessageLite$Builder                               : loaded 2 times (x 66B)
Class kotlin.jvm.internal.markers.KMappedMarker                                       : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Annotation$Argument$Value$Type           : loaded 2 times (x 78B)
Class org.gradle.internal.installation.CurrentGradleInstallationLocator               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$VersionRequirement$Level$1               : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.protobuf.FieldSet$1                                        : loaded 2 times (x 67B)
Class org.jetbrains.org.objectweb.asm.tree.LdcInsnNode                                : loaded 2 times (x 74B)
Class org.gradle.internal.service.DefaultServiceLocator                               : loaded 2 times (x 79B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Class$Kind$1                             : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.protobuf.SmallSortedMap$Entry                              : loaded 2 times (x 84B)
Class org.jetbrains.org.objectweb.asm.SymbolTable                                     : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Package$1                                : loaded 2 times (x 140B)
Class org.jetbrains.org.objectweb.asm.tree.UnsupportedClassVersionException           : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.util.capitalizeDecapitalize.CapitalizeDecapitalizeKt       : loaded 2 times (x 67B)
Class jdk.internal.jrtfs.JrtDirectoryStream                                           : loaded 2 times (x 85B)
Class org.gradle.internal.installation.CurrentGradleInstallation                      : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Annotation$ArgumentOrBuilder             : loaded 2 times (x 66B)
Class org.jetbrains.org.objectweb.asm.ModuleWriter                                    : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.protobuf.FieldSet$FieldDescriptorLite                      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.protobuf.AbstractMessageLite                               : loaded 2 times (x 94B)
Class org.gradle.util.internal.GUtil                                                  : loaded 2 times (x 67B)
Class settings_4rq5cjrcdj0mscfkcpdclh2wk                                              : loaded 2 times (x 175B)
Class [Lorg.jetbrains.org.objectweb.asm.Label;                                        : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.metadata.jvm.JvmProtoBuf$StringTableTypes$Record           : loaded 2 times (x 115B)
Class org.jetbrains.kotlin.protobuf.FieldSet                                          : loaded 2 times (x 68B)
Class kotlin.io.NoSuchFileException                                                   : loaded 2 times (x 78B)
Class kotlin.collections.CollectionsKt                                                : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Annotation$Argument$Value$Type$1         : loaded 2 times (x 70B)
Class [Lorg.jetbrains.kotlin.metadata.ProtoBuf$MemberKind;                            : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Package                                  : loaded 2 times (x 125B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$TypeTableOrBuilder                       : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$TypeOrBuilder                            : loaded 2 times (x 66B)
Class org.gradle.internal.installation.GradleInstallation                             : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.metadata.deserialization.ProtoTypeTableUtilKt              : loaded 2 times (x 67B)
Class [Lorg.jetbrains.kotlin.descriptors.DescriptorVisibility;                        : loaded 2 times (x 65B)
Class kotlin.collections.CollectionsKt__IteratorsJVMKt$iterator$1                     : loaded 2 times (x 75B)
Class jdk.internal.jimage.decompressor.ResourceDecompressor$StringsProvider           : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Annotation$Builder                       : loaded 2 times (x 132B)
Class kotlin.jvm.KotlinReflectionNotSupportedError                                    : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.metadata.jvm.JvmProtoBuf$StringTableTypes$Record$Operation : loaded 2 times (x 78B)

---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
OS uptime: 15 days 23:22 hours
Hyper-V role detected

CPU: total 4 (initial active 4) (4 cores per cpu, 1 threads per core) family 6 model 58 stepping 9 microcode 0x21, cx8, cmov, fxsr, mmx, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, tsc, tscinvbit, avx, aes, erms, clmul, vzeroupper, clflush, hv, rdtscp, f16c
Processor Information for all 4 processors :
  Max Mhz: 3101, Current Mhz: 3101, Mhz Limit: 3101

Memory: 4k page, system-wide physical 8161M (377M free)
TotalPageFile size 32737M (AvailPageFile size 6M)
current process WorkingSet (physical memory assigned to process): 856M, peak: 896M
current process commit charge ("private bytes"): 1058M, peak: 1061M

vm_info: OpenJDK 64-Bit Server VM (21.0.5+-13047016-b750.29) for windows-amd64 JRE (21.0.5+-13047016-b750.29), built on 2025-02-11T21:12:39Z by "builder" with MS VC++ 16.10 / 16.11 (VS2019)

END.
