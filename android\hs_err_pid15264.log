#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 2687008 bytes. Error detail: Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:168), pid=15264, tid=15224
#
# JRE version: OpenJDK Runtime Environment (21.0.5) (build 21.0.5+-13047016-b750.29)
# Java VM: OpenJDK 64-Bit Server VM (21.0.5+-13047016-b750.29, mixed mode, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -XX:MaxMetaspaceSize=1024m -Xmx4096m -Dfile.encoding=windows-1252 -Duser.country=BR -Duser.language=pt -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.14.1-bin\baw1sv0jfoi8rxs14qo3h49cs\gradle-8.14.1\lib\agents\gradle-instrumentation-agent-8.14.1.jar org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.14.1

Host: Intel(R) Xeon(R) CPU E3-1220 V2 @ 3.10GHz, 4 cores, 7G,  Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
Time: Wed Aug  6 13:15:40 2025 Hora oficial do Brasil elapsed time: 21.905986 seconds (0d 0h 0m 21s)

---------------  T H R E A D  ---------------

Current thread (0x000001be49a685f0):  JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=15224, stack(0x00000074e6600000,0x00000074e6700000) (1024K)]


Current CompileTask:
C2:21906 9112       4       org.gradle.internal.instantiation.generator.AbstractClassGenerator::inspectType (602 bytes)

Stack: [0x00000074e6600000,0x00000074e6700000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6cfb29]
V  [jvm.dll+0x85df93]
V  [jvm.dll+0x8604ee]
V  [jvm.dll+0x860bd3]
V  [jvm.dll+0x27e6b6]
V  [jvm.dll+0xbff6d]
V  [jvm.dll+0xc04a3]
V  [jvm.dll+0x3b5ffb]
V  [jvm.dll+0x381f97]
V  [jvm.dll+0x38140a]
V  [jvm.dll+0x247c62]
V  [jvm.dll+0x247231]
V  [jvm.dll+0x1c5ee4]
V  [jvm.dll+0x25697c]
V  [jvm.dll+0x254ec6]
V  [jvm.dll+0x3f0ce6]
V  [jvm.dll+0x806368]
V  [jvm.dll+0x6ce3fd]
C  [ucrtbase.dll+0x21bb2]
C  [KERNEL32.DLL+0x17374]
C  [ntdll.dll+0x4cc91]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000001be53619d40, length=51, elements={
0x000001be2e0d1400, 0x000001be49a85c90, 0x000001be49a86450, 0x000001be49a89020,
0x000001be49a657b0, 0x000001be49a65e10, 0x000001be49a67870, 0x000001be49a685f0,
0x000001be49a69080, 0x000001be49b8de70, 0x000001be49decad0, 0x000001be4f768560,
0x000001be4f7f6a00, 0x000001be4f960890, 0x000001be4f7dabe0, 0x000001be4f870a00,
0x000001be4f8a64d0, 0x000001be4f8a9610, 0x000001be4f8a9c70, 0x000001be4f8c8920,
0x000001be4f8c8290, 0x000001be4f8c8fb0, 0x000001be4f8c9640, 0x000001be4f8c61c0,
0x000001be4f8c6850, 0x000001be4f8c6ee0, 0x000001be4f8c7c00, 0x000001be4f8c7570,
0x000001be5233a120, 0x000001be5233a7b0, 0x000001be5233e950, 0x000001be5233b4d0,
0x000001be5233bb60, 0x000001be5233dc30, 0x000001be52339400, 0x000001be5233f670,
0x000001be52339a90, 0x000001be52338d70, 0x000001be523386e0, 0x000001be5233c1f0,
0x000001be5233ae40, 0x000001be5233c880, 0x000001be5233cf10, 0x000001be5233d5a0,
0x000001be4fe49eb0, 0x000001be4fe4cca0, 0x000001be4fe47de0, 0x000001be4fe4a540,
0x000001be4fe4abd0, 0x000001be4fe49820, 0x000001be4fe4b8f0
}

Java Threads: ( => current thread )
  0x000001be2e0d1400 JavaThread "main"                              [_thread_blocked, id=14704, stack(0x00000074e5700000,0x00000074e5800000) (1024K)]
  0x000001be49a85c90 JavaThread "Reference Handler"          daemon [_thread_blocked, id=13628, stack(0x00000074e6000000,0x00000074e6100000) (1024K)]
  0x000001be49a86450 JavaThread "Finalizer"                  daemon [_thread_blocked, id=17304, stack(0x00000074e6100000,0x00000074e6200000) (1024K)]
  0x000001be49a89020 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=12900, stack(0x00000074e6200000,0x00000074e6300000) (1024K)]
  0x000001be49a657b0 JavaThread "Attach Listener"            daemon [_thread_blocked, id=13704, stack(0x00000074e6300000,0x00000074e6400000) (1024K)]
  0x000001be49a65e10 JavaThread "Service Thread"             daemon [_thread_blocked, id=13396, stack(0x00000074e6400000,0x00000074e6500000) (1024K)]
  0x000001be49a67870 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=13036, stack(0x00000074e6500000,0x00000074e6600000) (1024K)]
=>0x000001be49a685f0 JavaThread "C2 CompilerThread0"         daemon [_thread_in_native, id=15224, stack(0x00000074e6600000,0x00000074e6700000) (1024K)]
  0x000001be49a69080 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=9368, stack(0x00000074e6700000,0x00000074e6800000) (1024K)]
  0x000001be49b8de70 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=8824, stack(0x00000074e6800000,0x00000074e6900000) (1024K)]
  0x000001be49decad0 JavaThread "Notification Thread"        daemon [_thread_blocked, id=7136, stack(0x00000074e6900000,0x00000074e6a00000) (1024K)]
  0x000001be4f768560 JavaThread "Daemon health stats"               [_thread_blocked, id=9564, stack(0x00000074e6e00000,0x00000074e6f00000) (1024K)]
  0x000001be4f7f6a00 JavaThread "Incoming local TCP Connector on port 54671"        [_thread_in_native, id=14380, stack(0x00000074e6f00000,0x00000074e7000000) (1024K)]
  0x000001be4f960890 JavaThread "Daemon periodic checks"            [_thread_blocked, id=12980, stack(0x00000074e7000000,0x00000074e7100000) (1024K)]
  0x000001be4f7dabe0 JavaThread "Daemon"                            [_thread_blocked, id=12744, stack(0x00000074e7100000,0x00000074e7200000) (1024K)]
  0x000001be4f870a00 JavaThread "Handler for socket connection from /127.0.0.1:54671 to /127.0.0.1:54672"        [_thread_in_native, id=420, stack(0x00000074e7200000,0x00000074e7300000) (1024K)]
  0x000001be4f8a64d0 JavaThread "Cancel handler"                    [_thread_blocked, id=13692, stack(0x00000074e7300000,0x00000074e7400000) (1024K)]
  0x000001be4f8a9610 JavaThread "Daemon worker"                     [_thread_in_Java, id=7004, stack(0x00000074e7400000,0x00000074e7500000) (1024K)]
  0x000001be4f8a9c70 JavaThread "Asynchronous log dispatcher for DefaultDaemonConnection: socket connection from /127.0.0.1:54671 to /127.0.0.1:54672"        [_thread_blocked, id=5520, stack(0x00000074e7500000,0x00000074e7600000) (1024K)]
  0x000001be4f8c8920 JavaThread "Stdin handler"                     [_thread_blocked, id=1088, stack(0x00000074e7600000,0x00000074e7700000) (1024K)]
  0x000001be4f8c8290 JavaThread "Daemon client event forwarder"        [_thread_blocked, id=10280, stack(0x00000074e7700000,0x00000074e7800000) (1024K)]
  0x000001be4f8c8fb0 JavaThread "Cache worker for journal cache (C:\Users\<USER>\.gradle\caches\journal-1)"        [_thread_blocked, id=9244, stack(0x00000074e6c00000,0x00000074e6d00000) (1024K)]
  0x000001be4f8c9640 JavaThread "File lock request listener"        [_thread_in_native, id=3560, stack(0x00000074e7800000,0x00000074e7900000) (1024K)]
  0x000001be4f8c61c0 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\.gradle\caches\8.14.1\fileHashes)"        [_thread_blocked, id=10864, stack(0x00000074e7900000,0x00000074e7a00000) (1024K)]
  0x000001be4f8c6850 JavaThread "Cache worker for file hash cache (F:\PROJETO\React\EscolaSabatina\android\.gradle\8.14.1\fileHashes)"        [_thread_blocked, id=13428, stack(0x00000074e7b00000,0x00000074e7c00000) (1024K)]
  0x000001be4f8c6ee0 JavaThread "Cache worker for Build Output Cleanup Cache (F:\PROJETO\React\EscolaSabatina\android\.gradle\buildOutputCleanup)"        [_thread_blocked, id=4032, stack(0x00000074e7c00000,0x00000074e7d00000) (1024K)]
  0x000001be4f8c7c00 JavaThread "File watcher server"        daemon [_thread_in_native, id=16920, stack(0x00000074e7d00000,0x00000074e7e00000) (1024K)]
  0x000001be4f8c7570 JavaThread "File watcher consumer"      daemon [_thread_blocked, id=10528, stack(0x00000074e7e00000,0x00000074e7f00000) (1024K)]
  0x000001be5233a120 JavaThread "jar transforms"                    [_thread_blocked, id=13740, stack(0x00000074e7f00000,0x00000074e8000000) (1024K)]
  0x000001be5233a7b0 JavaThread "Cache worker for checksums cache (F:\PROJETO\React\EscolaSabatina\android\.gradle\8.14.1\checksums)"        [_thread_blocked, id=11520, stack(0x00000074e8000000,0x00000074e8100000) (1024K)]
  0x000001be5233e950 JavaThread "Cache worker for file content cache (C:\Users\<USER>\.gradle\caches\8.14.1\fileContent)"        [_thread_blocked, id=10640, stack(0x00000074e7a00000,0x00000074e7b00000) (1024K)]
  0x000001be5233b4d0 JavaThread "Cache worker for cache directory md-supplier (C:\Users\<USER>\.gradle\caches\8.14.1\md-supplier)"        [_thread_blocked, id=14980, stack(0x00000074e8100000,0x00000074e8200000) (1024K)]
  0x000001be5233bb60 JavaThread "Cache worker for cache directory md-rule (C:\Users\<USER>\.gradle\caches\8.14.1\md-rule)"        [_thread_blocked, id=4140, stack(0x00000074e8200000,0x00000074e8300000) (1024K)]
  0x000001be5233dc30 JavaThread "Cache worker for Build Output Cleanup Cache (F:\PROJETO\React\EscolaSabatina\node_modules\@react-native\gradle-plugin\.gradle\buildOutputCleanup)"        [_thread_blocked, id=12112, stack(0x00000074e8400000,0x00000074e8500000) (1024K)]
  0x000001be52339400 JavaThread "Unconstrained build operations"        [_thread_blocked, id=8912, stack(0x00000074e8300000,0x00000074e8400000) (1024K)]
  0x000001be5233f670 JavaThread "Unconstrained build operations Thread 2"        [_thread_blocked, id=11832, stack(0x00000074e8500000,0x00000074e8600000) (1024K)]
  0x000001be52339a90 JavaThread "Unconstrained build operations Thread 3"        [_thread_blocked, id=14736, stack(0x00000074e8600000,0x00000074e8700000) (1024K)]
  0x000001be52338d70 JavaThread "Unconstrained build operations Thread 4"        [_thread_blocked, id=17156, stack(0x00000074e8700000,0x00000074e8800000) (1024K)]
  0x000001be523386e0 JavaThread "Unconstrained build operations Thread 5"        [_thread_blocked, id=12748, stack(0x00000074e8800000,0x00000074e8900000) (1024K)]
  0x000001be5233c1f0 JavaThread "Unconstrained build operations Thread 6"        [_thread_blocked, id=1440, stack(0x00000074e8900000,0x00000074e8a00000) (1024K)]
  0x000001be5233ae40 JavaThread "Unconstrained build operations Thread 7"        [_thread_blocked, id=15660, stack(0x00000074e8a00000,0x00000074e8b00000) (1024K)]
  0x000001be5233c880 JavaThread "Unconstrained build operations Thread 8"        [_thread_blocked, id=10496, stack(0x00000074e8b00000,0x00000074e8c00000) (1024K)]
  0x000001be5233cf10 JavaThread "Unconstrained build operations Thread 9"        [_thread_blocked, id=15956, stack(0x00000074e8c00000,0x00000074e8d00000) (1024K)]
  0x000001be5233d5a0 JavaThread "Unconstrained build operations Thread 10"        [_thread_blocked, id=7000, stack(0x00000074e8d00000,0x00000074e8e00000) (1024K)]
  0x000001be4fe49eb0 JavaThread "Unconstrained build operations Thread 11"        [_thread_blocked, id=15508, stack(0x00000074e8e00000,0x00000074e8f00000) (1024K)]
  0x000001be4fe4cca0 JavaThread "Unconstrained build operations Thread 12"        [_thread_blocked, id=11688, stack(0x00000074e8f00000,0x00000074e9000000) (1024K)]
  0x000001be4fe47de0 JavaThread "Unconstrained build operations Thread 13"        [_thread_blocked, id=14400, stack(0x00000074e9000000,0x00000074e9100000) (1024K)]
  0x000001be4fe4a540 JavaThread "Unconstrained build operations Thread 14"        [_thread_blocked, id=16092, stack(0x00000074e9100000,0x00000074e9200000) (1024K)]
  0x000001be4fe4abd0 JavaThread "Unconstrained build operations Thread 15"        [_thread_blocked, id=9348, stack(0x00000074e9200000,0x00000074e9300000) (1024K)]
  0x000001be4fe49820 JavaThread "build event listener"              [_thread_blocked, id=5788, stack(0x00000074e9300000,0x00000074e9400000) (1024K)]
  0x000001be4fe4b8f0 JavaThread "Memory manager"                    [_thread_blocked, id=15964, stack(0x00000074e9400000,0x00000074e9500000) (1024K)]
Total: 51

Other Threads:
  0x000001be49753110 VMThread "VM Thread"                           [id=13668, stack(0x00000074e5f00000,0x00000074e6000000) (1024K)]
  0x000001be49742110 WatcherThread "VM Periodic Task Thread"        [id=15016, stack(0x00000074e5e00000,0x00000074e5f00000) (1024K)]
  0x000001be301da940 WorkerThread "GC Thread#0"                     [id=16968, stack(0x00000074e5900000,0x00000074e5a00000) (1024K)]
  0x000001be4e0bda40 WorkerThread "GC Thread#1"                     [id=9172, stack(0x00000074e6a00000,0x00000074e6b00000) (1024K)]
  0x000001be4e12cee0 WorkerThread "GC Thread#2"                     [id=7624, stack(0x00000074e6b00000,0x00000074e6c00000) (1024K)]
  0x000001be4eb4b7e0 WorkerThread "GC Thread#3"                     [id=16168, stack(0x00000074e6d00000,0x00000074e6e00000) (1024K)]
  0x000001be301eb920 ConcurrentGCThread "G1 Main Marker"            [id=8072, stack(0x00000074e5a00000,0x00000074e5b00000) (1024K)]
  0x000001be301ec420 WorkerThread "G1 Conc#0"                       [id=1996, stack(0x00000074e5b00000,0x00000074e5c00000) (1024K)]
  0x000001be496150c0 ConcurrentGCThread "G1 Refine#0"               [id=8608, stack(0x00000074e5c00000,0x00000074e5d00000) (1024K)]
  0x000001be49616b50 ConcurrentGCThread "G1 Service"                [id=14428, stack(0x00000074e5d00000,0x00000074e5e00000) (1024K)]
Total: 10

Threads with active compile tasks:
C2 CompilerThread0  21941 9112       4       org.gradle.internal.instantiation.generator.AbstractClassGenerator::inspectType (602 bytes)
C1 CompilerThread0  21941 9188       3       java.beans.PropertyDescriptor::<init> (302 bytes)
Total: 2

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000700000000, size: 4096 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) not mapped
Compressed class space mapped at: 0x0000000800000000-0x0000000834000000, reserved size: 872415232
Narrow klass base: 0x0000000800000000, Narrow klass shift: 0, Narrow klass range: 0x34000000

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 4 size 8 Array Of Cards #cards 16 size 48 Howl #buckets 8 coarsen threshold 3686 Howl Bitmap #cards 512 size 80 coarsen threshold 460 Card regions per heap region 1 cards per card region 4096
 CPUs: 4 total, 4 available
 Memory: 8161M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 2M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 128M
 Heap Max Capacity: 4G
 Pre-touch: Disabled
 Parallel Workers: 4
 Concurrent Workers: 1
 Concurrent Refinement Workers: 4
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 116736K, used 86522K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 11 young (22528K), 2 survivors (4096K)
 Metaspace       used 85630K, committed 87680K, reserved 983040K
  class space    used 11723K, committed 12736K, reserved 851968K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom
|   0|0x0000000700000000, 0x0000000700200000, 0x0000000700200000|100%|HS|  |TAMS 0x0000000700000000| PB 0x0000000700000000| Complete 
|   1|0x0000000700200000, 0x0000000700400000, 0x0000000700400000|100%|HC|  |TAMS 0x0000000700200000| PB 0x0000000700200000| Complete 
|   2|0x0000000700400000, 0x0000000700600000, 0x0000000700600000|100%| O|  |TAMS 0x0000000700400000| PB 0x0000000700400000| Untracked 
|   3|0x0000000700600000, 0x0000000700800000, 0x0000000700800000|100%| O|  |TAMS 0x0000000700600000| PB 0x0000000700600000| Untracked 
|   4|0x0000000700800000, 0x0000000700a00000, 0x0000000700a00000|100%| O|  |TAMS 0x0000000700800000| PB 0x0000000700800000| Untracked 
|   5|0x0000000700a00000, 0x0000000700c00000, 0x0000000700c00000|100%| O|  |TAMS 0x0000000700a00000| PB 0x0000000700a00000| Untracked 
|   6|0x0000000700c00000, 0x0000000700e00000, 0x0000000700e00000|100%| O|  |TAMS 0x0000000700c00000| PB 0x0000000700c00000| Untracked 
|   7|0x0000000700e00000, 0x0000000701000000, 0x0000000701000000|100%| O|  |TAMS 0x0000000700e00000| PB 0x0000000700e00000| Untracked 
|   8|0x0000000701000000, 0x0000000701200000, 0x0000000701200000|100%| O|  |TAMS 0x0000000701000000| PB 0x0000000701000000| Untracked 
|   9|0x0000000701200000, 0x0000000701400000, 0x0000000701400000|100%| O|  |TAMS 0x0000000701200000| PB 0x0000000701200000| Untracked 
|  10|0x0000000701400000, 0x0000000701600000, 0x0000000701600000|100%| O|  |TAMS 0x0000000701400000| PB 0x0000000701400000| Untracked 
|  11|0x0000000701600000, 0x0000000701800000, 0x0000000701800000|100%| O|  |TAMS 0x0000000701600000| PB 0x0000000701600000| Untracked 
|  12|0x0000000701800000, 0x0000000701a00000, 0x0000000701a00000|100%| O|  |TAMS 0x0000000701800000| PB 0x0000000701800000| Untracked 
|  13|0x0000000701a00000, 0x0000000701c00000, 0x0000000701c00000|100%| O|  |TAMS 0x0000000701a00000| PB 0x0000000701a00000| Untracked 
|  14|0x0000000701c00000, 0x0000000701e00000, 0x0000000701e00000|100%| O|  |TAMS 0x0000000701c00000| PB 0x0000000701c00000| Untracked 
|  15|0x0000000701e00000, 0x0000000702000000, 0x0000000702000000|100%| O|  |TAMS 0x0000000701e00000| PB 0x0000000701e00000| Untracked 
|  16|0x0000000702000000, 0x0000000702200000, 0x0000000702200000|100%| O|  |TAMS 0x0000000702000000| PB 0x0000000702000000| Untracked 
|  17|0x0000000702200000, 0x0000000702400000, 0x0000000702400000|100%| O|  |TAMS 0x0000000702200000| PB 0x0000000702200000| Untracked 
|  18|0x0000000702400000, 0x0000000702600000, 0x0000000702600000|100%| O|  |TAMS 0x0000000702400000| PB 0x0000000702400000| Untracked 
|  19|0x0000000702600000, 0x0000000702800000, 0x0000000702800000|100%| O|  |TAMS 0x0000000702600000| PB 0x0000000702600000| Untracked 
|  20|0x0000000702800000, 0x0000000702a00000, 0x0000000702a00000|100%| O|  |TAMS 0x0000000702800000| PB 0x0000000702800000| Untracked 
|  21|0x0000000702a00000, 0x0000000702c00000, 0x0000000702c00000|100%| O|  |TAMS 0x0000000702a00000| PB 0x0000000702a00000| Untracked 
|  22|0x0000000702c00000, 0x0000000702e00000, 0x0000000702e00000|100%|HS|  |TAMS 0x0000000702c00000| PB 0x0000000702c00000| Complete 
|  23|0x0000000702e00000, 0x0000000703000000, 0x0000000703000000|100%|HC|  |TAMS 0x0000000702e00000| PB 0x0000000702e00000| Complete 
|  24|0x0000000703000000, 0x0000000703200000, 0x0000000703200000|100%|HS|  |TAMS 0x0000000703000000| PB 0x0000000703000000| Complete 
|  25|0x0000000703200000, 0x0000000703400000, 0x0000000703400000|100%|HC|  |TAMS 0x0000000703200000| PB 0x0000000703200000| Complete 
|  26|0x0000000703400000, 0x0000000703600000, 0x0000000703600000|100%| O|  |TAMS 0x0000000703400000| PB 0x0000000703400000| Untracked 
|  27|0x0000000703600000, 0x0000000703800000, 0x0000000703800000|100%| O|  |TAMS 0x0000000703600000| PB 0x0000000703600000| Untracked 
|  28|0x0000000703800000, 0x0000000703a00000, 0x0000000703a00000|100%| O|Cm|TAMS 0x0000000703800000| PB 0x0000000703800000| Complete 
|  29|0x0000000703a00000, 0x0000000703c00000, 0x0000000703c00000|100%| O|  |TAMS 0x0000000703a00000| PB 0x0000000703a00000| Untracked 
|  30|0x0000000703c00000, 0x0000000703e00000, 0x0000000703e00000|100%| O|  |TAMS 0x0000000703c00000| PB 0x0000000703c00000| Untracked 
|  31|0x0000000703e00000, 0x0000000704000000, 0x0000000704000000|100%| O|  |TAMS 0x0000000703e00000| PB 0x0000000703e00000| Untracked 
|  32|0x0000000704000000, 0x0000000704023438, 0x0000000704200000|  6%| O|  |TAMS 0x0000000704000000| PB 0x0000000704000000| Untracked 
|  33|0x0000000704200000, 0x0000000704200000, 0x0000000704400000|  0%| F|  |TAMS 0x0000000704200000| PB 0x0000000704200000| Untracked 
|  34|0x0000000704400000, 0x0000000704400000, 0x0000000704600000|  0%| F|  |TAMS 0x0000000704400000| PB 0x0000000704400000| Untracked 
|  35|0x0000000704600000, 0x0000000704600000, 0x0000000704800000|  0%| F|  |TAMS 0x0000000704600000| PB 0x0000000704600000| Untracked 
|  36|0x0000000704800000, 0x0000000704800000, 0x0000000704a00000|  0%| F|  |TAMS 0x0000000704800000| PB 0x0000000704800000| Untracked 
|  37|0x0000000704a00000, 0x0000000704a00000, 0x0000000704c00000|  0%| F|  |TAMS 0x0000000704a00000| PB 0x0000000704a00000| Untracked 
|  38|0x0000000704c00000, 0x0000000704c00000, 0x0000000704e00000|  0%| F|  |TAMS 0x0000000704c00000| PB 0x0000000704c00000| Untracked 
|  39|0x0000000704e00000, 0x0000000704e00000, 0x0000000705000000|  0%| F|  |TAMS 0x0000000704e00000| PB 0x0000000704e00000| Untracked 
|  40|0x0000000705000000, 0x0000000705000000, 0x0000000705200000|  0%| F|  |TAMS 0x0000000705000000| PB 0x0000000705000000| Untracked 
|  41|0x0000000705200000, 0x0000000705200000, 0x0000000705400000|  0%| F|  |TAMS 0x0000000705200000| PB 0x0000000705200000| Untracked 
|  42|0x0000000705400000, 0x000000070545b5c0, 0x0000000705600000| 17%| S|CS|TAMS 0x0000000705400000| PB 0x0000000705400000| Complete 
|  43|0x0000000705600000, 0x0000000705800000, 0x0000000705800000|100%| S|CS|TAMS 0x0000000705600000| PB 0x0000000705600000| Complete 
|  44|0x0000000705800000, 0x0000000705800000, 0x0000000705a00000|  0%| F|  |TAMS 0x0000000705800000| PB 0x0000000705800000| Untracked 
|  45|0x0000000705a00000, 0x0000000705a00000, 0x0000000705c00000|  0%| F|  |TAMS 0x0000000705a00000| PB 0x0000000705a00000| Untracked 
|  46|0x0000000705c00000, 0x0000000705c00000, 0x0000000705e00000|  0%| F|  |TAMS 0x0000000705c00000| PB 0x0000000705c00000| Untracked 
|  47|0x0000000705e00000, 0x0000000705e68bf0, 0x0000000706000000| 20%| E|  |TAMS 0x0000000705e00000| PB 0x0000000705e00000| Complete 
|  48|0x0000000706000000, 0x0000000706200000, 0x0000000706200000|100%| E|CS|TAMS 0x0000000706000000| PB 0x0000000706000000| Complete 
|  49|0x0000000706200000, 0x0000000706400000, 0x0000000706400000|100%| E|CS|TAMS 0x0000000706200000| PB 0x0000000706200000| Complete 
|  50|0x0000000706400000, 0x0000000706600000, 0x0000000706600000|100%| E|CS|TAMS 0x0000000706400000| PB 0x0000000706400000| Complete 
|  51|0x0000000706600000, 0x0000000706800000, 0x0000000706800000|100%| E|CS|TAMS 0x0000000706600000| PB 0x0000000706600000| Complete 
|  52|0x0000000706800000, 0x0000000706a00000, 0x0000000706a00000|100%| E|CS|TAMS 0x0000000706800000| PB 0x0000000706800000| Complete 
|  53|0x0000000706a00000, 0x0000000706c00000, 0x0000000706c00000|100%| E|CS|TAMS 0x0000000706a00000| PB 0x0000000706a00000| Complete 
|  63|0x0000000707e00000, 0x0000000708000000, 0x0000000708000000|100%| E|CS|TAMS 0x0000000707e00000| PB 0x0000000707e00000| Complete 
|2046|0x00000007ffc00000, 0x00000007ffe00000, 0x00000007ffe00000|100%| E|CS|TAMS 0x00000007ffc00000| PB 0x00000007ffc00000| Complete 
|2047|0x00000007ffe00000, 0x0000000800000000, 0x0000000800000000|100%| O|  |TAMS 0x00000007ffe00000| PB 0x00000007ffe00000| Untracked 

Card table byte_map: [0x000001be42ac0000,0x000001be432c0000] _byte_map_base: 0x000001be3f2c0000

Marking Bits: (CMBitMap*) 0x000001be301db040
 Bits: [0x000001be432c0000, 0x000001be472c0000)

Polling page: 0x000001be2e070000

Metaspace:

Usage:
  Non-class:     72.18 MB used.
      Class:     11.45 MB used.
       Both:     83.62 MB used.

Virtual space:
  Non-class space:      128.00 MB reserved,      73.19 MB ( 57%) committed,  2 nodes.
      Class space:      832.00 MB reserved,      12.44 MB (  1%) committed,  1 nodes.
             Both:      960.00 MB reserved,      85.62 MB (  9%) committed. 

Chunk freelists:
   Non-Class:  6.61 MB
       Class:  3.38 MB
        Both:  9.98 MB

MaxMetaspaceSize: 1.00 GB
CompressedClassSpaceSize: 832.00 MB
Initial GC threshold: 21.00 MB
Current GC threshold: 138.12 MB
CDS: off
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 6.
num_arena_births: 3798.
num_arena_deaths: 0.
num_vsnodes_births: 3.
num_vsnodes_deaths: 0.
num_space_committed: 1370.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 6.
num_chunks_taken_from_freelist: 7508.
num_chunk_merges: 6.
num_chunk_splits: 4860.
num_chunks_enlarged: 2965.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=4139Kb max_used=4139Kb free=115860Kb
 bounds [0x000001be3ad90000, 0x000001be3b1a0000, 0x000001be422c0000]
CodeHeap 'profiled nmethods': size=120000Kb used=15319Kb max_used=15319Kb free=104680Kb
 bounds [0x000001be332c0000, 0x000001be341c0000, 0x000001be3a7f0000]
CodeHeap 'non-nmethods': size=5760Kb used=2807Kb max_used=2857Kb free=2952Kb
 bounds [0x000001be3a7f0000, 0x000001be3aad0000, 0x000001be3ad90000]
 total_blobs=8848 nmethods=7912 adapters=840
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 21.856 Thread 0x000001be49a69080 9157       1       org.gradle.internal.reflect.annotations.impl.AbstractHasAnnotationMetadata::getDeclaredReturnType (5 bytes)
Event: 21.856 Thread 0x000001be49a69080 nmethod 9157 0x000001be3b19a310 code [0x000001be3b19a4a0, 0x000001be3b19a570]
Event: 21.867 Thread 0x000001be49a69080 9158       1       org.gradle.api.services.internal.BuildServiceDetails::getImplementationType (5 bytes)
Event: 21.867 Thread 0x000001be49a69080 nmethod 9158 0x000001be3b19a610 code [0x000001be3b19a7a0, 0x000001be3b19a870]
Event: 21.881 Thread 0x000001be49a69080 9159       3       org.gradle.model.internal.asm.MethodVisitorScope::_AASTORE (7 bytes)
Event: 21.881 Thread 0x000001be49a69080 nmethod 9159 0x000001be34196a90 code [0x000001be34196c40, 0x000001be34196eb0]
Event: 21.895 Thread 0x000001be49a69080 9160       1       org.codehaus.groovy.reflection.stdclasses.ObjectCachedClass::isAssignableFrom (2 bytes)
Event: 21.895 Thread 0x000001be49a69080 nmethod 9160 0x000001be3b19a910 code [0x000001be3b19aaa0, 0x000001be3b19ab68]
Event: 21.898 Thread 0x000001be49a69080 9161       3       java.beans.PropertyDescriptor::setClass0 (25 bytes)
Event: 21.898 Thread 0x000001be49a69080 nmethod 9161 0x000001be34196f90 code [0x000001be341971e0, 0x000001be34197ca8]
Event: 21.899 Thread 0x000001be49a69080 9162       3       com.sun.beans.TypeResolver::erase (159 bytes)
Event: 21.899 Thread 0x000001be49a69080 nmethod 9162 0x000001be34197f90 code [0x000001be341982e0, 0x000001be34199920]
Event: 21.899 Thread 0x000001be49a69080 9163       3       java.beans.FeatureDescriptor::getWeakReference (17 bytes)
Event: 21.900 Thread 0x000001be49a69080 nmethod 9163 0x000001be34199d10 code [0x000001be34199ee0, 0x000001be3419a318]
Event: 21.900 Thread 0x000001be49a69080 9164       3       java.beans.FeatureDescriptor::setTransient (30 bytes)
Event: 21.900 Thread 0x000001be49a69080 nmethod 9164 0x000001be3419a490 code [0x000001be3419a6e0, 0x000001be3419afa0]
Event: 21.900 Thread 0x000001be49a69080 9165       3       java.beans.PropertyDescriptor::setPropertyType (9 bytes)
Event: 21.900 Thread 0x000001be49a69080 nmethod 9165 0x000001be3419b290 code [0x000001be3419b460, 0x000001be3419b7f0]
Event: 21.901 Thread 0x000001be49a69080 9166       3       groovy.lang.MetaClassImpl::establishStaticMetaProperty (262 bytes)
Event: 21.901 Thread 0x000001be49a69080 nmethod 9166 0x000001be3419b990 code [0x000001be3419bc60, 0x000001be3419d418]

GC Heap History (20 events):
Event: 15.322 GC heap before
{Heap before GC invocations=23 (full 0):
 garbage-first heap   total 79872K, used 62238K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 11 young (22528K), 2 survivors (4096K)
 Metaspace       used 69184K, committed 70912K, reserved 917504K
  class space    used 9380K, committed 10240K, reserved 851968K
}
Event: 15.327 GC heap after
{Heap after GC invocations=24 (full 0):
 garbage-first heap   total 79872K, used 47942K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 2 young (4096K), 2 survivors (4096K)
 Metaspace       used 69184K, committed 70912K, reserved 917504K
  class space    used 9380K, committed 10240K, reserved 851968K
}
Event: 15.916 GC heap before
{Heap before GC invocations=24 (full 0):
 garbage-first heap   total 79872K, used 74566K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 12 young (24576K), 2 survivors (4096K)
 Metaspace       used 70480K, committed 72192K, reserved 917504K
  class space    used 9561K, committed 10368K, reserved 851968K
}
Event: 15.923 GC heap after
{Heap after GC invocations=25 (full 0):
 garbage-first heap   total 86016K, used 57795K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 2 young (4096K), 2 survivors (4096K)
 Metaspace       used 70480K, committed 72192K, reserved 917504K
  class space    used 9561K, committed 10368K, reserved 851968K
}
Event: 16.354 GC heap before
{Heap before GC invocations=25 (full 0):
 garbage-first heap   total 86016K, used 74179K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 10 young (20480K), 2 survivors (4096K)
 Metaspace       used 72916K, committed 74752K, reserved 917504K
  class space    used 9848K, committed 10752K, reserved 851968K
}
Event: 16.357 GC heap after
{Heap after GC invocations=26 (full 0):
 garbage-first heap   total 86016K, used 59000K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 1 young (2048K), 1 survivors (2048K)
 Metaspace       used 72916K, committed 74752K, reserved 917504K
  class space    used 9848K, committed 10752K, reserved 851968K
}
Event: 17.011 GC heap before
{Heap before GC invocations=27 (full 0):
 garbage-first heap   total 102400K, used 75384K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 9 young (18432K), 1 survivors (2048K)
 Metaspace       used 74115K, committed 75968K, reserved 983040K
  class space    used 10041K, committed 10944K, reserved 851968K
}
Event: 17.014 GC heap after
{Heap after GC invocations=28 (full 0):
 garbage-first heap   total 102400K, used 59378K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 1 young (2048K), 1 survivors (2048K)
 Metaspace       used 74115K, committed 75968K, reserved 983040K
  class space    used 10041K, committed 10944K, reserved 851968K
}
Event: 17.826 GC heap before
{Heap before GC invocations=28 (full 0):
 garbage-first heap   total 102400K, used 92146K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 17 young (34816K), 1 survivors (2048K)
 Metaspace       used 76480K, committed 78336K, reserved 983040K
  class space    used 10344K, committed 11264K, reserved 851968K
}
Event: 17.830 GC heap after
{Heap after GC invocations=29 (full 0):
 garbage-first heap   total 102400K, used 60243K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 2 young (4096K), 2 survivors (4096K)
 Metaspace       used 76480K, committed 78336K, reserved 983040K
  class space    used 10344K, committed 11264K, reserved 851968K
}
Event: 18.495 GC heap before
{Heap before GC invocations=29 (full 0):
 garbage-first heap   total 102400K, used 88915K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 16 young (32768K), 2 survivors (4096K)
 Metaspace       used 79308K, committed 81216K, reserved 983040K
  class space    used 10828K, committed 11776K, reserved 851968K
}
Event: 18.499 GC heap after
{Heap after GC invocations=30 (full 0):
 garbage-first heap   total 102400K, used 61729K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 2 young (4096K), 2 survivors (4096K)
 Metaspace       used 79308K, committed 81216K, reserved 983040K
  class space    used 10828K, committed 11776K, reserved 851968K
}
Event: 19.144 GC heap before
{Heap before GC invocations=31 (full 0):
 garbage-first heap   total 106496K, used 88353K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 15 young (30720K), 2 survivors (4096K)
 Metaspace       used 80859K, committed 82752K, reserved 983040K
  class space    used 11049K, committed 11968K, reserved 851968K
}
Event: 19.148 GC heap after
{Heap after GC invocations=32 (full 0):
 garbage-first heap   total 106496K, used 62648K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 2 young (4096K), 2 survivors (4096K)
 Metaspace       used 80859K, committed 82752K, reserved 983040K
  class space    used 11049K, committed 11968K, reserved 851968K
}
Event: 19.888 GC heap before
{Heap before GC invocations=32 (full 0):
 garbage-first heap   total 106496K, used 91320K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 16 young (32768K), 2 survivors (4096K)
 Metaspace       used 81952K, committed 83904K, reserved 983040K
  class space    used 11215K, committed 12160K, reserved 851968K
}
Event: 19.894 GC heap after
{Heap after GC invocations=33 (full 0):
 garbage-first heap   total 106496K, used 64885K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 2 young (4096K), 2 survivors (4096K)
 Metaspace       used 81952K, committed 83904K, reserved 983040K
  class space    used 11215K, committed 12160K, reserved 851968K
}
Event: 20.844 GC heap before
{Heap before GC invocations=33 (full 0):
 garbage-first heap   total 106496K, used 91509K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 16 young (32768K), 2 survivors (4096K)
 Metaspace       used 82892K, committed 84864K, reserved 983040K
  class space    used 11318K, committed 12288K, reserved 851968K
}
Event: 20.851 GC heap after
{Heap after GC invocations=34 (full 0):
 garbage-first heap   total 106496K, used 68201K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 2 young (4096K), 2 survivors (4096K)
 Metaspace       used 82892K, committed 84864K, reserved 983040K
  class space    used 11318K, committed 12288K, reserved 851968K
}
Event: 21.686 GC heap before
{Heap before GC invocations=35 (full 0):
 garbage-first heap   total 116736K, used 92777K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 14 young (28672K), 2 survivors (4096K)
 Metaspace       used 84901K, committed 86976K, reserved 983040K
  class space    used 11614K, committed 12672K, reserved 851968K
}
Event: 21.692 GC heap after
{Heap after GC invocations=36 (full 0):
 garbage-first heap   total 116736K, used 70138K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 2 young (4096K), 2 survivors (4096K)
 Metaspace       used 84901K, committed 86976K, reserved 983040K
  class space    used 11614K, committed 12672K, reserved 851968K
}

Dll operation events (3 events):
Event: 0.017 Loaded shared library C:\Program Files\Android\Android Studio\jbr\bin\java.dll
Event: 0.024 Loaded shared library C:\Program Files\Android\Android Studio\jbr\bin\zip.dll
Event: 0.884 Loaded shared library C:\Program Files\Android\Android Studio\jbr\bin\verify.dll

Deoptimization events (20 events):
Event: 21.434 Thread 0x000001be4f8a9610 Uncommon trap: trap_request=0xffffffde fr.pc=0x000001be3b178540 relative=0x0000000000000920
Event: 21.434 Thread 0x000001be4f8a9610 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000001be3b178540 method=com.google.common.collect.Sets$1$1.computeNext()Ljava/lang/Object; @ 16 c2
Event: 21.434 Thread 0x000001be4f8a9610 DEOPT PACKING pc=0x000001be3b178540 sp=0x00000074e74f8a50
Event: 21.434 Thread 0x000001be4f8a9610 DEOPT UNPACKING pc=0x000001be3a8446a2 sp=0x00000074e74f8960 mode 2
Event: 21.434 Thread 0x000001be4f8a9610 Uncommon trap: trap_request=0xffffffde fr.pc=0x000001be3b178540 relative=0x0000000000000920
Event: 21.434 Thread 0x000001be4f8a9610 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000001be3b178540 method=com.google.common.collect.Sets$1$1.computeNext()Ljava/lang/Object; @ 16 c2
Event: 21.434 Thread 0x000001be4f8a9610 DEOPT PACKING pc=0x000001be3b178540 sp=0x00000074e74f8a50
Event: 21.434 Thread 0x000001be4f8a9610 DEOPT UNPACKING pc=0x000001be3a8446a2 sp=0x00000074e74f8960 mode 2
Event: 21.651 Thread 0x000001be4f8a9610 Uncommon trap: trap_request=0xffffffde fr.pc=0x000001be3b17c8c4 relative=0x00000000000009a4
Event: 21.651 Thread 0x000001be4f8a9610 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000001be3b17c8c4 method=com.google.common.cache.LocalCache$Segment.getLiveValue(Lcom/google/common/cache/ReferenceEntry;J)Ljava/lang/Object; @ 21 c2
Event: 21.651 Thread 0x000001be4f8a9610 DEOPT PACKING pc=0x000001be3b17c8c4 sp=0x00000074e74f8e50
Event: 21.651 Thread 0x000001be4f8a9610 DEOPT UNPACKING pc=0x000001be3a8446a2 sp=0x00000074e74f8d00 mode 2
Event: 21.740 Thread 0x000001be4f8a9610 Uncommon trap: trap_request=0xffffff76 fr.pc=0x000001be3b1967a0 relative=0x0000000000000200
Event: 21.740 Thread 0x000001be4f8a9610 Uncommon trap: reason=predicate action=maybe_recompile pc=0x000001be3b1967a0 method=java.util.Arrays.fill([Ljava/lang/Object;IILjava/lang/Object;)V @ 13 c2
Event: 21.740 Thread 0x000001be4f8a9610 DEOPT PACKING pc=0x000001be3b1967a0 sp=0x00000074e74f63a0
Event: 21.740 Thread 0x000001be4f8a9610 DEOPT UNPACKING pc=0x000001be3a8446a2 sp=0x00000074e74f6370 mode 2
Event: 21.834 Thread 0x000001be4f8a9610 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000001be3b0c1928 relative=0x0000000000000708
Event: 21.835 Thread 0x000001be4f8a9610 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001be3b0c1928 method=sun.reflect.annotation.AnnotationInvocationHandler.invoke(Ljava/lang/Object;Ljava/lang/reflect/Method;[Ljava/lang/Object;)Ljava/lang/Object; @ 15 c2
Event: 21.835 Thread 0x000001be4f8a9610 DEOPT PACKING pc=0x000001be3b0c1928 sp=0x00000074e74f5f00
Event: 21.835 Thread 0x000001be4f8a9610 DEOPT UNPACKING pc=0x000001be3a8446a2 sp=0x00000074e74f5ec8 mode 2

Classes loaded (20 events):
Event: 17.991 Loading class com/sun/org/apache/xerces/internal/utils/XMLSecurityPropertyManager$Property
Event: 17.992 Loading class com/sun/org/apache/xerces/internal/utils/XMLSecurityPropertyManager$Property done
Event: 18.020 Loading class java/util/regex/Pattern$SliceI
Event: 18.021 Loading class java/util/regex/Pattern$SliceI done
Event: 18.413 Loading class java/lang/Character$UnicodeBlock
Event: 18.415 Loading class java/lang/Character$Subset
Event: 18.415 Loading class java/lang/Character$Subset done
Event: 18.416 Loading class java/lang/Character$UnicodeBlock done
Event: 21.141 Loading class java/lang/AbstractMethodError
Event: 21.141 Loading class java/lang/AbstractMethodError done
Event: 21.382 Loading class java/util/stream/ReduceOps$5
Event: 21.382 Loading class java/util/stream/ReduceOps$5 done
Event: 21.382 Loading class java/util/stream/ReduceOps$CountingSink$OfRef
Event: 21.382 Loading class java/util/stream/ReduceOps$CountingSink
Event: 21.382 Loading class java/util/stream/ReduceOps$CountingSink done
Event: 21.382 Loading class java/util/stream/ReduceOps$CountingSink$OfRef done
Event: 21.835 Loading class sun/reflect/annotation/AnnotationInvocationHandler$1
Event: 21.835 Loading class sun/reflect/annotation/AnnotationInvocationHandler$1 done
Event: 21.903 Loading class java/util/regex/Pattern$Pos
Event: 21.903 Loading class java/util/regex/Pattern$Pos done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 18.066 Thread 0x000001be4f8a9610 Exception <a 'java/lang/NoSuchMethodError'{0x00000007057c1568}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object)'> (0x00000007057c1568) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 18.318 Thread 0x000001be4f8a9610 Exception <a 'java/lang/NoSuchMethodError'{0x0000000704a25ba0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang
Event: 19.135 Thread 0x000001be4f8a9610 Exception <a 'java/lang/ClassNotFoundException'{0x0000000704b4f7b8}: org/jetbrains/kotlin/gradle/targets/js/npm/DefaultNpmDependencyExtensionBeanInfo> (0x0000000704b4f7b8) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 19.136 Thread 0x000001be4f8a9610 Exception <a 'java/lang/ClassNotFoundException'{0x0000000704b63c38}: org/jetbrains/kotlin/gradle/targets/js/npm/DefaultNpmDependencyExtensionCustomizer> (0x0000000704b63c38) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 19.150 Thread 0x000001be4f8a9610 Exception <a 'java/lang/ClassNotFoundException'{0x0000000707e16830}: org/jetbrains/kotlin/gradle/targets/js/npm/NpmDependencyExtensionKt$defaultNpmDependencyDelegate$1BeanInfo> (0x0000000707e16830) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 19.150 Thread 0x000001be4f8a9610 Exception <a 'java/lang/ClassNotFoundException'{0x0000000707e2acf0}: org/jetbrains/kotlin/gradle/targets/js/npm/NpmDependencyExtensionDelegateBeanInfo> (0x0000000707e2acf0) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 19.151 Thread 0x000001be4f8a9610 Exception <a 'java/lang/ClassNotFoundException'{0x0000000707e3f1b0}: org/jetbrains/kotlin/gradle/targets/js/npm/NpmDependencyExtensionDelegateCustomizer> (0x0000000707e3f1b0) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 19.153 Thread 0x000001be4f8a9610 Exception <a 'java/lang/ClassNotFoundException'{0x0000000707e7b0a0}: org/jetbrains/kotlin/gradle/targets/js/npm/NpmDependencyExtensionKt$defaultNpmDependencyDelegate$1Customizer> (0x0000000707e7b0a0) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 19.160 Thread 0x000001be4f8a9610 Exception <a 'java/lang/ClassNotFoundException'{0x0000000707eccc48}: org/jetbrains/kotlin/gradle/targets/js/npm/DefaultDevNpmDependencyExtensionBeanInfo> (0x0000000707eccc48) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 19.160 Thread 0x000001be4f8a9610 Exception <a 'java/lang/ClassNotFoundException'{0x0000000707ee1120}: org/jetbrains/kotlin/gradle/targets/js/npm/DefaultDevNpmDependencyExtensionCustomizer> (0x0000000707ee1120) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 19.167 Thread 0x000001be4f8a9610 Exception <a 'java/lang/ClassNotFoundException'{0x0000000707f3ca68}: org/jetbrains/kotlin/gradle/targets/js/npm/DefaultPeerNpmDependencyExtensionBeanInfo> (0x0000000707f3ca68) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 19.168 Thread 0x000001be4f8a9610 Exception <a 'java/lang/ClassNotFoundException'{0x0000000707f50f50}: org/jetbrains/kotlin/gradle/targets/js/npm/DefaultPeerNpmDependencyExtensionCustomizer> (0x0000000707f50f50) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 19.172 Thread 0x000001be4f8a9610 Exception <a 'java/lang/ClassNotFoundException'{0x0000000707fb8a98}: org/jetbrains/kotlin/gradle/targets/js/npm/DefaultPeerNpmDependencyExtension$delegate$1BeanInfo> (0x0000000707fb8a98) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 19.173 Thread 0x000001be4f8a9610 Exception <a 'java/lang/ClassNotFoundException'{0x0000000707fcd188}: org/jetbrains/kotlin/gradle/targets/js/npm/DefaultPeerNpmDependencyExtension$delegate$1Customizer> (0x0000000707fcd188) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 21.173 Thread 0x000001be4f8a9610 Implicit null exception at 0x000001be3b14d88d to 0x000001be3b152c5c
Event: 21.582 Thread 0x000001be4f8a9610 Exception <a 'java/lang/NoSuchMethodError'{0x0000000705b9be58}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object)'> (0x0000000705b9be58) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 21.896 Thread 0x000001be4f8a9610 Exception <a 'java/lang/ClassNotFoundException'{0x00000007062ab060}: org/gradle/api/plugins/internal/NaggingJavaPluginConventionBeanInfo> (0x00000007062ab060) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 21.896 Thread 0x000001be4f8a9610 Exception <a 'java/lang/ClassNotFoundException'{0x00000007062bad48}: org/gradle/api/plugins/JavaPluginConventionBeanInfo> (0x00000007062bad48) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 21.897 Thread 0x000001be4f8a9610 Exception <a 'java/lang/ClassNotFoundException'{0x00000007062ca9f8}: org/gradle/api/plugins/JavaPluginConventionCustomizer> (0x00000007062ca9f8) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 21.898 Thread 0x000001be4f8a9610 Exception <a 'java/lang/ClassNotFoundException'{0x00000007062f0560}: org/gradle/api/plugins/internal/NaggingJavaPluginConventionCustomizer> (0x00000007062f0560) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 19.144 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause)
Event: 19.148 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause) done
Event: 19.888 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause)
Event: 19.894 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause) done
Event: 19.965 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 19.965 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 20.492 Executing VM operation: ICBufferFull
Event: 20.492 Executing VM operation: ICBufferFull done
Event: 20.844 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause)
Event: 20.851 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause) done
Event: 20.928 Executing VM operation: G1PauseRemark
Event: 20.945 Executing VM operation: G1PauseRemark done
Event: 20.979 Executing VM operation: G1PauseCleanup
Event: 20.979 Executing VM operation: G1PauseCleanup done
Event: 21.378 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 21.378 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 21.521 Executing VM operation: ICBufferFull
Event: 21.521 Executing VM operation: ICBufferFull done
Event: 21.686 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause)
Event: 21.692 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause) done

Memory protections (0 events):
No events

Nmethod flushes (20 events):
Event: 20.945 Thread 0x000001be49753110 flushing  nmethod 0x000001be33958090
Event: 20.945 Thread 0x000001be49753110 flushing  nmethod 0x000001be33998990
Event: 20.945 Thread 0x000001be49753110 flushing  nmethod 0x000001be33c2fc90
Event: 20.945 Thread 0x000001be49753110 flushing  nmethod 0x000001be33c35890
Event: 20.945 Thread 0x000001be49753110 flushing  nmethod 0x000001be3aebb290
Event: 20.945 Thread 0x000001be49753110 flushing  nmethod 0x000001be3b0a2190
Event: 20.945 Thread 0x000001be49753110 flushing  nmethod 0x000001be333c8d10
Event: 20.945 Thread 0x000001be49753110 flushing  nmethod 0x000001be33470110
Event: 20.945 Thread 0x000001be49753110 flushing  nmethod 0x000001be334ad810
Event: 20.945 Thread 0x000001be49753110 flushing  nmethod 0x000001be334b2690
Event: 20.945 Thread 0x000001be49753110 flushing  nmethod 0x000001be335d4610
Event: 20.945 Thread 0x000001be49753110 flushing  nmethod 0x000001be336f0e10
Event: 20.945 Thread 0x000001be49753110 flushing  nmethod 0x000001be3374c010
Event: 20.945 Thread 0x000001be49753110 flushing  nmethod 0x000001be33871d10
Event: 20.945 Thread 0x000001be49753110 flushing  nmethod 0x000001be3391b590
Event: 20.945 Thread 0x000001be49753110 flushing  nmethod 0x000001be3391c910
Event: 20.945 Thread 0x000001be49753110 flushing  nmethod 0x000001be33a97210
Event: 20.945 Thread 0x000001be49753110 flushing  nmethod 0x000001be33aaaf90
Event: 20.945 Thread 0x000001be49753110 flushing  nmethod 0x000001be33c0ab90
Event: 20.945 Thread 0x000001be49753110 flushing  nmethod 0x000001be33ee9690

Events (20 events):
Event: 8.672 Thread 0x000001be520833b0 Thread exited: 0x000001be520833b0
Event: 8.739 Thread 0x000001be49a69080 Thread added: 0x000001be51698f80
Event: 9.921 Thread 0x000001be51698f80 Thread exited: 0x000001be51698f80
Event: 10.648 Thread 0x000001be4f8a9610 Thread added: 0x000001be52339400
Event: 10.648 Thread 0x000001be4f8a9610 Thread added: 0x000001be5233f670
Event: 10.716 Thread 0x000001be4f8a9610 Thread added: 0x000001be52339a90
Event: 10.718 Thread 0x000001be4f8a9610 Thread added: 0x000001be52338d70
Event: 10.778 Thread 0x000001be4f8a9610 Thread added: 0x000001be523386e0
Event: 10.779 Thread 0x000001be4f8a9610 Thread added: 0x000001be5233c1f0
Event: 14.540 Thread 0x000001be4f8a9610 Thread added: 0x000001be5233ae40
Event: 14.540 Thread 0x000001be4f8a9610 Thread added: 0x000001be5233c880
Event: 14.540 Thread 0x000001be4f8a9610 Thread added: 0x000001be5233cf10
Event: 14.564 Thread 0x000001be4f8a9610 Thread added: 0x000001be5233d5a0
Event: 14.565 Thread 0x000001be4f8a9610 Thread added: 0x000001be4fe49eb0
Event: 14.573 Thread 0x000001be4f8a9610 Thread added: 0x000001be4fe4cca0
Event: 14.847 Thread 0x000001be4f8a9610 Thread added: 0x000001be4fe47de0
Event: 14.848 Thread 0x000001be4f8a9610 Thread added: 0x000001be4fe4a540
Event: 14.849 Thread 0x000001be4f8a9610 Thread added: 0x000001be4fe4abd0
Event: 16.545 Thread 0x000001be4f8a9610 Thread added: 0x000001be4fe49820
Event: 18.048 Thread 0x000001be4f8a9610 Thread added: 0x000001be4fe4b8f0


Dynamic libraries:
0x00007ff7b0e10000 - 0x00007ff7b0e1a000 	C:\Program Files\Android\Android Studio\jbr\bin\java.exe
0x00007ffef0c10000 - 0x00007ffef0e08000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ffef09c0000 - 0x00007ffef0a82000 	C:\Windows\System32\KERNEL32.DLL
0x00007ffeee760000 - 0x00007ffeeea56000 	C:\Windows\System32\KERNELBASE.dll
0x00007ffeeb5a0000 - 0x00007ffeeb634000 	C:\Windows\SYSTEM32\apphelp.dll
0x00007ffeee320000 - 0x00007ffeee420000 	C:\Windows\System32\ucrtbase.dll
0x00007ffecc820000 - 0x00007ffecc838000 	C:\Program Files\Android\Android Studio\jbr\bin\jli.dll
0x00007ffeef0b0000 - 0x00007ffeef24d000 	C:\Windows\System32\USER32.dll
0x00007ffeee580000 - 0x00007ffeee5a2000 	C:\Windows\System32\win32u.dll
0x00007ffef0930000 - 0x00007ffef095b000 	C:\Windows\System32\GDI32.dll
0x00007ffeee5b0000 - 0x00007ffeee6c9000 	C:\Windows\System32\gdi32full.dll
0x00007ffeeea90000 - 0x00007ffeeeb2d000 	C:\Windows\System32\msvcp_win.dll
0x00007ffeca2b0000 - 0x00007ffeca2cb000 	C:\Program Files\Android\Android Studio\jbr\bin\VCRUNTIME140.dll
0x00007ffee0760000 - 0x00007ffee09fa000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7\COMCTL32.dll
0x00007ffeef5f0000 - 0x00007ffeef68e000 	C:\Windows\System32\msvcrt.dll
0x00007ffeeedc0000 - 0x00007ffeeedef000 	C:\Windows\System32\IMM32.DLL
0x00007ffede140000 - 0x00007ffede14c000 	C:\Program Files\Android\Android Studio\jbr\bin\vcruntime140_1.dll
0x00007ffec0510000 - 0x00007ffec059d000 	C:\Program Files\Android\Android Studio\jbr\bin\msvcp140.dll
0x00007ffea3830000 - 0x00007ffea44ba000 	C:\Program Files\Android\Android Studio\jbr\bin\server\jvm.dll
0x00007ffeef530000 - 0x00007ffeef5e1000 	C:\Windows\System32\ADVAPI32.dll
0x00007ffeef010000 - 0x00007ffeef0af000 	C:\Windows\System32\sechost.dll
0x00007ffeeeee0000 - 0x00007ffeef003000 	C:\Windows\System32\RPCRT4.dll
0x00007ffeeea60000 - 0x00007ffeeea87000 	C:\Windows\System32\bcrypt.dll
0x00007ffeef4c0000 - 0x00007ffeef52b000 	C:\Windows\System32\WS2_32.dll
0x00007ffeee100000 - 0x00007ffeee14b000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ffee2be0000 - 0x00007ffee2c07000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ffee5700000 - 0x00007ffee570a000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ffeee0e0000 - 0x00007ffeee0f2000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ffeec100000 - 0x00007ffeec112000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ffedd730000 - 0x00007ffedd73a000 	C:\Program Files\Android\Android Studio\jbr\bin\jimage.dll
0x00007ffedfc10000 - 0x00007ffedfe11000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ffec4fb0000 - 0x00007ffec4fe4000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ffeee6d0000 - 0x00007ffeee752000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ffebf790000 - 0x00007ffebf79e000 	C:\Program Files\Android\Android Studio\jbr\bin\instrument.dll
0x00007ffeca290000 - 0x00007ffeca2b0000 	C:\Program Files\Android\Android Studio\jbr\bin\java.dll
0x00007ffeca210000 - 0x00007ffeca228000 	C:\Program Files\Android\Android Studio\jbr\bin\zip.dll
0x00007ffef00d0000 - 0x00007ffef083e000 	C:\Windows\System32\SHELL32.dll
0x00007ffeec300000 - 0x00007ffeecaa4000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ffeefd70000 - 0x00007ffef00c3000 	C:\Windows\System32\combase.dll
0x00007ffeedb20000 - 0x00007ffeedb4b000 	C:\Windows\SYSTEM32\Wldp.dll
0x00007ffeeedf0000 - 0x00007ffeeeebd000 	C:\Windows\System32\OLEAUT32.dll
0x00007ffeeec30000 - 0x00007ffeeecdd000 	C:\Windows\System32\SHCORE.dll
0x00007ffeeece0000 - 0x00007ffeeed3b000 	C:\Windows\System32\shlwapi.dll
0x00007ffeee1d0000 - 0x00007ffeee1f4000 	C:\Windows\SYSTEM32\profapi.dll
0x00007ffedbb90000 - 0x00007ffedbba0000 	C:\Program Files\Android\Android Studio\jbr\bin\net.dll
0x00007ffee77d0000 - 0x00007ffee78da000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007ffeed900000 - 0x00007ffeed96a000 	C:\Windows\system32\mswsock.dll
0x00007ffec9a30000 - 0x00007ffec9a46000 	C:\Program Files\Android\Android Studio\jbr\bin\nio.dll
0x00007ffedb850000 - 0x00007ffedb860000 	C:\Program Files\Android\Android Studio\jbr\bin\verify.dll
0x00007ffeb9400000 - 0x00007ffeb9427000 	C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64\native-platform.dll
0x00007ffeb65c0000 - 0x00007ffeb6638000 	C:\Users\<USER>\.gradle\native\0.2.7\x86_64-windows-gnu\gradle-fileevents.dll
0x00007ffed0d90000 - 0x00007ffed0d99000 	C:\Program Files\Android\Android Studio\jbr\bin\management.dll
0x00007ffecd120000 - 0x00007ffecd12b000 	C:\Program Files\Android\Android Studio\jbr\bin\management_ext.dll
0x00007ffef0920000 - 0x00007ffef0928000 	C:\Windows\System32\PSAPI.DLL
0x00007ffeed600000 - 0x00007ffeed63b000 	C:\Windows\SYSTEM32\IPHLPAPI.DLL
0x00007ffeef8e0000 - 0x00007ffeef8e8000 	C:\Windows\System32\NSI.dll
0x00007ffecd050000 - 0x00007ffecd059000 	C:\Program Files\Android\Android Studio\jbr\bin\extnet.dll
0x00007ffeedeb0000 - 0x00007ffeedec8000 	C:\Windows\SYSTEM32\CRYPTSP.dll
0x00007ffeed240000 - 0x00007ffeed278000 	C:\Windows\system32\rsaenh.dll
0x00007ffeee150000 - 0x00007ffeee17e000 	C:\Windows\SYSTEM32\USERENV.dll
0x00007ffeeda90000 - 0x00007ffeeda9c000 	C:\Windows\SYSTEM32\CRYPTBASE.dll
0x00007ffe7dea0000 - 0x00007ffe7dea7000 	C:\Windows\system32\wshunix.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Android\Android Studio\jbr\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7;C:\Program Files\Android\Android Studio\jbr\bin\server;C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64;C:\Users\<USER>\.gradle\native\0.2.7\x86_64-windows-gnu

VM Arguments:
jvm_args: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -XX:MaxMetaspaceSize=1024m -Xmx4096m -Dfile.encoding=windows-1252 -Duser.country=BR -Duser.language=pt -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.14.1-bin\baw1sv0jfoi8rxs14qo3h49cs\gradle-8.14.1\lib\agents\gradle-instrumentation-agent-8.14.1.jar 
java_command: org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.14.1
java_class_path (initial): C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.14.1-bin\baw1sv0jfoi8rxs14qo3h49cs\gradle-8.14.1\lib\gradle-daemon-main-8.14.1.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 3                                         {product} {ergonomic}
   size_t CompressedClassSpaceSize                 = 872415232                                 {product} {ergonomic}
     uint ConcGCThreads                            = 1                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 4                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 2097152                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 134217728                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 4294967296                                {product} {command line}
   size_t MaxMetaspaceSize                         = 1073741824                                {product} {command line}
   size_t MaxNewSize                               = 2575302656                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 2097152                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5832780                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122912730                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122912730                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 4294967296                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=C:\Program Files\Java\jdk-11.0.10
PATH=c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\Microsoft SQL Server\130\Tools\Binn\;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn\;C:\Program Files\dotnet\;C:\Program Files (x86)\Microsoft SQL Server\150\DTS\Binn\;C:\Program Files\Azure Data Studio\bin;C:\Program Files\Git\cmd;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\110\Tools\Binn\;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\130\Tools\Binn\;C:\Program Files (x86)\Microsoft SQL Server\140\Tools\Binn\;C:\Program Files\Microsoft SQL Server\140\Tools\Binn\;C:\Program Files\Microsoft SQL Server\140\DTS\Binn\;C:\Program Files\Microsoft\Web Platform Installer\;C:\Program Files (x86)\dotnet\;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;C:\Program Files\Calibre2\;C:\Program Files\Java\jdk-11\bin;C:\Prog;C:\Program Files\Docker\Docker\resources\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\.local\share\solana\install\active_release\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\;C:\ProgramData\DockerDesktop\version-bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\Users\ADM\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\.dotnet\tools;C:\MinGW\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin
USERNAME=ADM
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 58 Stepping 9, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

JNI global refs:
JNI global refs: 27, weak refs: 5

JNI global refs memory usage: 835, weak refs: 833

Process memory usage:
Resident Set Size: 344488K (4% of 8357708K total physical memory with 350792K free physical memory)

OOME stack traces (most recent first):
Classloader memory used:
Loader org.gradle.internal.classloader.VisitableURLClassLoader                         : 40989K
Loader bootstrap                                                                       : 25692K
Loader org.gradle.initialization.MixInLegacyTypesClassLoader                           : 11763K
Loader org.gradle.internal.classloader.VisitableURLClassLoader$InstrumentingVisitableURLClassLoader: 5958K
Loader jdk.internal.loader.ClassLoaders$PlatformClassLoader                            : 529K
Loader org.gradle.internal.classloader.VisitableURLClassLoader                         : 333K
Loader jdk.internal.loader.ClassLoaders$AppClassLoader                                 : 236K
Loader jdk.internal.reflect.DelegatingClassLoader                                      : 140K
Loader org.gradle.groovy.scripts.internal.DefaultScriptCompilationHandler$ScriptClassLoader: 23824B
Loader org.codehaus.groovy.runtime.callsite.CallSiteClassLoader                        : 3648B

Classes loaded by more than one classloader:
Class Program                                                                         : loaded 12 times (x 68B)
Class Build_gradle$1                                                                  : loaded 10 times (x 74B)
Class Build_gradle                                                                    : loaded 10 times (x 126B)
Class org.gradle.kotlin.dsl.VersionCatalogAccessorsKt                                 : loaded 5 times (x 67B)
Class Build_gradle$4                                                                  : loaded 4 times (x 70B)
Class Build_gradle$6                                                                  : loaded 4 times (x 70B)
Class Build_gradle$2                                                                  : loaded 4 times (x 75B)
Class org.gradle.kotlin.dsl.Accessors96b3ii45gitqpy1kb3tvcvtxvKt                      : loaded 4 times (x 67B)
Class org.gradle.kotlin.dsl.ImplementationConfigurationAccessorsKt                    : loaded 4 times (x 67B)
Class org.gradle.kotlin.dsl.Accessors9osmdt78klrrxidc9srcw3tsqKt                      : loaded 4 times (x 67B)
Class Build_gradle$3                                                                  : loaded 4 times (x 70B)
Class Build_gradle$5                                                                  : loaded 4 times (x 70B)
Class org.gradle.kotlin.dsl.TestImplementationConfigurationAccessorsKt                : loaded 3 times (x 67B)
Class org.gradle.internal.service.ServiceLocator                                      : loaded 2 times (x 66B)
Class org.gradle.kotlin.dsl.Accessorse8w47d3slt021lb0cbtcdsmobKt                      : loaded 2 times (x 67B)
Class org.gradle.internal.classpath.TransformedClassPath                              : loaded 2 times (x 92B)
Class org.gradle.internal.InternalTransformer                                         : loaded 2 times (x 66B)
Class org.gradle.internal.classloader.ClasspathUtil$1                                 : loaded 2 times (x 72B)
Class org.gradle.api.internal.classpath.DefaultModuleRegistry$DefaultModule           : loaded 2 times (x 82B)
Class org.gradle.api.internal.classpath.Module                                        : loaded 2 times (x 66B)
Class org.gradle.api.internal.classpath.UnknownModuleException                        : loaded 2 times (x 78B)
Class org.gradle.api.internal.DefaultClassPathRegistry                                : loaded 2 times (x 72B)
Class org.gradle.api.internal.ClassPathRegistry                                       : loaded 2 times (x 66B)
Class org.gradle.internal.classpath.DefaultClassPath$ImmutableUniqueList              : loaded 2 times (x 195B)
Class org.gradle.internal.service.UnknownServiceException                             : loaded 2 times (x 79B)
Class org.gradle.internal.classpath.ClassPath                                         : loaded 2 times (x 66B)
Class org.gradle.api.UncheckedIOException                                             : loaded 2 times (x 78B)
Class Build_gradle$2$1                                                                : loaded 2 times (x 70B)
Class org.gradle.internal.installation.GradleInstallation$1                           : loaded 2 times (x 71B)
Class org.gradle.api.internal.ClassPathProvider                                       : loaded 2 times (x 66B)
Class org.gradle.api.internal.classpath.DefaultModuleRegistry                         : loaded 2 times (x 82B)
Class org.gradle.internal.service.ServiceLookupException                              : loaded 2 times (x 78B)
Class org.gradle.internal.classloader.ClassLoaderFactory                              : loaded 2 times (x 66B)
Class org.gradle.internal.service.CachingServiceLocator                               : loaded 2 times (x 78B)
Class org.gradle.internal.classloader.InstrumentingClassLoader                        : loaded 2 times (x 66B)
Class org.gradle.internal.classloader.ClasspathUtil                                   : loaded 2 times (x 67B)
Class org.gradle.api.specs.Spec                                                       : loaded 2 times (x 66B)
Class [Lorg.gradle.api.internal.ClassPathProvider;                                    : loaded 2 times (x 65B)
Class org.gradle.api.internal.DefaultClassPathProvider                                : loaded 2 times (x 72B)
Class org.gradle.api.internal.classpath.EffectiveClassPath                            : loaded 2 times (x 86B)
Class org.gradle.internal.classloader.ClassLoaderVisitor                              : loaded 2 times (x 72B)
Class org.gradle.internal.classloader.VisitableURLClassLoader$InstrumentingVisitableURLClassLoader: loaded 2 times (x 119B)
Class org.gradle.internal.classloader.DefaultClassLoaderFactory                       : loaded 2 times (x 78B)
Class org.gradle.internal.classloader.VisitableURLClassLoader$Spec                    : loaded 2 times (x 70B)
Class org.gradle.internal.classpath.DefaultClassPath                                  : loaded 2 times (x 86B)
Class Build_gradle$2$1$1                                                              : loaded 2 times (x 70B)
Class Build_gradle$7                                                                  : loaded 2 times (x 70B)
Class org.gradle.internal.classloader.ClassLoaderSpec                                 : loaded 2 times (x 67B)
Class org.gradle.internal.classloader.VisitableURLClassLoader                         : loaded 2 times (x 113B)
Class Settings_gradle                                                                 : loaded 2 times (x 124B)
Class org.gradle.internal.Cast                                                        : loaded 2 times (x 67B)
Class org.gradle.api.Action                                                           : loaded 2 times (x 66B)
Class org.gradle.api.internal.classpath.ManifestUtil                                  : loaded 2 times (x 67B)
Class org.gradle.internal.classpath.DefaultClassPath$ImmutableUniqueList$Builder      : loaded 2 times (x 71B)
Class org.gradle.api.GradleException                                                  : loaded 2 times (x 78B)
Class org.gradle.internal.classloader.ClassLoaderHierarchy                            : loaded 2 times (x 66B)
Class org.gradle.internal.IoActions                                                   : loaded 2 times (x 67B)
Class org.gradle.internal.classloader.SystemClassLoaderSpec                           : loaded 2 times (x 67B)
Class org.gradle.api.internal.classpath.ModuleRegistry                                : loaded 2 times (x 66B)
Class org.gradle.api.internal.classpath.GlobalCacheRootsProvider                      : loaded 2 times (x 66B)
Class org.gradle.internal.classloader.FilteringClassLoader                            : loaded 2 times (x 101B)
Class org.gradle.internal.installation.CurrentGradleInstallationLocator               : loaded 2 times (x 67B)
Class org.gradle.internal.service.DefaultServiceLocator                               : loaded 2 times (x 79B)
Class org.gradle.internal.installation.CurrentGradleInstallation                      : loaded 2 times (x 69B)
Class org.gradle.util.internal.GUtil                                                  : loaded 2 times (x 67B)
Class org.gradle.internal.installation.GradleInstallation                             : loaded 2 times (x 71B)

---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
OS uptime: 14 days 20:55 hours
Hyper-V role detected

CPU: total 4 (initial active 4) (4 cores per cpu, 1 threads per core) family 6 model 58 stepping 9 microcode 0x21, cx8, cmov, fxsr, mmx, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, tsc, tscinvbit, avx, aes, erms, clmul, vzeroupper, clflush, hv, rdtscp, f16c
Processor Information for all 4 processors :
  Max Mhz: 3101, Current Mhz: 3101, Mhz Limit: 3101

Memory: 4k page, system-wide physical 8161M (342M free)
TotalPageFile size 32737M (AvailPageFile size 11M)
current process WorkingSet (physical memory assigned to process): 336M, peak: 337M
current process commit charge ("private bytes"): 366M, peak: 369M

vm_info: OpenJDK 64-Bit Server VM (21.0.5+-13047016-b750.29) for windows-amd64 JRE (21.0.5+-13047016-b750.29), built on 2025-02-11T21:12:39Z by "builder" with MS VC++ 16.10 / 16.11 (VS2019)

END.
