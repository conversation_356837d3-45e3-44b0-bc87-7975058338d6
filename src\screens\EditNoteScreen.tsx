import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  TextInput,
  ScrollView,
  Alert,
  StatusBar,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { fonts } from '../theme/fonts';
import { useTheme } from '../context/ThemeContext';

interface Note {
  id: string;
  title: string;
  content: string;
  lesson: string;
  tags: string[];
  createdAt: string;
  updatedAt: string;
}

const STORAGE_KEY = '@escola_sabatina_notes';

const EditNoteScreen = ({ navigation, route }: any) => {
  const { colors, theme } = useTheme();
  const { note } = route.params || {};
  const [title, setTitle] = useState(note?.title || '');
  const [content, setContent] = useState(note?.content || '');
  const [selectedLesson, setSelectedLesson] = useState(note?.lesson || '');
  const [tags, setTags] = useState<string[]>(note?.tags || []);
  const [currentTag, setCurrentTag] = useState('');
  const [saving, setSaving] = useState(false);

  // Debug do tema
  console.log('📝 EditNoteScreen - Tema atual:', theme, 'Cor do texto:', colors.text);

  const hasUnsavedChanges =
    title !== (note?.title || '') ||
    content !== (note?.content || '') ||
    selectedLesson !== (note?.lesson || '') ||
    JSON.stringify(tags) !== JSON.stringify(note?.tags || []);

  const saveNote = async () => {
    if (!title.trim()) {
      Alert.alert('Erro', 'Por favor, digite um título para a anotação.');
      return;
    }

    setSaving(true);
    try {
      const existingNotes = await AsyncStorage.getItem(STORAGE_KEY);
      const notes: Note[] = existingNotes ? JSON.parse(existingNotes) : [];

      const newNote: Note = {
        id: note?.id || Date.now().toString(),
        title: title.trim(),
        content: content.trim(),
        lesson: selectedLesson,
        tags,
        createdAt: note?.createdAt || new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      if (note) {
        const index = notes.findIndex(n => n.id === note.id);
        if (index !== -1) {
          notes[index] = newNote;
        }
      } else {
        notes.unshift(newNote);
      }

      await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(notes));
      Alert.alert('Sucesso', 'Anotação salva com sucesso!', [
        { text: 'OK', onPress: () => navigation.goBack() }
      ]);
    } catch (error) {
      console.error('Erro ao salvar anotação:', error);
      Alert.alert('Erro', 'Não foi possível salvar a anotação.');
    } finally {
      setSaving(false);
    }
  };

  const deleteNote = () => {
    if (!note) return;

    Alert.alert(
      'Excluir Anotação',
      'Tem certeza que deseja excluir esta anotação?',
      [
        { text: 'Cancelar', style: 'cancel' },
        {
          text: 'Excluir',
          style: 'destructive',
          onPress: async () => {
            try {
              const existingNotes = await AsyncStorage.getItem(STORAGE_KEY);
              const notes: Note[] = existingNotes ? JSON.parse(existingNotes) : [];
              const filteredNotes = notes.filter(n => n.id !== note.id);
              await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(filteredNotes));
              navigation.goBack();
            } catch (error) {
              console.error('Erro ao excluir anotação:', error);
              Alert.alert('Erro', 'Não foi possível excluir a anotação.');
            }
          }
        }
      ]
    );
  };

  const addTag = () => {
    if (currentTag.trim() && !tags.includes(currentTag.trim())) {
      setTags([...tags, currentTag.trim()]);
      setCurrentTag('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove));
  };

  // Estilos dinâmicos baseados no tema
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: 16,
      paddingTop: 28,
      paddingVertical: 12,
      backgroundColor: colors.card,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    backButton: {
      padding: 8,
      borderRadius: 20,
    },
    headerTitle: {
      fontFamily: fonts.bold,
      fontSize: 18,
      color: colors.text,
      flex: 1,
      textAlign: 'center',
      marginHorizontal: 16,
    },
    headerActions: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    headerAction: {
      padding: 8,
      marginLeft: 8,
      borderRadius: 20,
    },
    content: {
      flex: 1,
      paddingHorizontal: 16,
    },
    inputContainer: {
      marginVertical: 12,
    },
    inputLabel: {
      fontFamily: fonts.bold,
      fontSize: 16,
      color: colors.text,
      marginBottom: 8,
    },
    titleInput: {
      backgroundColor: colors.card,
      borderRadius: 12,
      paddingHorizontal: 16,
      paddingVertical: 12,
      fontFamily: fonts.regular,
      fontSize: 16,
      color: colors.text,
      borderWidth: 1,
      borderColor: colors.border,
    },
    contentInput: {
      backgroundColor: colors.card,
      borderRadius: 12,
      paddingHorizontal: 16,
      paddingVertical: 12,
      fontFamily: fonts.regular,
      color: colors.text,
      borderWidth: 1,
      borderColor: colors.border,
      textAlignVertical: 'top',
      minHeight: 200,
    },
    lessonInput: {
      backgroundColor: colors.card,
      borderRadius: 12,
      paddingHorizontal: 16,
      paddingVertical: 12,
      fontFamily: fonts.regular,
      fontSize: 16,
      color: colors.text,
      borderWidth: 1,
      borderColor: colors.border,
    },
    tagsContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 8,
      marginBottom: 12,
    },
    tag: {
      backgroundColor: colors.primary + '15',
      borderRadius: 16,
      paddingHorizontal: 12,
      paddingVertical: 6,
      flexDirection: 'row',
      alignItems: 'center',
    },
    tagText: {
      fontFamily: fonts.regular,
      fontSize: 14,
      color: colors.primary,
      marginRight: 4,
    },
    tagInput: {
      backgroundColor: colors.card,
      borderRadius: 12,
      paddingHorizontal: 16,
      paddingVertical: 12,
      fontFamily: fonts.regular,
      fontSize: 16,
      color: colors.text,
      borderWidth: 1,
      borderColor: colors.border,
      marginBottom: 8,
    },
    addTagButton: {
      backgroundColor: colors.primary,
      borderRadius: 8,
      paddingHorizontal: 16,
      paddingVertical: 8,
      alignSelf: 'flex-start',
    },
    addTagText: {
      fontFamily: fonts.bold,
      fontSize: 14,
      color: colors.textInverse,
    },
    saveButton: {
      backgroundColor: colors.primary,
      borderRadius: 12,
      paddingVertical: 16,
      alignItems: 'center',
      marginVertical: 16,
    },
    saveButtonDisabled: {
      backgroundColor: colors.textSecondary,
      opacity: 0.6,
    },
    saveButtonText: {
      fontFamily: fonts.bold,
      fontSize: 16,
      color: colors.textInverse,
    },
  });

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar
        barStyle={colors.text === '#FFFFFF' ? 'light-content' : 'dark-content'}
        backgroundColor={colors.background}
      />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Icon name="arrow-left" size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>
          {note ? 'Editar Anotação' : 'Nova Anotação'}
        </Text>
        <View style={styles.headerActions}>
          {note && (
            <TouchableOpacity onPress={deleteNote} style={styles.headerAction}>
              <Icon name="delete" size={24} color={colors.error} />
            </TouchableOpacity>
          )}
        </View>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Título */}
        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>Título</Text>
          <TextInput
            style={styles.titleInput}
            placeholder="Digite o título da anotação..."
            placeholderTextColor={colors.textSecondary}
            value={title}
            onChangeText={setTitle}
          />
        </View>

        {/* Lição */}
        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>Lição</Text>
          <TextInput
            style={styles.lessonInput}
            placeholder="Digite a lição relacionada..."
            placeholderTextColor={colors.textSecondary}
            value={selectedLesson}
            onChangeText={setSelectedLesson}
          />
        </View>

        {/* Tags */}
        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>Tags</Text>
          {tags.length > 0 && (
            <View style={styles.tagsContainer}>
              {tags.map((tag, index) => (
                <View key={index} style={styles.tag}>
                  <Text style={styles.tagText}>{tag}</Text>
                  <TouchableOpacity onPress={() => removeTag(tag)}>
                    <Icon name="close" size={16} color={colors.primary} />
                  </TouchableOpacity>
                </View>
              ))}
            </View>
          )}
          <TextInput
            style={styles.tagInput}
            placeholder="Adicionar tag..."
            placeholderTextColor={colors.textSecondary}
            value={currentTag}
            onChangeText={setCurrentTag}
            onSubmitEditing={addTag}
          />
          <TouchableOpacity style={styles.addTagButton} onPress={addTag}>
            <Text style={styles.addTagText}>Adicionar Tag</Text>
          </TouchableOpacity>
        </View>

        {/* Conteúdo */}
        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>Conteúdo</Text>
          <TextInput
            style={styles.contentInput}
            placeholder="Digite o conteúdo da anotação..."
            placeholderTextColor={colors.textSecondary}
            value={content}
            onChangeText={setContent}
            multiline
            numberOfLines={10}
          />
        </View>

        {/* Botão Salvar */}
        <TouchableOpacity
          style={[styles.saveButton, (!hasUnsavedChanges || saving) && styles.saveButtonDisabled]}
          onPress={saveNote}
          disabled={!hasUnsavedChanges || saving}
        >
          <Text style={styles.saveButtonText}>
            {saving ? 'Salvando...' : 'Salvar Anotação'}
          </Text>
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
};

export default EditNoteScreen;