import { useEffect, useRef } from 'react';
import { useNavigation } from '@react-navigation/native';
import InterstitialAdService from '../services/InterstitialAdService';
import { AD_CONFIG } from '../config/ads';

interface UseInterstitialAdOptions {
  showOnNavigate?: boolean;
  navigationDelay?: number;
  excludeRoutes?: string[];
}

export const useInterstitialAd = (options: UseInterstitialAdOptions = {}) => {
  const {
    showOnNavigate = true,
    navigationDelay = AD_CONFIG.NAVIGATION_DELAY_AFTER_AD,
    excludeRoutes = []
  } = options;

  const navigation = useNavigation();
  const adService = useRef(InterstitialAdService.getInstance());
  const navigationCount = useRef(0);

  useEffect(() => {
    // Pré-carregar anúncio quando o componente monta
    adService.current.preloadAd();
  }, []);

  const showInterstitialAd = async (): Promise<boolean> => {
    try {
      if (!adService.current) {
        console.log('📭 Serviço de anúncios não está disponível');
        return false;
      }
      return await adService.current.showAd();
    } catch (error) {
      console.error('❌ Erro ao mostrar anúncio intersticial:', error);
      return false;
    }
  };

  const navigateWithAd = async (routeName: string, params?: any) => {
    try {
      // Verificar se a rota está excluída
      if (excludeRoutes.includes(routeName)) {
        navigation.navigate(routeName as never, params as never);
        return;
      }

      // Incrementar contador de navegação
      navigationCount.current += 1;

      // Mostrar anúncio baseado na configuração
      const shouldShowAd = navigationCount.current % AD_CONFIG.NAVIGATION_AD_FREQUENCY === 0;

      if (shouldShowAd && showOnNavigate) {
        const adShown = await showInterstitialAd();
        
        if (adShown) {
          // Aguardar um pouco antes de navegar
          setTimeout(() => {
            navigation.navigate(routeName as never, params as never);
          }, navigationDelay);
        } else {
          // Se não conseguiu mostrar o anúncio, navegar imediatamente
          navigation.navigate(routeName as never, params as never);
        }
      } else {
        // Navegar sem anúncio
        navigation.navigate(routeName as never, params as never);
      }
    } catch (error) {
      console.error('❌ Erro na navegação com anúncio:', error);
      // Em caso de erro, navegar normalmente
      navigation.navigate(routeName as never, params as never);
    }
  };

  const isAdReady = (): boolean => {
    return adService.current.isAdReady();
  };

  const preloadAd = (): void => {
    adService.current.preloadAd();
  };

  return {
    showInterstitialAd,
    navigateWithAd,
    isAdReady,
    preloadAd,
  };
};
