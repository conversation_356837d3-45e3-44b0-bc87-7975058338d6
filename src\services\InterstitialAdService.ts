import { InterstitialAd, AdEventType } from 'react-native-google-mobile-ads';
import { getAdUnitId, AD_CONFIG } from '../config/ads';

class InterstitialAdService {
  private static instance: InterstitialAdService;
  private interstitialAd: InterstitialAd | null = null;
  private isLoaded = false;
  private isLoading = false;
  private lastShownTime = 0;
  private readonly MIN_INTERVAL = AD_CONFIG.MIN_INTERVAL_BETWEEN_ADS;

  private constructor() {
    this.initializeAd();
  }

  public static getInstance(): InterstitialAdService {
    if (!InterstitialAdService.instance) {
      InterstitialAdService.instance = new InterstitialAdService();
    }
    return InterstitialAdService.instance;
  }

  private initializeAd(): void {
    try {
      // Verificar se o módulo está disponível
      if (!InterstitialAd) {
        console.warn('⚠️ Módulo react-native-google-mobile-ads não está disponível');
        return;
      }

      // Use configuração centralizada de anúncios
      const adUnitId = getAdUnitId('interstitial');

      console.log('🎯 Inicializando Interstitial Ad com ID:', adUnitId);

      this.interstitialAd = InterstitialAd.createForAdRequest(adUnitId);

      this.setupEventListeners();

      // Aguardar um pouco antes de carregar o primeiro anúncio
      setTimeout(() => {
        this.loadAd();
      }, 1000);

    } catch (error) {
      console.error('❌ Erro ao inicializar Interstitial Ad:', error);
      this.interstitialAd = null;
    }
  }

  private setupEventListeners(): void {
    if (!this.interstitialAd) return;

    this.interstitialAd.addAdEventListener(AdEventType.LOADED, () => {
      this.isLoaded = true;
      this.isLoading = false;
      console.log('🎯 Interstitial Ad carregado com sucesso');
    });

    this.interstitialAd.addAdEventListener(AdEventType.ERROR, (error) => {
      this.isLoaded = false;
      this.isLoading = false;
      console.error('❌ Erro ao carregar Interstitial Ad:', error);
      
      // Tentar recarregar após 5 segundos
      setTimeout(() => {
        this.loadAd();
      }, 5000);
    });

    this.interstitialAd.addAdEventListener(AdEventType.OPENED, () => {
      console.log('📺 Interstitial Ad aberto');
    });

    this.interstitialAd.addAdEventListener(AdEventType.CLOSED, () => {
      console.log('✅ Interstitial Ad fechado');
      this.isLoaded = false;
      this.lastShownTime = Date.now();
      
      // Carregar próximo anúncio
      setTimeout(() => {
        this.loadAd();
      }, 1000);
    });
  }

  private loadAd(): void {
    if (!this.interstitialAd || this.isLoading || this.isLoaded) return;

    try {
      this.isLoading = true;
      this.interstitialAd.load();
      console.log('🔄 Carregando Interstitial Ad...');
    } catch (error) {
      this.isLoading = false;
      console.error('❌ Erro ao carregar Interstitial Ad:', error);
    }
  }

  public async showAd(): Promise<boolean> {
    try {
      // Verificar se o serviço está disponível
      if (!this.interstitialAd) {
        console.log('📭 Interstitial Ad não está inicializado');
        return false;
      }

      // Verificar se passou tempo suficiente desde o último anúncio
      const timeSinceLastAd = Date.now() - this.lastShownTime;
      if (timeSinceLastAd < this.MIN_INTERVAL) {
        console.log('⏰ Muito cedo para mostrar outro anúncio');
        return false;
      }

      if (!this.isLoaded) {
        console.log('📭 Interstitial Ad não está carregado');
        this.loadAd(); // Tentar carregar
        return false;
      }

      console.log('🎯 Mostrando Interstitial Ad');
      await this.interstitialAd.show();
      return true;
    } catch (error) {
      console.error('❌ Erro ao mostrar Interstitial Ad:', error);
      return false;
    }
  }

  public isAdReady(): boolean {
    return this.isLoaded;
  }

  public preloadAd(): void {
    if (!this.isLoaded && !this.isLoading) {
      this.loadAd();
    }
  }
}

export default InterstitialAdService;
