import messaging from '@react-native-firebase/messaging';
import { Alert, Platform } from 'react-native';

class FirebaseMessagingService {
  private static instance: FirebaseMessagingService;

  public static getInstance(): FirebaseMessagingService {
    if (!FirebaseMessagingService.instance) {
      FirebaseMessagingService.instance = new FirebaseMessagingService();
    }
    return FirebaseMessagingService.instance;
  }

  // Inicializar o Firebase Messaging
  async initialize() {
    try {
      console.log('🔥 Inicializando Firebase Messaging...');
      
      // Habilitar auto-inicialização
      messaging().setAutoInitEnabled(true);
      
      // Solicitar permissão para notificações
      await this.requestPermission();
      
      // Obter token FCM
      const token = await this.getFCMToken();
      console.log('📱 FCM Token:', token);
      
      // Configurar listeners
      this.setupMessageListeners();
      
      console.log('✅ Firebase Messaging inicializado com sucesso!');
      
      return token;
    } catch (error) {
      console.error('❌ Erro ao inicializar Firebase Messaging:', error);
      throw error;
    }
  }

  // Solicitar permissão para notificações
  async requestPermission() {
    try {
      const authStatus = await messaging().requestPermission();
      const enabled =
        authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
        authStatus === messaging.AuthorizationStatus.PROVISIONAL;

      if (enabled) {
        console.log('✅ Permissão para notificações concedida');
        return true;
      } else {
        console.log('❌ Permissão para notificações negada');
        Alert.alert(
          'Notificações',
          'Para receber atualizações importantes, habilite as notificações nas configurações do app.'
        );
        return false;
      }
    } catch (error) {
      console.error('❌ Erro ao solicitar permissão:', error);
      return false;
    }
  }

  // Obter token FCM
  async getFCMToken() {
    try {
      const token = await messaging().getToken();
      console.log('📱 Token FCM obtido:', token);
      
      // Aqui você pode enviar o token para seu servidor
      // await this.sendTokenToServer(token);
      
      return token;
    } catch (error) {
      console.error('❌ Erro ao obter token FCM:', error);
      return null;
    }
  }

  // Configurar listeners de mensagens
  setupMessageListeners() {
    // Mensagem recebida quando o app está em foreground
    messaging().onMessage(async remoteMessage => {
      console.log('📨 Mensagem recebida em foreground:', remoteMessage);
      
      // Mostrar notificação local ou alert
      if (remoteMessage.notification) {
        Alert.alert(
          remoteMessage.notification.title || 'Escola Sabatina',
          remoteMessage.notification.body || 'Nova mensagem recebida'
        );
      }
    });

    // Mensagem recebida quando o app está em background/quit
    messaging().onNotificationOpenedApp(remoteMessage => {
      console.log('📨 Notificação aberta (background):', remoteMessage);
      
      // Navegar para tela específica se necessário
      this.handleNotificationNavigation(remoteMessage);
    });

    // Verificar se o app foi aberto por uma notificação
    messaging()
      .getInitialNotification()
      .then(remoteMessage => {
        if (remoteMessage) {
          console.log('📨 App aberto por notificação:', remoteMessage);
          this.handleNotificationNavigation(remoteMessage);
        }
      });

    // Listener para refresh do token
    messaging().onTokenRefresh(token => {
      console.log('🔄 Token FCM atualizado:', token);
      // Enviar novo token para o servidor
      // this.sendTokenToServer(token);
    });
  }

  // Lidar com navegação baseada na notificação
  handleNotificationNavigation(remoteMessage: any) {
    console.log('🧭 Processando navegação da notificação:', remoteMessage);
    
    // Aqui você pode implementar navegação baseada nos dados da notificação
    if (remoteMessage.data) {
      const { screen, lessonId, videoId } = remoteMessage.data;
      
      // Exemplo de navegação
      switch (screen) {
        case 'lesson':
          // Navegar para lição específica
          console.log('📖 Navegando para lição:', lessonId);
          break;
        case 'video':
          // Navegar para vídeo específico
          console.log('📺 Navegando para vídeo:', videoId);
          break;
        default:
          console.log('🏠 Navegando para home');
      }
    }
  }

  // Enviar token para servidor (implementar conforme sua API)
  async sendTokenToServer(token: string) {
    try {
      console.log('📤 Enviando token para servidor:', token);
      
      // Implementar chamada para sua API
      // const response = await fetch('https://sua-api.com/fcm-token', {
      //   method: 'POST',
      //   headers: {
      //     'Content-Type': 'application/json',
      //   },
      //   body: JSON.stringify({
      //     token,
      //     platform: Platform.OS,
      //     userId: 'user-id', // ID do usuário logado
      //   }),
      // });
      
      console.log('✅ Token enviado para servidor com sucesso');
    } catch (error) {
      console.error('❌ Erro ao enviar token para servidor:', error);
    }
  }

  // Inscrever em tópico
  async subscribeToTopic(topic: string) {
    try {
      await messaging().subscribeToTopic(topic);
      console.log(`✅ Inscrito no tópico: ${topic}`);
    } catch (error) {
      console.error(`❌ Erro ao se inscrever no tópico ${topic}:`, error);
    }
  }

  // Desinscrever de tópico
  async unsubscribeFromTopic(topic: string) {
    try {
      await messaging().unsubscribeFromTopic(topic);
      console.log(`✅ Desinscrito do tópico: ${topic}`);
    } catch (error) {
      console.error(`❌ Erro ao se desinscrever do tópico ${topic}:`, error);
    }
  }
}

export default FirebaseMessagingService;
