buildscript {
    ext {
        buildToolsVersion = "35.0.0"
        minSdkVersion = 24
        compileSdkVersion = 35
        targetSdkVersion = 35
        ndkVersion = "27.1.12297006"
        kotlinVersion = "2.1.20"
    }
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath("com.android.tools.build:gradle:8.8.0")
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin")
        classpath("com.google.gms:google-services:4.4.0")
    }

    // Forçar versão específica do AGP para compatibilidade com Android Studio
    configurations.classpath {
        resolutionStrategy {
            force 'com.android.tools.build:gradle:8.8.0'
        }
    }
}

apply plugin: "com.facebook.react.rootproject"
