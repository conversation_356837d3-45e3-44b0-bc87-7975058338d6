# Script para corrigir compatibilidade com Android Studio
# Ajusta versões do AGP e Gradle para compatibilidade

Write-Host "🔧 Corrigindo compatibilidade com Android Studio..." -ForegroundColor Green

# Verificar se estamos no diretório correto
if (-not (Test-Path "package.json")) {
    Write-Host "❌ Erro: Execute este script na raiz do projeto React Native" -ForegroundColor Red
    exit 1
}

Write-Host "📋 Versões ajustadas:" -ForegroundColor Cyan
Write-Host "   🔧 Android Gradle Plugin: 8.8.0 (compatível com Android Studio)" -ForegroundColor White
Write-Host "   📦 Gradle Wrapper: 8.9 (compatível com AGP 8.8.0)" -ForegroundColor White

Write-Host "🧹 Limpando cache e builds anteriores..." -ForegroundColor Cyan

# Limpar cache do npm
npm cache clean --force

# Limpar node_modules e reinstalar
if (Test-Path "node_modules") {
    Write-Host "   🗑️ Removendo node_modules..." -ForegroundColor Yellow
    Remove-Item -Recurse -Force "node_modules"
}

Write-Host "   📦 Reinstalando dependências..." -ForegroundColor Yellow
npm install

# Limpar builds do Android
Write-Host "   🧹 Limpando builds Android..." -ForegroundColor Yellow
Set-Location android

# Remover pastas de build
if (Test-Path "build") {
    Remove-Item -Recurse -Force "build"
}
if (Test-Path "app/build") {
    Remove-Item -Recurse -Force "app/build"
}
if (Test-Path ".gradle") {
    Remove-Item -Recurse -Force ".gradle"
}

# Limpar com Gradle
if (Test-Path "gradlew.bat") {
    Write-Host "   🔄 Executando gradle clean..." -ForegroundColor Yellow
    .\gradlew.bat clean
} else {
    Write-Host "   🔄 Executando gradle clean..." -ForegroundColor Yellow
    .\gradlew clean
}

Set-Location ..

Write-Host "🔄 Sincronizando projeto..." -ForegroundColor Cyan

# Gerar bundle para garantir que tudo está funcionando
Write-Host "   📦 Gerando bundle JavaScript..." -ForegroundColor Yellow

$assetsDir = "android/app/src/main/assets"
if (-not (Test-Path $assetsDir)) {
    New-Item -ItemType Directory -Path $assetsDir -Force
}

npx react-native bundle `
    --platform android `
    --dev false `
    --entry-file index.js `
    --bundle-output android/app/src/main/assets/index.android.bundle `
    --assets-dest android/app/src/main/res

Write-Host "✅ Correções aplicadas com sucesso!" -ForegroundColor Green
Write-Host ""
Write-Host "📱 Agora você pode:" -ForegroundColor Cyan
Write-Host "   1. Abrir Android Studio" -ForegroundColor White
Write-Host "   2. Open an Existing Project" -ForegroundColor White
Write-Host "   3. Selecionar pasta: $PWD\android" -ForegroundColor White
Write-Host "   4. Aguardar sincronização automática" -ForegroundColor White
Write-Host ""
Write-Host "🏗️ Para gerar builds:" -ForegroundColor Cyan
Write-Host "   📱 APK: Build > Build Bundle(s) / APK(s) > Build APK(s)" -ForegroundColor White
Write-Host "   📦 AAB: Build > Build Bundle(s) / APK(s) > Build Bundle(s)" -ForegroundColor White
Write-Host ""
Write-Host "🔧 Ou via linha de comando:" -ForegroundColor Cyan
Write-Host "   cd android" -ForegroundColor Gray
Write-Host "   .\gradlew.bat assembleRelease    # Para APK" -ForegroundColor Gray
Write-Host "   .\gradlew.bat bundleRelease      # Para AAB" -ForegroundColor Gray
Write-Host ""
Write-Host "⚠️  Se ainda houver problemas:" -ForegroundColor Yellow
Write-Host "   1. File > Invalidate Caches and Restart no Android Studio" -ForegroundColor White
Write-Host "   2. Verificar se Android Studio está atualizado" -ForegroundColor White
Write-Host "   3. Tools > SDK Manager > verificar SDK Tools" -ForegroundColor White
