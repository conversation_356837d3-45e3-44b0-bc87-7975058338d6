import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

export type ThemeType = 'light' | 'dark';

interface ThemeColors {
  // Cores principais
  primary: string;
  secondary: string;
  accent: string;
  
  // Cores de fundo
  background: string;
  surface: string;
  card: string;
  
  // Cores de texto
  text: string;
  textSecondary: string;
  textInverse: string;
  
  // Cores de estado
  success: string;
  warning: string;
  error: string;
  info: string;
  
  // Cores de interface
  border: string;
  shadow: string;
  overlay: string;
  
  // Cores específicas
  accentBlue: string;
  accentGreen: string;
  accentPurple: string;
  accentOrange: string;
}

const lightTheme: ThemeColors = {
  primary: '#2E7D32',
  secondary: '#FFA726',
  accent: '#FF7043',
  
  background: '#FFFFFF',
  surface: '#F8F9FA',
  card: '#FFFFFF',
  
  text: '#212121',
  textSecondary: '#757575',
  textInverse: '#FFFFFF',
  
  success: '#4CAF50',
  warning: '#FF9800',
  error: '#F44336',
  info: '#2196F3',
  
  border: '#E0E0E0',
  shadow: '#000000',
  overlay: 'rgba(0, 0, 0, 0.5)',
  
  accentBlue: '#1976D2',
  accentGreen: '#388E3C',
  accentPurple: '#7B1FA2',
  accentOrange: '#F57C00',
};

const darkTheme: ThemeColors = {
  primary: '#4CAF50',
  secondary: '#FFB74D',
  accent: '#FF8A65',
  
  background: '#121212',
  surface: '#1E1E1E',
  card: '#2D2D2D',
  
  text: '#FFFFFF',
  textSecondary: '#B0B0B0',
  textInverse: '#000000',
  
  success: '#66BB6A',
  warning: '#FFB74D',
  error: '#EF5350',
  info: '#42A5F5',
  
  border: '#404040',
  shadow: '#000000',
  overlay: 'rgba(0, 0, 0, 0.7)',
  
  accentBlue: '#42A5F5',
  accentGreen: '#66BB6A',
  accentPurple: '#AB47BC',
  accentOrange: '#FFB74D',
};

interface ThemeContextType {
  theme: ThemeType;
  colors: ThemeColors;
  toggleTheme: () => void;
  setTheme: (theme: ThemeType) => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

const THEME_STORAGE_KEY = '@escola_sabatina_theme';

interface ThemeProviderProps {
  children: ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const [theme, setThemeState] = useState<ThemeType>('light');

  useEffect(() => {
    loadTheme();
  }, []);

  const loadTheme = async () => {
    try {
      const savedTheme = await AsyncStorage.getItem(THEME_STORAGE_KEY);
      if (savedTheme && (savedTheme === 'light' || savedTheme === 'dark')) {
        setThemeState(savedTheme);
      }
    } catch (error) {
      console.error('Erro ao carregar tema:', error);
    }
  };

  const saveTheme = async (newTheme: ThemeType) => {
    try {
      await AsyncStorage.setItem(THEME_STORAGE_KEY, newTheme);
    } catch (error) {
      console.error('Erro ao salvar tema:', error);
    }
  };

  const setTheme = (newTheme: ThemeType) => {
    setThemeState(newTheme);
    saveTheme(newTheme);
  };

  const toggleTheme = () => {
    const newTheme = theme === 'light' ? 'dark' : 'light';
    setTheme(newTheme);
  };

  const colors = theme === 'light' ? lightTheme : darkTheme;

  return (
    <ThemeContext.Provider value={{ theme, colors, toggleTheme, setTheme }}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme deve ser usado dentro de um ThemeProvider');
  }
  return context;
};
