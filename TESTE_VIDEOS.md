# Como Testar a Funcionalidade de Vídeos

## Passos para Testar:

1. **Limpar e Reconstruir o Projeto:**
   ```bash
   # Limpar cache
   npx react-native start --reset-cache
   
   # Para Android
   cd android && ./gradlew clean && cd ..
   npx react-native run-android
   
   # Para iOS (se aplicável)
   cd ios && pod install && cd ..
   npx react-native run-ios
   ```

2. **Verificar Logs:**
   - Abra o Metro bundler e observe os logs
   - Verifique se aparecem mensagens como:
     - "Tentando abrir vídeo: {name, claimId, url}"
     - "Resposta da API: ..."
     - "Streaming URL encontrada: ..."

3. **Testar a Funcionalidade:**
   - Navegue para a tela de Vídeos
   - Toque em qualquer vídeo da lista
   - Observe se:
     - O loading aparece
     - O vídeo abre na tela do player
     - O vídeo reproduz corretamente

## Possíveis Problemas e Soluções:

### Se os vídeos ainda não abrirem:

1. **Verificar conectividade:**
   - Certifique-se de que o dispositivo tem acesso à internet
   - Teste a API manualmente: https://lighthouse.odysee.tv/search?s=@Davi_Game

2. **Verificar logs de erro:**
   - Procure por mensagens de erro no console
   - Verifique se a API está retornando dados válidos

3. **Testar com vídeo local:**
   - Temporariamente, teste com uma URL de vídeo conhecida
   - Exemplo: substitua a URL por um vídeo de teste

### Se o player não reproduz:

1. **Verificar permissões:**
   - Certifique-se de que as permissões foram aplicadas
   - Reinstale o app se necessário

2. **Verificar react-native-video:**
   - Verifique se a biblioteca está instalada corretamente
   - Execute: `npx react-native unlink react-native-video && npx react-native link react-native-video`

## Debugging Adicional:

Se ainda houver problemas, adicione este código temporário no VideosScreen para testar a API:

```javascript
const testAPI = async () => {
  try {
    const response = await fetch('https://lighthouse.odysee.tv/search?s=@Davi_Game&claimType=stream&mediaType=video&nsfw=false&size=5');
    const data = await response.json();
    console.log('Dados da API:', data);
    Alert.alert('API Test', JSON.stringify(data, null, 2));
  } catch (error) {
    console.error('Erro na API:', error);
    Alert.alert('Erro', error.message);
  }
};
```
