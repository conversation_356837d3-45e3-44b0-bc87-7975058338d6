# 🎯 Configuração AdMob para Produção

Este documento explica como os anúncios estão configurados para publicação na Google Play Store.

## 📋 Status Atual

### ✅ Configurado para PRODUÇÃO
- **Banner Ads**: IDs de produção ativos
- **Interstitial Ads**: IDs de produção ativos
- **Modo de teste**: DESABILITADO
- **Pronto para**: Google Play Store

## 🔧 Configuração Técnica

### IDs de Produção (Ativos)
```typescript
PRODUCTION_ADS = {
  APP_ID: 'ca-app-pub-8578368178484138~9333274602',
  BANNER_ID: 'ca-app-pub-8578368178484138/4623873403',
  INTERSTITIAL_ID: 'ca-app-pub-8578368178484138/6541258600',
}
```

### Configuração Atual
```typescript
// src/config/ads.ts
export const AD_CONFIG = {
  USE_PRODUCTION: true, // ✅ PRODUÇÃO ATIVA
  MIN_INTERVAL_BETWEEN_ADS: 30000, // 30 segundos
  NAVIGATION_AD_FREQUENCY: 3, // A cada 3 navegações
  TAB_PRESS_AD_FREQUENCY: 4, // A cada 4 mudanças de tab
}
```

## 📱 Comportamento dos Anúncios

### Banner Ads
- **Localização**: HomeScreen, SettingsScreen, LessonScreen, NotesScreen
- **Tipo**: Banner padrão (320x50)
- **Carregamento**: Automático ao abrir a tela
- **Fallback**: Oculta se falhar ao carregar

### Interstitial Ads
- **Frequência**: A cada 3 navegações entre telas
- **Frequência (tabs)**: A cada 4 mudanças de tab
- **Intervalo mínimo**: 30 segundos entre anúncios
- **Comportamento**: Não bloqueia navegação se falhar

## 🚀 Build para Produção

### Comando Rápido
```bash
# Windows
powershell -ExecutionPolicy Bypass -File scripts/build-production.ps1

# Linux/macOS
chmod +x scripts/build-production.sh
./scripts/build-production.sh
```

### Verificações Automáticas
O script verifica:
- ✅ Configuração de produção ativa
- ✅ Android SDK configurado
- ✅ Dependências atualizadas
- ✅ Cache limpo

### Arquivos Gerados
- **APK**: `android/app/build/outputs/apk/release/app-release.apk`
- **AAB**: `android/app/build/outputs/bundle/release/app-release.aab`

## 🧪 Testes Necessários

### Antes da Publicação
1. **Instalar APK** em dispositivos reais
2. **Verificar banner ads** em todas as telas
3. **Testar interstitial ads** navegando entre telas
4. **Confirmar frequência** dos anúncios
5. **Verificar comportamento** sem internet

### Checklist de Anúncios
- [ ] Banner carrega no HomeScreen
- [ ] Banner carrega no SettingsScreen  
- [ ] Banner carrega no LessonScreen
- [ ] Banner carrega no NotesScreen
- [ ] Interstitial aparece ao navegar (a cada 3x)
- [ ] Interstitial aparece ao mudar tab (a cada 4x)
- [ ] Intervalo de 30s respeitado
- [ ] App não trava se anúncio falhar

## 📊 Monitoramento

### Logs Importantes
```
🎯 Configuração de Anúncios: {
  mode: 'PRODUCTION',
  bannerId: 'ca-app-pub-8578368178484138/4623873403',
  interstitialId: 'ca-app-pub-8578368178484138/6541258600'
}

🎯 Banner Ad carregado com sucesso
🎯 Interstitial Ad carregado com sucesso
🎯 Mostrando Interstitial Ad
```

### Métricas no AdMob
Após publicação, monitore:
- **Impressões** de banner e interstitial
- **CTR** (Click Through Rate)
- **eCPM** (effective Cost Per Mille)
- **Taxa de preenchimento**

## ⚠️ Importante

### Para Publicação na Google Play
- ✅ **Sempre usar IDs de produção**
- ✅ **Testar em dispositivos reais**
- ✅ **Verificar políticas do AdMob**
- ✅ **Respeitar diretrizes da Google Play**

### Políticas AdMob
- **Não clicar** nos próprios anúncios
- **Não incentivar** cliques falsos
- **Respeitar** conteúdo apropriado
- **Seguir** diretrizes de posicionamento

## 🔄 Alternar para Teste (Desenvolvimento)

Se precisar voltar para modo de teste:

```typescript
// src/config/ads.ts
export const AD_CONFIG = {
  USE_PRODUCTION: false, // ⚠️ APENAS PARA DESENVOLVIMENTO
  // ...
}
```

**⚠️ LEMBRETE**: Sempre voltar para `true` antes da publicação!

## 📞 Suporte

### Problemas Comuns
1. **Anúncios não carregam**: Verificar IDs e conexão
2. **App trava**: Verificar tratamento de erros
3. **Baixo eCPM**: Otimizar posicionamento
4. **Política violada**: Revisar conteúdo

### Recursos
- [AdMob Help Center](https://support.google.com/admob)
- [Google Play Policies](https://play.google.com/about/developer-content-policy/)
- [React Native Google Mobile Ads](https://docs.page/invertase/react-native-google-mobile-ads)
