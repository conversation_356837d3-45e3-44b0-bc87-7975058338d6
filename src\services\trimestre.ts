import { api } from './api';
import { Trimestre } from '../models/Trimestre';

export async function listarTrimestres(token: string): Promise<Trimestre[]> {
  const response = await api.post('/Trimestre/', {}, {
    headers: { Authorization: `Bear<PERSON> ${token}` },
  });
  return response.data;
}

export async function getTrimestreAtual(token: string): Promise<Trimestre | null> {
  const trimestres = await listarTrimestres(token);
  return trimestres.length > 0 ? trimestres[trimestres.length - 1] : null;
} 