# Script para verificar se os builds foram gerados corretamente

Write-Host "🔍 Verificando builds gerados..." -ForegroundColor Green

$apkPath = "android/app/build/outputs/apk/release/app-release.apk"
$aabPath = "android/app/build/outputs/bundle/release/app-release.aab"

Write-Host ""
Write-Host "📱 Verificando APK..." -ForegroundColor Cyan
if (Test-Path $apkPath) {
    $apkSize = (Get-Item $apkPath).Length / 1MB
    Write-Host "   ✅ APK encontrado: $apkPath" -ForegroundColor Green
    Write-Host "   📏 Tamanho: $([math]::Round($apkSize, 2)) MB" -ForegroundColor White
    
    # Verificar se o APK não está vazio
    if ($apkSize -gt 0.5) {
        Write-Host "   ✅ Tamanho adequado para publicação" -ForegroundColor Green
    } else {
        Write-Host "   ⚠️  APK muito pequeno, pode estar corrompido" -ForegroundColor Yellow
    }
} else {
    Write-Host "   ❌ APK não encontrado em: $apkPath" -ForegroundColor Red
}

Write-Host ""
Write-Host "📦 Verificando AAB..." -ForegroundColor Cyan
if (Test-Path $aabPath) {
    $aabSize = (Get-Item $aabPath).Length / 1MB
    Write-Host "   ✅ AAB encontrado: $aabPath" -ForegroundColor Green
    Write-Host "   📏 Tamanho: $([math]::Round($aabSize, 2)) MB" -ForegroundColor White
    
    # Verificar se o AAB não está vazio
    if ($aabSize -gt 0.3) {
        Write-Host "   ✅ Tamanho adequado para Google Play" -ForegroundColor Green
    } else {
        Write-Host "   ⚠️  AAB muito pequeno, pode estar corrompido" -ForegroundColor Yellow
    }
} else {
    Write-Host "   ❌ AAB não encontrado em: $aabPath" -ForegroundColor Red
}

Write-Host ""
Write-Host "🔧 Verificando configurações..." -ForegroundColor Cyan

# Verificar se os anúncios estão em produção
$adsConfig = Get-Content "src/config/ads.ts" -Raw
if ($adsConfig -match "USE_PRODUCTION: true") {
    Write-Host "   ✅ Anúncios configurados para PRODUÇÃO" -ForegroundColor Green
} else {
    Write-Host "   ⚠️  Anúncios podem estar em modo de teste" -ForegroundColor Yellow
}

# Verificar versão no build.gradle
$buildGradle = Get-Content "android/app/build.gradle" -Raw
if ($buildGradle -match 'versionCode\s+(\d+)') {
    $versionCode = $matches[1]
    Write-Host "   📱 Version Code: $versionCode" -ForegroundColor White
}
if ($buildGradle -match 'versionName\s+"([^"]+)"') {
    $versionName = $matches[1]
    Write-Host "   🏷️  Version Name: $versionName" -ForegroundColor White
}

Write-Host ""
Write-Host "📋 Próximos passos:" -ForegroundColor Cyan

if ((Test-Path $apkPath) -and (Test-Path $aabPath)) {
    Write-Host "   1. ✅ Testar APK em dispositivo real:" -ForegroundColor White
    Write-Host "      adb install $apkPath" -ForegroundColor Gray
    Write-Host ""
    Write-Host "   2. ✅ Verificar anúncios funcionando" -ForegroundColor White
    Write-Host "      - Banner ads em todas as telas" -ForegroundColor Gray
    Write-Host "      - Interstitial ads na navegação" -ForegroundColor Gray
    Write-Host ""
    Write-Host "   3. ✅ Upload para Google Play:" -ForegroundColor White
    Write-Host "      - Usar arquivo: $aabPath" -ForegroundColor Gray
    Write-Host "      - Google Play Console > App releases" -ForegroundColor Gray
} else {
    Write-Host "   ❌ Builds não foram gerados corretamente" -ForegroundColor Red
    Write-Host "   🔧 Execute novamente o script de build:" -ForegroundColor Yellow
    Write-Host "      powershell -ExecutionPolicy Bypass -File scripts/android-studio-build.ps1" -ForegroundColor Gray
}

Write-Host ""
Write-Host "🔗 Links úteis:" -ForegroundColor Cyan
Write-Host "   📱 Google Play Console: https://play.google.com/console" -ForegroundColor Blue
Write-Host "   🎯 AdMob: https://admob.google.com" -ForegroundColor Blue
Write-Host "   📋 Android Developer: https://developer.android.com" -ForegroundColor Blue
